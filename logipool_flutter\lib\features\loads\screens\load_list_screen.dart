import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/models/load_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/widgets/unified_header.dart';
import '../../auth/bloc/auth_bloc.dart';
import '../bloc/load_bloc.dart';
import '../widgets/load_card.dart';
import '../widgets/load_filter_bottom_sheet.dart';
import '../widgets/loads_map_view.dart';

class LoadListScreen extends StatefulWidget {
  const LoadListScreen({super.key});

  @override
  State<LoadListScreen> createState() => _LoadListScreenState();
}

class _LoadListScreenState extends State<LoadListScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  LoadType? _selectedLoadType;
  LoadStatus? _selectedStatus;
  Priority? _selectedPriority;
  bool _verifiedOnly = false;

  int _currentPage = 0;
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _initializeTabs();
    _scrollController.addListener(_onScroll);

    // Load initial data
    _loadData();
  }

  void _initializeTabs() {
    final user = context.read<AuthService>().currentUser;
    final tabCount = _getTabCountForUser(user);
    _tabController = TabController(length: tabCount, vsync: this);
  }

  int _getTabCountForUser(UserModel? user) {
    if (user == null) return 3; // Posted, Bidding, Map

    if (user.role == 'CLIENT') {
      return 6; // All tabs
    } else if (user.company != null) {
      return 4; // Posted, Bidding, Company, Map
    } else {
      return 3; // Posted, Bidding, Map
    }
  }

  List<Tab> _getTabsForUser(UserModel? user) {
    final tabs = <Tab>[];

    if (user == null) {
      return [
        const Tab(icon: Icon(Icons.post_add), text: 'Posted'),
        const Tab(icon: Icon(Icons.gavel), text: 'Bidding'),
        const Tab(icon: Icon(Icons.map), text: 'Map'),
      ];
    }

    if (user.role == 'CLIENT') {
      return const [
        Tab(icon: Icon(Icons.list_alt), text: 'All'),
        Tab(icon: Icon(Icons.post_add), text: 'Posted'),
        Tab(icon: Icon(Icons.gavel), text: 'Bidding'),
        Tab(icon: Icon(Icons.map), text: 'Map'),
        Tab(icon: Icon(Icons.verified), text: 'Verified'),
        Tab(icon: Icon(Icons.person), text: 'My Loads'),
      ];
    } else if (user.company != null) {
      return const [
        Tab(icon: Icon(Icons.post_add), text: 'Posted'),
        Tab(icon: Icon(Icons.gavel), text: 'Bidding'),
        Tab(icon: Icon(Icons.business), text: 'Company'),
        Tab(icon: Icon(Icons.map), text: 'Map'),
      ];
    } else {
      return const [
        Tab(icon: Icon(Icons.post_add), text: 'Posted'),
        Tab(icon: Icon(Icons.gavel), text: 'Bidding'),
        Tab(icon: Icon(Icons.map), text: 'Map'),
      ];
    }
  }

  List<Widget> _getTabViewsForUser(UserModel? user) {
    if (user == null) {
      return [
        _buildLoadListView(), // Posted
        _buildBiddingLoadListView(), // Bidding
        _buildMapView(), // Map
      ];
    }

    if (user.role == 'CLIENT') {
      return [
        _buildLoadListView(), // All
        _buildLoadListView(), // Posted
        _buildBiddingLoadListView(), // Bidding
        _buildMapView(), // Map
        _buildLoadListView(), // Verified
        _buildLoadListView(), // My Loads
      ];
    } else if (user.company != null) {
      return [
        _buildLoadListView(), // Posted
        _buildBiddingLoadListView(), // Bidding
        _buildLoadListView(), // Company
        _buildMapView(), // Map
      ];
    } else {
      return [
        _buildLoadListView(), // Posted
        _buildBiddingLoadListView(), // Bidding
        _buildMapView(), // Map
      ];
    }
  }

  Widget _buildMapView() {
    return BlocBuilder<LoadBloc, LoadState>(
      builder: (context, state) {
        if (state is LoadLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is LoadListLoaded) {
          final loads = state.response.content;
          return LoadsMapView(
            loads: loads,
            onRefresh: () {
              setState(() {
                _currentPage = 0;
              });
              _loadData();
            },
          );
        }

        return const Center(
          child: Text('Something went wrong'),
        );
      },
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _loadData() {
    final user = context.read<AuthService>().currentUser;
    final currentTab = _tabController.index;

    if (user?.role == 'CLIENT') {
      // Client tabs: All, Posted, Bidding, Map, Verified, My Loads
      switch (currentTab) {
        case 0: // All Loads
          context
              .read<LoadBloc>()
              .add(const LoadFetchAllRequested(isRefresh: true));
          break;
        case 1: // Posted Loads
          context.read<LoadBloc>().add(
                const LoadFetchByStatusRequested(status: LoadStatus.posted),
              );
          break;
        case 2: // Available for Bidding
          context.read<LoadBloc>().add(
                const LoadFetchByStatusRequested(status: LoadStatus.posted),
              );
          break;
        case 3: // Map View
          context
              .read<LoadBloc>()
              .add(const LoadFetchAllRequested(isRefresh: true));
          break;
        case 4: // Verified Loads
          context.read<LoadBloc>().add(const LoadFetchVerifiedRequested());
          break;
        case 5: // My Loads
          context.read<LoadBloc>().add(const LoadFetchMyLoadsRequested());
          break;
      }
    } else if (user?.company != null) {
      // Company member tabs: Posted, Bidding, Company, Map
      switch (currentTab) {
        case 0: // Posted Loads
          context.read<LoadBloc>().add(
                const LoadFetchByStatusRequested(status: LoadStatus.posted),
              );
          break;
        case 1: // Available for Bidding
          context.read<LoadBloc>().add(
                const LoadFetchByStatusRequested(status: LoadStatus.posted),
              );
          break;
        case 2: // Company Loads (all loads visible to company member)
          context
              .read<LoadBloc>()
              .add(const LoadFetchAllRequested(isRefresh: true));
          break;
        case 3: // Map View
          context
              .read<LoadBloc>()
              .add(const LoadFetchAllRequested(isRefresh: true));
          break;
      }
    } else {
      // Default tabs: Posted, Bidding, Map
      switch (currentTab) {
        case 0: // Posted Loads
          context.read<LoadBloc>().add(
                const LoadFetchByStatusRequested(status: LoadStatus.posted),
              );
          break;
        case 1: // Available for Bidding
          context.read<LoadBloc>().add(
                const LoadFetchByStatusRequested(status: LoadStatus.posted),
              );
          break;
        case 2: // Map View
          context
              .read<LoadBloc>()
              .add(const LoadFetchAllRequested(isRefresh: true));
          break;
      }
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore) {
      _loadMore();
    }
  }

  void _loadMore() {
    final state = context.read<LoadBloc>().state;
    if (state is LoadListLoaded && state.response.hasNextPage) {
      setState(() {
        _isLoadingMore = true;
        _currentPage++;
      });

      // Use the same logic as _loadData but with pagination
      final user = context.read<AuthService>().currentUser;
      final currentTab = _tabController.index;

      if (user?.role == 'CLIENT') {
        // Client tabs: All, Posted, Bidding, Map, Verified, My Loads
        switch (currentTab) {
          case 0: // All Loads
            context.read<LoadBloc>().add(
                  LoadFetchAllRequested(page: _currentPage),
                );
            break;
          case 1: // Posted Loads
            context.read<LoadBloc>().add(
                  LoadFetchByStatusRequested(
                    status: LoadStatus.posted,
                    page: _currentPage,
                  ),
                );
            break;
          case 2: // Available for Bidding
            context.read<LoadBloc>().add(
                  LoadFetchByStatusRequested(
                    status: LoadStatus.posted,
                    page: _currentPage,
                  ),
                );
            break;
          case 3: // Map View
            context.read<LoadBloc>().add(
                  LoadFetchAllRequested(page: _currentPage),
                );
            break;
          case 4: // Verified Loads
            context.read<LoadBloc>().add(
                  LoadFetchVerifiedRequested(page: _currentPage),
                );
            break;
          case 5: // My Loads
            context.read<LoadBloc>().add(
                  LoadFetchMyLoadsRequested(page: _currentPage),
                );
            break;
        }
      } else if (user?.company != null) {
        // Company member tabs: Posted, Bidding, Company, Map
        switch (currentTab) {
          case 0: // Posted Loads
            context.read<LoadBloc>().add(
                  LoadFetchByStatusRequested(
                    status: LoadStatus.posted,
                    page: _currentPage,
                  ),
                );
            break;
          case 1: // Available for Bidding
            context.read<LoadBloc>().add(
                  LoadFetchByStatusRequested(
                    status: LoadStatus.posted,
                    page: _currentPage,
                  ),
                );
            break;
          case 2: // Company Loads
            context.read<LoadBloc>().add(
                  LoadFetchAllRequested(page: _currentPage),
                );
            break;
          case 3: // Map View
            context.read<LoadBloc>().add(
                  LoadFetchAllRequested(page: _currentPage),
                );
            break;
        }
      } else {
        // Default tabs: Posted, Bidding, Map
        switch (currentTab) {
          case 0: // Posted Loads
            context.read<LoadBloc>().add(
                  LoadFetchByStatusRequested(
                    status: LoadStatus.posted,
                    page: _currentPage,
                  ),
                );
            break;
          case 1: // Available for Bidding
            context.read<LoadBloc>().add(
                  LoadFetchByStatusRequested(
                    status: LoadStatus.posted,
                    page: _currentPage,
                  ),
                );
            break;
          case 2: // Map View
            context.read<LoadBloc>().add(
                  LoadFetchAllRequested(page: _currentPage),
                );
            break;
        }
      }
    }
  }

  void _showFilterBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => LoadFilterBottomSheet(
        selectedLoadType: _selectedLoadType,
        selectedStatus: _selectedStatus,
        selectedPriority: _selectedPriority,
        verifiedOnly: _verifiedOnly,
        onApplyFilters: (loadType, status, priority, verifiedOnly) {
          setState(() {
            _selectedLoadType = loadType;
            _selectedStatus = status;
            _selectedPriority = priority;
            _verifiedOnly = verifiedOnly;
            _currentPage = 0;
          });

          context.read<LoadBloc>().add(
                LoadSearchRequested(
                  loadType: loadType,
                  status: status,
                  priority: priority,
                  verifiedOnly: verifiedOnly,
                ),
              );
        },
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _selectedLoadType = null;
      _selectedStatus = null;
      _selectedPriority = null;
      _verifiedOnly = false;
      _currentPage = 0;
    });
    _loadData();
  }

  bool get _hasActiveFilters =>
      _selectedLoadType != null ||
      _selectedStatus != null ||
      _selectedPriority != null ||
      _verifiedOnly;

  Widget _buildLoadListView() {
    return BlocConsumer<LoadBloc, LoadState>(
      listener: (context, state) {
        if (state is LoadError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is LoadListLoaded) {
          setState(() {
            _isLoadingMore = false;
          });
        }
      },
      builder: (context, state) {
        if (state is LoadLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is LoadListLoaded) {
          final loads = state.response.content;

          if (loads.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.local_shipping_outlined,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No loads found',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _hasActiveFilters
                        ? 'Try adjusting your filters'
                        : 'Be the first to post a load',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              setState(() {
                _currentPage = 0;
              });
              _loadData();
            },
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: loads.length + (_isLoadingMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == loads.length) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final load = loads[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: LoadCard(
                    load: load,
                    onTap: () {
                      context.pushNamed(
                        'load-details',
                        pathParameters: {'id': load.id.toString()},
                      );
                    },
                  ),
                );
              },
            ),
          );
        }

        if (state is LoadError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'Error loading loads',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        // If state is LoadDetailLoaded (user returned from detail screen), reload the list
        if (state is LoadDetailLoaded) {
          // Trigger reload on next frame to avoid calling setState during build
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _loadData();
          });
          // Show loading indicator while reloading
          return const Center(child: CircularProgressIndicator());
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildBiddingLoadListView() {
    return BlocConsumer<LoadBloc, LoadState>(
      listener: (context, state) {
        if (state is LoadError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is LoadListLoaded) {
          setState(() {
            _isLoadingMore = false;
          });
        }
      },
      builder: (context, state) {
        if (state is LoadLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state is LoadListLoaded) {
          final allLoads = state.response.content;

          // Filter loads to show only those available for bidding
          final availableLoads = allLoads.where((load) {
            return load.status == LoadStatus.posted &&
                load.biddingClosesAt != null &&
                load.biddingClosesAt!.isAfter(DateTime.now());
          }).toList();

          if (availableLoads.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.gavel,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No loads available for bidding',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Check back later for new opportunities',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              setState(() {
                _currentPage = 0;
              });
              _loadData();
            },
            child: ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: availableLoads.length + (_isLoadingMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == availableLoads.length) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                final load = availableLoads[index];
                return Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: LoadCard(
                    load: load,
                    onTap: () {
                      context.pushNamed(
                        'load-details',
                        pathParameters: {'id': load.id.toString()},
                      );
                    },
                  ),
                );
              },
            ),
          );
        }

        if (state is LoadError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'Error loading loads',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  state.message,
                  style: Theme.of(context).textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadData,
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        // If state is LoadDetailLoaded (user returned from detail screen), reload the list
        if (state is LoadDetailLoaded) {
          // Trigger reload on next frame to avoid calling setState during build
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _loadData();
          });
          // Show loading indicator while reloading
          return const Center(child: CircularProgressIndicator());
        }

        return const SizedBox.shrink();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final user = context.watch<AuthService>().currentUser;
    final isClient = user?.role == 'CLIENT';

    return Scaffold(
      appBar: UnifiedHeader(
        title: 'Loads',
        onLogoutPressed: () => _showLogoutDialog(),
        actions: [
          IconButton(
            icon: Icon(
              Icons.filter_list,
              color: _hasActiveFilters
                  ? Theme.of(context).primaryColor
                  : Theme.of(context).colorScheme.onPrimary,
            ),
            onPressed: _showFilterBottomSheet,
            tooltip: 'Filter Loads',
          ),
          if (_hasActiveFilters)
            IconButton(
              icon: Icon(
                Icons.clear,
                color: Theme.of(context).colorScheme.onPrimary,
              ),
              onPressed: _clearFilters,
              tooltip: 'Clear Filters',
            ),
        ],
      ),
      body: Column(
        children: [
          // Tab bar positioned just above content
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 0.5,
                ),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              onTap: (index) {
                setState(() {
                  _currentPage = 0;
                });
                _loadData();
              },
              tabs: _getTabsForUser(user),
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: _getTabViewsForUser(user),
            ),
          ),
        ],
      ),
      floatingActionButton: isClient &&
              _tabController.index != 3 // Don't show on Map tab (index 3)
          ? FloatingActionButton(
              onPressed: () {
                context.pushNamed('create-load').then((_) {
                  // Refresh loads after creating a new one
                  _loadData();
                });
              },
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AuthBloc>().add(AuthLogoutRequested());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
