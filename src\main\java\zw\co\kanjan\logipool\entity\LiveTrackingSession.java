package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "live_tracking_sessions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LiveTrackingSession {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "load_id", nullable = false)
    private Load load;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "tracking_user_id", nullable = false)
    private User trackingUser;

    @Column(name = "device_identifier", length = 100)
    private String deviceIdentifier;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "started_at", nullable = false)
    @CreationTimestamp
    private LocalDateTime startedAt;

    @Column(name = "ended_at")
    private LocalDateTime endedAt;

    @Column(name = "last_update_at")
    @UpdateTimestamp
    private LocalDateTime lastUpdateAt;

    @Column(name = "current_latitude", precision = 10, scale = 8)
    private BigDecimal currentLatitude;

    @Column(name = "current_longitude", precision = 11, scale = 8)
    private BigDecimal currentLongitude;

    @Column(name = "current_location", length = 200)
    private String currentLocation;

    @Column(name = "total_updates", nullable = false)
    private Integer totalUpdates = 0;

    @Enumerated(EnumType.STRING)
    @Column(name = "session_status", nullable = false)
    private SessionStatus sessionStatus = SessionStatus.ACTIVE;

    @Column(name = "end_reason", length = 100)
    private String endReason;

    public enum SessionStatus {
        ACTIVE,
        ENDED_BY_USER,
        ENDED_BY_SYSTEM,
        ENDED_BY_CONFLICT,
        ENDED_BY_LOAD_COMPLETION
    }

    public void endSession(SessionStatus status, String reason) {
        this.isActive = false;
        this.sessionStatus = status;
        this.endReason = reason;
        this.endedAt = LocalDateTime.now();
    }

    public void updateLocation(BigDecimal latitude, BigDecimal longitude, String location) {
        this.currentLatitude = latitude;
        this.currentLongitude = longitude;
        this.currentLocation = location;
        this.lastUpdateAt = LocalDateTime.now();
        this.totalUpdates++;
    }
}
