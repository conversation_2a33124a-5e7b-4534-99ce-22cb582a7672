package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.NotificationDto;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.Notification;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AdminReportService {

    private final LoadRepository loadRepository;
    private final UserRepository userRepository;
    private final PersistentNotificationService persistentNotificationService;
    private final RealTimeNotificationService realTimeNotificationService;
    private final EmailTemplateService emailTemplateService;
    private final NotificationService notificationService;

    @Value("${app.notification.email.enabled:true}")
    private boolean emailEnabled;

    @Value("${app.notification.realtime.enabled:true}")
    private boolean realtimeEnabled;

    @Value("${app.notification.persistent.enabled:true}")
    private boolean persistentEnabled;

    @Value("${app.admin.daily-report.enabled:true}")
    private boolean dailyReportEnabled;

    /**
     * Send daily report of loads starting in the next 3 days to all admins
     * Runs every day at 8:00 AM
     */
    @Scheduled(cron = "0 0 8 * * *")
    @Transactional(readOnly = true)
    public void sendDailyUpcomingLoadsReport() {
        if (!dailyReportEnabled) {
            log.debug("Daily admin reports are disabled");
            return;
        }

        log.info("Starting daily upcoming loads report generation");

        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime threeDaysFromNow = now.plusDays(3);

            // Find loads starting in the next 3 days
            List<Load> upcomingLoads = loadRepository.findLoadsStartingInNext3Days(now, threeDaysFromNow);

            if (upcomingLoads.isEmpty()) {
                log.info("No loads starting in the next 3 days - skipping daily report");
                return;
            }

            // Get all admin users
            List<User> admins = userRepository.findAdminUsers();

            if (admins.isEmpty()) {
                log.warn("No admin users found - cannot send daily report");
                return;
            }

            String reportTitle = "Daily Load Report - Upcoming Loads";
            String reportMessage = generateReportMessage(upcomingLoads);

            // Send report to each admin
            for (User admin : admins) {
                sendReportToAdmin(admin, reportTitle, reportMessage, upcomingLoads);
            }

            log.info("Daily upcoming loads report sent to {} admins for {} loads", 
                    admins.size(), upcomingLoads.size());

        } catch (Exception e) {
            log.error("Failed to send daily upcoming loads report", e);
        }
    }

    private void sendReportToAdmin(User admin, String title, String message, List<Load> loads) {
        try {
            // Send persistent notification
            if (persistentEnabled) {
                persistentNotificationService.createNotification(
                    admin.getId(),
                    title,
                    message,
                    "ADMIN_DAILY_REPORT",
                    "MEDIUM",
                    Map.of(
                        "reportDate", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE),
                        "loadCount", loads.size(),
                        "reportType", "UPCOMING_LOADS"
                    ),
                    null,
                    "REPORT",
                    null
                );
            }

            // Send real-time notification
            if (realtimeEnabled) {
                realTimeNotificationService.sendNotificationToUser(
                    admin.getId(),
                    NotificationDto.builder()
                        .title(title)
                        .message(message)
                        .type("ADMIN_DAILY_REPORT")
                        .timestamp(LocalDateTime.now())
                        .data(Map.of(
                            "reportDate", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE),
                            "loadCount", loads.size()
                        ))
                        .build()
                );
            }

            // Send email report
            if (emailEnabled && admin.getEmail() != null) {
                sendDailyReportEmail(admin, title, loads);
            }

        } catch (Exception e) {
            log.error("Failed to send daily report to admin: {}", admin.getEmail(), e);
        }
    }

    private String generateReportMessage(List<Load> loads) {
        if (loads.size() == 1) {
            return String.format("There is 1 load starting in the next 3 days. Review the details in your admin dashboard.");
        } else {
            return String.format("There are %d loads starting in the next 3 days. Review the details in your admin dashboard.", 
                    loads.size());
        }
    }

    private void sendDailyReportEmail(User admin, String title, List<Load> loads) {
        try {
            String subject = title + " - " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMM dd, yyyy"));
            String body = buildDailyReportEmailBody(admin, loads);
            
            notificationService.sendHtmlEmail(admin.getEmail(), subject, body);
            log.info("Daily report email sent to admin: {}", admin.getEmail());
            
        } catch (Exception e) {
            log.error("Failed to send daily report email to admin: {}", admin.getEmail(), e);
        }
    }

    private String buildDailyReportEmailBody(User admin, List<Load> loads) {
        StringBuilder emailBody = new StringBuilder();
        
        emailBody.append(String.format("Dear %s,\n\n", admin.getFirstName()));
        emailBody.append("Here is your daily report of loads starting in the next 3 days:\n\n");
        
        if (loads.isEmpty()) {
            emailBody.append("No loads are scheduled to start in the next 3 days.\n\n");
        } else {
            emailBody.append(String.format("Total loads: %d\n\n", loads.size()));
            
            for (int i = 0; i < loads.size(); i++) {
                Load load = loads.get(i);
                emailBody.append(String.format("%d. %s\n", i + 1, load.getTitle()));
                emailBody.append(String.format("   Tracking: %s\n", load.getTrackingNumber()));
                emailBody.append(String.format("   Client: %s %s\n", 
                        load.getClient().getFirstName(), load.getClient().getLastName()));
                emailBody.append(String.format("   Route: %s → %s\n", 
                        load.getPickupLocation(), load.getDeliveryLocation()));
                emailBody.append(String.format("   Pickup Date: %s\n", 
                        load.getPickupDate().format(DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm"))));
                emailBody.append(String.format("   Status: %s\n", load.getStatus()));
                emailBody.append(String.format("   Priority: %s\n", load.getPriority()));
                
                if (load.getAssignedCompany() != null) {
                    emailBody.append(String.format("   Assigned to: %s\n", load.getAssignedCompany().getName()));
                }
                
                emailBody.append("\n");
            }
        }
        
        emailBody.append("You can view and manage these loads in the admin dashboard.\n\n");
        emailBody.append("Best regards,\n");
        emailBody.append("LogiPool Team");
        
        return emailBody.toString();
    }

    /**
     * Manual method to trigger daily report (for testing or manual execution)
     */
    public void triggerDailyReport() {
        log.info("Manually triggering daily upcoming loads report");
        sendDailyUpcomingLoadsReport();
    }
}
