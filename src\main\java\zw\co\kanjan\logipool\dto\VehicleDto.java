package zw.co.kanjan.logipool.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.Vehicle;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class VehicleDto {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Vehicle creation request")
    public static class VehicleCreateRequest {
        
        @NotBlank(message = "Registration number is required")
        @Size(max = 20, message = "Registration number must not exceed 20 characters")
        @Schema(description = "Vehicle registration number", example = "ABC-123")
        private String registrationNumber;
        
        @NotBlank(message = "Make is required")
        @Size(max = 50, message = "Make must not exceed 50 characters")
        @Schema(description = "Vehicle make", example = "Mercedes-Benz")
        private String make;
        
        @NotBlank(message = "Model is required")
        @Size(max = 50, message = "Model must not exceed 50 characters")
        @Schema(description = "Vehicle model", example = "Actros")
        private String model;
        
        @Schema(description = "Manufacture year", example = "2020")
        private Integer year;
        
        @NotNull(message = "Vehicle type is required")
        @Schema(description = "Vehicle type")
        private Vehicle.VehicleType type;
        
        @Schema(description = "Maximum weight capacity", example = "25000.0")
        private BigDecimal maxWeight;
        
        @Schema(description = "Maximum volume capacity", example = "100.0")
        private BigDecimal maxVolume;
        
        @Size(max = 20, message = "Weight unit must not exceed 20 characters")
        @Schema(description = "Weight unit", example = "kg")
        private String weightUnit = "kg";
        
        @Size(max = 20, message = "Volume unit must not exceed 20 characters")
        @Schema(description = "Volume unit", example = "m3")
        private String volumeUnit = "m3";
        
        @Schema(description = "Vehicle description")
        private String description;
        
        @Schema(description = "Daily rental rate", example = "150.00")
        private BigDecimal dailyRate;
        
        @Size(max = 10, message = "Currency must not exceed 10 characters")
        @Schema(description = "Currency", example = "USD")
        private String currency = "USD";
        
        @Schema(description = "Vehicle features (JSON string)")
        private String features;
        
        @Size(max = 500, message = "Image URL must not exceed 500 characters")
        @Schema(description = "Vehicle image URL")
        private String imageUrl;
        
        @Schema(description = "Whether available for rent", example = "true")
        private Boolean isAvailableForRent = false;

        @Schema(description = "Whether featured", example = "false")
        private Boolean isFeatured = false;

        @Schema(description = "Whether publicly visible", example = "false")
        private Boolean isPubliclyVisible = false;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Vehicle update request")
    public static class VehicleUpdateRequest {
        
        @Size(max = 50, message = "Make must not exceed 50 characters")
        @Schema(description = "Vehicle make", example = "Mercedes-Benz")
        private String make;
        
        @Size(max = 50, message = "Model must not exceed 50 characters")
        @Schema(description = "Vehicle model", example = "Actros")
        private String model;
        
        @Schema(description = "Manufacture year", example = "2020")
        private Integer year;
        
        @Schema(description = "Vehicle type")
        private Vehicle.VehicleType type;
        
        @Schema(description = "Maximum weight capacity", example = "25000.0")
        private BigDecimal maxWeight;
        
        @Schema(description = "Maximum volume capacity", example = "100.0")
        private BigDecimal maxVolume;
        
        @Schema(description = "Vehicle status")
        private Vehicle.VehicleStatus status;
        
        @Schema(description = "Vehicle description")
        private String description;
        
        @Schema(description = "Daily rental rate", example = "150.00")
        private BigDecimal dailyRate;
        
        @Schema(description = "Vehicle features (JSON string)")
        private String features;
        
        @Size(max = 500, message = "Image URL must not exceed 500 characters")
        @Schema(description = "Vehicle image URL")
        private String imageUrl;
        
        @Schema(description = "Whether available for rent", example = "true")
        private Boolean isAvailableForRent;

        @Schema(description = "Whether featured", example = "false")
        private Boolean isFeatured;

        @Schema(description = "Whether publicly visible", example = "false")
        private Boolean isPubliclyVisible;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Vehicle response")
    public static class VehicleResponse {
        
        @Schema(description = "Vehicle ID", example = "1")
        private Long id;
        
        @Schema(description = "Vehicle registration number", example = "ABC-123")
        private String registrationNumber;
        
        @Schema(description = "Vehicle make", example = "Mercedes-Benz")
        private String make;
        
        @Schema(description = "Vehicle model", example = "Actros")
        private String model;
        
        @Schema(description = "Manufacture year", example = "2020")
        private Integer year;
        
        @Schema(description = "Vehicle type")
        private Vehicle.VehicleType type;
        
        @Schema(description = "Maximum weight capacity", example = "25000.0")
        private BigDecimal maxWeight;
        
        @Schema(description = "Maximum volume capacity", example = "100.0")
        private BigDecimal maxVolume;
        
        @Schema(description = "Weight unit", example = "kg")
        private String weightUnit;
        
        @Schema(description = "Volume unit", example = "m3")
        private String volumeUnit;
        
        @Schema(description = "Vehicle status")
        private Vehicle.VehicleStatus status;
        
        @Schema(description = "Vehicle description")
        private String description;
        
        @Schema(description = "Daily rental rate", example = "150.00")
        private BigDecimal dailyRate;
        
        @Schema(description = "Currency", example = "USD")
        private String currency;
        
        @Schema(description = "Vehicle features")
        private List<String> features;
        
        @Schema(description = "Vehicle image URL")
        private String imageUrl;
        
        @Schema(description = "Whether available for rent", example = "true")
        private Boolean isAvailableForRent;

        @Schema(description = "Whether featured", example = "false")
        private Boolean isFeatured;

        @Schema(description = "Whether publicly visible", example = "false")
        private Boolean isPubliclyVisible;
        
        @Schema(description = "Public approval status")
        private Vehicle.PublicApprovalStatus publicApprovalStatus;
        
        @Schema(description = "Company information")
        private CompanyInfo company;
        
        @Schema(description = "Has insurance", example = "true")
        private Boolean hasInsurance;
        
        @Schema(description = "Has fitness certificate", example = "true")
        private Boolean hasFitnessCertificate;
        
        @Schema(description = "Has permits", example = "true")
        private Boolean hasPermits;
        
        @Schema(description = "Insurance expiry date")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime insuranceExpiryDate;
        
        @Schema(description = "Fitness certificate expiry date")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime fitnessExpiryDate;
        
        @Schema(description = "Permit expiry date")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime permitExpiryDate;
        
        @Schema(description = "Creation timestamp")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
        
        @Schema(description = "Last update timestamp")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Company information")
    public static class CompanyInfo {
        @Schema(description = "Company ID", example = "1")
        private Long id;
        
        @Schema(description = "Company name", example = "ABC Logistics")
        private String name;
        
        @Schema(description = "Company rating", example = "4.5")
        private BigDecimal rating;
        
        @Schema(description = "Company verification status")
        private String verificationStatus;
        
        @Schema(description = "Company location", example = "Harare")
        private String location;
        
        @Schema(description = "Company phone number")
        private String phoneNumber;
        
        @Schema(description = "Company email")
        private String email;
    }
}
