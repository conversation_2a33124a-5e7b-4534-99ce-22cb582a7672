// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in logipool_flutter/test/features/loads/widgets/load_tracking_section_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:ui' as _i7;

import 'package:logipool_flutter/shared/models/location_model.dart' as _i3;
import 'package:logipool_flutter/shared/models/paginated_response.dart' as _i4;
import 'package:logipool_flutter/shared/models/user_model.dart' as _i2;
import 'package:logipool_flutter/shared/services/auth_service.dart' as _i5;
import 'package:logipool_flutter/shared/services/location_tracking_service.dart'
    as _i8;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserModel_0 extends _i1.SmartFake implements _i2.UserModel {
  _FakeUserModel_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLocationResponse_1 extends _i1.SmartFake
    implements _i3.LocationResponse {
  _FakeLocationResponse_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakePaginatedResponse_2<T> extends _i1.SmartFake
    implements _i4.PaginatedResponse<T> {
  _FakePaginatedResponse_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTrackingSummary_3 extends _i1.SmartFake
    implements _i3.TrackingSummary {
  _FakeTrackingSummary_3(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeLocationPermissionResponse_4 extends _i1.SmartFake
    implements _i3.LocationPermissionResponse {
  _FakeLocationPermissionResponse_4(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i5.AuthService {
  MockAuthService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isLoggedIn => (super.noSuchMethod(
        Invocation.getter(#isLoggedIn),
        returnValue: false,
      ) as bool);

  @override
  bool get isLoading => (super.noSuchMethod(
        Invocation.getter(#isLoading),
        returnValue: false,
      ) as bool);

  @override
  bool get isAdmin => (super.noSuchMethod(
        Invocation.getter(#isAdmin),
        returnValue: false,
      ) as bool);

  @override
  bool get isClient => (super.noSuchMethod(
        Invocation.getter(#isClient),
        returnValue: false,
      ) as bool);

  @override
  bool get isTransporter => (super.noSuchMethod(
        Invocation.getter(#isTransporter),
        returnValue: false,
      ) as bool);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i6.Future<_i2.UserModel?> getCurrentUser() => (super.noSuchMethod(
        Invocation.method(
          #getCurrentUser,
          [],
        ),
        returnValue: _i6.Future<_i2.UserModel?>.value(),
      ) as _i6.Future<_i2.UserModel?>);

  @override
  _i6.Future<String?> getToken() => (super.noSuchMethod(
        Invocation.method(
          #getToken,
          [],
        ),
        returnValue: _i6.Future<String?>.value(),
      ) as _i6.Future<String?>);

  @override
  _i6.Future<_i2.UserModel> login({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #login,
          [],
          {
            #email: email,
            #password: password,
          },
        ),
        returnValue: _i6.Future<_i2.UserModel>.value(_FakeUserModel_0(
          this,
          Invocation.method(
            #login,
            [],
            {
              #email: email,
              #password: password,
            },
          ),
        )),
      ) as _i6.Future<_i2.UserModel>);

  @override
  _i6.Future<_i2.UserModel> register({
    required String? firstName,
    required String? lastName,
    required String? email,
    required String? password,
    List<String>? roles,
    String? phoneNumber,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #register,
          [],
          {
            #firstName: firstName,
            #lastName: lastName,
            #email: email,
            #password: password,
            #roles: roles,
            #phoneNumber: phoneNumber,
          },
        ),
        returnValue: _i6.Future<_i2.UserModel>.value(_FakeUserModel_0(
          this,
          Invocation.method(
            #register,
            [],
            {
              #firstName: firstName,
              #lastName: lastName,
              #email: email,
              #password: password,
              #roles: roles,
              #phoneNumber: phoneNumber,
            },
          ),
        )),
      ) as _i6.Future<_i2.UserModel>);

  @override
  _i6.Future<void> logout() => (super.noSuchMethod(
        Invocation.method(
          #logout,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? email,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateProfile,
          [],
          {
            #firstName: firstName,
            #lastName: lastName,
            #phoneNumber: phoneNumber,
            #email: email,
          },
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> changePassword({
    required String? currentPassword,
    required String? newPassword,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #changePassword,
          [],
          {
            #currentPassword: currentPassword,
            #newPassword: newPassword,
          },
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> forgotPassword(String? email) => (super.noSuchMethod(
        Invocation.method(
          #forgotPassword,
          [email],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<void> resetPassword({
    required String? token,
    required String? newPassword,
    required String? confirmPassword,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #resetPassword,
          [],
          {
            #token: token,
            #newPassword: newPassword,
            #confirmPassword: confirmPassword,
          },
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  void addListener(_i7.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i7.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}

/// A class which mocks [LocationTrackingService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLocationTrackingService extends _i1.Mock
    implements _i8.LocationTrackingService {
  MockLocationTrackingService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isTracking => (super.noSuchMethod(
        Invocation.getter(#isTracking),
        returnValue: false,
      ) as bool);

  @override
  bool get hasLocationPermission => (super.noSuchMethod(
        Invocation.getter(#hasLocationPermission),
        returnValue: false,
      ) as bool);

  @override
  bool get hasListeners => (super.noSuchMethod(
        Invocation.getter(#hasListeners),
        returnValue: false,
      ) as bool);

  @override
  _i6.Future<void> initialize() => (super.noSuchMethod(
        Invocation.method(
          #initialize,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<bool> requestLocationPermission() => (super.noSuchMethod(
        Invocation.method(
          #requestLocationPermission,
          [],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<bool> startTracking(int? loadId) => (super.noSuchMethod(
        Invocation.method(
          #startTracking,
          [loadId],
        ),
        returnValue: _i6.Future<bool>.value(false),
      ) as _i6.Future<bool>);

  @override
  _i6.Future<void> stopTracking() => (super.noSuchMethod(
        Invocation.method(
          #stopTracking,
          [],
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<_i3.LocationResponse> updateLocation(
          _i3.LocationUpdateRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #updateLocation,
          [request],
        ),
        returnValue:
            _i6.Future<_i3.LocationResponse>.value(_FakeLocationResponse_1(
          this,
          Invocation.method(
            #updateLocation,
            [request],
          ),
        )),
      ) as _i6.Future<_i3.LocationResponse>);

  @override
  _i6.Future<_i3.LocationResponse> getDriverLocation(int? driverId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getDriverLocation,
          [driverId],
        ),
        returnValue:
            _i6.Future<_i3.LocationResponse>.value(_FakeLocationResponse_1(
          this,
          Invocation.method(
            #getDriverLocation,
            [driverId],
          ),
        )),
      ) as _i6.Future<_i3.LocationResponse>);

  @override
  _i6.Future<_i4.PaginatedResponse<_i3.LocationResponse>>
      getDriverLocationHistory(
    int? driverId, {
    int? page = 0,
    int? size = 20,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #getDriverLocationHistory,
              [driverId],
              {
                #page: page,
                #size: size,
              },
            ),
            returnValue:
                _i6.Future<_i4.PaginatedResponse<_i3.LocationResponse>>.value(
                    _FakePaginatedResponse_2<_i3.LocationResponse>(
              this,
              Invocation.method(
                #getDriverLocationHistory,
                [driverId],
                {
                  #page: page,
                  #size: size,
                },
              ),
            )),
          ) as _i6.Future<_i4.PaginatedResponse<_i3.LocationResponse>>);

  @override
  _i6.Future<_i4.PaginatedResponse<_i3.LocationResponse>>
      getLoadTrackingHistory(
    int? loadId, {
    int? page = 0,
    int? size = 20,
  }) =>
          (super.noSuchMethod(
            Invocation.method(
              #getLoadTrackingHistory,
              [loadId],
              {
                #page: page,
                #size: size,
              },
            ),
            returnValue:
                _i6.Future<_i4.PaginatedResponse<_i3.LocationResponse>>.value(
                    _FakePaginatedResponse_2<_i3.LocationResponse>(
              this,
              Invocation.method(
                #getLoadTrackingHistory,
                [loadId],
                {
                  #page: page,
                  #size: size,
                },
              ),
            )),
          ) as _i6.Future<_i4.PaginatedResponse<_i3.LocationResponse>>);

  @override
  _i6.Future<_i3.TrackingSummary> getCompanyTrackingSummary(int? companyId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getCompanyTrackingSummary,
          [companyId],
        ),
        returnValue:
            _i6.Future<_i3.TrackingSummary>.value(_FakeTrackingSummary_3(
          this,
          Invocation.method(
            #getCompanyTrackingSummary,
            [companyId],
          ),
        )),
      ) as _i6.Future<_i3.TrackingSummary>);

  @override
  _i6.Future<_i3.LocationPermissionResponse> requestDriverLocationPermission(
          _i3.LocationPermissionRequest? request) =>
      (super.noSuchMethod(
        Invocation.method(
          #requestDriverLocationPermission,
          [request],
        ),
        returnValue: _i6.Future<_i3.LocationPermissionResponse>.value(
            _FakeLocationPermissionResponse_4(
          this,
          Invocation.method(
            #requestDriverLocationPermission,
            [request],
          ),
        )),
      ) as _i6.Future<_i3.LocationPermissionResponse>);

  @override
  _i6.Future<_i3.LocationPermissionResponse> grantLocationPermission(
          int? permissionId) =>
      (super.noSuchMethod(
        Invocation.method(
          #grantLocationPermission,
          [permissionId],
        ),
        returnValue: _i6.Future<_i3.LocationPermissionResponse>.value(
            _FakeLocationPermissionResponse_4(
          this,
          Invocation.method(
            #grantLocationPermission,
            [permissionId],
          ),
        )),
      ) as _i6.Future<_i3.LocationPermissionResponse>);

  @override
  _i6.Future<void> revokeLocationPermission(
    int? permissionId, {
    String? reason,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #revokeLocationPermission,
          [permissionId],
          {#reason: reason},
        ),
        returnValue: _i6.Future<void>.value(),
        returnValueForMissingStub: _i6.Future<void>.value(),
      ) as _i6.Future<void>);

  @override
  _i6.Future<Map<String, dynamic>> startLiveTracking(int? loadId) =>
      (super.noSuchMethod(
        Invocation.method(
          #startLiveTracking,
          [loadId],
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<Map<String, dynamic>> stopLiveTracking(int? loadId) =>
      (super.noSuchMethod(
        Invocation.method(
          #stopLiveTracking,
          [loadId],
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<Map<String, dynamic>> getLiveTrackingStatus(int? loadId) =>
      (super.noSuchMethod(
        Invocation.method(
          #getLiveTrackingStatus,
          [loadId],
        ),
        returnValue:
            _i6.Future<Map<String, dynamic>>.value(<String, dynamic>{}),
      ) as _i6.Future<Map<String, dynamic>>);

  @override
  _i6.Future<List<Map<String, dynamic>>> getActiveLiveTrackingSessions() =>
      (super.noSuchMethod(
        Invocation.method(
          #getActiveLiveTrackingSessions,
          [],
        ),
        returnValue: _i6.Future<List<Map<String, dynamic>>>.value(
            <Map<String, dynamic>>[]),
      ) as _i6.Future<List<Map<String, dynamic>>>);

  @override
  void addListener(_i7.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #addListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void removeListener(_i7.VoidCallback? listener) => super.noSuchMethod(
        Invocation.method(
          #removeListener,
          [listener],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void dispose() => super.noSuchMethod(
        Invocation.method(
          #dispose,
          [],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void notifyListeners() => super.noSuchMethod(
        Invocation.method(
          #notifyListeners,
          [],
        ),
        returnValueForMissingStub: null,
      );
}
