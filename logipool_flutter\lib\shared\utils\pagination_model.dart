import 'package:equatable/equatable.dart';

class PaginationModel<T> extends Equatable {
  final List<T> data;
  final int currentPage;
  final int totalPages;
  final int totalElements;
  final int pageSize;
  final bool hasNext;
  final bool hasPrevious;
  final bool isFirst;
  final bool isLast;

  const PaginationModel({
    required this.data,
    required this.currentPage,
    required this.totalPages,
    required this.totalElements,
    required this.pageSize,
    required this.hasNext,
    required this.hasPrevious,
    required this.isFirst,
    required this.isLast,
  });

  factory PaginationModel.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return PaginationModel(
      data: (json['content'] as List<dynamic>?)
              ?.map((item) => fromJsonT(item as Map<String, dynamic>))
              .toList() ??
          [],
      currentPage: (json['number'] as int?) ?? 0,
      totalPages: (json['totalPages'] as int?) ?? 0,
      totalElements: (json['totalElements'] as int?) ?? 0,
      pageSize: (json['size'] as int?) ?? 0,
      hasNext: !((json['last'] as bool?) ?? true),
      hasPrevious: !((json['first'] as bool?) ?? true),
      isFirst: (json['first'] as bool?) ?? true,
      isLast: (json['last'] as bool?) ?? true,
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'content': data.map((item) => toJsonT(item)).toList(),
      'number': currentPage,
      'totalPages': totalPages,
      'totalElements': totalElements,
      'size': pageSize,
      'last': isLast,
      'first': isFirst,
    };
  }

  PaginationModel<T> copyWith({
    List<T>? data,
    int? currentPage,
    int? totalPages,
    int? totalElements,
    int? pageSize,
    bool? hasNext,
    bool? hasPrevious,
    bool? isFirst,
    bool? isLast,
  }) {
    return PaginationModel(
      data: data ?? this.data,
      currentPage: currentPage ?? this.currentPage,
      totalPages: totalPages ?? this.totalPages,
      totalElements: totalElements ?? this.totalElements,
      pageSize: pageSize ?? this.pageSize,
      hasNext: hasNext ?? this.hasNext,
      hasPrevious: hasPrevious ?? this.hasPrevious,
      isFirst: isFirst ?? this.isFirst,
      isLast: isLast ?? this.isLast,
    );
  }

  // Helper methods
  bool get isEmpty => data.isEmpty;
  bool get isNotEmpty => data.isNotEmpty;
  int get length => data.length;

  // Pagination helpers
  int get nextPage => hasNext ? currentPage + 1 : currentPage;
  int get previousPage => hasPrevious ? currentPage - 1 : currentPage;
  
  // Calculate range information
  int get startIndex => currentPage * pageSize + 1;
  int get endIndex => startIndex + data.length - 1;

  @override
  List<Object?> get props => [
        data,
        currentPage,
        totalPages,
        totalElements,
        pageSize,
        hasNext,
        hasPrevious,
        isFirst,
        isLast,
      ];
}

// Extension for easier pagination handling
extension PaginationModelExtension<T> on PaginationModel<T> {
  /// Merge with another pagination model (useful for infinite scrolling)
  PaginationModel<T> merge(PaginationModel<T> other) {
    return PaginationModel(
      data: [...data, ...other.data],
      currentPage: other.currentPage,
      totalPages: other.totalPages,
      totalElements: other.totalElements,
      pageSize: other.pageSize,
      hasNext: other.hasNext,
      hasPrevious: other.hasPrevious,
      isFirst: isFirst, // Keep the original first status
      isLast: other.isLast,
    );
  }

  /// Get a specific item by index
  T? getItem(int index) {
    if (index >= 0 && index < data.length) {
      return data[index];
    }
    return null;
  }

  /// Check if a specific page number is valid
  bool isValidPage(int page) {
    return page >= 0 && page < totalPages;
  }
}
