package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import zw.co.kanjan.logipool.dto.EquipmentDto;
import zw.co.kanjan.logipool.entity.Equipment;
import zw.co.kanjan.logipool.service.EquipmentService;

import java.security.Principal;

@RestController
@RequestMapping("/api/equipment")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Equipment Management", description = "Equipment management endpoints for companies")
@SecurityRequirement(name = "bearerAuth")
public class EquipmentController {

    private final EquipmentService equipmentService;

    @PostMapping
    @Operation(summary = "Create new equipment", description = "Add new equipment to the company inventory")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<EquipmentDto.EquipmentResponse> createEquipment(
            @Valid @RequestBody EquipmentDto.EquipmentCreateRequest request,
            Principal principal) {
        
        log.info("Creating equipment for user: {}", principal.getName());
        EquipmentDto.EquipmentResponse equipment = equipmentService.createEquipment(request, principal.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(equipment);
    }

    @GetMapping
    @Operation(summary = "Get company equipment", description = "Get paginated list of company equipment")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN') or hasRole('DRIVER')")
    public ResponseEntity<Page<EquipmentDto.EquipmentResponse>> getCompanyEquipment(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir,
            @Parameter(description = "Filter by status") @RequestParam(required = false) Equipment.EquipmentStatus status,
            @Parameter(description = "Filter by type") @RequestParam(required = false) Equipment.EquipmentType type,
            Principal principal) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<EquipmentDto.EquipmentResponse> equipment = equipmentService.getCompanyEquipment(
                principal.getName(), pageable, status, type);
        
        return ResponseEntity.ok(equipment);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get equipment details", description = "Get detailed information about specific equipment")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN') or hasRole('DRIVER')")
    public ResponseEntity<EquipmentDto.EquipmentResponse> getEquipment(
            @Parameter(description = "Equipment ID") @PathVariable Long id,
            Principal principal) {
        
        EquipmentDto.EquipmentResponse equipment = equipmentService.getEquipment(id, principal.getName());
        return ResponseEntity.ok(equipment);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update equipment", description = "Update equipment information")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<EquipmentDto.EquipmentResponse> updateEquipment(
            @Parameter(description = "Equipment ID") @PathVariable Long id,
            @Valid @RequestBody EquipmentDto.EquipmentUpdateRequest request,
            Principal principal) {
        
        log.info("Updating equipment {} for user: {}", id, principal.getName());
        EquipmentDto.EquipmentResponse equipment = equipmentService.updateEquipment(id, request, principal.getName());
        return ResponseEntity.ok(equipment);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete equipment", description = "Remove equipment from inventory")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<Void> deleteEquipment(
            @Parameter(description = "Equipment ID") @PathVariable Long id,
            Principal principal) {
        
        log.info("Deleting equipment {} for user: {}", id, principal.getName());
        equipmentService.deleteEquipment(id, principal.getName());
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{id}/image")
    @Operation(summary = "Upload equipment image", description = "Upload an image for the equipment")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<String> uploadEquipmentImage(
            @Parameter(description = "Equipment ID") @PathVariable Long id,
            @Parameter(description = "Image file") @RequestParam("file") MultipartFile file,
            Principal principal) {
        
        log.info("Uploading image for equipment {} by user: {}", id, principal.getName());
        String imageUrl = equipmentService.uploadEquipmentImage(id, file, principal.getName());
        return ResponseEntity.ok(imageUrl);
    }

    @PostMapping("/{id}/request-public-approval")
    @Operation(summary = "Request public approval", description = "Submit equipment for public visibility approval")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<String> requestPublicApproval(
            @Parameter(description = "Equipment ID") @PathVariable Long id,
            Principal principal) {
        
        log.info("Requesting public approval for equipment {} by user: {}", id, principal.getName());
        equipmentService.requestPublicApproval(id, principal.getName());
        return ResponseEntity.ok("Public approval request submitted successfully");
    }

    @PostMapping("/{id}/toggle-public-visibility")
    @Operation(summary = "Toggle public visibility", description = "Enable or disable public visibility for approved equipment")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<String> togglePublicVisibility(
            @Parameter(description = "Equipment ID") @PathVariable Long id,
            @Parameter(description = "Enable public visibility") @RequestParam boolean enable,
            Principal principal) {
        
        log.info("Toggling public visibility for equipment {} to {} by user: {}", id, enable, principal.getName());
        equipmentService.togglePublicVisibility(id, enable, principal.getName());
        return ResponseEntity.ok("Public visibility updated successfully");
    }

    @PostMapping("/{id}/toggle-rental-availability")
    @Operation(summary = "Toggle rental availability", description = "Enable or disable equipment for rental")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<String> toggleRentalAvailability(
            @Parameter(description = "Equipment ID") @PathVariable Long id,
            @Parameter(description = "Enable rental availability") @RequestParam boolean enable,
            Principal principal) {
        
        log.info("Toggling rental availability for equipment {} to {} by user: {}", id, enable, principal.getName());
        equipmentService.toggleRentalAvailability(id, enable, principal.getName());
        return ResponseEntity.ok("Rental availability updated successfully");
    }

    @GetMapping("/types")
    @Operation(summary = "Get equipment types", description = "Get list of available equipment types")
    public ResponseEntity<Equipment.EquipmentType[]> getEquipmentTypes() {
        return ResponseEntity.ok(Equipment.EquipmentType.values());
    }

    @GetMapping("/statuses")
    @Operation(summary = "Get equipment statuses", description = "Get list of available equipment statuses")
    public ResponseEntity<Equipment.EquipmentStatus[]> getEquipmentStatuses() {
        return ResponseEntity.ok(Equipment.EquipmentStatus.values());
    }

    @GetMapping("/{id}/approval-status")
    @Operation(summary = "Get approval status", description = "Get public approval status for equipment")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<Equipment.PublicApprovalStatus> getApprovalStatus(
            @Parameter(description = "Equipment ID") @PathVariable Long id,
            Principal principal) {
        
        Equipment.PublicApprovalStatus status = equipmentService.getApprovalStatus(id, principal.getName());
        return ResponseEntity.ok(status);
    }

    @PostMapping("/{id}/maintenance")
    @Operation(summary = "Schedule maintenance", description = "Schedule maintenance for equipment")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<String> scheduleMaintenance(
            @Parameter(description = "Equipment ID") @PathVariable Long id,
            @Parameter(description = "Maintenance date") @RequestParam String maintenanceDate,
            @Parameter(description = "Maintenance notes") @RequestParam(required = false) String notes,
            Principal principal) {
        
        log.info("Scheduling maintenance for equipment {} by user: {}", id, principal.getName());
        equipmentService.scheduleMaintenance(id, maintenanceDate, notes, principal.getName());
        return ResponseEntity.ok("Maintenance scheduled successfully");
    }
}
