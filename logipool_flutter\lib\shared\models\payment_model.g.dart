// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PaymentModel _$PaymentModelFromJson(Map<String, dynamic> json) => PaymentModel(
      id: (json['id'] as num?)?.toInt(),
      amount: (json['amount'] as num).toDouble(),
      commissionAmount: (json['commissionAmount'] as num).toDouble(),
      netAmount: (json['netAmount'] as num).toDouble(),
      commissionRate: (json['commissionRate'] as num).toDouble(),
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      method: $enumDecode(_$PaymentMethodEnumMap, json['method']),
      type: $enumDecode(_$PaymentTypeEnumMap, json['type']),
      transactionId: json['transactionId'] as String?,
      paymentGatewayReference: json['paymentGatewayReference'] as String?,
      description: json['description'] as String?,
      notes: json['notes'] as String?,
      loadId: (json['loadId'] as num?)?.toInt(),
      loadTitle: json['loadTitle'] as String?,
      bidId: (json['bidId'] as num?)?.toInt(),
      payerId: (json['payerId'] as num?)?.toInt(),
      payerName: json['payerName'] as String?,
      payeeId: (json['payeeId'] as num?)?.toInt(),
      payeeName: json['payeeName'] as String?,
      invoiceId: (json['invoiceId'] as num?)?.toInt(),
      invoiceNumber: json['invoiceNumber'] as String?,
      paidAt: json['paidAt'] == null
          ? null
          : DateTime.parse(json['paidAt'] as String),
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$PaymentModelToJson(PaymentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'amount': instance.amount,
      'commissionAmount': instance.commissionAmount,
      'netAmount': instance.netAmount,
      'commissionRate': instance.commissionRate,
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'method': _$PaymentMethodEnumMap[instance.method]!,
      'type': _$PaymentTypeEnumMap[instance.type]!,
      'transactionId': instance.transactionId,
      'paymentGatewayReference': instance.paymentGatewayReference,
      'description': instance.description,
      'notes': instance.notes,
      'loadId': instance.loadId,
      'loadTitle': instance.loadTitle,
      'bidId': instance.bidId,
      'payerId': instance.payerId,
      'payerName': instance.payerName,
      'payeeId': instance.payeeId,
      'payeeName': instance.payeeName,
      'invoiceId': instance.invoiceId,
      'invoiceNumber': instance.invoiceNumber,
      'paidAt': instance.paidAt?.toIso8601String(),
      'dueDate': instance.dueDate?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$PaymentStatusEnumMap = {
  PaymentStatus.pending: 'PENDING',
  PaymentStatus.processing: 'PROCESSING',
  PaymentStatus.completed: 'COMPLETED',
  PaymentStatus.failed: 'FAILED',
  PaymentStatus.cancelled: 'CANCELLED',
  PaymentStatus.refunded: 'REFUNDED',
  PaymentStatus.disputed: 'DISPUTED',
};

const _$PaymentMethodEnumMap = {
  PaymentMethod.creditCard: 'CREDIT_CARD',
  PaymentMethod.debitCard: 'DEBIT_CARD',
  PaymentMethod.bankTransfer: 'BANK_TRANSFER',
  PaymentMethod.mobileMoney: 'MOBILE_MONEY',
  PaymentMethod.paypal: 'PAYPAL',
  PaymentMethod.stripe: 'STRIPE',
  PaymentMethod.cash: 'CASH',
};

const _$PaymentTypeEnumMap = {
  PaymentType.loadPayment: 'LOAD_PAYMENT',
  PaymentType.commissionPayment: 'COMMISSION_PAYMENT',
  PaymentType.refund: 'REFUND',
  PaymentType.penalty: 'PENALTY',
  PaymentType.bonus: 'BONUS',
};

PaymentCreateRequest _$PaymentCreateRequestFromJson(
        Map<String, dynamic> json) =>
    PaymentCreateRequest(
      amount: (json['amount'] as num).toDouble(),
      method: $enumDecode(_$PaymentMethodEnumMap, json['method']),
      type: $enumDecode(_$PaymentTypeEnumMap, json['type']),
      description: json['description'] as String?,
      notes: json['notes'] as String?,
      loadId: (json['loadId'] as num).toInt(),
      bidId: (json['bidId'] as num?)?.toInt(),
      payeeId: (json['payeeId'] as num).toInt(),
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
    );

Map<String, dynamic> _$PaymentCreateRequestToJson(
        PaymentCreateRequest instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'method': _$PaymentMethodEnumMap[instance.method]!,
      'type': _$PaymentTypeEnumMap[instance.type]!,
      'description': instance.description,
      'notes': instance.notes,
      'loadId': instance.loadId,
      'bidId': instance.bidId,
      'payeeId': instance.payeeId,
      'dueDate': instance.dueDate?.toIso8601String(),
    };

PaymentProcessRequest _$PaymentProcessRequestFromJson(
        Map<String, dynamic> json) =>
    PaymentProcessRequest(
      paymentId: (json['paymentId'] as num).toInt(),
      method: $enumDecode(_$PaymentMethodEnumMap, json['method']),
      paymentToken: json['paymentToken'] as String?,
      cardNumber: json['cardNumber'] as String?,
      expiryMonth: json['expiryMonth'] as String?,
      expiryYear: json['expiryYear'] as String?,
      cvv: json['cvv'] as String?,
      cardHolderName: json['cardHolderName'] as String?,
      bankAccount: json['bankAccount'] as String?,
      bankCode: json['bankCode'] as String?,
      mobileNumber: json['mobileNumber'] as String?,
      mobileProvider: json['mobileProvider'] as String?,
    );

Map<String, dynamic> _$PaymentProcessRequestToJson(
        PaymentProcessRequest instance) =>
    <String, dynamic>{
      'paymentId': instance.paymentId,
      'method': _$PaymentMethodEnumMap[instance.method]!,
      'paymentToken': instance.paymentToken,
      'cardNumber': instance.cardNumber,
      'expiryMonth': instance.expiryMonth,
      'expiryYear': instance.expiryYear,
      'cvv': instance.cvv,
      'cardHolderName': instance.cardHolderName,
      'bankAccount': instance.bankAccount,
      'bankCode': instance.bankCode,
      'mobileNumber': instance.mobileNumber,
      'mobileProvider': instance.mobileProvider,
    };

PaymentUpdateRequest _$PaymentUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    PaymentUpdateRequest(
      status: $enumDecodeNullable(_$PaymentStatusEnumMap, json['status']),
      method: $enumDecodeNullable(_$PaymentMethodEnumMap, json['method']),
      notes: json['notes'] as String?,
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
    );

Map<String, dynamic> _$PaymentUpdateRequestToJson(
        PaymentUpdateRequest instance) =>
    <String, dynamic>{
      'status': _$PaymentStatusEnumMap[instance.status],
      'method': _$PaymentMethodEnumMap[instance.method],
      'notes': instance.notes,
      'dueDate': instance.dueDate?.toIso8601String(),
    };

PaymentStatusResponse _$PaymentStatusResponseFromJson(
        Map<String, dynamic> json) =>
    PaymentStatusResponse(
      paymentId: (json['paymentId'] as num).toInt(),
      status: $enumDecode(_$PaymentStatusEnumMap, json['status']),
      transactionId: json['transactionId'] as String?,
      gatewayReference: json['gatewayReference'] as String?,
      message: json['message'] as String?,
      processedAt: json['processedAt'] == null
          ? null
          : DateTime.parse(json['processedAt'] as String),
    );

Map<String, dynamic> _$PaymentStatusResponseToJson(
        PaymentStatusResponse instance) =>
    <String, dynamic>{
      'paymentId': instance.paymentId,
      'status': _$PaymentStatusEnumMap[instance.status]!,
      'transactionId': instance.transactionId,
      'gatewayReference': instance.gatewayReference,
      'message': instance.message,
      'processedAt': instance.processedAt?.toIso8601String(),
    };

PaymentSummary _$PaymentSummaryFromJson(Map<String, dynamic> json) =>
    PaymentSummary(
      totalAmount: (json['totalAmount'] as num).toDouble(),
      totalCommission: (json['totalCommission'] as num).toDouble(),
      netAmount: (json['netAmount'] as num).toDouble(),
      totalCount: (json['totalCount'] as num).toInt(),
      pendingCount: (json['pendingCount'] as num).toInt(),
      completedCount: (json['completedCount'] as num).toInt(),
      failedCount: (json['failedCount'] as num).toInt(),
    );

Map<String, dynamic> _$PaymentSummaryToJson(PaymentSummary instance) =>
    <String, dynamic>{
      'totalAmount': instance.totalAmount,
      'totalCommission': instance.totalCommission,
      'netAmount': instance.netAmount,
      'totalCount': instance.totalCount,
      'pendingCount': instance.pendingCount,
      'completedCount': instance.completedCount,
      'failedCount': instance.failedCount,
    };
