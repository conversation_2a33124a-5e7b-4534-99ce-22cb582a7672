import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';

import '../../../shared/models/tracking_model.dart';
import '../../../shared/services/tracking_service.dart';
import '../../../core/di/service_locator.dart';
import '../bloc/tracking_bloc.dart';

class TrackingControls extends StatefulWidget {
  final int loadId;
  final VoidCallback? onTrackingUpdated;

  const TrackingControls({
    super.key,
    required this.loadId,
    this.onTrackingUpdated,
  });

  @override
  State<TrackingControls> createState() => _TrackingControlsState();
}

class _TrackingControlsState extends State<TrackingControls> {
  final _formKey = GlobalKey<FormState>();
  final _locationController = TextEditingController();
  final _notesController = TextEditingController();
  
  TrackingStatus _selectedStatus = TrackingStatus.inTransitToPickup;
  Position? _currentPosition;
  bool _useCurrentLocation = false;
  bool _isGettingLocation = false;

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _locationController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isGettingLocation = true;
    });

    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      setState(() {
        _currentPosition = position;
        _isGettingLocation = false;
      });
    } catch (e) {
      setState(() {
        _isGettingLocation = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _submitTracking() {
    if (!_formKey.currentState!.validate()) return;

    final request = TrackingUpdateRequest(
      loadId: widget.loadId,
      location: _locationController.text.trim(),
      latitude: _useCurrentLocation ? _currentPosition?.latitude : null,
      longitude: _useCurrentLocation ? _currentPosition?.longitude : null,
      status: _selectedStatus,
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      isAutomated: false,
    );

    context.read<TrackingBloc>().add(
      TrackingUpdateRequested(request: request),
    );
  }

  void _submitLocationUpdate() {
    if (_currentPosition == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Current location not available'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    final request = LocationUpdateRequest(
      loadId: widget.loadId,
      latitude: _currentPosition!.latitude,
      longitude: _currentPosition!.longitude,
      location: _locationController.text.trim().isEmpty 
          ? 'GPS Location' 
          : _locationController.text.trim(),
    );

    context.read<TrackingBloc>().add(
      LocationUpdateRequested(request: request),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TrackingBloc(
        trackingService: getIt<TrackingService>(),
      ),
      child: BlocListener<TrackingBloc, TrackingState>(
        listener: (context, state) {
          if (state is TrackingUpdated || state is LocationUpdated) {
            Navigator.of(context).pop();
            widget.onTrackingUpdated?.call();
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Tracking updated successfully'),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is TrackingError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: Container(
          padding: EdgeInsets.only(
            left: 16,
            right: 16,
            top: 16,
            bottom: MediaQuery.of(context).viewInsets.bottom + 16,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Update Tracking',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Quick location update
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Quick Location Update',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Send current GPS location without changing status',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _currentPosition != null && !_isGettingLocation
                                  ? _submitLocationUpdate
                                  : null,
                              icon: _isGettingLocation
                                  ? const SizedBox(
                                      width: 16,
                                      height: 16,
                                      child: CircularProgressIndicator(strokeWidth: 2),
                                    )
                                  : const Icon(Icons.my_location),
                              label: Text(_isGettingLocation 
                                  ? 'Getting Location...' 
                                  : 'Send Location'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          IconButton(
                            onPressed: _getCurrentLocation,
                            icon: const Icon(Icons.refresh),
                            tooltip: 'Refresh location',
                          ),
                        ],
                      ),
                      if (_currentPosition != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Current: ${_currentPosition!.latitude.toStringAsFixed(6)}, ${_currentPosition!.longitude.toStringAsFixed(6)}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontFamily: 'monospace',
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Full tracking update form
              Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Full Status Update',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),

                    // Status dropdown
                    DropdownButtonFormField<TrackingStatus>(
                      value: _selectedStatus,
                      decoration: const InputDecoration(
                        labelText: 'Status',
                        border: OutlineInputBorder(),
                      ),
                      items: TrackingStatus.values.map((status) {
                        return DropdownMenuItem(
                          value: status,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(status.displayName),
                              Text(
                                status.description,
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedStatus = value;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16),

                    // Location field
                    TextFormField(
                      controller: _locationController,
                      decoration: const InputDecoration(
                        labelText: 'Location Description',
                        hintText: 'e.g., Highway A1, 50km from Harare',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a location description';
                        }
                        if (value.trim().length > 200) {
                          return 'Location must not exceed 200 characters';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16),

                    // Use current location checkbox
                    CheckboxListTile(
                      title: const Text('Include GPS coordinates'),
                      subtitle: _currentPosition != null
                          ? Text(
                              '${_currentPosition!.latitude.toStringAsFixed(6)}, ${_currentPosition!.longitude.toStringAsFixed(6)}',
                              style: const TextStyle(fontFamily: 'monospace'),
                            )
                          : const Text('Location not available'),
                      value: _useCurrentLocation,
                      onChanged: _currentPosition != null
                          ? (value) {
                              setState(() {
                                _useCurrentLocation = value ?? false;
                              });
                            }
                          : null,
                      contentPadding: EdgeInsets.zero,
                    ),
                    const SizedBox(height: 16),

                    // Notes field
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'Notes (Optional)',
                        hintText: 'Additional information about this update',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      validator: (value) {
                        if (value != null && value.trim().length > 1000) {
                          return 'Notes must not exceed 1000 characters';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),

                    // Submit button
                    BlocBuilder<TrackingBloc, TrackingState>(
                      builder: (context, state) {
                        final isLoading = state is TrackingLoading;
                        
                        return SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: isLoading ? null : _submitTracking,
                            child: isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : const Text('Update Tracking'),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
