package zw.co.kanjan.logipool.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import zw.co.kanjan.logipool.dto.bid.BidCreateRequest;
import zw.co.kanjan.logipool.dto.bid.BidResponse;
import zw.co.kanjan.logipool.entity.Bid;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface BidMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "load", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "acceptedAt", ignore = true)
    @Mapping(target = "rejectedAt", ignore = true)
    Bid toEntity(BidCreateRequest request);
    
    @Mapping(source = "load.id", target = "loadId")
    @Mapping(source = "load.title", target = "loadTitle")
    @Mapping(source = "company.id", target = "companyId")
    @Mapping(source = "company.name", target = "companyName")
    BidResponse toResponse(Bid bid);
}
