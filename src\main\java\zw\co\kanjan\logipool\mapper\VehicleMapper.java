package zw.co.kanjan.logipool.mapper;

import org.mapstruct.*;
import zw.co.kanjan.logipool.dto.VehicleDto;
import zw.co.kanjan.logipool.entity.Vehicle;
import zw.co.kanjan.logipool.entity.Company;

import java.util.Arrays;
import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface VehicleMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "documents", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "approvedAt", ignore = true)
    @Mapping(target = "approvedBy", ignore = true)
    @Mapping(target = "publicApprovalStatus", constant = "PENDING")
    Vehicle toEntity(VehicleDto.VehicleCreateRequest request);
    
    @Mapping(source = "company", target = "company", qualifiedByName = "mapCompanyInfo")
    @Mapping(source = "features", target = "features", qualifiedByName = "parseFeatures")
    VehicleDto.VehicleResponse toResponse(Vehicle vehicle);
    
    @Mapping(source = "company", target = "company", qualifiedByName = "mapPublicCompanyInfo")
    @Mapping(source = "features", target = "features", qualifiedByName = "parseFeatures")
    @Mapping(target = "registrationNumber", ignore = true) // Hide registration for public
    VehicleDto.VehicleResponse toPublicResponse(Vehicle vehicle);
    
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "documents", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "approvedAt", ignore = true)
    @Mapping(target = "approvedBy", ignore = true)
    void updateEntity(VehicleDto.VehicleUpdateRequest request, @MappingTarget Vehicle vehicle);
    
    @Named("mapCompanyInfo")
    default VehicleDto.CompanyInfo mapCompanyInfo(Company company) {
        if (company == null) {
            return null;
        }
        
        return VehicleDto.CompanyInfo.builder()
                .id(company.getId())
                .name(company.getName())
                .rating(company.getRating())
                .verificationStatus(company.getVerificationStatus() != null ? 
                    company.getVerificationStatus().toString() : null)
                .location(company.getCity())
                .phoneNumber(company.getPhoneNumber())
                .email(company.getEmail())
                .build();
    }
    
    @Named("mapPublicCompanyInfo")
    default VehicleDto.CompanyInfo mapPublicCompanyInfo(Company company) {
        if (company == null) {
            return null;
        }
        
        return VehicleDto.CompanyInfo.builder()
                .id(company.getId())
                .name(company.getName())
                .rating(company.getRating())
                .verificationStatus(company.getVerificationStatus() != null ? 
                    company.getVerificationStatus().toString() : null)
                .location(company.getCity())
                // Hide sensitive information for public view
                .phoneNumber(null)
                .email(null)
                .build();
    }
    
    @Named("parseFeatures")
    default List<String> parseFeatures(String featuresJson) {
        if (featuresJson == null || featuresJson.trim().isEmpty()) {
            return List.of();
        }
        
        try {
            // Simple parsing - in production, use proper JSON parsing
            String cleaned = featuresJson.replaceAll("[\\[\\]\"]", "");
            if (cleaned.trim().isEmpty()) {
                return List.of();
            }
            return Arrays.asList(cleaned.split(",\\s*"));
        } catch (Exception e) {
            return List.of();
        }
    }
}
