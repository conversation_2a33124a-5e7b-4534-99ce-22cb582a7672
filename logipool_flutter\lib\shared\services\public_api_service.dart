import 'dart:convert';
import 'package:dio/dio.dart';
import '../models/vehicle_model.dart';
import '../models/equipment_model.dart';
import '../models/load_model.dart';
import '../models/paginated_response.dart';
import '../utils/api_client.dart';

class PublicApiService {
  final ApiClient _apiClient;

  PublicApiService(this._apiClient);

  // Browse vehicles
  Future<PaginatedResponse<VehicleModel>> browseVehicles({
    int page = 0,
    int size = 20,
    String sortBy = 'createdAt',
    String sortDir = 'desc',
    String? type,
    String? location,
    String? search,
    bool? availableForRent,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'size': size.toString(),
        'sortBy': sortBy,
        'sortDir': sortDir,
      };

      if (type != null) queryParams['type'] = type;
      if (location != null) queryParams['location'] = location;
      if (search != null) queryParams['search'] = search;
      if (availableForRent != null) queryParams['availableForRent'] = availableForRent.toString();

      // Build query string
      final queryString = queryParams.entries
          .where((entry) => entry.value != null)
          .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
          .join('&');

      final endpoint = '/api/public/vehicles${queryString.isNotEmpty ? '?$queryString' : ''}';
      final response = await _apiClient.get(endpoint);

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return PaginatedResponse<VehicleModel>.fromJson(
          jsonData as Map<String, dynamic>,
          (json) => VehicleModel.fromJson(json as Map<String, dynamic>),
        );
      } else {
        throw Exception('Failed to load vehicles: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to browse vehicles: $e');
    }
  }

  // Get vehicle details
  Future<VehicleModel> getVehicleDetails(int vehicleId) async {
    try {
      final response = await _apiClient.get('/api/public/vehicles/$vehicleId');
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return VehicleModel.fromJson(jsonData as Map<String, dynamic>);
      } else {
        throw Exception('Failed to load vehicle details: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get vehicle details: $e');
    }
  }

  // Browse equipment
  Future<PaginatedResponse<EquipmentModel>> browseEquipment({
    int page = 0,
    int size = 20,
    String sortBy = 'createdAt',
    String sortDir = 'desc',
    String? type,
    String? location,
    String? search,
    bool? availableForRent,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page.toString(),
        'size': size.toString(),
        'sortBy': sortBy,
        'sortDir': sortDir,
      };

      if (type != null) queryParams['type'] = type;
      if (location != null) queryParams['location'] = location;
      if (search != null) queryParams['search'] = search;
      if (availableForRent != null) queryParams['availableForRent'] = availableForRent.toString();

      // Build query string
      final queryString = queryParams.entries
          .where((entry) => entry.value != null)
          .map((entry) => '${entry.key}=${Uri.encodeComponent(entry.value.toString())}')
          .join('&');

      final endpoint = '/api/public/equipment${queryString.isNotEmpty ? '?$queryString' : ''}';
      final response = await _apiClient.get(endpoint);

      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return PaginatedResponse<EquipmentModel>.fromJson(
          jsonData as Map<String, dynamic>,
          (json) => EquipmentModel.fromJson(json as Map<String, dynamic>),
        );
      } else {
        throw Exception('Failed to load equipment: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to browse equipment: $e');
    }
  }

  // Get equipment details
  Future<EquipmentModel> getEquipmentDetails(int equipmentId) async {
    try {
      final response = await _apiClient.get('/api/public/equipment/$equipmentId');
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return EquipmentModel.fromJson(jsonData as Map<String, dynamic>);
      } else {
        throw Exception('Failed to load equipment details: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get equipment details: $e');
    }
  }

  // Get vehicle types
  Future<List<String>> getVehicleTypes() async {
    try {
      final response = await _apiClient.get('/api/public/vehicles/types');
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return List<String>.from(jsonData as List);
      } else {
        throw Exception('Failed to load vehicle types: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get vehicle types: $e');
    }
  }

  // Get equipment types
  Future<List<String>> getEquipmentTypes() async {
    try {
      final response = await _apiClient.get('/api/public/equipment/types');
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return List<String>.from(jsonData as List);
      } else {
        throw Exception('Failed to load equipment types: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get equipment types: $e');
    }
  }

  // Get available locations
  Future<List<String>> getAvailableLocations() async {
    try {
      final response = await _apiClient.get('/api/public/locations');
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return List<String>.from(jsonData as List);
      } else {
        throw Exception('Failed to load locations: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get available locations: $e');
    }
  }

  // Track load by tracking number (for guests)
  Future<LoadModel?> trackLoad(String trackingNumber) async {
    try {
      final response = await _apiClient.get('/api/public/track/$trackingNumber');
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return LoadModel.fromJson(jsonData as Map<String, dynamic>);
      } else if (response.statusCode == 404) {
        return null; // Load not found
      } else {
        throw Exception('Failed to track load: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to track load: $e');
    }
  }

  // Get public statistics
  Future<Map<String, dynamic>> getPublicStats() async {
    try {
      final response = await _apiClient.get('/api/public/stats');
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body);
        return jsonData as Map<String, dynamic>;
      } else {
        throw Exception('Failed to load stats: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get public stats: $e');
    }
  }

  // Get featured vehicles for home page
  Future<List<VehicleModel>> getFeaturedVehicles({int limit = 6}) async {
    try {
      final response = await _apiClient.get('/api/public/vehicles/featured?limit=$limit');
      if (response.statusCode == 200) {
        final jsonData = jsonDecode(response.body) as List;
        return jsonData.map((json) => VehicleModel.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load featured vehicles: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('Failed to get featured vehicles: $e');
    }
  }

  // Get featured equipment for home page
  Future<List<EquipmentModel>> getFeaturedEquipment({int limit = 6}) async {
    try {
      final response = await browseEquipment(
        page: 0,
        size: limit,
        sortBy: 'rating',
        sortDir: 'desc',
      );
      return response.content;
    } catch (e) {
      throw Exception('Failed to get featured equipment: $e');
    }
  }
}
