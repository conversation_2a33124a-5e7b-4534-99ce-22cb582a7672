package zw.co.kanjan.logipool.service;

import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import zw.co.kanjan.logipool.dto.DocumentDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.DocumentMapper;
import zw.co.kanjan.logipool.repository.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class DocumentService {
    
    private final DocumentRepository documentRepository;
    private final CompanyRepository companyRepository;
    private final CompanyMemberRepository companyMemberRepository;
    private final VehicleRepository vehicleRepository;
    private final LoadRepository loadRepository;
    private final UserRepository userRepository;
    private final DocumentMapper documentMapper;
    private final FileStorageService fileStorageService;
    
    // Required document types for company verification
    private static final List<Document.DocumentType> REQUIRED_COMPANY_DOCUMENTS = Arrays.asList(
        Document.DocumentType.COMPANY_REGISTRATION,
        Document.DocumentType.TAX_CLEARANCE,
        Document.DocumentType.BUSINESS_LICENSE
    );
    
    public DocumentDto.FileUploadResponse uploadDocument(MultipartFile file, 
                                                        DocumentDto.DocumentUploadRequest request, 
                                                        String username) {
        
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
        
        // Validate request and permissions
        validateUploadRequest(request, user);
        
        // Store file
        String category = fileStorageService.getCategoryForDocumentType(request.getType().name());
        String filePath = fileStorageService.storeFile(file, category);
        
        // Create document entity
        Document document = documentMapper.toEntity(request);
        document.setFilePath(filePath);
        document.setFileName(file.getOriginalFilename());
        document.setFileType(file.getContentType());
        document.setFileSize(file.getSize());
        document.setIsRequired(isRequiredDocument(request.getType()));
        
        // Set relationships
        setDocumentRelationships(document, request, user);
        
        Document savedDocument = documentRepository.save(document);
        log.info("Document uploaded successfully: {} by user: {}", savedDocument.getName(), username);
        
        return DocumentDto.FileUploadResponse.builder()
            .success(true)
            .message("Document uploaded successfully")
            .fileName(savedDocument.getFileName())
            .fileSize(savedDocument.getFileSize())
            .fileType(savedDocument.getFileType())
            .documentId(savedDocument.getId())
            .downloadUrl("/api/documents/" + savedDocument.getId() + "/download")
            .build();
    }
    
    @Transactional(readOnly = true)
    public DocumentDto.DocumentResponse getDocument(Long id, String username) {
        Document document = documentRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + id));
        
        // Check permissions
        validateDocumentAccess(document, username);
        
        DocumentDto.DocumentResponse response = documentMapper.toResponse(document);
        response.setDownloadUrl("/api/documents/" + document.getId() + "/download");
        return response;
    }
    
    @Transactional(readOnly = true)
    public Page<DocumentDto.DocumentResponse> getDocuments(Pageable pageable, String username) {
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
        
        Page<Document> documents;
        
        // Admin can see all documents
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            documents = documentRepository.findAll(pageable);
        } else {
            // Users can only see their own company's documents
            Company company = companyRepository.findByUser(user)
                .orElseThrow(() -> new BusinessException("Company not found for user"));
            documents = documentRepository.findByCompany(company, pageable);
        }
        
        return documents.map(doc -> {
            DocumentDto.DocumentResponse response = documentMapper.toResponse(doc);
            response.setDownloadUrl("/api/documents/" + doc.getId() + "/download");
            return response;
        });
    }
    
    @Transactional(readOnly = true)
    public Page<DocumentDto.DocumentResponse> getCompanyDocuments(Long companyId, Pageable pageable, String username) {
        Company company = companyRepository.findById(companyId)
            .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));

        // Check permissions
        validateCompanyAccess(company, username);

        Page<Document> documents = documentRepository.findByCompany(company, pageable);
        return documents.map(doc -> {
            DocumentDto.DocumentResponse response = documentMapper.toResponse(doc);
            response.setDownloadUrl("/api/documents/" + doc.getId() + "/download");
            return response;
        });
    }

    @Transactional(readOnly = true)
    public Page<DocumentDto.DocumentResponse> getVehicleDocuments(Long vehicleId, Pageable pageable, String username) {
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
            .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with id: " + vehicleId));

        // Check permissions - user must own the vehicle's company or be admin
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));

        if (!vehicle.getCompany().getUser().equals(user) &&
            user.getRoles().stream().noneMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            throw new BusinessException("You can only view documents for your own vehicles");
        }

        Page<Document> documents = documentRepository.findByVehicle(vehicle, pageable);
        return documents.map(doc -> {
            DocumentDto.DocumentResponse response = documentMapper.toResponse(doc);
            response.setDownloadUrl("/api/documents/" + doc.getId() + "/download");
            return response;
        });
    }
    
    @Transactional(readOnly = true)
    public DocumentDownloadResponse downloadDocument(Long id, String username) {
        Document document = documentRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + id));

        // Check permissions
        validateDocumentAccess(document, username);

        Resource resource = fileStorageService.loadFileAsResource(document.getFilePath());

        return DocumentDownloadResponse.builder()
            .resource(resource)
            .originalFileName(document.getFileName())
            .contentType(document.getFileType())
            .build();
    }

    @Data
    @Builder
    public static class DocumentDownloadResponse {
        private Resource resource;
        private String originalFileName;
        private String contentType;
    }
    
    public DocumentDto.DocumentResponse updateDocument(Long id, DocumentDto.DocumentUpdateRequest request, String username) {
        Document document = documentRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + id));
        
        // Check permissions
        validateDocumentAccess(document, username);
        
        // Only allow updates if document is not verified
        if (document.getStatus() == Document.DocumentStatus.VERIFIED) {
            throw new BusinessException("Cannot update verified document");
        }
        
        documentMapper.updateEntity(request, document);
        Document savedDocument = documentRepository.save(document);
        
        log.info("Document updated successfully: {} by user: {}", savedDocument.getName(), username);
        
        DocumentDto.DocumentResponse response = documentMapper.toResponse(savedDocument);
        response.setDownloadUrl("/api/documents/" + savedDocument.getId() + "/download");
        return response;
    }
    
    public void deleteDocument(Long id, String username) {
        Document document = documentRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + id));
        
        // Check permissions
        validateDocumentAccess(document, username);
        
        // Only allow deletion if document is not verified
        if (document.getStatus() == Document.DocumentStatus.VERIFIED) {
            throw new BusinessException("Cannot delete verified document");
        }
        
        // Delete file from storage
        fileStorageService.deleteFile(document.getFilePath());
        
        // Delete document record
        documentRepository.delete(document);
        
        log.info("Document deleted successfully: {} by user: {}", document.getName(), username);
    }
    
    public DocumentDto.DocumentResponse verifyDocument(Long id, DocumentDto.DocumentVerificationRequest request, String username) {
        Document document = documentRepository.findById(id)
            .orElseThrow(() -> new ResourceNotFoundException("Document not found with id: " + id));

        User verifier = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));

        // Check if user has permission to verify this document
        validateDocumentVerificationPermission(document, verifier);

        document.setStatus(request.getStatus());
        document.setVerifiedBy(verifier);
        document.setVerifiedAt(LocalDateTime.now());

        Document savedDocument = documentRepository.save(document);

        log.info("Document verification updated: {} to {} by user: {}",
                savedDocument.getName(), request.getStatus(), username);

        DocumentDto.DocumentResponse response = documentMapper.toResponse(savedDocument);
        response.setDownloadUrl("/api/documents/" + savedDocument.getId() + "/download");
        return response;
    }
    
    private void validateUploadRequest(DocumentDto.DocumentUploadRequest request, User user) {
        // Auto-associate with user's company if no association is specified and user has a company
        if (request.getCompanyId() == null && request.getVehicleId() == null && request.getLoadId() == null) {
            // Check if user has a company and auto-associate
            if (user.getCompany() != null) {
                request.setCompanyId(user.getCompany().getId());
                log.info("Auto-associating document with user's company: {}", user.getCompany().getId());
            } else {
                throw new BusinessException("Document must be associated with a company, vehicle, or load");
            }
        }

        // Validate multiple relationships are not specified
        int relationshipCount = 0;
        if (request.getCompanyId() != null) relationshipCount++;
        if (request.getVehicleId() != null) relationshipCount++;
        if (request.getLoadId() != null) relationshipCount++;

        if (relationshipCount > 1) {
            throw new BusinessException("Document can only be associated with one entity (company, vehicle, or load)");
        }
    }
    
    private void setDocumentRelationships(Document document, DocumentDto.DocumentUploadRequest request, User user) {
        if (request.getCompanyId() != null) {
            Company company = companyRepository.findById(request.getCompanyId())
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + request.getCompanyId()));
            
            // Check if user owns the company or is admin
            if (!company.getUser().equals(user) &&
                user.getRoles().stream().noneMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
                throw new BusinessException("You can only upload documents for your own company");
            }
            
            document.setCompany(company);
        }
        
        if (request.getVehicleId() != null) {
            Vehicle vehicle = vehicleRepository.findById(request.getVehicleId())
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with id: " + request.getVehicleId()));
            
            // Check if user owns the vehicle's company or is admin
            if (!vehicle.getCompany().getUser().equals(user) &&
                user.getRoles().stream().noneMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
                throw new BusinessException("You can only upload documents for your own vehicles");
            }
            
            document.setVehicle(vehicle);
        }
        
        if (request.getLoadId() != null) {
            Load load = loadRepository.findById(request.getLoadId())
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + request.getLoadId()));

            // Validate user has permission to upload documents for this load
            validateLoadDocumentPermission(load, user);

            document.setLoad(load);
        }
    }
    
    private void validateDocumentAccess(Document document, String username) {
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
        
        // Admin can access all documents
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }
        
        // Check if user has access to this document
        boolean hasAccess = false;
        
        if (document.getCompany() != null && document.getCompany().getUser().equals(user)) {
            hasAccess = true;
        } else if (document.getVehicle() != null && document.getVehicle().getCompany().getUser().equals(user)) {
            hasAccess = true;
        } else if (document.getLoad() != null) {
            // Check if user is the load client or has company member access
            hasAccess = hasLoadDocumentAccess(document.getLoad(), user);
        }
        
        if (!hasAccess) {
            throw new BusinessException("You don't have permission to access this document");
        }
    }
    
    private void validateCompanyAccess(Company company, String username) {
        User user = userRepository.findByUsername(username)
            .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
        
        // Admin can access all companies
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }
        
        // Check if user owns the company
        if (!company.getUser().equals(user)) {
            throw new BusinessException("You don't have permission to access this company's documents");
        }
    }
    
    private boolean isRequiredDocument(Document.DocumentType type) {
        return REQUIRED_COMPANY_DOCUMENTS.contains(type);
    }

    // Load-specific document management methods

    @Transactional(readOnly = true)
    public Page<DocumentDto.DocumentResponse> getLoadDocuments(Long loadId, Pageable pageable, String username) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));

        // Validate user has access to load documents
        if (!hasLoadDocumentAccess(load, user)) {
            throw new BusinessException("You don't have permission to view documents for this load");
        }

        Page<Document> documents = documentRepository.findByLoad(load, pageable);
        return documents.map(doc -> {
            DocumentDto.DocumentResponse response = documentMapper.toResponse(doc);
            response.setDownloadUrl("/api/documents/" + doc.getId() + "/download");
            return response;
        });
    }

    public DocumentDto.FileUploadResponse uploadLoadDocument(Long loadId, MultipartFile file,
                                                           DocumentDto.DocumentUploadRequest request,
                                                           String username) {

        // Set the load ID in the request
        request.setLoadId(loadId);

        // Use the existing upload method which now has enhanced validation
        return uploadDocument(file, request, username);
    }

    public DocumentDto.DocumentResponse generateProofOfDelivery(Long loadId, String notes, String username) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));

        // Validate load is delivered
        if (load.getStatus() != Load.LoadStatus.DELIVERED) {
            throw new BusinessException("Proof of delivery can only be generated for delivered loads");
        }

        // Validate user has permission
        validateLoadDocumentPermission(load, user);

        // Create proof of delivery document
        Document document = Document.builder()
                .name("Proof of Delivery - Load #" + loadId)
                .type(Document.DocumentType.PROOF_OF_DELIVERY)
                .description("Auto-generated proof of delivery. " + (notes != null ? notes : ""))
                .status(Document.DocumentStatus.VERIFIED)
                .load(load)
                .fileName("pod_load_" + loadId + "_" + System.currentTimeMillis() + ".pdf")
                .fileType("application/pdf")
                .verifiedAt(LocalDateTime.now())
                .verifiedBy(user)
                .build();

        // Here you would integrate with a PDF generation service
        // For now, we'll create a placeholder
        String filePath = "documents/loads/" + loadId + "/pod_" + System.currentTimeMillis() + ".pdf";
        document.setFilePath(filePath);
        document.setFileSize(1024L); // Placeholder size

        Document savedDocument = documentRepository.save(document);

        log.info("Proof of delivery generated for load {} by user {}", loadId, username);

        DocumentDto.DocumentResponse response = documentMapper.toResponse(savedDocument);
        response.setDownloadUrl("/api/documents/" + savedDocument.getId() + "/download");
        return response;
    }

    private void validateLoadDocumentPermission(Load load, User user) {
        // Admin can upload documents for any load
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Load client can upload documents for their own loads
        if (load.getClient().equals(user)) {
            return;
        }

        // If load is assigned to a company, check if user is a company member with document upload permission
        if (load.getAssignedCompany() != null) {
            // Check if user is the company owner
            if (load.getAssignedCompany().getUser().equals(user)) {
                return;
            }

            // Check if user is a company member with document upload permission
            CompanyMember member = companyMemberRepository.findByCompanyAndUser(load.getAssignedCompany(), user)
                    .orElse(null);

            if (member != null &&
                member.getStatus() == CompanyMember.MemberStatus.ACTIVE &&
                member.getCanUploadDocuments()) {
                return;
            }
        }

        throw new BusinessException("You don't have permission to upload documents for this load");
    }

    private boolean hasLoadDocumentAccess(Load load, User user) {
        // Admin can access all documents
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return true;
        }

        // Load client can access documents for their own loads
        if (load.getClient().equals(user)) {
            return true;
        }

        // If load is assigned to a company, check if user is a company member
        if (load.getAssignedCompany() != null) {
            // Check if user is the company owner
            if (load.getAssignedCompany().getUser().equals(user)) {
                return true;
            }

            // Check if user is an active company member
            CompanyMember member = companyMemberRepository.findByCompanyAndUser(load.getAssignedCompany(), user)
                    .orElse(null);

            if (member != null && member.getStatus() == CompanyMember.MemberStatus.ACTIVE) {
                return true;
            }
        }

        return false;
    }

    private void validateDocumentVerificationPermission(Document document, User user) {
        // Admin can verify any documents
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // For load documents, check load-specific permissions
        if (document.getLoad() != null) {
            Load load = document.getLoad();

            // Load client can verify their own load documents
            if (load.getClient().equals(user)) {
                return;
            }

            // Company members with document permissions can verify
            if (load.getAssignedCompany() != null) {
                // Check if user is the company owner
                if (load.getAssignedCompany().getUser().equals(user)) {
                    return;
                }

                CompanyMember member = companyMemberRepository.findByCompanyAndUser(load.getAssignedCompany(), user)
                        .orElse(null);

                if (member != null &&
                    member.getStatus() == CompanyMember.MemberStatus.ACTIVE &&
                    (member.getCanUploadDocuments() ||
                     member.getRole() == CompanyMember.CompanyRole.MANAGER ||
                     member.getRole() == CompanyMember.CompanyRole.OWNER)) {
                    return;
                }
            }
        }

        // For company documents, check if user owns the company
        if (document.getCompany() != null) {
            if (document.getCompany().getUser().equals(user)) {
                return;
            }
        }

        // For vehicle documents, check if user owns the company that owns the vehicle
        if (document.getVehicle() != null) {
            if (document.getVehicle().getCompany().getUser().equals(user)) {
                return;
            }
        }

        throw new BusinessException("You don't have permission to verify this document");
    }
}
