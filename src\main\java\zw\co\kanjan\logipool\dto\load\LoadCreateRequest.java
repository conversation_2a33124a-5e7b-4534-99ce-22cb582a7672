package zw.co.kanjan.logipool.dto.load;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Data;
import zw.co.kanjan.logipool.entity.Load;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class LoadCreateRequest {
    
    @NotBlank
    @Size(max = 100)
    private String title;
    
    @Size(max = 1000)
    private String description;
    
    @NotBlank
    @Size(max = 50)
    private String cargoType;
    
    @Positive
    private BigDecimal weight;
    
    @Size(max = 20)
    private String weightUnit = "kg";
    
    private BigDecimal volume;
    
    @Size(max = 20)
    private String volumeUnit = "m3";
    
    @NotBlank
    @Size(max = 200)
    private String pickupLocation;

    @DecimalMin(value = "-90.0", message = "Pickup latitude must be between -90 and 90")
    @DecimalMax(value = "90.0", message = "Pickup latitude must be between -90 and 90")
    private BigDecimal pickupLatitude;

    @DecimalMin(value = "-180.0", message = "Pickup longitude must be between -180 and 180")
    @DecimalMax(value = "180.0", message = "Pickup longitude must be between -180 and 180")
    private BigDecimal pickupLongitude;

    @NotBlank
    @Size(max = 200)
    private String deliveryLocation;

    @DecimalMin(value = "-90.0", message = "Delivery latitude must be between -90 and 90")
    @DecimalMax(value = "90.0", message = "Delivery latitude must be between -90 and 90")
    private BigDecimal deliveryLatitude;

    @DecimalMin(value = "-180.0", message = "Delivery longitude must be between -180 and 180")
    @DecimalMax(value = "180.0", message = "Delivery longitude must be between -180 and 180")
    private BigDecimal deliveryLongitude;
    
    @NotNull
    private LocalDateTime pickupDate;
    
    @NotNull
    private LocalDateTime deliveryDate;
    
    private BigDecimal estimatedDistance;
    
    @Size(max = 20)
    private String distanceUnit = "km";
    
    private Load.LoadType loadType;
    
    private Load.PaymentType paymentType;
    
    private BigDecimal paymentRate;
    
    @Size(max = 20)
    private String paymentUnit;
    
    private BigDecimal estimatedValue;
    
    private Load.Priority priority = Load.Priority.NORMAL;
    
    private Boolean requiresInsurance = false;
    
    private Boolean requiresSpecialHandling = false;
    
    @Size(max = 1000)
    private String specialInstructions;
    
    private LocalDateTime biddingClosesAt;
}
