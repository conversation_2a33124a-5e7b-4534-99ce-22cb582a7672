import 'dart:convert';
import 'package:http/http.dart' as http;

import '../../core/constants/app_constants.dart';

class ApiClient {
  static const String baseUrl = AppConstants.baseUrlNoApi;
  static ApiClient? _instance;
  
  static ApiClient get instance {
    _instance ??= ApiClient._internal();
    return _instance!;
  }
  
  ApiClient._internal();
  
  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  Map<String, String> _headersWithAuth(String? token) => {
    ..._headers,
    if (token != null) 'Authorization': 'Bearer $token',
  };
  
  Future<http.Response> get(String endpoint, {String? token}) async {
    final url = Uri.parse('$baseUrl$endpoint');
    return await http.get(url, headers: _headersWithAuth(token));
  }
  
  Future<http.Response> post(String endpoint, {
    Map<String, dynamic>? body,
    String? token,
  }) async {
    final url = Uri.parse('$baseUrl$endpoint');
    return await http.post(
      url,
      headers: _headersWithAuth(token),
      body: body != null ? jsonEncode(body) : null,
    );
  }
  
  Future<http.Response> put(String endpoint, {
    Map<String, dynamic>? body,
    String? token,
  }) async {
    final url = Uri.parse('$baseUrl$endpoint');
    return await http.put(
      url,
      headers: _headersWithAuth(token),
      body: body != null ? jsonEncode(body) : null,
    );
  }
  
  Future<http.Response> delete(String endpoint, {String? token}) async {
    final url = Uri.parse('$baseUrl$endpoint');
    return await http.delete(url, headers: _headersWithAuth(token));
  }
  
  Future<http.Response> patch(String endpoint, {
    Map<String, dynamic>? body,
    String? token,
  }) async {
    final url = Uri.parse('$baseUrl$endpoint');
    return await http.patch(
      url,
      headers: _headersWithAuth(token),
      body: body != null ? jsonEncode(body) : null,
    );
  }
}
