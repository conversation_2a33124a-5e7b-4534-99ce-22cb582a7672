package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.TrackingVerification;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.TrackingVerificationRepository;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class TrackingVerificationService {
    
    private final TrackingVerificationRepository trackingVerificationRepository;
    private final LoadRepository loadRepository;
    private final NotificationService notificationService;
    private final EmailTemplateService emailTemplateService;
    private final PublicBrowsingService publicBrowsingService;
    private final TrackingSecurityService trackingSecurityService;
    
    @Value("${app.tracking.verification.token-expiry-hours:24}")
    private int tokenExpiryHours;
    
    @Value("${app.tracking.verification.code-expiry-minutes:15}")
    private int codeExpiryMinutes;
    
    @Value("${app.tracking.verification.max-requests-per-hour:3}")
    private int maxRequestsPerHour;
    
    @Value("${app.tracking.verification.max-ip-requests-per-hour:10}")
    private int maxIpRequestsPerHour;
    
    @Value("${app.tracking.verification.max-access-count:5}")
    private int maxAccessCount;
    
    @Value("${app.base-url:http://localhost:8080}")
    private String baseUrl;
    
    private static final SecureRandom secureRandom = new SecureRandom();
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@(.+)$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^\\+?[1-9]\\d{1,14}$");
    
    /**
     * Request tracking verification via email or SMS
     */
    public String requestTrackingVerification(String trackingNumber, String contact, 
                                            TrackingVerification.VerificationMethod method,
                                            String ipAddress, String userAgent) {
        
        // Validate tracking number exists
        Load load = loadRepository.findByTrackingNumber(trackingNumber)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with tracking number: " + trackingNumber));
        
        // Validate contact information
        validateContact(contact, method);
        
        // Check rate limits and security
        trackingSecurityService.checkRateLimit(trackingNumber, contact, ipAddress);
        
        // Generate verification
        TrackingVerification verification = createVerification(trackingNumber, contact, method, ipAddress, userAgent);
        verification = trackingVerificationRepository.save(verification);
        
        // Send verification
        sendVerification(verification, load);
        
        log.info("Tracking verification requested for tracking number: {} via {}", trackingNumber, method);
        return verification.getToken();
    }
    
    /**
     * Verify tracking access using verification code
     */
    public String verifyTrackingCode(String trackingNumber, String verificationCode, String ipAddress) {
        TrackingVerification verification = trackingVerificationRepository
                .findByTrackingNumberAndCode(trackingNumber, verificationCode, LocalDateTime.now())
                .orElseThrow(() -> new BusinessException("Invalid or expired verification code"));
        
        verification.markAsVerified();
        verification.setIpAddress(ipAddress);
        trackingVerificationRepository.save(verification);
        
        log.info("Tracking verification code verified for tracking number: {}", trackingNumber);
        return verification.getToken();
    }
    
    /**
     * Validate tracking access using token
     */
    public TrackingVerification validateTrackingAccess(String token, String ipAddress) {
        TrackingVerification verification = trackingVerificationRepository
                .findValidByToken(token, LocalDateTime.now())
                .orElseThrow(() -> new BusinessException("Invalid or expired tracking access token"));

        // Validate access and check for suspicious activity
        trackingSecurityService.validateTrackingAccess(verification, ipAddress);

        // Update access tracking
        verification.incrementAccessCount();
        verification.setIpAddress(ipAddress);
        trackingVerificationRepository.save(verification);

        log.info("Tracking access validated for tracking number: {}", verification.getTrackingNumber());
        return verification;
    }
    
    /**
     * Get tracking details with valid token
     */
    public LoadResponse getTrackingDetails(String token, String ipAddress) {
        TrackingVerification verification = validateTrackingAccess(token, ipAddress);

        return publicBrowsingService.getSecureTrackingDetails(verification.getTrackingNumber());
    }
    
    /**
     * Cleanup expired verifications
     */
    @Transactional
    public void cleanupExpiredVerifications() {
        int deletedCount = trackingVerificationRepository.findExpired(LocalDateTime.now()).size();
        trackingVerificationRepository.deleteExpired(LocalDateTime.now());
        log.info("Cleaned up {} expired tracking verifications", deletedCount);
    }
    
    private void validateContact(String contact, TrackingVerification.VerificationMethod method) {
        if (contact == null || contact.trim().isEmpty()) {
            throw new BusinessException("Contact information is required");
        }
        
        switch (method) {
            case EMAIL:
                if (!EMAIL_PATTERN.matcher(contact).matches()) {
                    throw new BusinessException("Invalid email address format");
                }
                break;
            case SMS:
                if (!PHONE_PATTERN.matcher(contact).matches()) {
                    throw new BusinessException("Invalid phone number format");
                }
                break;
            case BOTH:
                throw new BusinessException("BOTH verification method not supported in this version");
        }
    }
    


    private TrackingVerification createVerification(String trackingNumber, String contact,
                                                  TrackingVerification.VerificationMethod method,
                                                  String ipAddress, String userAgent) {

        String token = generateSecureToken();
        String verificationCode = generateVerificationCode();
        LocalDateTime expiryDate = method == TrackingVerification.VerificationMethod.EMAIL
                ? LocalDateTime.now().plusHours(tokenExpiryHours)
                : LocalDateTime.now().plusMinutes(codeExpiryMinutes);

        TrackingVerification.TrackingVerificationBuilder builder = TrackingVerification.builder()
                .token(token)
                .trackingNumber(trackingNumber)
                .verificationMethod(method)
                .verificationCode(verificationCode)
                .expiryDate(expiryDate)
                .ipAddress(ipAddress)
                .userAgent(userAgent);

        if (method == TrackingVerification.VerificationMethod.EMAIL) {
            builder.email(contact);
        } else {
            builder.phoneNumber(contact);
        }

        return builder.build();
    }

    private void sendVerification(TrackingVerification verification, Load load) {
        try {
            if (verification.getVerificationMethod() == TrackingVerification.VerificationMethod.EMAIL) {
                sendEmailVerification(verification, load);
            } else {
                sendSmsVerification(verification, load);
            }
        } catch (Exception e) {
            log.error("Failed to send verification for tracking number: {}", verification.getTrackingNumber(), e);
            throw new BusinessException("Failed to send verification. Please try again.");
        }
    }

    private void sendEmailVerification(TrackingVerification verification, Load load) {
        String trackingUrl = baseUrl + "/public/track?token=" + verification.getToken();

        Map<String, Object> variables = emailTemplateService.createTrackingVerificationVariables(
                verification.getTrackingNumber(),
                load.getTitle(),
                load.getPickupLocation(),
                load.getDeliveryLocation(),
                trackingUrl,
                String.valueOf(tokenExpiryHours),
                baseUrl + "/support"
        );

        String htmlContent = emailTemplateService.processTemplate("tracking-verification", variables);
        notificationService.sendHtmlEmail(
                verification.getEmail(),
                "Secure Access to Your Shipment Tracking - " + verification.getTrackingNumber(),
                htmlContent
        );

        log.info("Tracking verification email sent to: {}", verification.getEmail());
    }

    private void sendSmsVerification(TrackingVerification verification, Load load) {
        String message = String.format(
                "LogiPool Tracking Verification\n" +
                "Tracking #: %s\n" +
                "Verification Code: %s\n" +
                "Valid for %d minutes\n" +
                "Enter this code on the tracking page to view your shipment details.",
                verification.getTrackingNumber(),
                verification.getVerificationCode(),
                codeExpiryMinutes
        );

        // Note: SMS service integration would be implemented here
        // For now, we'll log the message
        log.info("SMS verification would be sent to {}: {}", verification.getPhoneNumber(), message);
    }

    private String generateSecureToken() {
        return UUID.randomUUID().toString().replace("-", "") +
               System.currentTimeMillis() +
               secureRandom.nextInt(10000);
    }

    private String generateVerificationCode() {
        return String.format("%06d", secureRandom.nextInt(1000000));
    }
}
