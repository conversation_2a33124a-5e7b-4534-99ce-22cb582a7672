import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:logipool_flutter/core/constants/app_constants.dart';

import '../models/company_model.dart';
import '../../core/network/api_client.dart';
import '../utils/pagination_model.dart';

class CompanyService {
  final ApiClient _apiClient;

  CompanyService({ApiClient? apiClient})
      : _apiClient =
            apiClient ?? ApiClient(baseUrl: AppConstants.baseUrlNoApi);

  /// Create a new company
  Future<CompanyModel> createCompany(CompanyCreateRequest request) async {
    try {
      final response = await _apiClient.post(
        '/companies',
        data: request.toJson(),
      );

      return CompanyModel.fromJson(response.data);
    } on DioException catch (e) {
      debugPrint('Error creating company: ${e.message}');
      if (e.response?.statusCode == 400) {
        final errorMessage =
            e.response?.data['message'] ?? 'Invalid company data';
        throw Exception(errorMessage);
      }
      throw Exception('Failed to create company: ${e.message}');
    } catch (e) {
      debugPrint('Unexpected error creating company: $e');
      throw Exception('Failed to create company');
    }
  }

  /// Update an existing company
  Future<CompanyModel> updateCompany(
      int companyId, CompanyUpdateRequest request) async {
    try {
      final response = await _apiClient.put(
        '/companies/$companyId',
        data: request.toJson(),
      );

      return CompanyModel.fromJson(response.data);
    } on DioException catch (e) {
      debugPrint('Error updating company: ${e.message}');
      if (e.response?.statusCode == 400) {
        final errorMessage =
            e.response?.data['message'] ?? 'Invalid company data';
        throw Exception(errorMessage);
      } else if (e.response?.statusCode == 403) {
        throw Exception('You can only update your own company');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Company not found');
      }
      throw Exception('Failed to update company: ${e.message}');
    } catch (e) {
      debugPrint('Unexpected error updating company: $e');
      throw Exception('Failed to update company');
    }
  }

  /// Get company by ID
  Future<CompanyModel> getCompanyById(int companyId) async {
    try {
      final response = await _apiClient.get('/companies/$companyId');
      return CompanyModel.fromJson(response.data);
    } on DioException catch (e) {
      debugPrint('Error fetching company: ${e.message}');
      if (e.response?.statusCode == 404) {
        throw Exception('Company not found');
      }
      throw Exception('Failed to fetch company: ${e.message}');
    } catch (e) {
      debugPrint('Unexpected error fetching company: $e');
      throw Exception('Failed to fetch company');
    }
  }

  /// Get current user's company
  Future<CompanyModel?> getMyCompany() async {
    try {
      final response = await _apiClient.get('/companies/my-company');
      return CompanyModel.fromJson(response.data);
    } on DioException catch (e) {
      debugPrint('DioException fetching my company: ${e.message}');
      debugPrint('Response status code: ${e.response?.statusCode}');
      debugPrint('Response data: ${e.response?.data}');
      if (e.response?.statusCode == 404) {
        debugPrint('User does not have a company yet - returning null');
        return null; // User doesn't have a company yet
      }
      throw Exception('Failed to fetch company: ${e.message}');
    } catch (e) {
      debugPrint('Unexpected error fetching my company: $e');
      throw Exception('Failed to fetch company');
    }
  }

  /// Get all companies with pagination
  Future<PaginationModel<CompanyModel>> getAllCompanies({
    int page = 0,
    int size = 10,
    String sortBy = 'name',
    String sortDir = 'asc',
  }) async {
    try {
      final response = await _apiClient.get(
        '/companies',
        queryParameters: {
          'page': page,
          'size': size,
          'sortBy': sortBy,
          'sortDir': sortDir,
        },
      );

      return PaginationModel<CompanyModel>.fromJson(
        response.data,
        (json) => CompanyModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      debugPrint('Error fetching companies: ${e.message}');
      throw Exception('Failed to fetch companies: ${e.message}');
    } catch (e) {
      debugPrint('Unexpected error fetching companies: $e');
      throw Exception('Failed to fetch companies');
    }
  }

  /// Search companies by name
  Future<PaginationModel<CompanyModel>> searchCompaniesByName(
    String name, {
    int page = 0,
    int size = 10,
    String sortBy = 'name',
    String sortDir = 'asc',
  }) async {
    try {
      final response = await _apiClient.get(
        '/companies/search',
        queryParameters: {
          'name': name,
          'page': page,
          'size': size,
          'sortBy': sortBy,
          'sortDir': sortDir,
        },
      );

      return PaginationModel<CompanyModel>.fromJson(
        response.data,
        (json) => CompanyModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      debugPrint('Error searching companies: ${e.message}');
      throw Exception('Failed to search companies: ${e.message}');
    } catch (e) {
      debugPrint('Unexpected error searching companies: $e');
      throw Exception('Failed to search companies');
    }
  }

  /// Get companies by verification status
  Future<PaginationModel<CompanyModel>> getCompaniesByVerificationStatus(
    VerificationStatus status, {
    int page = 0,
    int size = 10,
    String sortBy = 'name',
    String sortDir = 'asc',
  }) async {
    try {
      final response = await _apiClient.get(
        '/companies/verification-status',
        queryParameters: {
          'status': status.name.toUpperCase(),
          'page': page,
          'size': size,
          'sortBy': sortBy,
          'sortDir': sortDir,
        },
      );

      return PaginationModel<CompanyModel>.fromJson(
        response.data,
        (json) => CompanyModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      debugPrint(
          'Error fetching companies by verification status: ${e.message}');
      throw Exception('Failed to fetch companies: ${e.message}');
    } catch (e) {
      debugPrint(
          'Unexpected error fetching companies by verification status: $e');
      throw Exception('Failed to fetch companies');
    }
  }

  /// Verify company (Admin only)
  Future<CompanyModel> verifyCompany(
      int companyId, VerificationStatus status) async {
    try {
      final response = await _apiClient.put(
        '/companies/$companyId/verify',
        queryParameters: {
          'status': status.name.toUpperCase(),
        },
      );

      return CompanyModel.fromJson(response.data);
    } on DioException catch (e) {
      debugPrint('Error verifying company: ${e.message}');
      if (e.response?.statusCode == 403) {
        throw Exception('Only administrators can verify companies');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Company not found');
      }
      throw Exception('Failed to verify company: ${e.message}');
    } catch (e) {
      debugPrint('Unexpected error verifying company: $e');
      throw Exception('Failed to verify company');
    }
  }

  /// Delete company
  Future<void> deleteCompany(int companyId) async {
    try {
      await _apiClient.delete('/companies/$companyId');
    } on DioException catch (e) {
      debugPrint('Error deleting company: ${e.message}');
      if (e.response?.statusCode == 403) {
        throw Exception('You can only delete your own company');
      } else if (e.response?.statusCode == 404) {
        throw Exception('Company not found');
      }
      throw Exception('Failed to delete company: ${e.message}');
    } catch (e) {
      debugPrint('Unexpected error deleting company: $e');
      throw Exception('Failed to delete company');
    }
  }

  /// Check if user has a company
  Future<bool> hasCompany() async {
    try {
      final company = await getMyCompany();
      return company != null;
    } catch (e) {
      return false;
    }
  }

  /// Get company statistics
  Future<Map<String, dynamic>> getCompanyStatistics(int companyId) async {
    try {
      final response = await _apiClient.get('/companies/$companyId/statistics');
      return response.data;
    } on DioException catch (e) {
      debugPrint('Error fetching company statistics: ${e.message}');
      throw Exception('Failed to fetch company statistics: ${e.message}');
    } catch (e) {
      debugPrint('Unexpected error fetching company statistics: $e');
      throw Exception('Failed to fetch company statistics');
    }
  }
}
