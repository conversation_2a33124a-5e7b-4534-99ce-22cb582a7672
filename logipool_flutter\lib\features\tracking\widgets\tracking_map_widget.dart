import 'package:flutter/material.dart';
import '../../../shared/models/location_model.dart';

class TrackingMapWidget extends StatefulWidget {
  final List<LocationResponse> locations;
  final Function(LocationResponse)? onLocationTap;

  const TrackingMapWidget({
    super.key,
    required this.locations,
    this.onLocationTap,
  });

  @override
  State<TrackingMapWidget> createState() => _TrackingMapWidgetState();
}

class _TrackingMapWidgetState extends State<TrackingMapWidget> {
  @override
  Widget build(BuildContext context) {
    // This is a placeholder for the map implementation
    // In a real app, you would use Google Maps, OpenStreetMap, or another mapping solution
    return Container(
      color: Colors.grey[100],
      child: Stack(
        children: [
          // Map placeholder
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue[50]!,
                  Colors.green[50]!,
                ],
              ),
            ),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.map,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'Map View',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Interactive map with driver locations',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Location markers overlay (simulated)
          ...widget.locations.asMap().entries.map((entry) {
            final index = entry.key;
            final location = entry.value;
            
            // Simulate marker positions
            final left = 50.0 + (index * 80.0) % (MediaQuery.of(context).size.width - 100);
            final top = 100.0 + (index * 60.0) % (MediaQuery.of(context).size.height - 200);
            
            return Positioned(
              left: left,
              top: top,
              child: GestureDetector(
                onTap: () => widget.onLocationTap?.call(location),
                child: _buildLocationMarker(location),
              ),
            );
          }),
          
          // Map controls
          Positioned(
            top: 16,
            right: 16,
            child: Column(
              children: [
                FloatingActionButton.small(
                  heroTag: 'zoom_in',
                  onPressed: () {
                    // Zoom in functionality
                  },
                  child: const Icon(Icons.add),
                ),
                const SizedBox(height: 8),
                FloatingActionButton.small(
                  heroTag: 'zoom_out',
                  onPressed: () {
                    // Zoom out functionality
                  },
                  child: const Icon(Icons.remove),
                ),
                const SizedBox(height: 8),
                FloatingActionButton.small(
                  heroTag: 'my_location',
                  onPressed: () {
                    // Center on user location
                  },
                  child: const Icon(Icons.my_location),
                ),
              ],
            ),
          ),
          
          // Legend
          Positioned(
            bottom: 16,
            left: 16,
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Legend',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildLegendItem(Colors.green, 'On Duty'),
                    _buildLegendItem(Colors.grey, 'Off Duty'),
                    _buildLegendItem(Colors.orange, 'Estimated'),
                    _buildLegendItem(Colors.red, 'Offline'),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationMarker(LocationResponse location) {
    Color markerColor;
    IconData markerIcon;
    
    if (location.status == LocationStatus.offline) {
      markerColor = Colors.red;
      markerIcon = Icons.location_disabled;
    } else if (location.status == LocationStatus.estimated) {
      markerColor = Colors.orange;
      markerIcon = Icons.location_searching;
    } else if (location.isOnDuty) {
      markerColor = Colors.green;
      markerIcon = Icons.local_shipping;
    } else {
      markerColor = Colors.grey;
      markerIcon = Icons.person;
    }
    
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Stack(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: markerColor,
              shape: BoxShape.circle,
            ),
            child: Icon(
              markerIcon,
              color: Colors.white,
              size: 20,
            ),
          ),
          if (location.isRecent)
            Positioned(
              top: -2,
              right: -2,
              child: Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Colors.green,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(Color color, String label) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: const TextStyle(fontSize: 10),
          ),
        ],
      ),
    );
  }
}
