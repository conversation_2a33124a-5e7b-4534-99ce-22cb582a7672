package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.LoadMapper;
import zw.co.kanjan.logipool.repository.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class LoadLifecycleService {
    
    private final LoadRepository loadRepository;
    private final BidRepository bidRepository;
    private final PaymentRepository paymentRepository;
    private final InvoiceRepository invoiceRepository;
    private final DocumentRepository documentRepository;
    private final UserRepository userRepository;
    private final CompanyMemberRepository companyMemberRepository;
    private final LoadMapper loadMapper;
    private final NotificationService notificationService;
    private final InvoiceService invoiceService;
    private final PaymentService paymentService;
    
    @Value("${app.commission.rate:0.10}")
    private BigDecimal commissionRate;
    
    @Value("${app.payment.auto-release-days:7}")
    private int autoReleaseDays;
    
    /**
     * Complete the entire load lifecycle from assignment to final closure
     */
    public LoadResponse completeLoadLifecycle(Long loadId, String username) {
        log.info("Starting complete load lifecycle for load {} by user {}", loadId, username);
        
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate user has permission to complete lifecycle
        validateLifecyclePermission(load, user);
        
        // Execute lifecycle steps
        executeLifecycleSteps(load, user);
        
        Load updatedLoad = loadRepository.save(load);
        
        log.info("Load lifecycle completed successfully for load {}", loadId);
        return loadMapper.toResponse(updatedLoad);
    }
    
    /**
     * Mark load as delivered and trigger completion workflow
     */
    public LoadResponse markLoadAsDelivered(Long loadId, String deliveryNotes, String username) {
        log.info("Marking load {} as delivered by user {}", loadId, username);
        
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate user has permission to mark as delivered
        validateDeliveryPermission(load, user);
        
        // Validate load is in transit
        if (load.getStatus() != Load.LoadStatus.IN_TRANSIT) {
            throw new BusinessException("Load must be in transit to mark as delivered");
        }
        
        // Update load status
        load.setStatus(Load.LoadStatus.DELIVERED);
        load.setDeliveredAt(LocalDateTime.now());
        
        // Add delivery notes to special instructions
        if (deliveryNotes != null && !deliveryNotes.trim().isEmpty()) {
            String existingInstructions = load.getSpecialInstructions() != null ? load.getSpecialInstructions() : "";
            String timestamp = LocalDateTime.now().toString();
            String updatedInstructions = existingInstructions + "\n[DELIVERED - " + timestamp + " by " + username + "]: " + deliveryNotes;
            load.setSpecialInstructions(updatedInstructions);
        }
        
        Load savedLoad = loadRepository.save(load);
        
        // Trigger post-delivery workflow
        triggerPostDeliveryWorkflow(savedLoad, user);
        
        log.info("Load {} marked as delivered successfully", loadId);
        return loadMapper.toResponse(savedLoad);
    }
    
    /**
     * Verify all required documents are uploaded and verified
     */
    public boolean verifyLoadDocuments(Long loadId, String username) {
        log.info("Verifying documents for load {} by user {}", loadId, username);
        
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate user has permission to verify documents
        validateDocumentVerificationPermission(load, user);
        
        // Get all documents for the load
        List<Document> loadDocuments = documentRepository.findByLoad(load);
        
        // Check for required documents
        boolean hasProofOfDelivery = loadDocuments.stream()
                .anyMatch(doc -> doc.getType() == Document.DocumentType.PROOF_OF_DELIVERY && 
                               doc.getStatus() == Document.DocumentStatus.VERIFIED);
        
        boolean hasWaybill = loadDocuments.stream()
                .anyMatch(doc -> doc.getType() == Document.DocumentType.WAYBILL && 
                               doc.getStatus() == Document.DocumentStatus.VERIFIED);
        
        boolean allDocumentsVerified = loadDocuments.stream()
                .filter(doc -> doc.getIsRequired() != null && doc.getIsRequired())
                .allMatch(doc -> doc.getStatus() == Document.DocumentStatus.VERIFIED);
        
        boolean documentsComplete = hasProofOfDelivery && hasWaybill && allDocumentsVerified;
        
        if (documentsComplete) {
            log.info("All required documents verified for load {}", loadId);
            // Update load to indicate documents are complete
            load.setDocumentsVerified(true);
            loadRepository.save(load);
            
            // Trigger document completion workflow
            triggerDocumentCompletionWorkflow(load, user);
        } else {
            log.warn("Document verification incomplete for load {}: POD={}, Waybill={}, AllVerified={}", 
                    loadId, hasProofOfDelivery, hasWaybill, allDocumentsVerified);
        }
        
        return documentsComplete;
    }
    
    /**
     * Process payment for completed load
     */
    public Payment processLoadPayment(Long loadId, String username) {
        log.info("Processing payment for load {} by user {}", loadId, username);
        
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate load is ready for payment
        validatePaymentReadiness(load, user);
        
        // Get the accepted bid
        Bid acceptedBid = load.getBids().stream()
                .filter(bid -> bid.getStatus() == Bid.BidStatus.ACCEPTED)
                .findFirst()
                .orElseThrow(() -> new BusinessException("No accepted bid found for load"));
        
        // Check if payment already exists
        if (paymentRepository.existsByLoad(load)) {
            throw new BusinessException("Payment already exists for this load");
        }
        
        // Calculate payment amounts
        BigDecimal totalAmount = acceptedBid.getAmount();
        BigDecimal commissionAmount = totalAmount.multiply(commissionRate).setScale(2, RoundingMode.HALF_UP);
        BigDecimal netAmount = totalAmount.subtract(commissionAmount);
        
        // Create payment record
        Payment payment = Payment.builder()
                .amount(totalAmount)
                .commissionAmount(commissionAmount)
                .netAmount(netAmount)
                .commissionRate(commissionRate)
                .status(Payment.PaymentStatus.PENDING)
                .type(Payment.PaymentType.LOAD_PAYMENT)
                .description("Payment for load: " + load.getTitle())
                .load(load)
                .bid(acceptedBid)
                .payer(load.getClient())
                .payee(load.getAssignedCompany().getUser())
                .dueDate(LocalDateTime.now().plusDays(autoReleaseDays))
                .build();
        
        // Check if invoice exists, if not create one
        if (!invoiceRepository.existsByLoad(load)) {
            invoiceService.generateAutomaticInvoice(loadId, username);
        }
        
        Payment savedPayment = paymentRepository.save(payment);
        
        // Send payment notifications
        notificationService.sendPaymentCreatedNotification(savedPayment);
        
        log.info("Payment created successfully for load {}", loadId);
        return savedPayment;
    }
    
    /**
     * Finalize and close the load after all processes are complete
     */
    public LoadResponse finalizeLoad(Long loadId, String username) {
        log.info("Finalizing load {} by user {}", loadId, username);
        
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate load is ready for finalization
        validateFinalizationReadiness(load, user);
        
        // Mark load as completed
        load.setStatus(Load.LoadStatus.COMPLETED);
        load.setCompletedAt(LocalDateTime.now());
        
        Load savedLoad = loadRepository.save(load);
        
        // Send completion notifications
        notificationService.sendLoadCompletedNotification(savedLoad);
        
        // Trigger any post-completion workflows
        triggerPostCompletionWorkflow(savedLoad, user);
        
        log.info("Load {} finalized successfully", loadId);
        return loadMapper.toResponse(savedLoad);
    }
    
    // Private helper methods

    private void executeLifecycleSteps(Load load, User user) {
        log.info("Executing lifecycle steps for load {}", load.getId());

        // Step 1: Verify load is delivered
        if (load.getStatus() != Load.LoadStatus.DELIVERED) {
            throw new BusinessException("Load must be delivered before completing lifecycle");
        }

        // Step 2: Verify documents
        if (!verifyLoadDocuments(load.getId(), user.getUsername())) {
            throw new BusinessException("All required documents must be verified before completing lifecycle");
        }

        // Step 3: Generate invoice if not exists
        if (!invoiceRepository.existsByLoad(load)) {
            invoiceService.generateAutomaticInvoice(load.getId(), user.getUsername());
        }

        // Step 4: Process payment if not exists
        if (!paymentRepository.existsByLoad(load)) {
            processLoadPayment(load.getId(), user.getUsername());
        }

        // Step 5: Finalize load
        finalizeLoad(load.getId(), user.getUsername());
    }

    private void triggerPostDeliveryWorkflow(Load load, User user) {
        log.info("Triggering post-delivery workflow for load {}", load.getId());

        // Send delivery notifications
        notificationService.sendLoadDeliveredNotification(load);

        // Auto-generate proof of delivery if not exists
        List<Document> podDocuments = documentRepository.findByLoad(load).stream()
                .filter(doc -> doc.getType() == Document.DocumentType.PROOF_OF_DELIVERY)
                .toList();

        if (podDocuments.isEmpty()) {
            // Generate automatic proof of delivery
            try {
                // This would integrate with DocumentService to generate POD
                log.info("Auto-generating proof of delivery for load {}", load.getId());
            } catch (Exception e) {
                log.warn("Failed to auto-generate proof of delivery for load {}: {}", load.getId(), e.getMessage());
            }
        }

        // Schedule automatic invoice generation after 24 hours if not manually created
        scheduleAutomaticInvoiceGeneration(load);
    }

    private void triggerDocumentCompletionWorkflow(Load load, User user) {
        log.info("Triggering document completion workflow for load {}", load.getId());

        // Send document completion notifications
        notificationService.sendDocumentsVerifiedNotification(load);

        // Auto-process payment if all conditions are met
        if (load.getStatus() == Load.LoadStatus.DELIVERED &&
            !paymentRepository.existsByLoad(load)) {

            try {
                processLoadPayment(load.getId(), user.getUsername());
            } catch (Exception e) {
                log.warn("Failed to auto-process payment for load {}: {}", load.getId(), e.getMessage());
            }
        }
    }

    private void triggerPostCompletionWorkflow(Load load, User user) {
        log.info("Triggering post-completion workflow for load {}", load.getId());

        // Update company and driver statistics
        updateCompanyStatistics(load);
        updateDriverStatistics(load);

        // Archive old location data
        archiveLocationData(load);

        // Send completion surveys
        sendCompletionSurveys(load);
    }

    private void validateLifecyclePermission(Load load, User user) {
        // Admin can complete any lifecycle
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Load client can complete their own load lifecycle
        if (load.getClient().equals(user)) {
            return;
        }

        // Company members with appropriate permissions can complete lifecycle
        if (load.getAssignedCompany() != null) {
            CompanyMember member = companyMemberRepository.findByCompanyAndUser(load.getAssignedCompany(), user)
                    .orElse(null);

            if (member != null &&
                member.getStatus() == CompanyMember.MemberStatus.ACTIVE &&
                (member.getRole() == CompanyMember.CompanyRole.MANAGER ||
                 member.getRole() == CompanyMember.CompanyRole.OWNER)) {
                return;
            }
        }

        throw new BusinessException("You don't have permission to complete this load lifecycle");
    }

    private void validateDeliveryPermission(Load load, User user) {
        // Admin can mark any load as delivered
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Company members with delivery permissions can mark as delivered
        if (load.getAssignedCompany() != null) {
            CompanyMember member = companyMemberRepository.findByCompanyAndUser(load.getAssignedCompany(), user)
                    .orElse(null);

            if (member != null &&
                member.getStatus() == CompanyMember.MemberStatus.ACTIVE &&
                member.getCanUpdateLoadStatus()) {
                return;
            }
        }

        throw new BusinessException("You don't have permission to mark this load as delivered");
    }

    private void validateDocumentVerificationPermission(Load load, User user) {
        // Admin can verify any documents
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Load client can verify their own load documents
        if (load.getClient().equals(user)) {
            return;
        }

        // Company members with document permissions can verify
        if (load.getAssignedCompany() != null) {
            CompanyMember member = companyMemberRepository.findByCompanyAndUser(load.getAssignedCompany(), user)
                    .orElse(null);

            if (member != null &&
                member.getStatus() == CompanyMember.MemberStatus.ACTIVE &&
                (member.getCanUploadDocuments() ||
                 member.getRole() == CompanyMember.CompanyRole.MANAGER ||
                 member.getRole() == CompanyMember.CompanyRole.OWNER)) {
                return;
            }
        }

        throw new BusinessException("You don't have permission to verify documents for this load");
    }

    private void validatePaymentReadiness(Load load, User user) {
        // Validate load is delivered
        if (load.getStatus() != Load.LoadStatus.DELIVERED) {
            throw new BusinessException("Load must be delivered before processing payment");
        }

        // Validate documents are verified (if required)
        if (load.getRequiresDocumentVerification() && !load.getDocumentsVerified()) {
            throw new BusinessException("All required documents must be verified before processing payment");
        }

        // Validate user has permission
        validateLifecyclePermission(load, user);
    }

    private void validateFinalizationReadiness(Load load, User user) {
        // Validate load is delivered
        if (load.getStatus() != Load.LoadStatus.DELIVERED) {
            throw new BusinessException("Load must be delivered before finalization");
        }

        // Validate documents are verified
        if (!load.getDocumentsVerified()) {
            throw new BusinessException("All required documents must be verified before finalization");
        }

        // Validate payment exists
        if (!paymentRepository.existsByLoad(load)) {
            throw new BusinessException("Payment must be processed before finalization");
        }

        // Validate user has permission
        validateLifecyclePermission(load, user);
    }

    private void scheduleAutomaticInvoiceGeneration(Load load) {
        // This would integrate with a scheduling service
        log.info("Scheduling automatic invoice generation for load {}", load.getId());
    }

    private void updateCompanyStatistics(Load load) {
        // Update company performance statistics
        log.info("Updating company statistics for load completion: {}", load.getId());
    }

    private void updateDriverStatistics(Load load) {
        // Update driver performance statistics
        log.info("Updating driver statistics for load completion: {}", load.getId());
    }

    private void archiveLocationData(Load load) {
        // Archive location tracking data for the completed load
        log.info("Archiving location data for completed load: {}", load.getId());
    }

    private void sendCompletionSurveys(Load load) {
        // Send satisfaction surveys to client and transporter
        log.info("Sending completion surveys for load: {}", load.getId());
    }
}
