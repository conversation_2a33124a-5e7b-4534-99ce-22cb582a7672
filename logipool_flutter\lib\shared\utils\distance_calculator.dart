import 'dart:math';

class DistanceCalculator {
  /// Calculate the distance between two points using the Haversine formula
  /// Returns distance in kilometers
  static double calculateDistance({
    required double lat1,
    required double lon1,
    required double lat2,
    required double lon2,
  }) {
    const double earthRadius = 6371; // Earth's radius in kilometers

    // Convert degrees to radians
    double lat1Rad = _degreesToRadians(lat1);
    double lon1Rad = _degreesToRadians(lon1);
    double lat2Rad = _degreesToRadians(lat2);
    double lon2Rad = _degreesToRadians(lon2);

    // Calculate differences
    double deltaLat = lat2Rad - lat1Rad;
    double deltaLon = lon2Rad - lon1Rad;

    // Haversine formula
    double a = sin(deltaLat / 2) * sin(deltaLat / 2) +
        cos(lat1Rad) * cos(lat2Rad) * sin(deltaLon / 2) * sin(deltaLon / 2);
    double c = 2 * atan2(sqrt(a), sqrt(1 - a));

    return earthRadius * c;
  }

  /// Convert degrees to radians
  static double _degreesToRadians(double degrees) {
    return degrees * pi / 180;
  }

  /// Format distance for display
  static String formatDistance(double distanceKm) {
    if (distanceKm < 1) {
      return '${(distanceKm * 1000).round()} m';
    } else if (distanceKm < 10) {
      return '${distanceKm.toStringAsFixed(1)} km';
    } else {
      return '${distanceKm.round()} km';
    }
  }

  /// Calculate estimated travel time based on distance and average speed
  /// Returns time in hours
  static double calculateTravelTime({
    required double distanceKm,
    double averageSpeedKmh = 80, // Default highway speed
  }) {
    return distanceKm / averageSpeedKmh;
  }

  /// Format travel time for display
  static String formatTravelTime(double timeHours) {
    if (timeHours < 1) {
      return '${(timeHours * 60).round()} min';
    } else if (timeHours < 24) {
      int hours = timeHours.floor();
      int minutes = ((timeHours - hours) * 60).round();
      if (minutes == 0) {
        return '${hours}h';
      }
      return '${hours}h ${minutes}m';
    } else {
      int days = (timeHours / 24).floor();
      int remainingHours = (timeHours % 24).round();
      if (remainingHours == 0) {
        return '${days}d';
      }
      return '${days}d ${remainingHours}h';
    }
  }

  /// Calculate fuel cost estimate based on distance
  static double calculateFuelCost({
    required double distanceKm,
    double fuelConsumptionPer100Km = 12, // Liters per 100km for trucks
    double fuelPricePerLiter = 1.5, // USD per liter (adjust for local currency)
  }) {
    return (distanceKm / 100) * fuelConsumptionPer100Km * fuelPricePerLiter;
  }

  /// Format fuel cost for display
  static String formatFuelCost(double cost, {String currency = 'USD'}) {
    return '$currency ${cost.toStringAsFixed(2)}';
  }

  /// Calculate route efficiency score (0-100)
  /// Higher score means more efficient route
  static int calculateRouteEfficiency({
    required double actualDistanceKm,
    required double straightLineDistanceKm,
  }) {
    if (straightLineDistanceKm == 0) return 0;
    
    double efficiency = (straightLineDistanceKm / actualDistanceKm) * 100;
    return efficiency.clamp(0, 100).round();
  }

  /// Check if two locations are within a certain radius
  static bool isWithinRadius({
    required double lat1,
    required double lon1,
    required double lat2,
    required double lon2,
    required double radiusKm,
  }) {
    double distance = calculateDistance(
      lat1: lat1,
      lon1: lon1,
      lat2: lat2,
      lon2: lon2,
    );
    return distance <= radiusKm;
  }

  /// Calculate the midpoint between two coordinates
  static Map<String, double> calculateMidpoint({
    required double lat1,
    required double lon1,
    required double lat2,
    required double lon2,
  }) {
    double lat1Rad = _degreesToRadians(lat1);
    double lon1Rad = _degreesToRadians(lon1);
    double lat2Rad = _degreesToRadians(lat2);
    double lon2Rad = _degreesToRadians(lon2);

    double deltaLon = lon2Rad - lon1Rad;

    double bx = cos(lat2Rad) * cos(deltaLon);
    double by = cos(lat2Rad) * sin(deltaLon);

    double midLat = atan2(
      sin(lat1Rad) + sin(lat2Rad),
      sqrt((cos(lat1Rad) + bx) * (cos(lat1Rad) + bx) + by * by),
    );

    double midLon = lon1Rad + atan2(by, cos(lat1Rad) + bx);

    return {
      'latitude': _radiansToDegrees(midLat),
      'longitude': _radiansToDegrees(midLon),
    };
  }

  /// Convert radians to degrees
  static double _radiansToDegrees(double radians) {
    return radians * 180 / pi;
  }

  /// Calculate bearing from one point to another
  /// Returns bearing in degrees (0-360)
  static double calculateBearing({
    required double lat1,
    required double lon1,
    required double lat2,
    required double lon2,
  }) {
    double lat1Rad = _degreesToRadians(lat1);
    double lat2Rad = _degreesToRadians(lat2);
    double deltaLon = _degreesToRadians(lon2 - lon1);

    double y = sin(deltaLon) * cos(lat2Rad);
    double x = cos(lat1Rad) * sin(lat2Rad) - sin(lat1Rad) * cos(lat2Rad) * cos(deltaLon);

    double bearing = atan2(y, x);
    bearing = _radiansToDegrees(bearing);
    bearing = (bearing + 360) % 360; // Normalize to 0-360

    return bearing;
  }

  /// Convert bearing to compass direction
  static String bearingToCompass(double bearing) {
    const List<String> directions = [
      'N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE',
      'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'
    ];
    
    int index = ((bearing + 11.25) / 22.5).floor() % 16;
    return directions[index];
  }
}
