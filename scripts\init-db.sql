-- LogiPool Database Initialization Script
-- This script sets up the production database with necessary configurations

-- Create database if it doesn't exist (this is handled by Docker environment variables)
-- CREATE DATABASE logipool_prod;

-- Connect to the database
\c logipool_prod;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- <PERSON><PERSON> custom types
DO $$ BEGIN
    CREATE TYPE user_role AS ENUM ('ADMIN', 'CLIENT', 'TRANSPORTER');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE load_status AS ENUM ('POSTED', 'BIDDING', 'ASSIGNED', 'IN_TRANSIT', 'DELIVERED', 'CANCELLED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE bid_status AS ENUM ('PENDING', 'ACCEPTED', 'REJECTED', 'WITHDRAWN');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE document_status AS ENUM ('PENDING', 'VERIFIED', 'REJECTED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE payment_status AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'REFUNDED');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
    CREATE TYPE tracking_status AS ENUM ('PICKUP_SCHEDULED', 'PICKED_UP', 'IN_TRANSIT', 'OUT_FOR_DELIVERY', 'DELIVERED', 'EXCEPTION');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance
-- Note: These will be created by Hibernate, but we can add additional ones here

-- Indexes for spatial queries (if using PostGIS)
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_load_pickup_location ON loads USING GIST (pickup_location);
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_load_delivery_location ON loads USING GIST (delivery_location);

-- Indexes for text search
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_load_description_gin ON loads USING GIN (to_tsvector('english', description));
-- CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_company_name_gin ON companies USING GIN (to_tsvector('english', name));

-- Create application user for the application (if not using environment variables)
-- DO $$ BEGIN
--     IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'logipool_app') THEN
--         CREATE ROLE logipool_app WITH LOGIN PASSWORD 'secure_app_password';
--     END IF;
-- END $$;

-- Grant necessary permissions
-- GRANT CONNECT ON DATABASE logipool_prod TO logipool_app;
-- GRANT USAGE ON SCHEMA public TO logipool_app;
-- GRANT CREATE ON SCHEMA public TO logipool_app;

-- Create audit log table for tracking changes
CREATE TABLE IF NOT EXISTS audit_log (
    id BIGSERIAL PRIMARY KEY,
    table_name VARCHAR(255) NOT NULL,
    operation VARCHAR(10) NOT NULL,
    old_values JSONB,
    new_values JSONB,
    user_id BIGINT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT
);

-- Create index on audit log
CREATE INDEX IF NOT EXISTS idx_audit_log_table_name ON audit_log (table_name);
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit_log (timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_log_user_id ON audit_log (user_id);

-- Create function for audit logging
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (table_name, operation, old_values)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD));
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (table_name, operation, old_values, new_values)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD), row_to_json(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (table_name, operation, new_values)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(NEW));
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create configuration table for application settings
CREATE TABLE IF NOT EXISTS app_config (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value TEXT,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert default configuration values
INSERT INTO app_config (config_key, config_value, description) VALUES
    ('app.version', '1.0.0', 'Application version'),
    ('app.maintenance.enabled', 'false', 'Maintenance mode flag'),
    ('app.registration.enabled', 'true', 'User registration enabled flag'),
    ('app.commission.rate', '0.075', 'Default commission rate'),
    ('app.max.file.size', '52428800', 'Maximum file upload size in bytes'),
    ('app.session.timeout', '1800', 'Session timeout in seconds')
ON CONFLICT (config_key) DO NOTHING;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create performance monitoring view
CREATE OR REPLACE VIEW performance_stats AS
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation,
    most_common_vals,
    most_common_freqs
FROM pg_stats 
WHERE schemaname = 'public';

-- Create database maintenance procedures
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs(days_to_keep INTEGER DEFAULT 90)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM audit_log 
    WHERE timestamp < CURRENT_TIMESTAMP - INTERVAL '1 day' * days_to_keep;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create function to analyze table statistics
CREATE OR REPLACE FUNCTION update_table_statistics()
RETURNS VOID AS $$
BEGIN
    ANALYZE;
END;
$$ LANGUAGE plpgsql;

-- Set up connection limits and timeouts
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- Reload configuration
SELECT pg_reload_conf();

-- Create backup user (optional)
-- DO $$ BEGIN
--     IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'backup_user') THEN
--         CREATE ROLE backup_user WITH LOGIN PASSWORD 'backup_password';
--         GRANT CONNECT ON DATABASE logipool_prod TO backup_user;
--         GRANT USAGE ON SCHEMA public TO backup_user;
--         GRANT SELECT ON ALL TABLES IN SCHEMA public TO backup_user;
--         ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO backup_user;
--     END IF;
-- END $$;

-- Log initialization completion
INSERT INTO audit_log (table_name, operation, new_values) 
VALUES ('system', 'INIT', '{"message": "Database initialization completed", "timestamp": "' || CURRENT_TIMESTAMP || '"}');

-- Display initialization summary
SELECT 'Database initialization completed successfully' AS status;
