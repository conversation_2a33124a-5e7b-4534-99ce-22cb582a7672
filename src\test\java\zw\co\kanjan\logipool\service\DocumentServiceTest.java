package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.Resource;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Document;
import zw.co.kanjan.logipool.entity.Role;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.repository.DocumentRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentServiceTest {

    @Mock
    private DocumentRepository documentRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private FileStorageService fileStorageService;

    @Mock
    private Resource mockResource;

    @InjectMocks
    private DocumentService documentService;

    private Document testDocument;
    private User testUser;

    @BeforeEach
    void setUp() {
        // Create admin role
        Role adminRole = Role.builder()
                .id(1L)
                .name(Role.RoleName.ADMIN)
                .build();

        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .roles(Set.of(adminRole))
                .build();

        // Create a company for the document
        Company testCompany = Company.builder()
                .id(1L)
                .name("Test Company")
                .user(testUser)
                .build();

        testDocument = Document.builder()
                .id(1L)
                .name("Test Document")
                .fileName("test-document.pdf")
                .fileType("application/pdf")
                .filePath("documents/test-document.pdf")
                .fileSize(1024L)
                .company(testCompany)
                .build();
    }

    @Test
    void downloadDocument_ShouldReturnDocumentDownloadResponse_WhenDocumentExists() {
        // Given
        String username = "testuser";
        when(documentRepository.findById(1L)).thenReturn(Optional.of(testDocument));
        when(userRepository.findByUsername(username)).thenReturn(Optional.of(testUser));
        when(fileStorageService.loadFileAsResource(testDocument.getFilePath())).thenReturn(mockResource);

        // When
        DocumentService.DocumentDownloadResponse response = documentService.downloadDocument(1L, username);

        // Then
        assertNotNull(response);
        assertEquals(mockResource, response.getResource());
        assertEquals("test-document.pdf", response.getOriginalFileName());
        assertEquals("application/pdf", response.getContentType());

        verify(documentRepository).findById(1L);
        verify(fileStorageService).loadFileAsResource(testDocument.getFilePath());
    }

    @Test
    void downloadDocument_ShouldThrowResourceNotFoundException_WhenDocumentNotFound() {
        // Given
        String username = "testuser";
        when(documentRepository.findById(anyLong())).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> {
            documentService.downloadDocument(999L, username);
        });

        verify(documentRepository).findById(999L);
        verify(fileStorageService, never()).loadFileAsResource(any());
    }

    @Test
    void downloadDocument_ShouldPreserveOriginalFileName_WithExtension() {
        // Given
        String username = "testuser";
        Document documentWithExtension = Document.builder()
                .id(2L)
                .name("Invoice Document")
                .fileName("invoice-2024-001.xlsx")
                .fileType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
                .filePath("documents/invoice-2024-001.xlsx")
                .fileSize(2048L)
                .build();

        when(documentRepository.findById(2L)).thenReturn(Optional.of(documentWithExtension));
        when(userRepository.findByUsername(username)).thenReturn(Optional.of(testUser));
        when(fileStorageService.loadFileAsResource(documentWithExtension.getFilePath())).thenReturn(mockResource);

        // When
        DocumentService.DocumentDownloadResponse response = documentService.downloadDocument(2L, username);

        // Then
        assertNotNull(response);
        assertEquals("invoice-2024-001.xlsx", response.getOriginalFileName());
        assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", response.getContentType());
        assertTrue(response.getOriginalFileName().endsWith(".xlsx"));
    }

    @Test
    void downloadDocument_ShouldHandleNullFileType() {
        // Given
        String username = "testuser";
        Document documentWithNullType = Document.builder()
                .id(3L)
                .name("Document Without Type")
                .fileName("document.txt")
                .fileType(null)
                .filePath("documents/document.txt")
                .fileSize(512L)
                .build();

        when(documentRepository.findById(3L)).thenReturn(Optional.of(documentWithNullType));
        when(userRepository.findByUsername(username)).thenReturn(Optional.of(testUser));
        when(fileStorageService.loadFileAsResource(documentWithNullType.getFilePath())).thenReturn(mockResource);

        // When
        DocumentService.DocumentDownloadResponse response = documentService.downloadDocument(3L, username);

        // Then
        assertNotNull(response);
        assertEquals("document.txt", response.getOriginalFileName());
        assertNull(response.getContentType());
    }
}
