import 'package:json_annotation/json_annotation.dart';

part 'bid_model.g.dart';

@JsonSerializable()
class BidModel {
  final int? id;
  final double amount;
  final String? proposal;
  final DateTime estimatedPickupTime;
  final DateTime estimatedDeliveryTime;
  final BidStatus status;
  final String? notes;
  final int loadId;
  final String? loadTitle;
  final int companyId;
  final String? companyName;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? acceptedAt;
  final DateTime? rejectedAt;

  const BidModel({
    this.id,
    required this.amount,
    this.proposal,
    required this.estimatedPickupTime,
    required this.estimatedDeliveryTime,
    required this.status,
    this.notes,
    required this.loadId,
    this.loadTitle,
    required this.companyId,
    this.companyName,
    this.createdAt,
    this.updatedAt,
    this.acceptedAt,
    this.rejectedAt,
  });

  factory BidModel.fromJson(Map<String, dynamic> json) => _$BidModelFromJson(json);
  Map<String, dynamic> toJson() => _$BidModelToJson(this);

  BidModel copyWith({
    int? id,
    double? amount,
    String? proposal,
    DateTime? estimatedPickupTime,
    DateTime? estimatedDeliveryTime,
    BidStatus? status,
    String? notes,
    int? loadId,
    String? loadTitle,
    int? companyId,
    String? companyName,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? acceptedAt,
    DateTime? rejectedAt,
  }) {
    return BidModel(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      proposal: proposal ?? this.proposal,
      estimatedPickupTime: estimatedPickupTime ?? this.estimatedPickupTime,
      estimatedDeliveryTime: estimatedDeliveryTime ?? this.estimatedDeliveryTime,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      loadId: loadId ?? this.loadId,
      loadTitle: loadTitle ?? this.loadTitle,
      companyId: companyId ?? this.companyId,
      companyName: companyName ?? this.companyName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      acceptedAt: acceptedAt ?? this.acceptedAt,
      rejectedAt: rejectedAt ?? this.rejectedAt,
    );
  }
}

enum BidStatus {
  @JsonValue('PENDING')
  pending,
  @JsonValue('ACCEPTED')
  accepted,
  @JsonValue('REJECTED')
  rejected,
  @JsonValue('WITHDRAWN')
  withdrawn;

  String get displayName {
    switch (this) {
      case BidStatus.pending:
        return 'Pending';
      case BidStatus.accepted:
        return 'Accepted';
      case BidStatus.rejected:
        return 'Rejected';
      case BidStatus.withdrawn:
        return 'Withdrawn';
    }
  }

  bool get isPending => this == BidStatus.pending;
  bool get isAccepted => this == BidStatus.accepted;
  bool get isRejected => this == BidStatus.rejected;
  bool get isWithdrawn => this == BidStatus.withdrawn;
  bool get isActive => isPending;
  bool get isCompleted => isAccepted || isRejected || isWithdrawn;
}

@JsonSerializable()
class BidCreateRequest {
  final int loadId;
  final double amount;
  final String? proposal;
  final DateTime estimatedPickupTime;
  final DateTime estimatedDeliveryTime;
  final String? notes;

  const BidCreateRequest({
    required this.loadId,
    required this.amount,
    this.proposal,
    required this.estimatedPickupTime,
    required this.estimatedDeliveryTime,
    this.notes,
  });

  factory BidCreateRequest.fromJson(Map<String, dynamic> json) => _$BidCreateRequestFromJson(json);
  Map<String, dynamic> toJson() => _$BidCreateRequestToJson(this);
}

@JsonSerializable()
class BidUpdateRequest {
  final double? amount;
  final String? proposal;
  final DateTime? estimatedPickupTime;
  final DateTime? estimatedDeliveryTime;
  final String? notes;

  const BidUpdateRequest({
    this.amount,
    this.proposal,
    this.estimatedPickupTime,
    this.estimatedDeliveryTime,
    this.notes,
  });

  factory BidUpdateRequest.fromJson(Map<String, dynamic> json) => _$BidUpdateRequestFromJson(json);
  Map<String, dynamic> toJson() => _$BidUpdateRequestToJson(this);
}
