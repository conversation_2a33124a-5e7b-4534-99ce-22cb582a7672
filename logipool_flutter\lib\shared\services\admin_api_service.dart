import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/admin_model.dart';
import '../models/paginated_response.dart';
import '../../core/network/api_client.dart';

class AdminApiService {
  final ApiClient _apiClient;

  AdminApiService(this._apiClient);

  // Dashboard APIs
  Future<DashboardOverview> getDashboardOverview() async {
    try {
      final response = await _apiClient.get('/admin/dashboard');
      return DashboardOverview.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load dashboard overview: $e');
    }
  }

  // User Management APIs
  Future<PaginatedResponse<UserManagementResponse>> getAllUsers({
    int page = 0,
    int size = 20,
    String? status,
    String? role,
    String? search,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'size': size.toString(),
      };
      
      if (status != null) queryParams['status'] = status;
      if (role != null) queryParams['role'] = role;
      if (search != null) queryParams['search'] = search;

      final response = await _apiClient.get('/admin/users', queryParameters: queryParams);

      return PaginatedResponse<UserManagementResponse>.fromJson(
        response.data as Map<String, dynamic>,
        (json) => UserManagementResponse.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw Exception('Failed to load users: $e');
    }
  }

  Future<UserManagementResponse> getUserById(int userId) async {
    try {
      final response = await _apiClient.get('/admin/users/$userId');
      return UserManagementResponse.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load user: $e');
    }
  }

  Future<void> updateUserStatus(int userId, String status) async {
    try {
      final request = UserStatusUpdateRequest(status: status);
      await _apiClient.put('/admin/users/$userId/status', data: request.toJson());
    } catch (e) {
      throw Exception('Failed to update user status: $e');
    }
  }

  Future<void> updateUserRoles(int userId, List<String> roles) async {
    try {
      final request = UserRoleUpdateRequest(roles: roles);
      await _apiClient.put('/admin/users/$userId/roles', data: request.toJson());
    } catch (e) {
      throw Exception('Failed to update user roles: $e');
    }
  }

  Future<void> deleteUser(int userId) async {
    try {
      await _apiClient.delete('/admin/users/$userId');
    } catch (e) {
      throw Exception('Failed to delete user: $e');
    }
  }

  Future<void> bulkUpdateUserStatus(List<int> userIds, String status) async {
    try {
      final body = {
        'userIds': userIds,
        'status': status,
      };
      await _apiClient.post('/admin/users/bulk/status', data: body);
    } catch (e) {
      throw Exception('Failed to bulk update user status: $e');
    }
  }

  // Company Management APIs
  Future<PaginatedResponse<CompanyManagementResponse>> getAllCompanies({
    int page = 0,
    int size = 20,
    String? verificationStatus,
    String? search,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'size': size.toString(),
      };
      
      if (verificationStatus != null) queryParams['verificationStatus'] = verificationStatus;
      if (search != null) queryParams['search'] = search;

      final response = await _apiClient.get('/admin/companies', queryParameters: queryParams);

      return PaginatedResponse<CompanyManagementResponse>.fromJson(
        response.data as Map<String, dynamic>,
        (json) => CompanyManagementResponse.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw Exception('Failed to load companies: $e');
    }
  }

  Future<CompanyManagementResponse> getCompanyById(int companyId) async {
    try {
      final response = await _apiClient.get('/admin/companies/$companyId');
      return CompanyManagementResponse.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load company: $e');
    }
  }

  Future<void> verifyCompany(int companyId, String status, {String? notes}) async {
    try {
      final request = CompanyVerificationRequest(status: status, notes: notes);
      await _apiClient.post('/admin/companies/$companyId/verify', data: request.toJson());
    } catch (e) {
      throw Exception('Failed to verify company: $e');
    }
  }

  Future<void> deleteCompany(int companyId) async {
    try {
      await _apiClient.delete('/admin/companies/$companyId');
    } catch (e) {
      throw Exception('Failed to delete company: $e');
    }
  }

  // Load Management APIs
  Future<PaginatedResponse<LoadManagementResponse>> getAllLoads({
    int page = 0,
    int size = 20,
    String? status,
    String? search,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'size': size.toString(),
      };
      
      if (status != null) queryParams['status'] = status;
      if (search != null) queryParams['search'] = search;

      final response = await _apiClient.get('/admin/loads', queryParameters: queryParams);

      return PaginatedResponse<LoadManagementResponse>.fromJson(
        response.data as Map<String, dynamic>,
        (json) => LoadManagementResponse.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw Exception('Failed to load loads: $e');
    }
  }

  Future<LoadManagementResponse> getLoadById(int loadId) async {
    try {
      final response = await _apiClient.get('/admin/loads/$loadId');
      return LoadManagementResponse.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load load details: $e');
    }
  }

  Future<void> assignLoadToCompany(int loadId, int companyId) async {
    try {
      final request = LoadAssignmentRequest(companyId: companyId);
      await _apiClient.post('/admin/loads/$loadId/assign', data: request.toJson());
    } catch (e) {
      throw Exception('Failed to assign load: $e');
    }
  }

  Future<void> deleteLoad(int loadId) async {
    try {
      await _apiClient.delete('/admin/loads/$loadId');
    } catch (e) {
      throw Exception('Failed to delete load: $e');
    }
  }

  // Analytics APIs
  Future<SystemAnalytics> getSystemAnalytics() async {
    try {
      final response = await _apiClient.get('/admin/analytics');
      return SystemAnalytics.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load system analytics: $e');
    }
  }

  Future<List<RecentActivity>> getRecentActivities() async {
    try {
      final response = await _apiClient.get('/admin/system/activities');
      return (response.data as List)
          .map((json) => RecentActivity.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to load recent activities: $e');
    }
  }

  // Configuration APIs
  Future<SystemConfigurationResponse> getSystemConfiguration() async {
    try {
      final response = await _apiClient.get('/admin/system/configuration');
      return SystemConfigurationResponse.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load system configuration: $e');
    }
  }

  Future<void> updateSystemConfiguration(Map<String, dynamic> settings) async {
    try {
      final request = SystemConfigurationUpdateRequest(settings: settings);
      await _apiClient.put('/admin/system/configuration', data: request.toJson());
    } catch (e) {
      throw Exception('Failed to update system configuration: $e');
    }
  }

  // Audit Log APIs
  Future<PaginatedResponse<AuditLogResponse>> getAuditLogs({
    int page = 0,
    int size = 20,
    String? action,
    String? entityType,
    String? performedBy,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'size': size.toString(),
      };
      
      if (action != null) queryParams['action'] = action;
      if (entityType != null) queryParams['entityType'] = entityType;
      if (performedBy != null) queryParams['performedBy'] = performedBy;
      if (startDate != null) queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();

      final response = await _apiClient.get('/admin/audit-logs', queryParameters: queryParams);
      
      return PaginatedResponse<AuditLogResponse>.fromJson(
        response.data as Map<String, dynamic>,
        (json) => AuditLogResponse.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw Exception('Failed to load audit logs: $e');
    }
  }

  // Alert APIs
  Future<PaginatedResponse<SystemAlertResponse>> getSystemAlerts({
    int page = 0,
    int size = 20,
    String? severity,
    bool? acknowledged,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'size': size.toString(),
      };
      
      if (severity != null) queryParams['severity'] = severity;
      if (acknowledged != null) queryParams['acknowledged'] = acknowledged.toString();

      final response = await _apiClient.get('/admin/alerts', queryParameters: queryParams);

      return PaginatedResponse<SystemAlertResponse>.fromJson(
        response.data as Map<String, dynamic>,
        (json) => SystemAlertResponse.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw Exception('Failed to load system alerts: $e');
    }
  }

  Future<void> acknowledgeAlert(int alertId) async {
    try {
      await _apiClient.post('/admin/alerts/$alertId/acknowledge', data: {});
    } catch (e) {
      throw Exception('Failed to acknowledge alert: $e');
    }
  }

  // Backup APIs
  Future<SystemBackupResponse> initiateSystemBackup(String backupType) async {
    try {
      final response = await _apiClient.post('/admin/system/backup', data: {
        'backupType': backupType,
      });
      return SystemBackupResponse.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to initiate system backup: $e');
    }
  }

  Future<List<SystemBackupResponse>> getSystemBackups() async {
    try {
      final response = await _apiClient.get('/admin/system/backups');
      return (response.data as List)
          .map((json) => SystemBackupResponse.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to load system backups: $e');
    }
  }

  // Export APIs
  Future<String> exportUsers({String? status}) async {
    try {
      final queryParams = <String, String>{};
      if (status != null) queryParams['status'] = status;
      
      final response = await _apiClient.get('/admin/export/users', queryParameters: queryParams);
      return response.data as String;
    } catch (e) {
      throw Exception('Failed to export users: $e');
    }
  }

  Future<String> exportCompanies({String? verificationStatus}) async {
    try {
      final queryParams = <String, String>{};
      if (verificationStatus != null) queryParams['status'] = verificationStatus;
      
      final response = await _apiClient.get('/admin/export/companies', queryParameters: queryParams);
      return response.data as String;
    } catch (e) {
      throw Exception('Failed to export companies: $e');
    }
  }

  Future<String> exportLoads({String? status}) async {
    try {
      final queryParams = <String, String>{};
      if (status != null) queryParams['status'] = status;
      
      final response = await _apiClient.get('/admin/export/loads', queryParameters: queryParams);
      return response.data as String;
    } catch (e) {
      throw Exception('Failed to export loads: $e');
    }
  }

  // Reports APIs
  Future<DashboardOverview> getDailyReport() async {
    try {
      final response = await _apiClient.get('/admin/reports/daily');
      return DashboardOverview.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load daily report: $e');
    }
  }

  Future<SystemAnalytics> getWeeklyReport() async {
    try {
      final response = await _apiClient.get('/admin/reports/weekly');
      return SystemAnalytics.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load weekly report: $e');
    }
  }

  // Maintenance APIs
  Future<void> enableMaintenanceMode() async {
    try {
      await _apiClient.post('/admin/system/maintenance/enable', data: {});
    } catch (e) {
      throw Exception('Failed to enable maintenance mode: $e');
    }
  }

  Future<void> disableMaintenanceMode() async {
    try {
      await _apiClient.post('/admin/system/maintenance/disable', data: {});
    } catch (e) {
      throw Exception('Failed to disable maintenance mode: $e');
    }
  }
}
