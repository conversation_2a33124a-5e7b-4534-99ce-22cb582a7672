import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../models/load_model.dart';
import '../models/paginated_response.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/api_exception.dart';
import '../../core/network/api_client.dart';

class LoadService extends ChangeNotifier {
  final ApiClient _apiClient;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  LoadService(this._apiClient);

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  // Create a new load
  Future<LoadModel> createLoad(LoadModel load) async {
    _setLoading(true);

    try {
      final response = await _apiClient.post(
        AppConstants.loadsEndpoint,
        data: load.toJson(),
      );

      if (response.statusCode == 200) {
        return LoadModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw ApiException(
          message: 'Failed to create load',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to create load',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Get all loads with pagination
  Future<PaginatedResponse<LoadModel>> getAllLoads({
    int page = 0,
    int size = 10,
    String sortBy = 'createdAt',
    String sortDir = 'desc',
  }) async {
    _setLoading(true);

    try {
      final response = await _apiClient.get(
        AppConstants.loadsEndpoint,
        queryParameters: {
          'page': page,
          'size': size,
          'sortBy': sortBy,
          'sortDir': sortDir,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        return PaginatedResponse<LoadModel>.fromJson(
          data,
          (json) => LoadModel.fromJson(json as Map<String, dynamic>),
        );
      } else {
        throw ApiException(
          message: 'Failed to fetch loads',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch loads',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Get loads by status
  Future<PaginatedResponse<LoadModel>> getLoadsByStatus(
    LoadStatus status, {
    int page = 0,
    int size = 10,
    String sortBy = 'createdAt',
    String sortDir = 'desc',
  }) async {
    _setLoading(true);

    try {
      final response = await _apiClient.get(
        '${AppConstants.loadsEndpoint}/status/${status.name.toUpperCase()}',
        queryParameters: {
          'page': page,
          'size': size,
          'sortBy': sortBy,
          'sortDir': sortDir,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        return PaginatedResponse<LoadModel>.fromJson(
          data,
          (json) => LoadModel.fromJson(json as Map<String, dynamic>),
        );
      } else {
        throw ApiException(
          message: 'Failed to fetch loads by status',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch loads by status',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Get verified loads
  Future<PaginatedResponse<LoadModel>> getVerifiedLoads({
    int page = 0,
    int size = 10,
    String sortBy = 'createdAt',
    String sortDir = 'desc',
  }) async {
    _setLoading(true);

    try {
      final response = await _apiClient.get(
        '${AppConstants.loadsEndpoint}/verified',
        queryParameters: {
          'page': page,
          'size': size,
          'sortBy': sortBy,
          'sortDir': sortDir,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        return PaginatedResponse<LoadModel>.fromJson(
          data,
          (json) => LoadModel.fromJson(json as Map<String, dynamic>),
        );
      } else {
        throw ApiException(
          message: 'Failed to fetch verified loads',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch verified loads',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Get current user's loads
  Future<PaginatedResponse<LoadModel>> getMyLoads({
    int page = 0,
    int size = 10,
    String sortBy = 'createdAt',
    String sortDir = 'desc',
  }) async {
    _setLoading(true);

    try {
      final response = await _apiClient.get(
        '${AppConstants.loadsEndpoint}/my-loads',
        queryParameters: {
          'page': page,
          'size': size,
          'sortBy': sortBy,
          'sortDir': sortDir,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        return PaginatedResponse<LoadModel>.fromJson(
          data,
          (json) => LoadModel.fromJson(json as Map<String, dynamic>),
        );
      } else {
        throw ApiException(
          message: 'Failed to fetch my loads',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch my loads',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Get load by ID
  Future<LoadModel> getLoadById(int id) async {
    _setLoading(true);

    try {
      final response = await _apiClient.get(
        '${AppConstants.loadsEndpoint}/$id',
      );

      if (response.statusCode == 200) {
        return LoadModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw ApiException(
          message: 'Failed to fetch load details',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch load details',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Update load
  Future<LoadModel> updateLoad(int id, LoadModel load) async {
    _setLoading(true);

    try {
      final response = await _apiClient.put(
        '${AppConstants.loadsEndpoint}/$id',
        data: load.toJson(),
      );

      if (response.statusCode == 200) {
        return LoadModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw ApiException(
          message: 'Failed to update load',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to update load',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Update load status
  Future<LoadModel> updateLoadStatus(int id, LoadStatus status) async {
    _setLoading(true);

    try {
      final response = await _apiClient.put<Map<String, dynamic>>(
        '${AppConstants.loadsEndpoint}/$id/status/update',
        queryParameters: {'status': status.backendValue},
      );

      if (response.statusCode == 200) {
        return LoadModel.fromJson(response.data!);
      } else {
        throw ApiException(
          message: 'Failed to update load status',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to update load status',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Update load status with notes
  Future<LoadModel> updateLoadStatusWithNotes(
      int id, LoadStatus status, String? notes) async {
    _setLoading(true);

    try {
      final data = {
        'loadId': id,
        'status': status.backendValue,
      };
      if (notes != null && notes.isNotEmpty) {
        data['notes'] = notes;
      }

      final response = await _apiClient.put<Map<String, dynamic>>(
        '${AppConstants.loadsEndpoint}/status/update',
        data: data,
      );

      if (response.statusCode == 200) {
        return LoadModel.fromJson(response.data!);
      } else {
        throw ApiException(
          message: 'Failed to update load status',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to update load status',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Assign load to company
  Future<LoadModel> assignLoad(int loadId, int companyId) async {
    _setLoading(true);

    try {
      final response = await _apiClient.put(
        '${AppConstants.loadsEndpoint}/$loadId/assign/$companyId',
      );

      if (response.statusCode == 200) {
        return LoadModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw ApiException(
          message: 'Failed to assign load',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to assign load',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Delete load
  Future<void> deleteLoad(int id) async {
    _setLoading(true);

    try {
      final response = await _apiClient.delete(
        '${AppConstants.loadsEndpoint}/$id',
      );

      if (response.statusCode != 200 && response.statusCode != 204) {
        throw ApiException(
          message: 'Failed to delete load',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to delete load',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }

  // Search loads
  Future<PaginatedResponse<LoadModel>> searchLoads({
    String? query,
    LoadType? loadType,
    LoadStatus? status,
    Priority? priority,
    String? pickupLocation,
    String? deliveryLocation,
    DateTime? pickupDateFrom,
    DateTime? pickupDateTo,
    DateTime? deliveryDateFrom,
    DateTime? deliveryDateTo,
    double? minWeight,
    double? maxWeight,
    bool? requiresInsurance,
    bool? requiresSpecialHandling,
    bool? verifiedOnly,
    int page = 0,
    int size = 10,
    String sortBy = 'createdAt',
    String sortDir = 'desc',
  }) async {
    _setLoading(true);

    try {
      final queryParameters = <String, dynamic>{
        'page': page,
        'size': size,
        'sortBy': sortBy,
        'sortDir': sortDir,
      };

      if (query != null && query.isNotEmpty) queryParameters['query'] = query;
      if (loadType != null)
        queryParameters['loadType'] = loadType.name.toUpperCase();
      if (status != null) queryParameters['status'] = status.name.toUpperCase();
      if (priority != null)
        queryParameters['priority'] = priority.name.toUpperCase();
      if (pickupLocation != null && pickupLocation.isNotEmpty) {
        queryParameters['pickupLocation'] = pickupLocation;
      }
      if (deliveryLocation != null && deliveryLocation.isNotEmpty) {
        queryParameters['deliveryLocation'] = deliveryLocation;
      }
      if (pickupDateFrom != null) {
        queryParameters['pickupDateFrom'] = pickupDateFrom.toIso8601String();
      }
      if (pickupDateTo != null) {
        queryParameters['pickupDateTo'] = pickupDateTo.toIso8601String();
      }
      if (deliveryDateFrom != null) {
        queryParameters['deliveryDateFrom'] =
            deliveryDateFrom.toIso8601String();
      }
      if (deliveryDateTo != null) {
        queryParameters['deliveryDateTo'] = deliveryDateTo.toIso8601String();
      }
      if (minWeight != null) queryParameters['minWeight'] = minWeight;
      if (maxWeight != null) queryParameters['maxWeight'] = maxWeight;
      if (requiresInsurance != null)
        queryParameters['requiresInsurance'] = requiresInsurance;
      if (requiresSpecialHandling != null) {
        queryParameters['requiresSpecialHandling'] = requiresSpecialHandling;
      }
      if (verifiedOnly != null) queryParameters['verifiedOnly'] = verifiedOnly;

      final response = await _apiClient.get(
        '${AppConstants.loadsEndpoint}/search',
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final data = response.data as Map<String, dynamic>;
        return PaginatedResponse<LoadModel>.fromJson(
          data,
          (json) => LoadModel.fromJson(json as Map<String, dynamic>),
        );
      } else {
        throw ApiException(
          message: 'Failed to search loads',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to search loads',
        statusCode: e.response?.statusCode ?? 500,
      );
    } finally {
      _setLoading(false);
    }
  }
}
