// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'notification_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NotificationModel _$NotificationModelFromJson(Map<String, dynamic> json) =>
    NotificationModel(
      id: (json['id'] as num?)?.toInt(),
      title: json['title'] as String,
      message: json['message'] as String,
      type: $enumDecode(_$NotificationTypeEnumMap, json['type']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      data: json['data'] as Map<String, dynamic>?,
      read: json['read'] as bool? ?? false,
      priority: $enumDecodeNullable(
              _$NotificationPriorityEnumMap, json['priority']) ??
          NotificationPriority.medium,
      referenceId: (json['referenceId'] as num?)?.toInt(),
      referenceType: json['referenceType'] as String?,
      readAt: json['readAt'] == null
          ? null
          : DateTime.parse(json['readAt'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
    );

Map<String, dynamic> _$NotificationModelToJson(NotificationModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'message': instance.message,
      'type': _$NotificationTypeEnumMap[instance.type]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'data': instance.data,
      'read': instance.read,
      'priority': _$NotificationPriorityEnumMap[instance.priority]!,
      'referenceId': instance.referenceId,
      'referenceType': instance.referenceType,
      'readAt': instance.readAt?.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
    };

const _$NotificationTypeEnumMap = {
  NotificationType.loadPosted: 'LOAD_POSTED',
  NotificationType.bidReceived: 'BID_RECEIVED',
  NotificationType.bidAccepted: 'BID_ACCEPTED',
  NotificationType.bidRejected: 'BID_REJECTED',
  NotificationType.loadStatusUpdate: 'LOAD_STATUS_UPDATE',
  NotificationType.paymentReceived: 'PAYMENT_RECEIVED',
  NotificationType.documentRequired: 'DOCUMENT_REQUIRED',
  NotificationType.systemAnnouncement: 'SYSTEM_ANNOUNCEMENT',
  NotificationType.accountVerification: 'ACCOUNT_VERIFICATION',
  NotificationType.securityAlert: 'SECURITY_ALERT',
  NotificationType.trackingUpdate: 'TRACKING_UPDATE',
};

const _$NotificationPriorityEnumMap = {
  NotificationPriority.low: 'LOW',
  NotificationPriority.medium: 'MEDIUM',
  NotificationPriority.high: 'HIGH',
  NotificationPriority.urgent: 'URGENT',
};

NotificationMarkReadRequest _$NotificationMarkReadRequestFromJson(
        Map<String, dynamic> json) =>
    NotificationMarkReadRequest(
      notificationIds: (json['notificationIds'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$NotificationMarkReadRequestToJson(
        NotificationMarkReadRequest instance) =>
    <String, dynamic>{
      'notificationIds': instance.notificationIds,
    };

NotificationDeleteRequest _$NotificationDeleteRequestFromJson(
        Map<String, dynamic> json) =>
    NotificationDeleteRequest(
      notificationIds: (json['notificationIds'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
    );

Map<String, dynamic> _$NotificationDeleteRequestToJson(
        NotificationDeleteRequest instance) =>
    <String, dynamic>{
      'notificationIds': instance.notificationIds,
    };

NotificationSummary _$NotificationSummaryFromJson(Map<String, dynamic> json) =>
    NotificationSummary(
      totalCount: (json['totalCount'] as num).toInt(),
      unreadCount: (json['unreadCount'] as num).toInt(),
      todayCount: (json['todayCount'] as num).toInt(),
      weekCount: (json['weekCount'] as num).toInt(),
      typeBreakdown: Map<String, int>.from(json['typeBreakdown'] as Map),
      priorityBreakdown:
          Map<String, int>.from(json['priorityBreakdown'] as Map),
    );

Map<String, dynamic> _$NotificationSummaryToJson(
        NotificationSummary instance) =>
    <String, dynamic>{
      'totalCount': instance.totalCount,
      'unreadCount': instance.unreadCount,
      'todayCount': instance.todayCount,
      'weekCount': instance.weekCount,
      'typeBreakdown': instance.typeBreakdown,
      'priorityBreakdown': instance.priorityBreakdown,
    };

NotificationSettings _$NotificationSettingsFromJson(
        Map<String, dynamic> json) =>
    NotificationSettings(
      pushNotifications: json['pushNotifications'] as bool? ?? true,
      emailNotifications: json['emailNotifications'] as bool? ?? true,
      smsNotifications: json['smsNotifications'] as bool? ?? false,
      typeSettings: (json['typeSettings'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as bool),
          ) ??
          const {},
      quietHours: (json['quietHours'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      doNotDisturb: json['doNotDisturb'] as bool? ?? false,
    );

Map<String, dynamic> _$NotificationSettingsToJson(
        NotificationSettings instance) =>
    <String, dynamic>{
      'pushNotifications': instance.pushNotifications,
      'emailNotifications': instance.emailNotifications,
      'smsNotifications': instance.smsNotifications,
      'typeSettings': instance.typeSettings,
      'quietHours': instance.quietHours,
      'doNotDisturb': instance.doNotDisturb,
    };
