import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/payment_model.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../bloc/payment_bloc.dart';
import '../widgets/payment_card.dart';
import '../widgets/payment_filter_sheet.dart';

class PaymentListScreen extends StatefulWidget {
  const PaymentListScreen({super.key});

  @override
  State<PaymentListScreen> createState() => _PaymentListScreenState();
}

class _PaymentListScreenState extends State<PaymentListScreen> {
  final ScrollController _scrollController = ScrollController();
  PaymentStatus? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    context.read<PaymentBloc>().add(const LoadPayments());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<PaymentBloc>().add(const LoadMorePayments());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => PaymentFilterSheet(
        selectedStatus: _selectedStatus,
        startDate: _startDate,
        endDate: _endDate,
        onApplyFilter: (status, startDate, endDate) {
          setState(() {
            _selectedStatus = status;
            _startDate = startDate;
            _endDate = endDate;
          });
          context.read<PaymentBloc>().add(LoadPayments(
            refresh: true,
            status: status,
            startDate: startDate,
            endDate: endDate,
          ));
        },
        onClearFilter: () {
          setState(() {
            _selectedStatus = null;
            _startDate = null;
            _endDate = null;
          });
          context.read<PaymentBloc>().add(const LoadPayments(refresh: true));
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Payments'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterSheet,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              context.go('/payments/create');
            },
          ),
        ],
      ),
      body: BlocConsumer<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is PaymentOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
            // Refresh the list after successful operation
            context.read<PaymentBloc>().add(LoadPayments(
              refresh: true,
              status: _selectedStatus,
              startDate: _startDate,
              endDate: _endDate,
            ));
          }
        },
        builder: (context, state) {
          if (state is PaymentLoading && state is! PaymentLoaded) {
            return const LoadingWidget();
          }

          if (state is PaymentLoaded) {
            if (state.payments.isEmpty) {
              return EmptyStateWidget(
                icon: Icon(
                  Icons.payment,
                  size: 64,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
                title: 'No Payments Found',
                message: _hasActiveFilters
                    ? 'No payments match your current filters'
                    : 'You haven\'t made any payments yet',
                buttonText: 'Create Payment',
                showActionButton: true,
                onAction: () {
                  context.go('/payments/create');
                },
              );
            }

            return RefreshIndicator(
              onRefresh: () async {
                context.read<PaymentBloc>().add(LoadPayments(
                  refresh: true,
                  status: _selectedStatus,
                  startDate: _startDate,
                  endDate: _endDate,
                ));
              },
              child: Column(
                children: [
                  if (_hasActiveFilters) _buildFilterChips(),
                  Expanded(
                    child: ListView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      itemCount: state.hasReachedMax
                          ? state.payments.length
                          : state.payments.length + 1,
                      itemBuilder: (context, index) {
                        if (index >= state.payments.length) {
                          return const Center(
                            child: Padding(
                              padding: EdgeInsets.all(16),
                              child: CircularProgressIndicator(),
                            ),
                          );
                        }

                        final payment = state.payments[index];
                        return PaymentCard(
                          payment: payment,
                          onTap: () {
                            context.go('/payments/${payment.id}');
                          },
                          onProcess: payment.status.isPending
                              ? () {
                                  context.go('/payments/${payment.id}/process');
                                }
                              : null,
                          onCancel: payment.status.isActive
                              ? () {
                                  _showCancelDialog(payment);
                                }
                              : null,
                        );
                      },
                    ),
                  ),
                ],
              ),
            );
          }

          if (state is PaymentError) {
            return AppErrorWidget(
              message: state.message,
              onRetry: () {
                context.read<PaymentBloc>().add(LoadPayments(
                  refresh: true,
                  status: _selectedStatus,
                  startDate: _startDate,
                  endDate: _endDate,
                ));
              },
            );
          }

          return const LoadingWidget();
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.go('/payments/create');
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  bool get _hasActiveFilters {
    return _selectedStatus != null || _startDate != null || _endDate != null;
  }

  Widget _buildFilterChips() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Wrap(
        spacing: 8,
        children: [
          if (_selectedStatus != null)
            Chip(
              label: Text(_selectedStatus!.displayName),
              onDeleted: () {
                setState(() {
                  _selectedStatus = null;
                });
                context.read<PaymentBloc>().add(LoadPayments(
                  refresh: true,
                  startDate: _startDate,
                  endDate: _endDate,
                ));
              },
            ),
          if (_startDate != null && _endDate != null)
            Chip(
              label: Text(
                '${_startDate!.day}/${_startDate!.month} - ${_endDate!.day}/${_endDate!.month}',
              ),
              onDeleted: () {
                setState(() {
                  _startDate = null;
                  _endDate = null;
                });
                context.read<PaymentBloc>().add(LoadPayments(
                  refresh: true,
                  status: _selectedStatus,
                ));
              },
            ),
        ],
      ),
    );
  }

  void _showCancelDialog(PaymentModel payment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Payment'),
        content: Text(
          'Are you sure you want to cancel payment #${payment.id}?\n\n'
          'Amount: \$${payment.amount.toStringAsFixed(2)}\n'
          'Status: ${payment.status.displayName}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<PaymentBloc>().add(CancelPayment(payment.id!));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }
}
