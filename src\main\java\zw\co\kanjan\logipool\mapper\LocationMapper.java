package zw.co.kanjan.logipool.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValuePropertyMappingStrategy;
import zw.co.kanjan.logipool.dto.tracking.LocationDto;
import zw.co.kanjan.logipool.entity.DriverLocation;
import zw.co.kanjan.logipool.entity.LocationPermission;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface LocationMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "driver", ignore = true)
    @Mapping(target = "load", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "timestamp", ignore = true)
    @Mapping(target = "expiresAt", ignore = true)
    DriverLocation toEntity(LocationDto.LocationUpdateRequest request);
    
    @Mapping(source = "driver.id", target = "driverId")
    @Mapping(source = "driver", target = "driverName", qualifiedByName = "formatDriverName")
    @Mapping(source = "load.id", target = "loadId")
    @Mapping(source = "load.title", target = "loadTitle")
    @Mapping(source = "company.id", target = "companyId")
    @Mapping(source = "company.name", target = "companyName")
    LocationDto.LocationResponse toResponse(DriverLocation location);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "driver", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "load", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "isActive", ignore = true)
    @Mapping(target = "grantedBy", ignore = true)
    @Mapping(target = "grantedAt", ignore = true)
    @Mapping(target = "revokedBy", ignore = true)
    @Mapping(target = "revokedAt", ignore = true)
    @Mapping(target = "revokeReason", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    LocationPermission toPermissionEntity(LocationDto.PermissionRequest request);

    @Mapping(source = "driver.id", target = "driverId")
    @Mapping(source = "driver", target = "driverName", qualifiedByName = "formatDriverName")
    @Mapping(source = "company.id", target = "companyId")
    @Mapping(source = "company.name", target = "companyName")
    @Mapping(source = "load.id", target = "loadId")
    @Mapping(source = "load.title", target = "loadTitle")
    @Mapping(source = "grantedBy.username", target = "grantedBy")
    @Mapping(source = "revokedBy.username", target = "revokedBy")
    LocationDto.PermissionResponse toPermissionResponse(LocationPermission permission);

    @org.mapstruct.Named("formatDriverName")
    default String formatDriverName(zw.co.kanjan.logipool.entity.User driver) {
        if (driver == null) {
            return null;
        }
        String firstName = driver.getFirstName();
        String lastName = driver.getLastName();

        if (firstName == null && lastName == null) {
            return driver.getUsername();
        }
        if (firstName == null) {
            return lastName;
        }
        if (lastName == null) {
            return firstName;
        }
        return firstName + " " + lastName;
    }
}
