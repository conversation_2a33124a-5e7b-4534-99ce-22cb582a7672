package zw.co.kanjan.logipool.service;

import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.invoice.InvoiceDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.InvoiceMapper;
import zw.co.kanjan.logipool.repository.*;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

// iText PDF imports
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;

import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.io.font.constants.StandardFonts;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class InvoiceService {
    
    private final InvoiceRepository invoiceRepository;
    private final LoadRepository loadRepository;
    private final BidRepository bidRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final CompanyMemberRepository companyMemberRepository;
    private final DocumentRepository documentRepository;
    private final InvoiceMapper invoiceMapper;
    private final NotificationService notificationService;
    
    @Value("${app.invoice.tax-rate:0.15}")
    private BigDecimal defaultTaxRate;
    
    @Value("${app.invoice.commission-rate:0.10}")
    private BigDecimal defaultCommissionRate;
    
    @Value("${app.invoice.payment-terms-days:30}")
    private int defaultPaymentTermsDays;
    
    public InvoiceDto.InvoiceResponse createInvoice(InvoiceDto.InvoiceCreateRequest request, String username) {
        log.info("Creating invoice for load {} by user {}", request.getLoadId(), username);
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        Load load = loadRepository.findById(request.getLoadId())
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + request.getLoadId()));
        
        // Validate user has permission to create invoice for this load
        validateInvoiceCreationPermission(load, user);
        
        // Validate load is in appropriate status for invoicing
        if (load.getStatus() != Load.LoadStatus.DELIVERED &&
            load.getStatus() != Load.LoadStatus.IN_TRANSIT &&
            load.getStatus() != Load.LoadStatus.ASSIGNED) {
            throw new BusinessException("Invoice can only be created for loads that are assigned, in transit, or delivered");
        }
        
        // Check if invoice already exists for this load
        if (invoiceRepository.existsByLoad(load)) {
            throw new BusinessException("Invoice already exists for this load");
        }
        
        Invoice invoice = buildInvoiceFromRequest(request, load, user);
        Invoice savedInvoice = invoiceRepository.save(invoice);
        
        log.info("Invoice {} created successfully for load {}", savedInvoice.getInvoiceNumber(), request.getLoadId());
        return invoiceMapper.toResponse(savedInvoice);
    }
    
    public InvoiceDto.InvoiceResponse generateAutomaticInvoice(Long loadId, String username) {
        log.info("Generating automatic invoice for load {} by user {}", loadId, username);
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));
        
        // Validate user has permission
        validateInvoiceCreationPermission(load, user);
        
        // Validate load is in appropriate status for invoicing
        if (load.getStatus() != Load.LoadStatus.DELIVERED &&
            load.getStatus() != Load.LoadStatus.IN_TRANSIT &&
            load.getStatus() != Load.LoadStatus.ASSIGNED) {
            throw new BusinessException("Automatic invoice can only be generated for loads that are assigned, in transit, or delivered");
        }
        
        // Check if invoice already exists
        if (invoiceRepository.existsByLoad(load)) {
            throw new BusinessException("Invoice already exists for this load");
        }

        // Enhanced check for uploaded invoice documents
        List<Document> existingInvoiceDocuments = getValidInvoiceDocuments(load);
        if (!existingInvoiceDocuments.isEmpty()) {
            Document latestInvoice = existingInvoiceDocuments.get(0); // Already sorted by creation date desc
            throw new BusinessException(String.format(
                "An invoice document (%s) has already been uploaded for this load on %s. " +
                "Please use the uploaded invoice instead of generating a new one. " +
                "If you need to replace it, please delete the existing document first.",
                latestInvoice.getName(),
                latestInvoice.getCreatedAt().format(DateTimeFormatter.ofPattern("MMM dd, yyyy"))
            ));
        }

        // Get the accepted bid for this load
        Bid acceptedBid = load.getBids().stream()
                .filter(bid -> bid.getStatus() == Bid.BidStatus.ACCEPTED)
                .findFirst()
                .orElseThrow(() -> new BusinessException("No accepted bid found for this load"));
        
        Invoice invoice = buildAutomaticInvoice(load, acceptedBid, user);
        Invoice savedInvoice = invoiceRepository.save(invoice);
        
        // Send notification to client
        notificationService.sendInvoiceGeneratedNotification(savedInvoice);
        
        log.info("Automatic invoice {} generated successfully for load {}", savedInvoice.getInvoiceNumber(), loadId);
        return invoiceMapper.toResponse(savedInvoice);
    }

    public InvoiceDto.InvoicePreviewResponse previewAutomaticInvoice(Long loadId, String username) {
        log.info("Previewing automatic invoice for load {} by user {}", loadId, username);

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        // Validate user has permission
        validateInvoiceCreationPermission(load, user);

        // Validate load is in appropriate status for invoicing
        if (load.getStatus() != Load.LoadStatus.DELIVERED &&
            load.getStatus() != Load.LoadStatus.IN_TRANSIT &&
            load.getStatus() != Load.LoadStatus.ASSIGNED) {
            throw new BusinessException("Automatic invoice can only be previewed for loads that are assigned, in transit, or delivered");
        }

        // Get the accepted bid for this load
        Bid acceptedBid = load.getBids().stream()
                .filter(bid -> bid.getStatus() == Bid.BidStatus.ACCEPTED)
                .findFirst()
                .orElseThrow(() -> new BusinessException("No accepted bid found for this load"));

        // Build invoice preview (without saving)
        Invoice previewInvoice = buildAutomaticInvoice(load, acceptedBid, user);

        // Enhanced check for uploaded invoice documents
        List<Document> existingInvoiceDocuments = getValidInvoiceDocuments(load);
        boolean hasExistingInvoiceDocument = !existingInvoiceDocuments.isEmpty();

        // Create preview response
        InvoiceDto.InvoicePreviewResponse response = new InvoiceDto.InvoicePreviewResponse();
        response.setInvoiceNumber(previewInvoice.getInvoiceNumber());
        response.setSubtotal(previewInvoice.getSubtotal());
        response.setTaxAmount(previewInvoice.getTaxAmount());
        response.setTaxRate(defaultTaxRate);
        response.setDiscountAmount(previewInvoice.getDiscountAmount());
        response.setTotalAmount(previewInvoice.getTotalAmount());
        response.setCommissionAmount(previewInvoice.getCommissionAmount());
        response.setCommissionRate(defaultCommissionRate);
        response.setNetAmount(previewInvoice.getNetAmount());
        response.setType(previewInvoice.getType());
        response.setDescription(previewInvoice.getDescription());
        response.setDueDate(previewInvoice.getDueDate());
        response.setLoadId(load.getId());
        response.setLoadTitle(load.getTitle());
        response.setBidId(acceptedBid.getId());
        response.setBidAmount(acceptedBid.getAmount());
        response.setClientId(load.getClient().getId());
        response.setClientName(load.getClient().getFirstName() + " " + load.getClient().getLastName());
        response.setTransporterId(load.getAssignedCompany().getUser().getId());
        response.setTransporterName(load.getAssignedCompany().getUser().getFirstName() + " " + load.getAssignedCompany().getUser().getLastName());
        response.setCompanyId(load.getAssignedCompany().getId());
        response.setCompanyName(load.getAssignedCompany().getName());
        response.setHasExistingInvoiceDocument(hasExistingInvoiceDocument);

        // Add detailed information about existing invoice documents
        if (hasExistingInvoiceDocument) {
            Document latestInvoice = existingInvoiceDocuments.get(0);
            String existingInvoiceInfo = String.format(
                "An invoice document '%s' was uploaded on %s (Status: %s). " +
                "Consider using the uploaded invoice instead of generating a new one.",
                latestInvoice.getName(),
                latestInvoice.getCreatedAt().format(DateTimeFormatter.ofPattern("MMM dd, yyyy")),
                latestInvoice.getStatus().toString().toLowerCase()
            );
            response.setCommissionNote(existingInvoiceInfo + " " +
                String.format("Platform commission of %.1f%% will be deducted from the total amount",
                    defaultCommissionRate.multiply(new BigDecimal("100")).doubleValue()));
        } else {
            response.setCommissionNote(String.format("Platform commission of %.1f%% will be deducted from the total amount",
                    defaultCommissionRate.multiply(new BigDecimal("100")).doubleValue()));
        }

        // Map items
        List<InvoiceDto.InvoiceItemResponse> itemResponses = previewInvoice.getItems().stream()
                .map(invoiceMapper::toItemResponse)
                .toList();
        response.setItems(itemResponses);

        log.info("Invoice preview generated for load {}", loadId);
        return response;
    }

    public InvoiceDto.InvoiceResponse generateInvoiceWithModifications(Long loadId, InvoiceDto.InvoiceModificationRequest request, String username) {
        log.info("Generating invoice with modifications for load {} by user {}", loadId, username);

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        // Validate user has permission
        validateInvoiceCreationPermission(load, user);

        // Validate load is in appropriate status for invoicing
        if (load.getStatus() != Load.LoadStatus.DELIVERED &&
            load.getStatus() != Load.LoadStatus.IN_TRANSIT &&
            load.getStatus() != Load.LoadStatus.ASSIGNED) {
            throw new BusinessException("Invoice can only be generated for loads that are assigned, in transit, or delivered");
        }

        // Enhanced check for uploaded invoice documents
        List<Document> existingInvoiceDocuments = getValidInvoiceDocuments(load);
        if (!existingInvoiceDocuments.isEmpty()) {
            Document latestInvoice = existingInvoiceDocuments.get(0); // Already sorted by creation date desc
            throw new BusinessException(String.format(
                "An invoice document (%s) has already been uploaded for this load on %s. " +
                "Please use the uploaded invoice instead of generating a new one. " +
                "If you need to replace it, please delete the existing document first.",
                latestInvoice.getName(),
                latestInvoice.getCreatedAt().format(DateTimeFormatter.ofPattern("MMM dd, yyyy"))
            ));
        }

        // Get the accepted bid for this load
        Bid acceptedBid = load.getBids().stream()
                .filter(bid -> bid.getStatus() == Bid.BidStatus.ACCEPTED)
                .findFirst()
                .orElseThrow(() -> new BusinessException("No accepted bid found for this load"));

        Invoice savedInvoice;

        // Check if invoice already exists - if so, update it; otherwise create new one
        List<Invoice> existingInvoices = invoiceRepository.findByLoad(load);
        if (!existingInvoices.isEmpty()) {
            // Find the most recent draft invoice, or the most recent invoice if no draft exists
            Invoice invoice = existingInvoices.stream()
                    .filter(inv -> inv.getStatus() == Invoice.InvoiceStatus.DRAFT)
                    .findFirst()
                    .orElse(existingInvoices.get(0)); // Get the first (most recent) invoice

            // Validate invoice can be updated (only draft invoices can be updated)
            if (invoice.getStatus() != Invoice.InvoiceStatus.DRAFT) {
                throw new BusinessException("Only draft invoices can be updated with modifications");
            }

            // Update existing invoice with modifications
            updateInvoiceWithModifications(invoice, request);
            savedInvoice = invoiceRepository.save(invoice);

            log.info("Invoice {} updated with modifications for load {}", savedInvoice.getInvoiceNumber(), loadId);
        } else {
            // Build new invoice with modifications
            Invoice invoice = buildInvoiceWithModifications(load, acceptedBid, request, user);
            savedInvoice = invoiceRepository.save(invoice);

            log.info("Invoice {} generated with modifications for load {}", savedInvoice.getInvoiceNumber(), loadId);
        }

        // Send notification to client
        notificationService.sendInvoiceGeneratedNotification(savedInvoice);

        return invoiceMapper.toResponse(savedInvoice);
    }

    @Transactional(readOnly = true)
    public Resource generateInvoicePdf(Long invoiceId, String username) {
        InvoiceFileResponse fileResponse = generateInvoiceFile(invoiceId, username);
        return fileResponse.getResource();
    }

    @Transactional(readOnly = true)
    public InvoiceFileResponse generateInvoiceFile(Long invoiceId, String username) {
        log.info("Generating invoice file for invoice {} by user {}", invoiceId, username);

        Invoice invoice = invoiceRepository.findById(invoiceId)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + invoiceId));

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        // Validate user has access to this invoice
        validateInvoiceAccess(invoice, user);

        // Generate PDF using iText library
        try {
            // Generate PDF content for the invoice
            byte[] pdfBytes = generateInvoicePdf(invoice);

            // Create a temporary file with the PDF content
            Path tempFile = Files.createTempFile("invoice_" + invoiceId, ".pdf");
            Files.write(tempFile, pdfBytes);

            // Create a resource from the temporary file
            Resource resource = new UrlResource(tempFile.toUri());

            // Generate filename using invoice number if available
            String fileName;
            if (invoice.getInvoiceNumber() != null && !invoice.getInvoiceNumber().isEmpty()) {
                fileName = "Invoice_" + invoice.getInvoiceNumber() + ".pdf";
            } else {
                fileName = "Invoice_" + invoiceId + ".pdf";
            }

            log.info("Invoice PDF generated successfully for invoice {}", invoiceId);
            return InvoiceFileResponse.builder()
                    .resource(resource)
                    .fileName(fileName)
                    .contentType("application/pdf")
                    .build();

        } catch (Exception e) {
            log.error("Failed to generate invoice file for invoice {}: {}", invoiceId, e.getMessage());
            throw new BusinessException("Failed to generate invoice file: " + e.getMessage());
        }
    }

    @Transactional(readOnly = true)
    public InvoiceDto.InvoiceResponse getInvoice(Long invoiceId, String username) {
        Invoice invoice = invoiceRepository.findById(invoiceId)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + invoiceId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate user has access to this invoice
        validateInvoiceAccess(invoice, user);
        
        return invoiceMapper.toResponse(invoice);
    }
    
    @Transactional(readOnly = true)
    public Page<InvoiceDto.InvoiceResponse> getInvoices(Pageable pageable, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        Page<Invoice> invoices;

        // Admin can see all invoices
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            invoices = invoiceRepository.findAll(pageable);
        } else {
            // Users can see invoices where they are client, transporter, or company member
            invoices = invoiceRepository.findByClientOrTransporterOrCompanyMember(user.getId(), pageable);
        }

        return invoices.map(invoiceMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public InvoiceDto.InvoiceSummary getInvoiceSummary(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        List<Invoice> userInvoices;

        // Admin can see all invoices
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            userInvoices = invoiceRepository.findAll();
        } else {
            // Users can see invoices where they are client, transporter, or company member
            userInvoices = invoiceRepository.findByClientOrTransporterOrCompanyMember(user.getId());
        }

        InvoiceDto.InvoiceSummary summary = new InvoiceDto.InvoiceSummary();
        summary.setTotalCount(userInvoices.size());
        summary.setTotalAmount(userInvoices.stream()
                .map(Invoice::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setTotalCommission(userInvoices.stream()
                .map(Invoice::getCommissionAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setTotalTax(userInvoices.stream()
                .map(Invoice::getTaxAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
        summary.setDraftCount(userInvoices.stream()
                .mapToLong(invoice -> invoice.getStatus() == Invoice.InvoiceStatus.DRAFT ? 1 : 0)
                .sum());
        summary.setSentCount(userInvoices.stream()
                .mapToLong(invoice -> invoice.getStatus() == Invoice.InvoiceStatus.SENT ? 1 : 0)
                .sum());
        summary.setPaidCount(userInvoices.stream()
                .mapToLong(invoice -> invoice.getStatus() == Invoice.InvoiceStatus.PAID ? 1 : 0)
                .sum());
        summary.setOverdueCount(userInvoices.stream()
                .mapToLong(invoice -> invoice.getStatus() == Invoice.InvoiceStatus.OVERDUE ? 1 : 0)
                .sum());

        return summary;
    }
    
    @Transactional(readOnly = true)
    public Page<InvoiceDto.InvoiceResponse> getLoadInvoices(Long loadId, Pageable pageable, String username) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate user has access to load
        validateLoadAccess(load, user);
        
        Page<Invoice> invoices = invoiceRepository.findByLoad(load, pageable);
        return invoices.map(invoiceMapper::toResponse);
    }
    
    public InvoiceDto.InvoiceResponse updateInvoice(Long invoiceId, InvoiceDto.InvoiceUpdateRequest request, String username) {
        Invoice invoice = invoiceRepository.findById(invoiceId)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + invoiceId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate user has permission to update invoice
        validateInvoiceUpdatePermission(invoice, user);
        
        // Validate invoice can be updated (only draft invoices can be updated)
        if (invoice.getStatus() != Invoice.InvoiceStatus.DRAFT) {
            throw new BusinessException("Only draft invoices can be updated");
        }
        
        updateInvoiceFromRequest(invoice, request);
        recalculateInvoiceTotals(invoice);
        
        Invoice savedInvoice = invoiceRepository.save(invoice);
        
        log.info("Invoice {} updated successfully by user {}", invoiceId, username);
        return invoiceMapper.toResponse(savedInvoice);
    }
    
    public InvoiceDto.InvoiceResponse sendInvoice(Long invoiceId, String username) {
        Invoice invoice = invoiceRepository.findById(invoiceId)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + invoiceId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate user has permission to send invoice
        validateInvoiceUpdatePermission(invoice, user);
        
        // Validate invoice can be sent
        if (invoice.getStatus() != Invoice.InvoiceStatus.DRAFT) {
            throw new BusinessException("Only draft invoices can be sent");
        }
        
        invoice.setStatus(Invoice.InvoiceStatus.SENT);
        invoice.setSentAt(LocalDateTime.now());
        
        Invoice savedInvoice = invoiceRepository.save(invoice);
        
        // Send notification to client
        notificationService.sendInvoiceSentNotification(savedInvoice);
        
        log.info("Invoice {} sent successfully by user {}", invoiceId, username);
        return invoiceMapper.toResponse(savedInvoice);
    }
    
    public void deleteInvoice(Long invoiceId, String username) {
        Invoice invoice = invoiceRepository.findById(invoiceId)
                .orElseThrow(() -> new ResourceNotFoundException("Invoice not found with id: " + invoiceId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate user has permission to delete invoice
        validateInvoiceUpdatePermission(invoice, user);
        
        // Validate invoice can be deleted (only draft invoices can be deleted)
        if (invoice.getStatus() != Invoice.InvoiceStatus.DRAFT) {
            throw new BusinessException("Only draft invoices can be deleted");
        }
        
        invoiceRepository.delete(invoice);
        
        log.info("Invoice {} deleted successfully by user {}", invoiceId, username);
    }
    
    // Private helper methods

    private void validateInvoiceCreationPermission(Load load, User user) {
        // Admin can create invoices for any load
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Load client can create invoices for their own loads
        if (load.getClient().equals(user)) {
            return;
        }

        // If load is assigned to a company, check if user is a company member with invoice generation permission
        if (load.getAssignedCompany() != null) {
            // Check if user is the company owner
            if (load.getAssignedCompany().getUser().equals(user)) {
                return;
            }

            // Check if user is a company member with invoice generation permission
            CompanyMember member = companyMemberRepository.findByCompanyAndUser(load.getAssignedCompany(), user)
                    .orElse(null);

            if (member != null &&
                member.getStatus() == CompanyMember.MemberStatus.ACTIVE &&
                member.getCanGenerateInvoices()) {
                return;
            }
        }

        throw new BusinessException("You don't have permission to create invoices for this load");
    }

    private void validateInvoiceAccess(Invoice invoice, User user) {
        // Admin can access all invoices
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Client can access their own invoices
        if (invoice.getClient() != null && invoice.getClient().equals(user)) {
            return;
        }

        // Transporter can access their invoices
        if (invoice.getTransporter() != null && invoice.getTransporter().equals(user)) {
            return;
        }

        // Company members can access company invoices
        if (invoice.getCompany() != null) {
            // Check if user is the company owner
            if (invoice.getCompany().getUser().equals(user)) {
                return;
            }

            // Check if user is an active company member
            CompanyMember member = companyMemberRepository.findByCompanyAndUser(invoice.getCompany(), user)
                    .orElse(null);

            if (member != null && member.getStatus() == CompanyMember.MemberStatus.ACTIVE) {
                return;
            }
        }

        throw new BusinessException("You don't have permission to access this invoice");
    }

    private void validateInvoiceUpdatePermission(Invoice invoice, User user) {
        // Admin can update any invoice
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Company members with invoice generation permission can update company invoices
        if (invoice.getCompany() != null) {
            // Check if user is the company owner
            if (invoice.getCompany().getUser().equals(user)) {
                return;
            }

            // Check if user is a company member with invoice generation permission
            CompanyMember member = companyMemberRepository.findByCompanyAndUser(invoice.getCompany(), user)
                    .orElse(null);

            if (member != null &&
                member.getStatus() == CompanyMember.MemberStatus.ACTIVE &&
                member.getCanGenerateInvoices()) {
                return;
            }
        }

        throw new BusinessException("You don't have permission to update this invoice");
    }

    private void validateLoadAccess(Load load, User user) {
        // Admin can access all loads
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Load client can access their own loads
        if (load.getClient().equals(user)) {
            return;
        }

        // Company members can access company loads
        if (load.getAssignedCompany() != null) {
            // Check if user is the company owner
            if (load.getAssignedCompany().getUser().equals(user)) {
                return;
            }

            // Check if user is an active company member
            CompanyMember member = companyMemberRepository.findByCompanyAndUser(load.getAssignedCompany(), user)
                    .orElse(null);

            if (member != null && member.getStatus() == CompanyMember.MemberStatus.ACTIVE) {
                return;
            }
        }

        throw new BusinessException("You don't have permission to access this load");
    }

    private Invoice buildInvoiceFromRequest(InvoiceDto.InvoiceCreateRequest request, Load load, User user) {
        Invoice invoice = invoiceMapper.toEntity(request);

        // Set basic properties
        invoice.setInvoiceNumber(generateInvoiceNumber());
        invoice.setStatus(Invoice.InvoiceStatus.DRAFT);
        invoice.setLoad(load);

        // Set relationships
        if (request.getClientId() != null) {
            User client = userRepository.findById(request.getClientId())
                    .orElseThrow(() -> new ResourceNotFoundException("Client not found"));
            invoice.setClient(client);
        } else {
            invoice.setClient(load.getClient());
        }

        if (request.getTransporterId() != null) {
            User transporter = userRepository.findById(request.getTransporterId())
                    .orElseThrow(() -> new ResourceNotFoundException("Transporter not found"));
            invoice.setTransporter(transporter);
        }

        if (request.getCompanyId() != null) {
            Company company = companyRepository.findById(request.getCompanyId())
                    .orElseThrow(() -> new ResourceNotFoundException("Company not found"));
            invoice.setCompany(company);
        } else if (load.getAssignedCompany() != null) {
            invoice.setCompany(load.getAssignedCompany());
        }

        if (request.getBidId() != null) {
            Bid bid = bidRepository.findById(request.getBidId())
                    .orElseThrow(() -> new ResourceNotFoundException("Bid not found"));
            invoice.setBid(bid);
        }

        // Create invoice items
        List<InvoiceItem> items = new ArrayList<>();
        for (InvoiceDto.InvoiceItemRequest itemRequest : request.getItems()) {
            InvoiceItem item = invoiceMapper.toItemEntity(itemRequest);
            item.setInvoice(invoice);
            item.setTotalPrice(itemRequest.getQuantity().multiply(itemRequest.getUnitPrice()));
            items.add(item);
        }
        invoice.setItems(items);

        // Calculate totals
        calculateInvoiceTotals(invoice);

        return invoice;
    }

    private Invoice buildAutomaticInvoice(Load load, Bid acceptedBid, User user) {
        Invoice invoice = Invoice.builder()
                .invoiceNumber(generateInvoiceNumber())
                .type(Invoice.InvoiceType.LOAD_INVOICE)
                .status(Invoice.InvoiceStatus.DRAFT)
                .description("Transportation services for Load #" + load.getId() + " - " + load.getTitle())
                .dueDate(LocalDateTime.now().plusDays(defaultPaymentTermsDays))
                .load(load)
                .bid(acceptedBid)
                .client(load.getClient())
                .company(load.getAssignedCompany())
                .build();

        // Create invoice items based on the accepted bid
        List<InvoiceItem> items = new ArrayList<>();

        // Main transportation service item
        InvoiceItem transportationItem = InvoiceItem.builder()
                .description("Transportation services - " + load.getPickupLocation() + " to " + load.getDeliveryLocation())
                .quantity(BigDecimal.ONE)
                .unitPrice(acceptedBid.getAmount())
                .totalPrice(acceptedBid.getAmount())
                .unit("service")
                .type(InvoiceItem.ItemType.TRANSPORTATION_FEE)
                .invoice(invoice)
                .build();
        items.add(transportationItem);

        // Add insurance fee if required
        if (load.getRequiresInsurance()) {
            // Calculate insurance as 2% of transportation fee
            BigDecimal insuranceFee = acceptedBid.getAmount().multiply(new BigDecimal("0.02"));
            InvoiceItem insuranceItem = InvoiceItem.builder()
                    .description("Insurance coverage")
                    .quantity(BigDecimal.ONE)
                    .unitPrice(insuranceFee)
                    .totalPrice(insuranceFee)
                    .unit("service")
                    .type(InvoiceItem.ItemType.INSURANCE_FEE)
                    .invoice(invoice)
                    .build();
            items.add(insuranceItem);
        }

        // Add handling fee if special handling is required
        if (load.getRequiresSpecialHandling()) {
            // Calculate handling fee as 5% of transportation fee
            BigDecimal handlingFee = acceptedBid.getAmount().multiply(new BigDecimal("0.05"));
            InvoiceItem handlingItem = InvoiceItem.builder()
                    .description("Special handling charges")
                    .quantity(BigDecimal.ONE)
                    .unitPrice(handlingFee)
                    .totalPrice(handlingFee)
                    .unit("service")
                    .type(InvoiceItem.ItemType.HANDLING_FEE)
                    .invoice(invoice)
                    .build();
            items.add(handlingItem);
        }

        invoice.setItems(items);

        // Calculate totals
        calculateInvoiceTotals(invoice);

        return invoice;
    }

    private Invoice buildInvoiceWithModifications(Load load, Bid acceptedBid, InvoiceDto.InvoiceModificationRequest request, User user) {
        Invoice invoice = Invoice.builder()
                .invoiceNumber(generateInvoiceNumber())
                .type(Invoice.InvoiceType.LOAD_INVOICE)
                .status(Invoice.InvoiceStatus.DRAFT)
                .description(request.getDescription() != null ? request.getDescription() :
                    "Transportation services for Load #" + load.getId() + " - " + load.getTitle())
                .notes(request.getNotes())
                .dueDate(request.getDueDate() != null ? request.getDueDate() :
                    LocalDateTime.now().plusDays(defaultPaymentTermsDays))
                .discountAmount(request.getDiscountAmount() != null ? request.getDiscountAmount() : BigDecimal.ZERO)
                .load(load)
                .bid(acceptedBid)
                .client(load.getClient())
                .company(load.getAssignedCompany())
                .build();

        // Create invoice items from request or use default
        List<InvoiceItem> items = new ArrayList<>();

        if (request.getItems() != null && !request.getItems().isEmpty()) {
            // Use custom items from request
            for (InvoiceDto.InvoiceItemRequest itemRequest : request.getItems()) {
                InvoiceItem item = invoiceMapper.toItemEntity(itemRequest);
                item.setInvoice(invoice);
                item.setTotalPrice(itemRequest.getQuantity().multiply(itemRequest.getUnitPrice()));
                items.add(item);
            }
        } else {
            // Use default items based on load and bid
            InvoiceItem transportationItem = InvoiceItem.builder()
                    .description("Transportation services - " + load.getPickupLocation() + " to " + load.getDeliveryLocation())
                    .quantity(BigDecimal.ONE)
                    .unitPrice(acceptedBid.getAmount())
                    .totalPrice(acceptedBid.getAmount())
                    .unit("service")
                    .type(InvoiceItem.ItemType.TRANSPORTATION_FEE)
                    .invoice(invoice)
                    .build();
            items.add(transportationItem);

            // Add insurance fee if required
            if (load.getRequiresInsurance()) {
                BigDecimal insuranceFee = acceptedBid.getAmount().multiply(new BigDecimal("0.02"));
                InvoiceItem insuranceItem = InvoiceItem.builder()
                        .description("Insurance coverage")
                        .quantity(BigDecimal.ONE)
                        .unitPrice(insuranceFee)
                        .totalPrice(insuranceFee)
                        .unit("service")
                        .type(InvoiceItem.ItemType.INSURANCE_FEE)
                        .invoice(invoice)
                        .build();
                items.add(insuranceItem);
            }

            // Add handling fee if special handling is required
            if (load.getRequiresSpecialHandling()) {
                BigDecimal handlingFee = acceptedBid.getAmount().multiply(new BigDecimal("0.05"));
                InvoiceItem handlingItem = InvoiceItem.builder()
                        .description("Special handling charges")
                        .quantity(BigDecimal.ONE)
                        .unitPrice(handlingFee)
                        .totalPrice(handlingFee)
                        .unit("service")
                        .type(InvoiceItem.ItemType.HANDLING_FEE)
                        .invoice(invoice)
                        .build();
                items.add(handlingItem);
            }
        }

        invoice.setItems(items);

        // Calculate totals
        calculateInvoiceTotals(invoice);

        return invoice;
    }

    private void calculateInvoiceTotals(Invoice invoice) {
        BigDecimal subtotal = invoice.getItems().stream()
                .map(InvoiceItem::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal taxAmount = subtotal.multiply(defaultTaxRate).setScale(2, RoundingMode.HALF_UP);
        BigDecimal commissionAmount = subtotal.multiply(defaultCommissionRate).setScale(2, RoundingMode.HALF_UP);
        BigDecimal totalAmount = subtotal.add(taxAmount).subtract(invoice.getDiscountAmount() != null ? invoice.getDiscountAmount() : BigDecimal.ZERO);
        BigDecimal netAmount = totalAmount.subtract(commissionAmount);

        invoice.setSubtotal(subtotal);
        invoice.setTaxAmount(taxAmount);
        invoice.setCommissionAmount(commissionAmount);
        invoice.setTotalAmount(totalAmount);
        invoice.setNetAmount(netAmount);
    }

    private void recalculateInvoiceTotals(Invoice invoice) {
        // Recalculate item totals
        for (InvoiceItem item : invoice.getItems()) {
            item.setTotalPrice(item.getQuantity().multiply(item.getUnitPrice()));
        }

        // Recalculate invoice totals
        calculateInvoiceTotals(invoice);
    }

    private void updateInvoiceFromRequest(Invoice invoice, InvoiceDto.InvoiceUpdateRequest request) {
        if (request.getStatus() != null) {
            invoice.setStatus(request.getStatus());
        }
        if (request.getDescription() != null) {
            invoice.setDescription(request.getDescription());
        }
        if (request.getNotes() != null) {
            invoice.setNotes(request.getNotes());
        }
        if (request.getDueDate() != null) {
            invoice.setDueDate(request.getDueDate());
        }
        if (request.getItems() != null) {
            // Clear existing items and add new ones
            invoice.getItems().clear();
            for (InvoiceDto.InvoiceItemRequest itemRequest : request.getItems()) {
                InvoiceItem item = invoiceMapper.toItemEntity(itemRequest);
                item.setInvoice(invoice);
                item.setTotalPrice(itemRequest.getQuantity().multiply(itemRequest.getUnitPrice()));
                invoice.getItems().add(item);
            }
        }
    }

    private void updateInvoiceWithModifications(Invoice invoice, InvoiceDto.InvoiceModificationRequest request) {
        if (request.getDescription() != null) {
            invoice.setDescription(request.getDescription());
        }
        if (request.getNotes() != null) {
            invoice.setNotes(request.getNotes());
        }
        if (request.getDueDate() != null) {
            invoice.setDueDate(request.getDueDate());
        }
        if (request.getDiscountAmount() != null) {
            invoice.setDiscountAmount(request.getDiscountAmount());
        }
        if (request.getItems() != null) {
            // Clear existing items and add new ones
            invoice.getItems().clear();
            for (InvoiceDto.InvoiceItemRequest itemRequest : request.getItems()) {
                InvoiceItem item = invoiceMapper.toItemEntity(itemRequest);
                item.setInvoice(invoice);
                item.setTotalPrice(itemRequest.getQuantity().multiply(itemRequest.getUnitPrice()));
                invoice.getItems().add(item);
            }
        }

        // Recalculate invoice totals after modifications
        calculateInvoiceTotals(invoice);
    }

    private String generateInvoiceNumber() {
        String prefix = "INV";
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        long count = invoiceRepository.count() + 1;
        return String.format("%s-%s-%04d", prefix, timestamp, count);
    }

    /**
     * Get valid invoice documents for a load, filtering out rejected or expired documents
     * and sorting by creation date (newest first)
     */
    private List<Document> getValidInvoiceDocuments(Load load) {
        List<Document> allInvoiceDocuments = documentRepository.findByLoadAndType(load, Document.DocumentType.INVOICE);

        return allInvoiceDocuments.stream()
                .filter(doc -> doc.getStatus() != Document.DocumentStatus.REJECTED)
                .filter(doc -> doc.getStatus() != Document.DocumentStatus.EXPIRED)
                .filter(doc -> doc.getExpiryDate() == null || doc.getExpiryDate().isAfter(LocalDateTime.now()))
                .sorted((d1, d2) -> d2.getCreatedAt().compareTo(d1.getCreatedAt())) // Newest first
                .toList();
    }

    /**
     * Get information about uploaded invoice documents for a load
     */
    public InvoiceDto.UploadedInvoiceDocumentsResponse getUploadedInvoiceDocuments(Long loadId, String username) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found"));

        // Verify user has access to this load (simplified check)
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        boolean hasAccess = load.getClient().getUsername().equals(username) ||
                           (load.getAssignedCompany() != null &&
                            load.getAssignedCompany().getId().equals(user.getCompany().getId()));

        if (!hasAccess) {
            throw new AccessDeniedException("You don't have access to this load");
        }

        List<Document> allInvoiceDocuments = documentRepository.findByLoadAndType(load, Document.DocumentType.INVOICE);
        List<Document> validInvoiceDocuments = getValidInvoiceDocuments(load);

        InvoiceDto.UploadedInvoiceDocumentsResponse response = new InvoiceDto.UploadedInvoiceDocumentsResponse();
        response.setHasUploadedInvoices(!allInvoiceDocuments.isEmpty());
        response.setTotalDocuments(allInvoiceDocuments.size());

        // Convert documents to DTO
        List<InvoiceDto.UploadedInvoiceDocumentInfo> documentInfos = allInvoiceDocuments.stream()
                .map(doc -> {
                    InvoiceDto.UploadedInvoiceDocumentInfo info = new InvoiceDto.UploadedInvoiceDocumentInfo();
                    info.setDocumentId(doc.getId());
                    info.setName(doc.getName());
                    info.setStatus(doc.getStatus().toString());
                    info.setUploadedAt(doc.getCreatedAt());
                    info.setUploadedBy("System"); // Default value since field doesn't exist in Document entity
                    info.setFileSize(doc.getFileSize());
                    info.setDownloadUrl("/api/documents/" + doc.getId() + "/download");
                    info.setLatest(doc.equals(allInvoiceDocuments.get(0))); // First in list is latest
                    info.setValid(validInvoiceDocuments.contains(doc));
                    return info;
                })
                .toList();

        response.setDocuments(documentInfos);

        // Set message and recommendation
        if (validInvoiceDocuments.isEmpty()) {
            if (allInvoiceDocuments.isEmpty()) {
                response.setMessage("No invoice documents have been uploaded for this load.");
                response.setRecommendation("You can generate a new invoice or upload an existing invoice document.");
            } else {
                response.setMessage("Invoice documents have been uploaded but none are currently valid (may be rejected or expired).");
                response.setRecommendation("Please upload a new valid invoice document or generate a new invoice.");
            }
        } else {
            Document latestValid = validInvoiceDocuments.get(0);
            response.setMessage(String.format("Found %d valid invoice document(s). Latest: '%s' uploaded on %s.",
                    validInvoiceDocuments.size(),
                    latestValid.getName(),
                    latestValid.getCreatedAt().format(DateTimeFormatter.ofPattern("MMM dd, yyyy"))));
            response.setRecommendation("Consider using the uploaded invoice instead of generating a new one to avoid duplication.");
        }

        return response;
    }

    private String generateSimpleInvoiceContent(Invoice invoice) {
        StringBuilder content = new StringBuilder();

        content.append("INVOICE\n");
        content.append("=======\n\n");

        content.append("Invoice Number: ").append(invoice.getInvoiceNumber()).append("\n");
        content.append("Date: ").append(invoice.getCreatedAt().format(DateTimeFormatter.ofPattern("MMM dd, yyyy"))).append("\n");
        content.append("Due Date: ").append(invoice.getDueDate().format(DateTimeFormatter.ofPattern("MMM dd, yyyy"))).append("\n\n");

        content.append("BILL TO:\n");
        content.append("--------\n");
        if (invoice.getClient() != null) {
            content.append("Client: ").append(invoice.getClient().getFirstName()).append(" ").append(invoice.getClient().getLastName()).append("\n");
            content.append("Email: ").append(invoice.getClient().getEmail()).append("\n");
        }
        content.append("\n");

        content.append("BILL FROM:\n");
        content.append("----------\n");
        if (invoice.getTransporter() != null) {
            content.append("Transporter: ").append(invoice.getTransporter().getFirstName()).append(" ").append(invoice.getTransporter().getLastName()).append("\n");
            content.append("Email: ").append(invoice.getTransporter().getEmail()).append("\n");
        }
        if (invoice.getCompany() != null) {
            content.append("Company: ").append(invoice.getCompany().getName()).append("\n");
        }
        content.append("\n");

        if (invoice.getLoad() != null) {
            content.append("LOAD DETAILS:\n");
            content.append("-------------\n");
            content.append("Load ID: ").append(invoice.getLoad().getId()).append("\n");
            content.append("Title: ").append(invoice.getLoad().getTitle()).append("\n");
            content.append("Pickup: ").append(invoice.getLoad().getPickupLocation()).append("\n");
            content.append("Delivery: ").append(invoice.getLoad().getDeliveryLocation()).append("\n");
            content.append("\n");
        }

        content.append("INVOICE ITEMS:\n");
        content.append("--------------\n");
        if (invoice.getItems() != null && !invoice.getItems().isEmpty()) {
            for (InvoiceItem item : invoice.getItems()) {
                content.append(String.format("%-30s %8.2f x $%8.2f = $%10.2f\n",
                    item.getDescription(),
                    item.getQuantity(),
                    item.getUnitPrice(),
                    item.getTotalPrice()));
            }
        } else {
            content.append("Transportation Service                    1.00 x $").append(String.format("%8.2f", invoice.getSubtotal())).append(" = $").append(String.format("%10.2f", invoice.getSubtotal())).append("\n");
        }
        content.append("\n");

        content.append("TOTALS:\n");
        content.append("-------\n");
        content.append(String.format("Subtotal:        $%10.2f\n", invoice.getSubtotal()));
        content.append(String.format("Tax:             $%10.2f\n", invoice.getTaxAmount()));
        content.append(String.format("Discount:        $%10.2f\n", invoice.getDiscountAmount()));
        content.append(String.format("Total Amount:    $%10.2f\n", invoice.getTotalAmount()));
        content.append(String.format("Commission:      $%10.2f\n", invoice.getCommissionAmount()));
        content.append(String.format("Net Amount:      $%10.2f\n", invoice.getNetAmount()));
        content.append("\n");

        if (invoice.getDescription() != null && !invoice.getDescription().isEmpty()) {
            content.append("DESCRIPTION:\n");
            content.append("------------\n");
            content.append(invoice.getDescription()).append("\n\n");
        }

        if (invoice.getNotes() != null && !invoice.getNotes().isEmpty()) {
            content.append("NOTES:\n");
            content.append("------\n");
            content.append(invoice.getNotes()).append("\n\n");
        }

        content.append("Status: ").append(invoice.getStatus()).append("\n");
        content.append("Generated on: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm"))).append("\n");

        return content.toString();
    }

    private byte[] generateInvoicePdf(Invoice invoice) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            PdfWriter writer = new PdfWriter(baos);
            PdfDocument pdfDoc = new PdfDocument(writer);
            com.itextpdf.layout.Document document = new com.itextpdf.layout.Document(pdfDoc);

            // Set up fonts
            PdfFont boldFont = PdfFontFactory.createFont(StandardFonts.HELVETICA_BOLD);
            PdfFont regularFont = PdfFontFactory.createFont(StandardFonts.HELVETICA);

            // Title
            Paragraph title = new Paragraph("INVOICE")
                    .setFont(boldFont)
                    .setFontSize(24)
                    .setMarginBottom(20);
            document.add(title);

            // Invoice basic info
            document.add(new Paragraph("Invoice Number: " + (invoice.getInvoiceNumber() != null ? invoice.getInvoiceNumber() : "N/A")).setFont(regularFont));
            document.add(new Paragraph("Date: " + invoice.getCreatedAt().format(DateTimeFormatter.ofPattern("MMM dd, yyyy"))).setFont(regularFont));
            document.add(new Paragraph("Due Date: " + invoice.getDueDate().format(DateTimeFormatter.ofPattern("MMM dd, yyyy"))).setFont(regularFont));
            document.add(new Paragraph("Status: " + invoice.getStatus()).setFont(boldFont).setFontSize(14));
            document.add(new Paragraph("\n"));

            // Bill To
            document.add(new Paragraph("BILL TO:").setFont(boldFont).setFontSize(12));
            if (invoice.getClient() != null) {
                document.add(new Paragraph(invoice.getClient().getFirstName() + " " + invoice.getClient().getLastName()).setFont(regularFont));
                document.add(new Paragraph(invoice.getClient().getEmail()).setFont(regularFont));
            }
            document.add(new Paragraph("\n"));

            // Bill From
            document.add(new Paragraph("BILL FROM:").setFont(boldFont).setFontSize(12));
            if (invoice.getTransporter() != null) {
                document.add(new Paragraph(invoice.getTransporter().getFirstName() + " " + invoice.getTransporter().getLastName()).setFont(regularFont));
                document.add(new Paragraph(invoice.getTransporter().getEmail()).setFont(regularFont));
            }
            if (invoice.getCompany() != null) {
                document.add(new Paragraph("Company: " + invoice.getCompany().getName()).setFont(regularFont));
            }
            document.add(new Paragraph("\n"));

            // Load Details
            if (invoice.getLoad() != null) {
                document.add(new Paragraph("LOAD DETAILS:").setFont(boldFont).setFontSize(12));
                document.add(new Paragraph("Load ID: " + invoice.getLoad().getId().toString()).setFont(regularFont));
                document.add(new Paragraph("Title: " + invoice.getLoad().getTitle()).setFont(regularFont));
                document.add(new Paragraph("Pickup: " + invoice.getLoad().getPickupLocation()).setFont(regularFont));
                document.add(new Paragraph("Delivery: " + invoice.getLoad().getDeliveryLocation()).setFont(regularFont));
                document.add(new Paragraph("\n"));
            }

            // Invoice Items
            document.add(new Paragraph("INVOICE ITEMS:").setFont(boldFont).setFontSize(12));

            if (invoice.getItems() != null && !invoice.getItems().isEmpty()) {
                for (InvoiceItem item : invoice.getItems()) {
                    document.add(new Paragraph(String.format("%-30s %8.2f x $%8.2f = $%10.2f",
                            item.getDescription(),
                            item.getQuantity(),
                            item.getUnitPrice(),
                            item.getTotalPrice())).setFont(regularFont));
                }
            }
            document.add(new Paragraph("\n"));

            // Totals
            document.add(new Paragraph("Subtotal: $" + invoice.getSubtotal().toString()).setFont(boldFont));

            if (invoice.getTaxAmount() != null && invoice.getTaxAmount().compareTo(BigDecimal.ZERO) > 0) {
                document.add(new Paragraph("Tax: $" + invoice.getTaxAmount().toString()).setFont(boldFont));
            }

            document.add(new Paragraph("Total Amount: $" + invoice.getTotalAmount().toString()).setFont(boldFont).setFontSize(14));

            // Notes
            if (invoice.getNotes() != null && !invoice.getNotes().trim().isEmpty()) {
                document.add(new Paragraph("\n"));
                document.add(new Paragraph("Notes:").setFont(boldFont).setFontSize(12));
                document.add(new Paragraph(invoice.getNotes()).setFont(regularFont));
            }

            // Footer
            document.add(new Paragraph("\n"));
            document.add(new Paragraph("Generated on: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm")))
                    .setFont(regularFont)
                    .setFontSize(10));

            document.close();
            return baos.toByteArray();

        } catch (Exception e) {
            log.error("Failed to generate PDF for invoice {}: {}", invoice.getId(), e.getMessage());
            throw new BusinessException("Failed to generate invoice PDF: " + e.getMessage());
        }
    }

    @Data
    @Builder
    public static class InvoiceFileResponse {
        private Resource resource;
        private String fileName;
        private String contentType;
    }
}
