import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/bid_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/models/load_model.dart';
import '../../../shared/enums/user_role.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/services/load_service.dart';
import '../../../core/di/service_locator.dart';
import '../../../shared/widgets/unified_header.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../core/constants/app_constants.dart';
import '../bloc/bid_bloc.dart';

class BidDetailsScreen extends StatefulWidget {
  final int bidId;

  const BidDetailsScreen({
    super.key,
    required this.bidId,
  });

  @override
  State<BidDetailsScreen> createState() => _BidDetailsScreenState();
}

class _BidDetailsScreenState extends State<BidDetailsScreen> {
  LoadModel? _loadDetails;
  bool _isLoadingLoadDetails = false;

  @override
  void initState() {
    super.initState();
    context.read<BidBloc>().add(BidFetchRequested(bidId: widget.bidId));
  }

  Future<void> _loadLoadDetails(int loadId) async {
    if (_loadDetails?.id == loadId) return; // Already loaded

    setState(() {
      _isLoadingLoadDetails = true;
    });

    try {
      final loadService = getIt<LoadService>();
      final loadDetails = await loadService.getLoadById(loadId);
      setState(() {
        _loadDetails = loadDetails;
        _isLoadingLoadDetails = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingLoadDetails = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load load details: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedHeader(
        title: 'Bid Details',
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            onPressed: () {
              context
                  .read<BidBloc>()
                  .add(BidFetchRequested(bidId: widget.bidId));
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: BlocConsumer<BidBloc, BidState>(
        listener: (context, state) {
          if (state is BidError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is BidOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
            // Refresh the bid details
            context.read<BidBloc>().add(BidFetchRequested(bidId: widget.bidId));
          }
        },
        builder: (context, state) {
          if (state is BidLoading) {
            return const LoadingWidget();
          }

          if (state is BidSuccess) {
            return _buildBidDetailsView(context, state.bid);
          }

          if (state is BidError) {
            return CustomErrorWidget(
              message: state.message,
              onRetry: () {
                context
                    .read<BidBloc>()
                    .add(BidFetchRequested(bidId: widget.bidId));
              },
            );
          }

          return const LoadingWidget();
        },
      ),
    );
  }

  Widget _buildBidDetailsView(BuildContext context, BidModel bid) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<BidBloc>().add(BidFetchRequested(bidId: widget.bidId));
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildBidHeader(context, bid),
            const SizedBox(height: 24),
            _buildBidAmount(context, bid),
            const SizedBox(height: 24),
            _buildTimeDetails(context, bid),
            const SizedBox(height: 24),
            if (bid.proposal != null && bid.proposal!.isNotEmpty) ...[
              _buildProposal(context, bid),
              const SizedBox(height: 24),
            ],
            if (bid.notes != null && bid.notes!.isNotEmpty) ...[
              _buildNotes(context, bid),
              const SizedBox(height: 24),
            ],
            _buildCompanyInfo(context, bid),
            const SizedBox(height: 24),
            _buildLoadInfo(context, bid),
            const SizedBox(height: 24),
            _buildTimestamps(context, bid),
            const SizedBox(height: 24),
            _buildActionButtons(context, bid),
          ],
        ),
      ),
    );
  }

  Widget _buildBidHeader(BuildContext context, BidModel bid) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Bid #${bid.id}',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'From: ${bid.companyName ?? 'Unknown Company'}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      if (bid.loadTitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          'Load: ${bid.loadTitle}',
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                _buildStatusChip(context, bid.status),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, BidStatus status) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (status) {
      case BidStatus.pending:
        backgroundColor = Colors.orange[100]!;
        textColor = Colors.orange[700]!;
        icon = Icons.schedule;
        break;
      case BidStatus.accepted:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[700]!;
        icon = Icons.check_circle;
        break;
      case BidStatus.rejected:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[700]!;
        icon = Icons.cancel;
        break;
      case BidStatus.withdrawn:
        backgroundColor = Colors.grey[200]!;
        textColor = Colors.grey[700]!;
        icon = Icons.remove_circle;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 18,
            color: textColor,
          ),
          const SizedBox(width: 6),
          Text(
            status.displayName,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.w600,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBidAmount(BuildContext context, BidModel bid) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '\$');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Bid Amount',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.attach_money,
                    color: theme.colorScheme.onPrimaryContainer,
                    size: 32,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    currencyFormat.format(bid.amount),
                    style: theme.textTheme.headlineMedium?.copyWith(
                      color: theme.colorScheme.onPrimaryContainer,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeDetails(BuildContext context, BidModel bid) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy');
    final timeFormat = DateFormat('HH:mm');

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Schedule Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildTimeCard(
                    context,
                    'Estimated Pickup',
                    bid.estimatedPickupTime,
                    Icons.schedule,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildTimeCard(
                    context,
                    'Estimated Delivery',
                    bid.estimatedDeliveryTime,
                    Icons.flag,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Duration: ${_calculateDuration(bid.estimatedPickupTime, bid.estimatedDeliveryTime)}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeCard(BuildContext context, String label, DateTime dateTime,
      IconData icon, Color color) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy');
    final timeFormat = DateFormat('HH:mm');

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: color.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Text(
                label,
                style: theme.textTheme.labelSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            dateFormat.format(dateTime),
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            timeFormat.format(dateTime),
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  String _calculateDuration(DateTime start, DateTime end) {
    final duration = end.difference(start);
    final days = duration.inDays;
    final hours = duration.inHours % 24;

    if (days > 0) {
      return '${days}d ${hours}h';
    } else {
      return '${hours}h';
    }
  }

  Widget _buildProposal(BuildContext context, BidModel bid) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.description,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Proposal',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Text(
                bid.proposal!,
                style: theme.textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotes(BuildContext context, BidModel bid) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Additional Notes',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              child: Text(
                bid.notes!,
                style: theme.textTheme.bodyMedium,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyInfo(BuildContext context, BidModel bid) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.business,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Company Information',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              context,
              'Company Name',
              bid.companyName ?? 'Unknown Company',
              Icons.business_center,
            ),
            const SizedBox(height: 8),
            _buildInfoRow(
              context,
              'Company ID',
              '#${bid.companyId}',
              Icons.tag,
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  context.push('/companies/detail/${bid.companyId}');
                },
                icon: const Icon(Icons.visibility),
                label: const Text('View Company Profile'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(
      BuildContext context, String label, String value, IconData icon) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: theme.colorScheme.onSurfaceVariant,
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadInfo(BuildContext context, BidModel bid) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy');

    // Load the load details when this widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadLoadDetails(bid.loadId);
    });

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.local_shipping,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Load Information',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_isLoadingLoadDetails)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Basic load info (always available from bid)
            if (bid.loadTitle != null) ...[
              _buildInfoRow(
                context,
                'Load Title',
                bid.loadTitle!,
                Icons.title,
              ),
              const SizedBox(height: 8),
            ],
            _buildInfoRow(
              context,
              'Load ID',
              '#${bid.loadId}',
              Icons.tag,
            ),

            // Detailed load info (from loaded details)
            if (_loadDetails != null) ...[
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),

              // Cargo Information
              _buildLoadSection(
                context,
                'Cargo Details',
                Icons.inventory,
                [
                  _buildInfoRow(context, 'Cargo Type', _loadDetails!.cargoType,
                      Icons.category),
                  _buildInfoRow(
                      context,
                      'Weight',
                      '${_loadDetails!.weight} ${_loadDetails!.weightUnit}',
                      Icons.scale),
                  if (_loadDetails!.volume != null)
                    _buildInfoRow(
                        context,
                        'Volume',
                        '${_loadDetails!.volume} ${_loadDetails!.volumeUnit}',
                        Icons.view_in_ar),
                  if (_loadDetails!.description != null)
                    _buildInfoRow(context, 'Description',
                        _loadDetails!.description!, Icons.description),
                ],
              ),

              const SizedBox(height: 16),

              // Route Information
              _buildLoadSection(
                context,
                'Route Details',
                Icons.route,
                [
                  _buildInfoRow(context, 'Pickup Location',
                      _loadDetails!.pickupLocation, Icons.location_on),
                  _buildInfoRow(
                      context,
                      'Pickup Date',
                      dateFormat.format(_loadDetails!.pickupDate),
                      Icons.schedule),
                  _buildInfoRow(context, 'Delivery Location',
                      _loadDetails!.deliveryLocation, Icons.flag),
                  _buildInfoRow(
                      context,
                      'Delivery Date',
                      dateFormat.format(_loadDetails!.deliveryDate),
                      Icons.schedule),
                  if (_loadDetails!.estimatedDistance != null)
                    _buildInfoRow(
                        context,
                        'Distance',
                        '${_loadDetails!.estimatedDistance} ${_loadDetails!.distanceUnit}',
                        Icons.straighten),
                ],
              ),

              const SizedBox(height: 16),

              // Payment Information
              if (_loadDetails!.paymentRate != null ||
                  _loadDetails!.estimatedValue != null)
                _buildLoadSection(
                  context,
                  'Payment Details',
                  Icons.payment,
                  [
                    if (_loadDetails!.paymentRate != null)
                      _buildInfoRow(
                          context,
                          'Payment Rate',
                          '\$${_loadDetails!.paymentRate!.toStringAsFixed(2)} per ${_loadDetails!.paymentUnit}',
                          Icons.attach_money),
                    if (_loadDetails!.estimatedValue != null)
                      _buildInfoRow(
                          context,
                          'Estimated Value',
                          '\$${_loadDetails!.estimatedValue!.toStringAsFixed(2)}',
                          Icons.monetization_on),
                    _buildInfoRow(
                        context,
                        'Payment Type',
                        _loadDetails!.paymentType?.name ?? 'Not specified',
                        Icons.payment),
                  ],
                ),

              const SizedBox(height: 16),

              // Requirements
              _buildLoadSection(
                context,
                'Requirements',
                Icons.checklist,
                [
                  _buildInfoRow(
                      context,
                      'Priority',
                      _loadDetails!.priority.name.toUpperCase(),
                      Icons.priority_high),
                  _buildInfoRow(context, 'Verified Load',
                      _loadDetails!.isVerified ? 'Yes' : 'No', Icons.verified),
                  _buildInfoRow(
                      context,
                      'Insurance Required',
                      _loadDetails!.requiresInsurance ? 'Yes' : 'No',
                      Icons.security),
                  _buildInfoRow(
                      context,
                      'Special Handling',
                      _loadDetails!.requiresSpecialHandling ? 'Yes' : 'No',
                      Icons.warning),
                  if (_loadDetails!.specialInstructions != null)
                    _buildInfoRow(context, 'Special Instructions',
                        _loadDetails!.specialInstructions!, Icons.info),
                ],
              ),
            ],

            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  context.push('/loads/${bid.loadId}');
                },
                icon: const Icon(Icons.visibility),
                label: const Text('View Full Load Details'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadSection(BuildContext context, String title, IconData icon,
      List<Widget> children) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 18, color: theme.colorScheme.primary),
            const SizedBox(width: 8),
            Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Column(
            children: children
                .map((child) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: child,
                    ))
                .toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildTimestamps(BuildContext context, BidModel bid) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Timeline',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (bid.createdAt != null) ...[
              _buildTimestampRow(
                context,
                'Created',
                bid.createdAt!,
                Icons.add_circle,
                Colors.blue,
              ),
              const SizedBox(height: 8),
            ],
            if (bid.updatedAt != null) ...[
              _buildTimestampRow(
                context,
                'Last Updated',
                bid.updatedAt!,
                Icons.edit,
                Colors.orange,
              ),
              const SizedBox(height: 8),
            ],
            if (bid.acceptedAt != null) ...[
              _buildTimestampRow(
                context,
                'Accepted',
                bid.acceptedAt!,
                Icons.check_circle,
                Colors.green,
              ),
              const SizedBox(height: 8),
            ],
            if (bid.rejectedAt != null) ...[
              _buildTimestampRow(
                context,
                'Rejected',
                bid.rejectedAt!,
                Icons.cancel,
                Colors.red,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimestampRow(BuildContext context, String label,
      DateTime dateTime, IconData icon, Color color) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy HH:mm');

    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
        Expanded(
          child: Text(
            dateFormat.format(dateTime),
            style: theme.textTheme.bodyMedium,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, BidModel bid) {
    return FutureBuilder<UserModel?>(
      future: context.read<AuthService>().getCurrentUser(),
      builder: (context, snapshot) {
        final currentUser = snapshot.data;
        if (currentUser == null) {
          return const SizedBox.shrink();
        }

        final isTransporter = currentUser.role == UserRole.transporter.name;
        final isClient = currentUser.role == UserRole.client.name;
        final isMyBid =
            isTransporter && bid.companyId.toString() == currentUser.id;
        final canManageBid =
            isClient; // Clients can accept/reject bids on their loads

        if (!isMyBid && !canManageBid) {
          return const SizedBox.shrink();
        }

        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Actions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 16),
                if (isMyBid && bid.status == BidStatus.pending) ...[
                  // Transporter actions for their own pending bids
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _withdrawBid(context, bid),
                          icon: const Icon(Icons.remove_circle),
                          label: const Text('Withdraw'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red,
                            side: const BorderSide(color: Colors.red),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _editBid(context, bid),
                          icon: const Icon(Icons.edit),
                          label: const Text('Edit'),
                        ),
                      ),
                    ],
                  ),
                ] else if (canManageBid && bid.status == BidStatus.pending) ...[
                  // Client actions for pending bids on their loads
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _rejectBid(context, bid),
                          icon: const Icon(Icons.close),
                          label: const Text('Reject'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red,
                            side: const BorderSide(color: Colors.red),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () => _acceptBid(context, bid),
                          icon: const Icon(Icons.check),
                          label: const Text('Accept'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  // Status information for non-actionable bids
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _getStatusColor(bid.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: _getStatusColor(bid.status).withOpacity(0.3),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          _getStatusIcon(bid.status),
                          color: _getStatusColor(bid.status),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Bid ${bid.status.name.toUpperCase()}',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: _getStatusColor(bid.status),
                                    fontWeight: FontWeight.w600,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getStatusColor(BidStatus status) {
    switch (status) {
      case BidStatus.pending:
        return Colors.orange;
      case BidStatus.accepted:
        return Colors.green;
      case BidStatus.rejected:
        return Colors.red;
      case BidStatus.withdrawn:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(BidStatus status) {
    switch (status) {
      case BidStatus.pending:
        return Icons.schedule;
      case BidStatus.accepted:
        return Icons.check_circle;
      case BidStatus.rejected:
        return Icons.cancel;
      case BidStatus.withdrawn:
        return Icons.remove_circle;
    }
  }

  // Action methods
  void _withdrawBid(BuildContext context, BidModel bid) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Withdraw Bid'),
        content: const Text(
            'Are you sure you want to withdraw this bid? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<BidBloc>().add(BidWithdrawRequested(bidId: bid.id!));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Withdraw'),
          ),
        ],
      ),
    );
  }

  void _editBid(BuildContext context, BidModel bid) {
    // Navigate to edit bid screen
    context.push('/bids/${bid.id}/edit');
  }

  void _acceptBid(BuildContext context, BidModel bid) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Accept Bid'),
        content: Text(
            'Are you sure you want to accept this bid from ${bid.companyName}?\n\nThis will assign the load to this company and reject all other pending bids.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Show success message and navigate back
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content:
                      Text('Bid from ${bid.companyName} accepted successfully'),
                  backgroundColor: Colors.green,
                ),
              );
              context.pop(true); // Return true to indicate success
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Accept'),
          ),
        ],
      ),
    );
  }

  void _rejectBid(BuildContext context, BidModel bid) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Bid'),
        content: Text(
            'Are you sure you want to reject this bid from ${bid.companyName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Show success message and navigate back
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Bid from ${bid.companyName} rejected'),
                  backgroundColor: Colors.orange,
                ),
              );
              context.pop(true); // Return true to indicate success
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }
}
