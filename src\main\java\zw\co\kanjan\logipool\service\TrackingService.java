package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.tracking.TrackingDto;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.LoadTracking;
import zw.co.kanjan.logipool.entity.LiveTrackingSession;
import zw.co.kanjan.logipool.entity.Role;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.TrackingMapper;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.LoadTrackingRepository;
import zw.co.kanjan.logipool.repository.LiveTrackingSessionRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class TrackingService {

    private final LoadTrackingRepository trackingRepository;
    private final LoadRepository loadRepository;
    private final UserRepository userRepository;
    private final LiveTrackingSessionRepository liveTrackingSessionRepository;
    private final TrackingMapper trackingMapper;
    private final NotificationService notificationService;
    private final RealTimeNotificationService realTimeNotificationService;

    public TrackingDto.TrackingResponse updateTracking(TrackingDto.TrackingUpdateRequest request, String username) {
        log.info("Updating tracking for load: {} by user: {}", request.getLoadId(), username);

        // Validate load exists
        Load load = loadRepository.findById(request.getLoadId())
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + request.getLoadId()));

        // Validate user exists
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));

        // Validate user has permission to update tracking
        validateTrackingPermission(load, user);

        // Validate status transition
        validateStatusTransition(load, request.getStatus());

        // Create tracking entry
        LoadTracking tracking = trackingMapper.toEntity(request);
        tracking.setLoad(load);
        tracking.setUpdatedBy(user);
        tracking.setTimestamp(LocalDateTime.now());

        // Save tracking entry
        LoadTracking savedTracking = trackingRepository.save(tracking);

        // Update load status if necessary
        updateLoadStatusFromTracking(load, request.getStatus());

        // Send notifications
        sendTrackingNotifications(load, savedTracking);

        // Send real-time updates
        sendRealTimeTrackingUpdate(load, savedTracking);

        log.info("Tracking updated successfully for load: {}", request.getLoadId());
        return trackingMapper.toResponse(savedTracking);
    }

    public TrackingDto.TrackingResponse updateLocation(TrackingDto.LocationUpdateRequest request, String username) {
        log.info("Updating location for load: {} by user: {}", request.getLoadId(), username);

        Load load = loadRepository.findById(request.getLoadId())
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + request.getLoadId()));

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));

        validateTrackingPermission(load, user);

        // Get current status or default to IN_TRANSIT
        LoadTracking.TrackingStatus currentStatus = getCurrentTrackingStatus(load)
                .orElse(LoadTracking.TrackingStatus.IN_TRANSIT_TO_DELIVERY);

        // Create tracking entry with location update
        LoadTracking tracking = LoadTracking.builder()
                .load(load)
                .location(request.getLocation() != null ? request.getLocation() : "GPS Location")
                .latitude(request.getLatitude())
                .longitude(request.getLongitude())
                .status(currentStatus)
                .isAutomated(true)
                .updatedBy(user)
                .timestamp(LocalDateTime.now())
                .build();

        LoadTracking savedTracking = trackingRepository.save(tracking);

        // Update live tracking session if active
        updateLiveTrackingSession(load.getId(), user, request);

        // Send real-time location update
        sendRealTimeLocationUpdate(request, currentStatus);

        return trackingMapper.toResponse(savedTracking);
    }

    @Transactional(readOnly = true)
    public TrackingDto.LoadTrackingHistory getLoadTrackingHistory(Long loadId) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        List<LoadTracking> trackingHistory = trackingRepository.findByLoadOrderByTimestampDesc(load);
        
        if (trackingHistory.isEmpty()) {
            throw new ResourceNotFoundException("No tracking history found for load: " + loadId);
        }

        LoadTracking latestTracking = trackingHistory.get(0);
        TrackingDto.LoadTrackingHistory history = trackingMapper.toTrackingHistory(latestTracking);
        history.setTrackingHistory(trackingMapper.toResponseList(trackingHistory));
        history.setTotalEntries(trackingHistory.size());

        return history;
    }

    @Transactional(readOnly = true)
    public Page<TrackingDto.TrackingResponse> getTrackingHistory(Long loadId, Pageable pageable) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        Page<LoadTracking> trackingPage = trackingRepository.findByLoadOrderByTimestampDesc(load, pageable);
        return trackingPage.map(trackingMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Optional<TrackingDto.TrackingResponse> getLatestTracking(Long loadId) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        return trackingRepository.findLatestByLoad(load)
                .map(trackingMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public List<TrackingDto.RealTimeLocation> getActiveLoadsLocation() {
        try {
            log.info("Fetching active loads in transit...");
            List<Load> activeLoads;

            try {
                activeLoads = trackingRepository.findLoadsInTransit();
                log.info("Found {} loads in transit using JPQL query", activeLoads.size());
            } catch (Exception e) {
                log.warn("JPQL query failed, trying native query: {}", e.getMessage());
                activeLoads = trackingRepository.findLoadsInTransitNative();
                log.info("Found {} loads in transit using native query", activeLoads.size());
            }

            List<TrackingDto.RealTimeLocation> locations = activeLoads.stream()
                    .map(load -> {
                        try {
                            return getLoadCurrentLocation(load);
                        } catch (Exception e) {
                            log.error("Error getting current location for load {}: {}", load.getId(), e.getMessage(), e);
                            return Optional.<TrackingDto.RealTimeLocation>empty();
                        }
                    })
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .toList();

            log.info("Returning {} active locations", locations.size());
            return locations;
        } catch (Exception e) {
            log.error("Error fetching active loads location: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to fetch active loads location", e);
        }
    }

    @Transactional(readOnly = true)
    public TrackingDto.TrackingStatistics getTrackingStatistics() {
        return TrackingDto.TrackingStatistics.builder()
                .totalLoads(trackingRepository.countLoadsByStatus(LoadTracking.TrackingStatus.LOAD_POSTED))
                .loadsInTransitToPickup(trackingRepository.countLoadsByStatus(LoadTracking.TrackingStatus.IN_TRANSIT_TO_PICKUP))
                .loadsInTransitToDelivery(trackingRepository.countLoadsByStatus(LoadTracking.TrackingStatus.IN_TRANSIT_TO_DELIVERY))
                .loadsDelivered(trackingRepository.countLoadsByStatus(LoadTracking.TrackingStatus.DELIVERED))
                .loadsDelayed(trackingRepository.countLoadsByStatus(LoadTracking.TrackingStatus.DELAYED))
                .loadsWithIssues(trackingRepository.countLoadsByStatus(LoadTracking.TrackingStatus.ISSUE_REPORTED))
                .generatedAt(LocalDateTime.now())
                .build();
    }

    public TrackingDto.TrackingResponse confirmDelivery(TrackingDto.DeliveryConfirmationRequest request, String username) {
        log.info("Confirming delivery for load: {} by user: {}", request.getLoadId(), username);

        Load load = loadRepository.findById(request.getLoadId())
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + request.getLoadId()));

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));

        validateTrackingPermission(load, user);

        // Create delivery confirmation tracking entry
        LoadTracking tracking = LoadTracking.builder()
                .load(load)
                .location(request.getDeliveryLocation())
                .latitude(request.getDeliveryLatitude())
                .longitude(request.getDeliveryLongitude())
                .status(LoadTracking.TrackingStatus.DELIVERED)
                .notes(buildDeliveryNotes(request))
                .isAutomated(false)
                .updatedBy(user)
                .timestamp(LocalDateTime.now())
                .build();

        LoadTracking savedTracking = trackingRepository.save(tracking);

        // Update load status to delivered
        load.setStatus(Load.LoadStatus.DELIVERED);
        loadRepository.save(load);

        // Send delivery notifications
        sendDeliveryNotifications(load, savedTracking);

        log.info("Delivery confirmed for load: {}", request.getLoadId());
        return trackingMapper.toResponse(savedTracking);
    }

    @Transactional(readOnly = true)
    public List<TrackingDto.TrackingResponse> getDelayedLoads() {
        List<Load> delayedLoads = trackingRepository.findDelayedLoads();
        
        return delayedLoads.stream()
                .map(load -> trackingRepository.findLatestByLoad(load))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(trackingMapper::toResponse)
                .toList();
    }

    @Transactional(readOnly = true)
    public List<TrackingDto.TrackingResponse> getLoadsWithIssues() {
        List<Load> loadsWithIssues = trackingRepository.findLoadsWithIssues();
        
        return loadsWithIssues.stream()
                .map(load -> trackingRepository.findLatestByLoad(load))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(trackingMapper::toResponse)
                .toList();
    }

    @Transactional(readOnly = true)
    public List<TrackingDto.TrackingResponse> getCompanyLoadsTracking(Long companyId) {
        List<Load> companyLoads = trackingRepository.findLoadsByAssignedCompany(companyId);
        
        return companyLoads.stream()
                .map(load -> trackingRepository.findLatestByLoad(load))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(trackingMapper::toResponse)
                .toList();
    }

    // Private helper methods
    private void validateTrackingPermission(Load load, User user) {
        boolean hasPermission = false;

        // Admin can update any tracking
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            hasPermission = true;
        }
        // Client can update their own loads
        else if (load.getClient().getId().equals(user.getId())) {
            hasPermission = true;
        }
        // Transporter can update assigned loads
        else if (load.getAssignedCompany() != null && 
                 load.getAssignedCompany().getUser().getId().equals(user.getId())) {
            hasPermission = true;
        }

        if (!hasPermission) {
            throw new BusinessException("User does not have permission to update tracking for this load");
        }
    }

    private void validateStatusTransition(Load load, LoadTracking.TrackingStatus newStatus) {
        Optional<LoadTracking> latestTracking = trackingRepository.findLatestByLoad(load);
        
        if (latestTracking.isPresent()) {
            LoadTracking.TrackingStatus currentStatus = latestTracking.get().getStatus();
            
            // Add business rules for valid status transitions
            if (currentStatus == LoadTracking.TrackingStatus.DELIVERED && 
                newStatus != LoadTracking.TrackingStatus.DELIVERED) {
                throw new BusinessException("Cannot change status after delivery is confirmed");
            }
        }
    }

    private Optional<LoadTracking.TrackingStatus> getCurrentTrackingStatus(Load load) {
        return trackingRepository.findLatestByLoad(load)
                .map(LoadTracking::getStatus);
    }

    private void updateLoadStatusFromTracking(Load load, LoadTracking.TrackingStatus trackingStatus) {
        Load.LoadStatus newLoadStatus = switch (trackingStatus) {
            case DELIVERED -> Load.LoadStatus.DELIVERED;
            case IN_TRANSIT_TO_PICKUP, IN_TRANSIT_TO_DELIVERY, 
                 ARRIVED_AT_PICKUP, ARRIVED_AT_DELIVERY,
                 LOADING_IN_PROGRESS, LOADED, UNLOADING_IN_PROGRESS -> Load.LoadStatus.IN_TRANSIT;
            default -> load.getStatus(); // Keep current status
        };

        if (newLoadStatus != load.getStatus()) {
            load.setStatus(newLoadStatus);
            loadRepository.save(load);
        }
    }

    private void sendTrackingNotifications(Load load, LoadTracking tracking) {
        try {
            notificationService.sendLoadStatusUpdateNotification(load, tracking.getStatus().toString());
        } catch (Exception e) {
            log.error("Failed to send tracking notification for load: {}", load.getId(), e);
        }
    }

    private void sendRealTimeTrackingUpdate(Load load, LoadTracking tracking) {
        try {
            TrackingDto.TrackingResponse response = trackingMapper.toResponse(tracking);
            realTimeNotificationService.sendNotificationToUser(
                load.getClient().getId(),
                buildTrackingNotification(response)
            );

            // Also notify assigned company
            if (load.getAssignedCompany() != null) {
                realTimeNotificationService.sendNotificationToUser(
                    load.getAssignedCompany().getUser().getId(),
                    buildTrackingNotification(response)
                );
            }
        } catch (Exception e) {
            log.error("Failed to send real-time tracking update for load: {}", load.getId(), e);
        }
    }

    private void sendRealTimeLocationUpdate(TrackingDto.LocationUpdateRequest request, 
                                          LoadTracking.TrackingStatus status) {
        try {
            TrackingDto.RealTimeLocation location = trackingMapper.toRealTimeLocation(request);
            location.setStatus(status);
            
            // Broadcast location update to subscribers
            realTimeNotificationService.broadcastNotification(
                buildLocationNotification(location)
            );
        } catch (Exception e) {
            log.error("Failed to send real-time location update for load: {}", request.getLoadId(), e);
        }
    }

    private void sendDeliveryNotifications(Load load, LoadTracking tracking) {
        try {
            notificationService.sendLoadStatusUpdateNotification(load, "DELIVERED");
            
            // Send delivery confirmation to client
            realTimeNotificationService.sendNotificationToUser(
                load.getClient().getId(),
                buildDeliveryNotification(load, tracking)
            );
        } catch (Exception e) {
            log.error("Failed to send delivery notification for load: {}", load.getId(), e);
        }
    }

    private Optional<TrackingDto.RealTimeLocation> getLoadCurrentLocation(Load load) {
        return trackingRepository.findLatestByLoad(load)
                .filter(tracking -> tracking.getLatitude() != null && tracking.getLongitude() != null)
                .map(tracking -> TrackingDto.RealTimeLocation.builder()
                        .loadId(load.getId())
                        .latitude(tracking.getLatitude())
                        .longitude(tracking.getLongitude())
                        .timestamp(tracking.getTimestamp())
                        .status(tracking.getStatus())
                        .build());
    }

    private String buildDeliveryNotes(TrackingDto.DeliveryConfirmationRequest request) {
        StringBuilder notes = new StringBuilder();
        if (request.getDeliveryNotes() != null) {
            notes.append(request.getDeliveryNotes());
        }
        if (request.getRecipientName() != null) {
            notes.append(" | Received by: ").append(request.getRecipientName());
        }
        return notes.toString();
    }

    private zw.co.kanjan.logipool.dto.NotificationDto buildTrackingNotification(TrackingDto.TrackingResponse tracking) {
        return zw.co.kanjan.logipool.dto.NotificationDto.builder()
                .title("Tracking Update")
                .message(String.format("Load '%s' status updated to %s", tracking.getLoadTitle(), tracking.getStatus()))
                .type("TRACKING_UPDATE")
                .timestamp(LocalDateTime.now())
                .build();
    }

    private zw.co.kanjan.logipool.dto.NotificationDto buildLocationNotification(TrackingDto.RealTimeLocation location) {
        return zw.co.kanjan.logipool.dto.NotificationDto.builder()
                .title("Location Update")
                .message(String.format("Load %d location updated", location.getLoadId()))
                .type("LOCATION_UPDATE")
                .timestamp(LocalDateTime.now())
                .build();
    }

    private zw.co.kanjan.logipool.dto.NotificationDto buildDeliveryNotification(Load load, LoadTracking tracking) {
        return zw.co.kanjan.logipool.dto.NotificationDto.builder()
                .title("Delivery Confirmed")
                .message(String.format("Load '%s' has been delivered successfully", load.getTitle()))
                .type("DELIVERY_CONFIRMED")
                .timestamp(LocalDateTime.now())
                .build();
    }

    // Live Tracking Methods

    public TrackingDto.LiveTrackingResponse startLiveTracking(Long loadId, String username) {
        log.info("Starting live tracking for load: {} by user: {}", loadId, username);

        // Validate load exists
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        // Validate user exists
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));

        // Validate user has permission to start live tracking
        validateLiveTrackingPermission(load, user);

        // Check if there's already an active session for this load
        Optional<LiveTrackingSession> existingSession = liveTrackingSessionRepository.findActiveSessionByLoadId(loadId);
        if (existingSession.isPresent()) {
            LiveTrackingSession session = existingSession.get();
            if (!session.getTrackingUser().getId().equals(user.getId())) {
                throw new BusinessException(
                    String.format("Load is already being tracked by %s. Only one device can track a load at a time.",
                        session.getTrackingUser().getFullName())
                );
            } else {
                // User is trying to start tracking again - return existing session
                return TrackingDto.LiveTrackingResponse.builder()
                        .loadId(loadId)
                        .isActive(true)
                        .trackingDevice(user.getUsername())
                        .startedAt(session.getStartedAt())
                        .message("Live tracking is already active for this load")
                        .build();
            }
        }

        // End any other active sessions for this user
        liveTrackingSessionRepository.endActiveSessionsForUser(
            user.getId(),
            LiveTrackingSession.SessionStatus.ENDED_BY_SYSTEM,
            "Started tracking another load",
            LocalDateTime.now()
        );

        // Create new live tracking session
        LiveTrackingSession newSession = LiveTrackingSession.builder()
                .load(load)
                .trackingUser(user)
                .deviceIdentifier(user.getUsername())
                .isActive(true)
                .startedAt(LocalDateTime.now())
                .build();

        liveTrackingSessionRepository.save(newSession);

        log.info("Live tracking session started for load: {} by user: {}", loadId, username);

        return TrackingDto.LiveTrackingResponse.builder()
                .loadId(loadId)
                .isActive(true)
                .trackingDevice(user.getUsername())
                .startedAt(newSession.getStartedAt())
                .message("Live tracking started successfully")
                .build();
    }

    public TrackingDto.LiveTrackingResponse stopLiveTracking(Long loadId, String username) {
        log.info("Stopping live tracking for load: {} by user: {}", loadId, username);

        // Validate user exists
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));

        // Find active session for this load
        Optional<LiveTrackingSession> sessionOpt = liveTrackingSessionRepository.findActiveSessionByLoadId(loadId);
        if (sessionOpt.isEmpty()) {
            throw new BusinessException("No active live tracking session found for this load");
        }

        LiveTrackingSession session = sessionOpt.get();

        // Check if user has permission to stop tracking
        if (!session.getTrackingUser().getId().equals(user.getId()) && !isAdmin(user)) {
            throw new BusinessException("You can only stop tracking sessions that you started");
        }

        // End the session
        session.endSession(LiveTrackingSession.SessionStatus.ENDED_BY_USER, "Stopped by user");
        liveTrackingSessionRepository.save(session);

        log.info("Live tracking session stopped for load: {} by user: {}", loadId, username);

        return TrackingDto.LiveTrackingResponse.builder()
                .loadId(loadId)
                .isActive(false)
                .trackingDevice(session.getTrackingUser().getUsername())
                .startedAt(session.getStartedAt())
                .message("Live tracking stopped successfully")
                .build();
    }

    public TrackingDto.LiveTrackingStatus getLiveTrackingStatus(Long loadId) {
        Optional<LiveTrackingSession> sessionOpt = liveTrackingSessionRepository.findActiveSessionByLoadId(loadId);

        if (sessionOpt.isEmpty()) {
            Load load = loadRepository.findById(loadId)
                    .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

            return TrackingDto.LiveTrackingStatus.builder()
                    .loadId(loadId)
                    .loadTitle(load.getTitle())
                    .isActive(false)
                    .build();
        }

        LiveTrackingSession session = sessionOpt.get();
        return TrackingDto.LiveTrackingStatus.builder()
                .loadId(loadId)
                .loadTitle(session.getLoad().getTitle())
                .isActive(session.getIsActive())
                .trackingDevice(session.getDeviceIdentifier())
                .trackingUserName(session.getTrackingUser().getFullName())
                .startedAt(session.getStartedAt())
                .lastUpdateAt(session.getLastUpdateAt())
                .currentLatitude(session.getCurrentLatitude())
                .currentLongitude(session.getCurrentLongitude())
                .currentLocation(session.getCurrentLocation())
                .build();
    }

    public List<TrackingDto.LiveTrackingStatus> getActiveLiveTrackingSessions() {
        List<LiveTrackingSession> activeSessions = liveTrackingSessionRepository.findAllActiveSessions();

        return activeSessions.stream()
                .map(session -> TrackingDto.LiveTrackingStatus.builder()
                        .loadId(session.getLoad().getId())
                        .loadTitle(session.getLoad().getTitle())
                        .isActive(session.getIsActive())
                        .trackingDevice(session.getDeviceIdentifier())
                        .trackingUserName(session.getTrackingUser().getFullName())
                        .startedAt(session.getStartedAt())
                        .lastUpdateAt(session.getLastUpdateAt())
                        .currentLatitude(session.getCurrentLatitude())
                        .currentLongitude(session.getCurrentLongitude())
                        .currentLocation(session.getCurrentLocation())
                        .build())
                .toList();
    }

    private void validateLiveTrackingPermission(Load load, User user) {
        // Check if load is in a trackable status
        if (load.getStatus() != Load.LoadStatus.ASSIGNED && load.getStatus() != Load.LoadStatus.IN_TRANSIT) {
            throw new BusinessException("Live tracking is only available for loads that are assigned or in transit");
        }

        // Check if user is from the assigned company or is admin
        if (isAdmin(user)) {
            return; // Admins can track any load
        }

        if (load.getAssignedCompany() == null) {
            throw new BusinessException("Load is not assigned to any company");
        }

        if (!load.getAssignedCompany().getId().equals(user.getCompany().getId())) {
            throw new BusinessException("You can only track loads assigned to your company");
        }
    }

    private boolean isAdmin(User user) {
        return user.getRoles().stream()
                .anyMatch(role -> role.getName().equals(Role.RoleName.ADMIN));
    }

    private void updateLiveTrackingSession(Long loadId, User user, TrackingDto.LocationUpdateRequest request) {
        Optional<LiveTrackingSession> sessionOpt = liveTrackingSessionRepository.findActiveSessionByLoadId(loadId);

        if (sessionOpt.isPresent()) {
            LiveTrackingSession session = sessionOpt.get();

            // Only update if the tracking user matches the user making the location update
            if (session.getTrackingUser().getId().equals(user.getId())) {
                session.updateLocation(
                    request.getLatitude(),
                    request.getLongitude(),
                    request.getLocation()
                );
                liveTrackingSessionRepository.save(session);
                log.debug("Updated live tracking session for load: {} with new location", loadId);
            }
        }
    }
}
