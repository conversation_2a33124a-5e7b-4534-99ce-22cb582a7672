import 'package:flutter/material.dart';
import '../../../shared/models/notification_model.dart';

class NotificationFilterSheet extends StatefulWidget {
  final NotificationType? selectedType;
  final NotificationPriority? selectedPriority;
  final bool? selectedReadStatus;
  final Function(NotificationType?, NotificationPriority?, bool?) onApplyFilter;
  final VoidCallback onClearFilter;

  const NotificationFilterSheet({
    super.key,
    this.selectedType,
    this.selectedPriority,
    this.selectedReadStatus,
    required this.onApplyFilter,
    required this.onClearFilter,
  });

  @override
  State<NotificationFilterSheet> createState() => _NotificationFilterSheetState();
}

class _NotificationFilterSheetState extends State<NotificationFilterSheet> {
  NotificationType? _selectedType;
  NotificationPriority? _selectedPriority;
  bool? _selectedReadStatus;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.selectedType;
    _selectedPriority = widget.selectedPriority;
    _selectedReadStatus = widget.selectedReadStatus;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context),
          const SizedBox(height: 24),
          _buildTypeFilter(context),
          const SizedBox(height: 24),
          _buildPriorityFilter(context),
          const SizedBox(height: 24),
          _buildReadStatusFilter(context),
          const SizedBox(height: 32),
          _buildActionButtons(context),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Text(
          'Filter Notifications',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.close),
        ),
      ],
    );
  }

  Widget _buildTypeFilter(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notification Type',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: NotificationType.values.map((type) {
            final isSelected = _selectedType == type;
            return FilterChip(
              label: Text(type.displayName),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedType = selected ? type : null;
                });
              },
              avatar: Icon(
                _getTypeIcon(type),
                size: 16,
                color: isSelected 
                    ? Theme.of(context).colorScheme.onPrimary
                    : _getTypeColor(type),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildPriorityFilter(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Priority Level',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: NotificationPriority.values.map((priority) {
            final isSelected = _selectedPriority == priority;
            return FilterChip(
              label: Text(priority.displayName),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedPriority = selected ? priority : null;
                });
              },
              avatar: Icon(
                _getPriorityIcon(priority),
                size: 16,
                color: isSelected 
                    ? Theme.of(context).colorScheme.onPrimary
                    : _getPriorityColor(priority),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildReadStatusFilter(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Read Status',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: FilterChip(
                label: const Text('Unread'),
                selected: _selectedReadStatus == false,
                onSelected: (selected) {
                  setState(() {
                    _selectedReadStatus = selected ? false : null;
                  });
                },
                avatar: Icon(
                  Icons.mark_email_unread,
                  size: 16,
                  color: _selectedReadStatus == false
                      ? Theme.of(context).colorScheme.onPrimary
                      : Theme.of(context).primaryColor,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: FilterChip(
                label: const Text('Read'),
                selected: _selectedReadStatus == true,
                onSelected: (selected) {
                  setState(() {
                    _selectedReadStatus = selected ? true : null;
                  });
                },
                avatar: Icon(
                  Icons.mark_email_read,
                  size: 16,
                  color: _selectedReadStatus == true
                      ? Theme.of(context).colorScheme.onPrimary
                      : Colors.grey,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              setState(() {
                _selectedType = null;
                _selectedPriority = null;
                _selectedReadStatus = null;
              });
              widget.onClearFilter();
              Navigator.pop(context);
            },
            child: const Text('Clear All'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              widget.onApplyFilter(_selectedType, _selectedPriority, _selectedReadStatus);
              Navigator.pop(context);
            },
            child: const Text('Apply Filters'),
          ),
        ),
      ],
    );
  }

  IconData _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.loadPosted:
        return Icons.local_shipping;
      case NotificationType.bidReceived:
        return Icons.gavel;
      case NotificationType.bidAccepted:
        return Icons.check_circle;
      case NotificationType.bidRejected:
        return Icons.cancel;
      case NotificationType.loadStatusUpdate:
        return Icons.update;
      case NotificationType.paymentReceived:
        return Icons.payment;
      case NotificationType.documentRequired:
        return Icons.description;
      case NotificationType.systemAnnouncement:
        return Icons.announcement;
      case NotificationType.accountVerification:
        return Icons.verified_user;
      case NotificationType.securityAlert:
        return Icons.security;
      case NotificationType.trackingUpdate:
        return Icons.location_on;
    }
  }

  Color _getTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.loadPosted:
        return Colors.blue;
      case NotificationType.bidReceived:
        return Colors.orange;
      case NotificationType.bidAccepted:
        return Colors.green;
      case NotificationType.bidRejected:
        return Colors.red;
      case NotificationType.loadStatusUpdate:
        return Colors.purple;
      case NotificationType.paymentReceived:
        return Colors.teal;
      case NotificationType.documentRequired:
        return Colors.amber;
      case NotificationType.systemAnnouncement:
        return Colors.indigo;
      case NotificationType.accountVerification:
        return Colors.cyan;
      case NotificationType.securityAlert:
        return Colors.red;
      case NotificationType.trackingUpdate:
        return Colors.green;
    }
  }

  IconData _getPriorityIcon(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Icons.keyboard_arrow_down;
      case NotificationPriority.medium:
        return Icons.remove;
      case NotificationPriority.high:
        return Icons.keyboard_arrow_up;
      case NotificationPriority.urgent:
        return Icons.priority_high;
    }
  }

  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Colors.grey;
      case NotificationPriority.medium:
        return Colors.blue;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.urgent:
        return Colors.red;
    }
  }
}
