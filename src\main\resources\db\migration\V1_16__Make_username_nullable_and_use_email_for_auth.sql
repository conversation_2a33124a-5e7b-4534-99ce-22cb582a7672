-- Migration to make username nullable and transition to email-based authentication
-- This migration handles the transition from username-based to email-based authentication

-- Step 1: Make username column nullable
ALTER TABLE users ALTER COLUMN username DROP NOT NULL;

-- Step 2: For existing users without username, set username to email (temporary)
-- This ensures backward compatibility during the transition
UPDATE users 
SET username = email 
WHERE username IS NULL OR username = '';

-- Step 3: Add a comment to document the change
COMMENT ON COLUMN users.username IS 'Username field - now optional, email is used for authentication';
COMMENT ON COLUMN users.email IS 'Email field - primary identifier for authentication';

-- Step 4: Create an index on email for better performance (if not already exists)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email);

-- Step 5: Ensure email uniqueness constraint exists (should already exist but adding for safety)
-- This is a no-op if the constraint already exists
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'uk_users_email' 
        AND table_name = 'users'
    ) THEN
        ALTER TABLE users ADD CONSTRAINT uk_users_email UNIQUE (email);
    END IF;
END $$;
