#!/bin/bash

# LogiPool Production Deployment Script with <PERSON><PERSON><PERSON><PERSON>
# This script deploys the LogiPool application with <PERSON><PERSON><PERSON><PERSON> reverse proxy

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_COMPOSE_FILE="docker-compose.traefik.yml"
ENV_FILE=".env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if .env file exists
    if [[ ! -f "$PROJECT_ROOT/$ENV_FILE" ]]; then
        log_error "Environment file $ENV_FILE not found. Please copy .env.production to .env and configure it."
        exit 1
    fi
    
    # Check if domain name is configured
    if ! grep -q "DOMAIN_NAME=" "$PROJECT_ROOT/$ENV_FILE"; then
        log_error "DOMAIN_NAME not configured in $ENV_FILE"
        exit 1
    fi
    
    # Check if ACME email is configured
    if ! grep -q "ACME_EMAIL=" "$PROJECT_ROOT/$ENV_FILE"; then
        log_error "ACME_EMAIL not configured in $ENV_FILE"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Setup Docker networks
setup_networks() {
    log_info "Setting up Docker networks..."
    
    # Run the network setup script
    if [[ -f "$SCRIPT_DIR/setup-networks.sh" ]]; then
        bash "$SCRIPT_DIR/setup-networks.sh"
    else
        # Create traefik-public network if it doesn't exist
        if ! docker network ls | grep -q "traefik-public"; then
            docker network create traefik-public
            log_success "Created traefik-public network"
        fi
    fi
}

# Setup SSL certificates
setup_ssl() {
    log_info "Setting up SSL certificates..."
    
    # Run the SSL setup script
    if [[ -f "$SCRIPT_DIR/ssl-setup.sh" ]]; then
        bash "$SCRIPT_DIR/ssl-setup.sh" setup
    else
        log_warning "SSL setup script not found. Creating ACME storage manually..."
        mkdir -p "$PROJECT_ROOT/traefik_letsencrypt"
        echo '{}' > "$PROJECT_ROOT/traefik_letsencrypt/acme.json"
        chmod 600 "$PROJECT_ROOT/traefik_letsencrypt/acme.json"
    fi
}

# Backup current deployment
backup_current() {
    log_info "Creating backup of current deployment..."
    
    BACKUP_DIR="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup database if running
    if docker-compose -f "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE" ps postgres | grep -q "Up"; then
        log_info "Backing up database..."
        docker-compose -f "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE" exec -T postgres pg_dump -U logipool logipool_prod > "$BACKUP_DIR/database_backup.sql" || log_warning "Database backup failed"
    fi
    
    # Backup uploaded files
    if [[ -d "$PROJECT_ROOT/uploads" ]]; then
        log_info "Backing up uploaded files..."
        cp -r "$PROJECT_ROOT/uploads" "$BACKUP_DIR/" || log_warning "File backup failed"
    fi
    
    # Backup SSL certificates
    if [[ -f "$PROJECT_ROOT/traefik_letsencrypt/acme.json" ]]; then
        log_info "Backing up SSL certificates..."
        cp "$PROJECT_ROOT/traefik_letsencrypt/acme.json" "$BACKUP_DIR/" || log_warning "SSL backup failed"
    fi
    
    log_success "Backup created at $BACKUP_DIR"
}

# Build application
build_application() {
    log_info "Building application..."
    
    cd "$PROJECT_ROOT"
    
    # Pull latest images
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull
    
    # Build application image
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache logipool-backend
    
    log_success "Application built successfully"
}

# Deploy application
deploy_application() {
    log_info "Deploying application with Traefik..."
    
    cd "$PROJECT_ROOT"
    
    # Stop current containers
    log_info "Stopping current containers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down || log_warning "No containers to stop"
    
    # Start new containers
    log_info "Starting new containers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log_success "Application deployed successfully"
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    local domain_name
    domain_name=$(grep "DOMAIN_NAME=" "$PROJECT_ROOT/$ENV_FILE" | cut -d'=' -f2)
    
    local max_attempts=60  # Increased for SSL certificate generation
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        # Check if backend is healthy
        if docker-compose -f "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE" ps logipool-backend | grep -q "Up (healthy)"; then
            log_success "Backend health check passed"
            
            # Check if SSL certificate is working
            if curl -f -s "https://$domain_name/actuator/health" &> /dev/null; then
                log_success "HTTPS health check passed"
                return 0
            else
                log_info "HTTPS not ready yet, checking HTTP..."
                if curl -f -s "http://$domain_name/actuator/health" &> /dev/null; then
                    log_warning "HTTP works but HTTPS not ready yet (SSL certificate may still be generating)"
                fi
            fi
        fi
        
        log_info "Health check attempt $attempt/$max_attempts, retrying in 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Health check failed after $max_attempts attempts"
    return 1
}

# Show deployment status
show_status() {
    log_info "Deployment Status:"
    echo
    
    cd "$PROJECT_ROOT"
    docker-compose -f "$DOCKER_COMPOSE_FILE" ps
    
    echo
    log_info "Traefik Dashboard: http://localhost:8080 (if enabled)"
    
    local domain_name
    domain_name=$(grep "DOMAIN_NAME=" "$ENV_FILE" | cut -d'=' -f2 2>/dev/null || echo "not-configured")
    log_info "Application URL: https://$domain_name"
    log_info "pgAdmin URL: https://pgadmin.$domain_name"
}

# Cleanup old images
cleanup() {
    log_info "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove old images (keep last 3 versions)
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}" | \
    grep logipool | \
    tail -n +4 | \
    awk '{print $3}' | \
    xargs -r docker rmi || log_warning "No old images to remove"
    
    log_success "Cleanup completed"
}

# Rollback function
rollback() {
    log_warning "Rolling back to previous version..."
    
    cd "$PROJECT_ROOT"
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Here you would restore from backup
    # For now, we'll just restart the services
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log_warning "Rollback completed"
}

# Main deployment function
main() {
    log_info "Starting LogiPool production deployment with Traefik..."
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Run checks
    check_root
    check_prerequisites
    
    # Setup infrastructure
    setup_networks
    setup_ssl
    
    # Create backup
    backup_current
    
    # Build and deploy
    if build_application && deploy_application; then
        # Health check
        if health_check; then
            cleanup
            show_status
            log_success "Deployment completed successfully!"
        else
            log_error "Health check failed, rolling back..."
            rollback
            exit 1
        fi
    else
        log_error "Deployment failed, rolling back..."
        rollback
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "backup")
        backup_current
        ;;
    "build")
        build_application
        ;;
    "deploy")
        deploy_application
        ;;
    "health")
        health_check
        ;;
    "status")
        show_status
        ;;
    "cleanup")
        cleanup
        ;;
    "rollback")
        rollback
        ;;
    "setup-networks")
        setup_networks
        ;;
    "setup-ssl")
        setup_ssl
        ;;
    *)
        main
        ;;
esac
