import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/models/company_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/enums/user_role.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/widgets/unified_header.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/di/service_locator.dart';
import '../../../shared/services/company_member_service.dart';
import '../../auth/bloc/auth_bloc.dart';
import '../bloc/company_bloc.dart';
import '../bloc/company_member_bloc.dart';
import 'company_members_screen.dart';

class CompanyProfileScreen extends StatefulWidget {
  const CompanyProfileScreen({super.key});

  @override
  State<CompanyProfileScreen> createState() => _CompanyProfileScreenState();
}

class _CompanyProfileScreenState extends State<CompanyProfileScreen> {
  @override
  void initState() {
    super.initState();
    context.read<CompanyBloc>().add(const MyCompanyFetchRequested());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedHeader(
        title: 'Company Profile',
        onLogoutPressed: () => _showLogoutDialog(),
        actions: [
          FutureBuilder<UserModel?>(
            future: context.read<AuthService>().getCurrentUser(),
            builder: (context, snapshot) {
              final user = snapshot.data;
              if (user?.role == AppConstants.roleAdmin) {
                return IconButton(
                  icon: Icon(
                    Icons.admin_panel_settings,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                  onPressed: () => context.push('/companies/directory'),
                  tooltip: 'Admin Panel',
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: BlocConsumer<CompanyBloc, CompanyState>(
        listener: (context, state) {
          if (state is CompanyError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is CompanyLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is MyCompanyLoaded) {
            if (state.company == null) {
              return _buildNoCompanyView(context);
            }
            return _buildCompanyProfileView(context, state.company!);
          }

          if (state is CompanyError) {
            return _buildErrorView(context, state.message);
          }

          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildNoCompanyView(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.business_outlined,
              size: 120,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 24),
            Text(
              'No Company Profile',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'You haven\'t registered your company yet. Create a company profile to start using LogiPool services.',
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () async {
                final result = await context.push('/companies/register');
                if (result == true) {
                  context
                      .read<CompanyBloc>()
                      .add(const MyCompanyFetchRequested());
                }
              },
              icon: const Icon(Icons.add_business),
              label: const Text('Register Company'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 32,
                  vertical: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyProfileView(BuildContext context, CompanyModel company) {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<CompanyBloc>().add(const MyCompanyFetchRequested());
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCompanyHeader(context, company),
            const SizedBox(height: 24),
            _buildVerificationStatus(context, company),
            const SizedBox(height: 24),
            _buildCompanyStats(context, company),
            const SizedBox(height: 24),
            _buildCompanyDetails(context, company),
            const SizedBox(height: 24),
            _buildContactInfo(context, company),
            const SizedBox(height: 24),
            _buildActionButtons(context, company),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyHeader(BuildContext context, CompanyModel company) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: theme.colorScheme.primary,
                  child: Text(
                    company.name.isNotEmpty
                        ? company.name[0].toUpperCase()
                        : 'C',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        company.name,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        company.type.displayName,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      if (company.description != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          company.description!,
                          style: theme.textTheme.bodyMedium,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerificationStatus(BuildContext context, CompanyModel company) {
    final theme = Theme.of(context);
    final status = company.verificationStatus;

    Color statusColor;
    IconData statusIcon;

    switch (status) {
      case VerificationStatus.verified:
        statusColor = Colors.green;
        statusIcon = Icons.verified;
        break;
      case VerificationStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.pending;
        break;
      case VerificationStatus.rejected:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      case VerificationStatus.suspended:
        statusColor = Colors.red;
        statusIcon = Icons.block;
        break;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(statusIcon, color: statusColor, size: 32),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Verification Status',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    status.displayName,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    status.description,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyStats(BuildContext context, CompanyModel company) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Company Statistics',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Rating',
                    '${company.rating.toStringAsFixed(1)}/5.0',
                    Icons.star,
                    Colors.amber,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total Jobs',
                    company.totalJobs.toString(),
                    Icons.work,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Completed',
                    company.completedJobs.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Success Rate',
                    '${company.completionRate.toStringAsFixed(1)}%',
                    Icons.trending_up,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildCompanyDetails(BuildContext context, CompanyModel company) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Company Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow('Registration Number', company.registrationNumber),
            if (company.taxNumber != null)
              _buildDetailRow('Tax Number', company.taxNumber!),
            _buildDetailRow('Address', company.address),
            _buildDetailRow('City', company.city),
            _buildDetailRow('Country', company.country),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfo(BuildContext context, CompanyModel company) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contact Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (company.phoneNumber != null)
              _buildDetailRow('Phone', company.phoneNumber!),
            if (company.email != null) _buildDetailRow('Email', company.email!),
            if (company.website != null)
              _buildDetailRow('Website', company.website!),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, CompanyModel company) {
    return FutureBuilder<UserModel?>(
      future: context.read<AuthService>().getCurrentUser(),
      builder: (context, snapshot) {
        final currentUser = snapshot.data;
        final isTransporter = currentUser?.role == AppConstants.roleTransporter;

        return Column(
          children: [
            // First row: Edit Profile and Documents
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      final result =
                          await context.push('/companies/edit/${company.id}');
                      if (result == true) {
                        context
                            .read<CompanyBloc>()
                            .add(const MyCompanyFetchRequested());
                      }
                    },
                    icon: const Icon(Icons.edit),
                    label: const Text('Edit Profile'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => context.push(
                        '/documents?mode=company&companyId=${company.id}'),
                    icon: const Icon(Icons.folder),
                    label: const Text('Documents'),
                  ),
                ),
              ],
            ),
            // Second row: Team Management and Browse Companies
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => Navigator.of(context).push(
                      MaterialPageRoute<void>(
                        builder: (context) => BlocProvider(
                          create: (context) => CompanyMemberBloc(
                            getIt<CompanyMemberService>(),
                          ),
                          child: CompanyMembersScreen(company: company),
                        ),
                      ),
                    ),
                    icon: const Icon(Icons.people),
                    label: const Text('Manage Team'),
                  ),
                ),
                if (isTransporter) ...[
                  const SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => context.push('/companies/directory'),
                      icon: const Icon(Icons.business),
                      label: const Text('Browse Companies'),
                    ),
                  ),
                ],
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildErrorView(BuildContext context, String message) {
    final theme = Theme.of(context);

    // Check if the error is related to "company not found" - show register option
    final isCompanyNotFound =
        message.toLowerCase().contains('company not found') ||
            message.toLowerCase().contains('failed to fetch company');

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isCompanyNotFound ? Icons.business_outlined : Icons.error_outline,
              size: 80,
              color: isCompanyNotFound ? Colors.grey[400] : Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              isCompanyNotFound
                  ? 'No Company Profile'
                  : 'Error Loading Company',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              isCompanyNotFound
                  ? 'You haven\'t registered your company yet. Create a company profile to start using LogiPool services.'
                  : message,
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            if (isCompanyNotFound) ...[
              ElevatedButton.icon(
                onPressed: () async {
                  final result = await context.push('/companies/register');
                  if (result == true) {
                    context
                        .read<CompanyBloc>()
                        .add(const MyCompanyFetchRequested());
                  }
                },
                icon: const Icon(Icons.add_business),
                label: const Text('Register Company'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                ),
              ),
            ] else ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ElevatedButton.icon(
                    onPressed: () {
                      context
                          .read<CompanyBloc>()
                          .add(const MyCompanyFetchRequested());
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                  ),
                  const SizedBox(width: 16),
                  OutlinedButton.icon(
                    onPressed: () async {
                      final result = await context.push('/companies/register');
                      if (result == true) {
                        context
                            .read<CompanyBloc>()
                            .add(const MyCompanyFetchRequested());
                      }
                    },
                    icon: const Icon(Icons.add_business),
                    label: const Text('Register Company'),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AuthBloc>().add(AuthLogoutRequested());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
