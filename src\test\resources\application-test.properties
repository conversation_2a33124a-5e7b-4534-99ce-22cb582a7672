# Test Configuration
spring.application.name=logipool-test

# H2 In-Memory Database for Testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Configuration for Testing
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.format_sql=false

# JWT Configuration for Testing
app.jwt.secret=test-secret-key-for-jwt-token-generation-2025-testing-only
app.jwt.expiration=3600000
app.jwt.refresh-expiration=7200000

# Disable Redis for testing
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration

# File Upload Configuration for Testing
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
app.file.upload-dir=./test-uploads
app.file.max-size-mb=10
app.file.allowed-types=pdf,jpg,jpeg,png,doc,docx

# Payment Configuration for Testing
app.payment.commission-rate=7.5
app.payment.min-commission=5.00
app.payment.max-commission=500.00
app.payment.gateway=MOCK

# Notification Configuration for Testing
app.notification.email.enabled=false
app.notification.sms.enabled=false

# Test Data Configuration - ENABLED for testing
app.test-data.enabled=true

# Logging Configuration for Testing
logging.level.zw.co.kanjan.logipool=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN

# Disable security for some test endpoints
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always
