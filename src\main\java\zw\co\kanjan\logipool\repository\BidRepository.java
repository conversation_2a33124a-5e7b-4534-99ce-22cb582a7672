package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Bid;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Load;

import java.util.List;
import java.util.Optional;

@Repository
public interface BidRepository extends JpaRepository<Bid, Long> {
    
    Page<Bid> findByLoad(Load load, Pageable pageable);
    
    Page<Bid> findByCompany(Company company, Pageable pageable);
    
    Page<Bid> findByStatus(Bid.BidStatus status, Pageable pageable);
    
    @Query("SELECT b FROM Bid b WHERE b.load = :load AND b.company = :company")
    Optional<Bid> findByLoadAndCompany(@Param("load") Load load, @Param("company") Company company);
    
    @Query("SELECT b FROM Bid b WHERE b.load = :load AND b.status = :status")
    List<Bid> findByLoadAndStatus(@Param("load") Load load, @Param("status") Bid.BidStatus status);
    
    @Query("SELECT b FROM Bid b WHERE b.load = :load ORDER BY b.amount ASC")
    List<Bid> findByLoadOrderByAmountAsc(@Param("load") Load load);
    
    @Query("SELECT COUNT(b) FROM Bid b WHERE b.load = :load")
    Long countByLoad(@Param("load") Load load);

    @Query("SELECT COUNT(b) FROM Bid b WHERE b.load = :load")
    Integer countByLoadAsInteger(@Param("load") Load load);
    
    @Query("SELECT COUNT(b) FROM Bid b WHERE b.company = :company AND b.status = :status")
    Long countByCompanyAndStatus(@Param("company") Company company, @Param("status") Bid.BidStatus status);
    
    Boolean existsByLoadAndCompany(Load load, Company company);

    Page<Bid> findByLoadOrderByCreatedAtDesc(Load load, Pageable pageable);

    Page<Bid> findByCompanyOrderByCreatedAtDesc(Company company, Pageable pageable);
}
