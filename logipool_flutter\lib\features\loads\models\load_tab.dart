import 'package:flutter/material.dart';

enum LoadTabType {
  all,
  posted,
  bidding,
  verified,
  myLoads,
  company,
  map,
}

class LoadTab {
  final IconData icon;
  final String text;
  final int index;
  final LoadTabType loadType;

  const LoadTab({
    required this.icon,
    required this.text,
    required this.index,
    required this.loadType,
  });

  LoadTab copyWith({
    IconData? icon,
    String? text,
    int? index,
    LoadTabType? loadType,
  }) {
    return LoadTab(
      icon: icon ?? this.icon,
      text: text ?? this.text,
      index: index ?? this.index,
      loadType: loadType ?? this.loadType,
    );
  }

  Tab toTab() {
    return Tab(
      icon: Icon(icon),
      text: text,
    );
  }
}
