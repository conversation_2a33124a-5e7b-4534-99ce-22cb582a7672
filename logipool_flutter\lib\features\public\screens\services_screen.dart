import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/widgets/public_footer.dart';

class ServicesScreen extends StatelessWidget {
  const ServicesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildHeroSection(context),
          _buildServicesGrid(context),
          _buildProcessSection(context),
          _buildCallToActionSection(context),
          const PublicFooter(),
        ],
      ),
    );
  }

  Widget _buildHeroSection(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: <PERSON>ack(
        children: [
          // Background image
          Positioned.fill(
            child: Image.asset(
              'assets/images/pexels-nc-farm-bureau-mark-2257524.jpg',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
          ),
          // Overlay
          Positioned.fill(
            child: Container(
              color: Theme.of(context).primaryColor.withOpacity(0.7),
            ),
          ),
          // Content
          Positioned.fill(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 64),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.local_shipping,
                    size: 64,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Our Services',
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 48,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Comprehensive logistics solutions for all your transportation needs',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white70,
                      fontSize: 20,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServicesGrid(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth = screenWidth / (screenWidth > 768 ? 2 : 1);
    final itemHeight = 390 + (screenWidth > 768 ? 0 : 20); // desired height
    final aspectRatio = itemWidth / itemHeight;

    return Container(
      padding: const EdgeInsets.all(32),
      child: GridView.count(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        crossAxisCount: MediaQuery.of(context).size.width > 768 ? 2 : 1,
        crossAxisSpacing: 24,
        mainAxisSpacing: 24,
        childAspectRatio: aspectRatio,
        children: [
          _buildServiceCard( 
            context,
            Icons.local_shipping,
            'Freight Transportation',
            'Reliable freight transport services with verified logistics companies across the region.',
            ['Full truckload (FTL)', 'Less than truckload (LTL)', 'Express delivery', 'Scheduled routes'],
          ),
          _buildServiceCard(
            context,
            Icons.ac_unit,
            'Refrigerated Transport',
            'Temperature-controlled transportation for perishable goods and pharmaceuticals.',
            ['Cold chain logistics', 'Temperature monitoring', 'Pharmaceutical transport', 'Food & beverage'],
          ),
          _buildServiceCard(
            context,
            Icons.construction,
            'Heavy Equipment Transport',
            'Specialized transport for heavy machinery, construction equipment, and oversized cargo.',
            ['Construction equipment', 'Industrial machinery', 'Oversized cargo', 'Project logistics'],
          ),
          _buildServiceCard(
            context,
            Icons.inventory,
            'Container Services',
            'Container transportation and logistics for import/export operations.',
            ['Container transport', 'Port logistics', 'Import/Export', 'Cross-border transport'],
          ),
          _buildServiceCard(
            context,
            Icons.track_changes,
            'Real-time Tracking',
            'GPS-enabled tracking system for complete visibility of your shipments.',
            ['Live GPS tracking', 'Delivery notifications', 'Route optimization', 'Status updates'],
          ),
          _buildServiceCard(
            context,
            Icons.security,
            'Secure Transport',
            'High-security transportation for valuable and sensitive cargo.',
            ['Valuable goods', 'Secure vehicles', 'Insurance coverage', 'Escort services'],
          ),
        ],
      ),
    );
  }

  Widget _buildServiceCard(BuildContext context, IconData icon, String title, String description, List<String> features) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  size: 40,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ...features.map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 16,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    feature,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            )),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => context.go('/contact'),
              child: const Text('Get Quote'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProcessSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      color: Theme.of(context).primaryColor.withOpacity(0.05),
      child: Column(
        children: [
          Text(
            'How It Works',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 32),
          Row(
            children: [
              Expanded(
                child: _buildProcessStep(
                  context,
                  '1',
                  'Post Your Request',
                  'Submit your transportation requirements and get quotes from verified logistics companies.',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildProcessStep(
                  context,
                  '2',
                  'Compare & Choose',
                  'Review bids, compare prices, and select the best logistics partner for your needs.',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildProcessStep(
                  context,
                  '3',
                  'Track & Deliver',
                  'Monitor your shipment in real-time and receive notifications upon delivery.',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildProcessStep(BuildContext context, String step, String title, String description) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor,
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Text(
              step,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 8),
        Text(
          description,
          style: Theme.of(context).textTheme.bodyMedium,
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildCallToActionSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      color: Theme.of(context).primaryColor,
      child: Column(
        children: [
          Text(
            'Ready to Get Started?',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Join thousands of satisfied customers who trust LogiPool for their logistics needs.',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: () => context.go('/login'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Theme.of(context).primaryColor,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
                child: const Text(
                  'Get Started',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              OutlinedButton(
                onPressed: () => context.go('/contact'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.white,
                  side: const BorderSide(color: Colors.white, width: 2),
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                ),
                child: const Text(
                  'Contact Us',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
