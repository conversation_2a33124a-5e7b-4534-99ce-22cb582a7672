package zw.co.kanjan.logipool.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.TrackingVerification;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TrackingVerificationRepository extends JpaRepository<TrackingVerification, Long> {
    
    /**
     * Find tracking verification by token
     */
    Optional<TrackingVerification> findByToken(String token);
    
    /**
     * Find valid (verified, not used, not expired) tracking verification by token
     */
    @Query("SELECT tv FROM TrackingVerification tv WHERE tv.token = :token " +
           "AND tv.isVerified = true AND tv.isUsed = false AND tv.expiryDate > :now")
    Optional<TrackingVerification> findValidByToken(@Param("token") String token, @Param("now") LocalDateTime now);
    
    /**
     * Find tracking verification by tracking number and verification code
     */
    @Query("SELECT tv FROM TrackingVerification tv WHERE tv.trackingNumber = :trackingNumber " +
           "AND tv.verificationCode = :code AND tv.isUsed = false AND tv.expiryDate > :now")
    Optional<TrackingVerification> findByTrackingNumberAndCode(
            @Param("trackingNumber") String trackingNumber, 
            @Param("code") String code, 
            @Param("now") LocalDateTime now);
    
    /**
     * Find latest tracking verification for a tracking number and contact method
     */
    @Query("SELECT tv FROM TrackingVerification tv WHERE tv.trackingNumber = :trackingNumber " +
           "AND (tv.email = :contact OR tv.phoneNumber = :contact) " +
           "ORDER BY tv.createdAt DESC LIMIT 1")
    Optional<TrackingVerification> findLatestByTrackingNumberAndContact(
            @Param("trackingNumber") String trackingNumber, 
            @Param("contact") String contact);
    
    /**
     * Count verification requests for a tracking number and contact in the last hour
     */
    @Query("SELECT COUNT(tv) FROM TrackingVerification tv WHERE tv.trackingNumber = :trackingNumber " +
           "AND (tv.email = :contact OR tv.phoneNumber = :contact) " +
           "AND tv.createdAt > :since")
    Long countRecentRequestsByTrackingNumberAndContact(
            @Param("trackingNumber") String trackingNumber, 
            @Param("contact") String contact, 
            @Param("since") LocalDateTime since);
    
    /**
     * Count verification requests from an IP address in the last hour
     */
    @Query("SELECT COUNT(tv) FROM TrackingVerification tv WHERE tv.ipAddress = :ipAddress " +
           "AND tv.createdAt > :since")
    Long countRecentRequestsByIpAddress(@Param("ipAddress") String ipAddress, @Param("since") LocalDateTime since);
    
    /**
     * Find all expired tracking verifications
     */
    @Query("SELECT tv FROM TrackingVerification tv WHERE tv.expiryDate < :now")
    List<TrackingVerification> findExpired(@Param("now") LocalDateTime now);
    
    /**
     * Delete expired tracking verifications
     */
    @Modifying
    @Query("DELETE FROM TrackingVerification tv WHERE tv.expiryDate < :now")
    void deleteExpired(@Param("now") LocalDateTime now);
    
    /**
     * Delete all tracking verifications for a specific tracking number
     */
    @Modifying
    @Query("DELETE FROM TrackingVerification tv WHERE tv.trackingNumber = :trackingNumber")
    void deleteAllByTrackingNumber(@Param("trackingNumber") String trackingNumber);
    
    /**
     * Find tracking verifications that have been accessed multiple times (potential abuse)
     */
    @Query("SELECT tv FROM TrackingVerification tv WHERE tv.accessCount > :maxAccess")
    List<TrackingVerification> findHighAccessVerifications(@Param("maxAccess") Integer maxAccess);
    
    /**
     * Find tracking verifications by email for audit purposes
     */
    List<TrackingVerification> findByEmailOrderByCreatedAtDesc(String email);
    
    /**
     * Find tracking verifications by phone number for audit purposes
     */
    List<TrackingVerification> findByPhoneNumberOrderByCreatedAtDesc(String phoneNumber);
    
    /**
     * Find all tracking verifications for a specific tracking number
     */
    List<TrackingVerification> findByTrackingNumberOrderByCreatedAtDesc(String trackingNumber);
    
    /**
     * Check if there's a valid verification for a tracking number
     */
    @Query("SELECT COUNT(tv) > 0 FROM TrackingVerification tv WHERE tv.trackingNumber = :trackingNumber " +
           "AND tv.isVerified = true AND tv.isUsed = false AND tv.expiryDate > :now")
    boolean hasValidVerification(@Param("trackingNumber") String trackingNumber, @Param("now") LocalDateTime now);
}
