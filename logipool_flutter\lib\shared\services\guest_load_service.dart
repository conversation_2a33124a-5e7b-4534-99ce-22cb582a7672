import 'dart:convert';
import '../models/guest_load_inquiry_model.dart';
import '../utils/api_client.dart';

class GuestLoadService {
  static final GuestLoadService _instance = GuestLoadService._internal();
  factory GuestLoadService() => _instance;
  GuestLoadService._internal();

  static GuestLoadService get instance => _instance;

  final ApiClient _apiClient = ApiClient.instance;

  Future<Map<String, dynamic>> submitLoadInquiry(GuestLoadInquiryModel inquiry) async {
    try {
      final response = await _apiClient.post(
        '/public/guest-load-inquiry',
        body: inquiry.toJson(),
      );

      if (response.statusCode == 200) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        throw Exception('Failed to submit load inquiry: ${response.reasonPhrase}');
      }
    } catch (e) {
      if (e.toString().contains('400')) {
        // Handle validation errors
        throw Exception('Invalid request data. Please check your input.');
      } else if (e.toString().contains('429')) {
        // Handle rate limiting
        throw Exception('Too many requests. Please wait before submitting another inquiry.');
      }

      throw Exception('Failed to submit load inquiry: $e');
    }
  }

  Future<bool> checkInquiryStatus(String inquiryId) async {
    try {
      final response = await _apiClient.get('/public/guest-load-inquiry/$inquiryId/status');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        return data['exists'] ?? false;
      }

      return false;
    } catch (e) {
      // If we can't check status, assume it doesn't exist
      return false;
    }
  }
}
