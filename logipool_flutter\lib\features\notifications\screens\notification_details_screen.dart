import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/notification_model.dart';
import '../../../shared/widgets/unified_header.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../bloc/notification_bloc.dart';

class NotificationDetailsScreen extends StatefulWidget {
  final int notificationId;

  const NotificationDetailsScreen({
    super.key,
    required this.notificationId,
  });

  @override
  State<NotificationDetailsScreen> createState() =>
      _NotificationDetailsScreenState();
}

class _NotificationDetailsScreenState extends State<NotificationDetailsScreen> {
  NotificationModel? _notification;

  @override
  void initState() {
    super.initState();
    _loadNotificationAndMarkAsRead();
  }

  void _loadNotificationAndMarkAsRead() {
    // First, mark the notification as read
    context.read<NotificationBloc>().add(
          MarkNotificationAsRead(widget.notificationId),
        );

    // Then load the notification details
    context.read<NotificationBloc>().add(const LoadNotifications());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedHeader(
        title: 'Notification Details',
        actions: [
          if (_notification != null) ...[
            IconButton(
              icon: Icon(
                Icons.delete,
                color: Theme.of(context).colorScheme.onPrimary,
              ),
              onPressed: () => _showDeleteConfirmation(),
              tooltip: 'Delete Notification',
            ),
          ],
        ],
      ),
      body: BlocConsumer<NotificationBloc, NotificationState>(
        listener: (context, state) {
          if (state is NotificationError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is NotificationOperationSuccess) {
            if (state.message.contains('deleted')) {
              // Navigate back if notification was deleted
              context.pop();
            }
          }
        },
        builder: (context, state) {
          if (state is NotificationLoading) {
            return const LoadingWidget();
          }

          if (state is NotificationLoaded) {
            // Find the notification in the loaded list
            final notification = state.notifications.firstWhere(
              (n) => n.id == widget.notificationId,
              orElse: () => throw Exception('Notification not found'),
            );
            _notification = notification;
            return _buildNotificationDetails(context, notification);
          }

          if (state is NotificationError) {
            return CustomErrorWidget(
              message: state.message,
              onRetry: () => _loadNotificationAndMarkAsRead(),
            );
          }

          return const LoadingWidget();
        },
      ),
    );
  }

  Widget _buildNotificationDetails(
      BuildContext context, NotificationModel notification) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy \'at\' hh:mm a');

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      _buildNotificationIcon(notification),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              notification.title,
                              style: theme.textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                _buildTypeChip(notification.type),
                                const SizedBox(width: 8),
                                _buildPriorityChip(notification.priority),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    dateFormat.format(notification.timestamp),
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Message Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Message',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    notification.message,
                    style: theme.textTheme.bodyLarge,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Additional Information Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Additional Information',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildInfoRow('Type', notification.type.displayName),
                  _buildInfoRow('Priority', notification.priority.displayName),
                  if (notification.referenceType != null)
                    _buildInfoRow(
                        'Reference Type', notification.referenceType!),
                  if (notification.referenceId != null)
                    _buildInfoRow(
                        'Reference ID', notification.referenceId.toString()),
                  if (notification.readAt != null)
                    _buildInfoRow(
                        'Read At', dateFormat.format(notification.readAt!)),
                  if (notification.expiresAt != null)
                    _buildInfoRow('Expires At',
                        dateFormat.format(notification.expiresAt!)),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Action Buttons
          if (notification.referenceId != null &&
              notification.referenceType != null)
            _buildActionButtons(context, notification),
        ],
      ),
    );
  }

  Widget _buildNotificationIcon(NotificationModel notification) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: _getTypeColor(notification.type).withOpacity(0.1),
        borderRadius: BorderRadius.circular(25),
      ),
      child: Icon(
        _getTypeIcon(notification.type),
        color: _getTypeColor(notification.type),
        size: 24,
      ),
    );
  }

  Widget _buildTypeChip(NotificationType type) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getTypeColor(type).withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _getTypeColor(type).withOpacity(0.3)),
      ),
      child: Text(
        type.displayName,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: _getTypeColor(type),
              fontWeight: FontWeight.w600,
            ),
      ),
    );
  }

  Widget _buildPriorityChip(NotificationPriority priority) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _getPriorityColor(priority).withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: _getPriorityColor(priority).withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getPriorityIcon(priority),
            size: 14,
            color: _getPriorityColor(priority),
          ),
          const SizedBox(width: 4),
          Text(
            priority.displayName,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: _getPriorityColor(priority),
                  fontWeight: FontWeight.w600,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[600],
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(
      BuildContext context, NotificationModel notification) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Related Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _navigateToReference(notification),
                icon: Icon(_getReferenceIcon(notification.referenceType!)),
                label:
                    Text(_getReferenceButtonText(notification.referenceType!)),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToReference(NotificationModel notification) {
    final referenceType = notification.referenceType!;
    final referenceId = notification.referenceId!;

    switch (referenceType) {
      case 'LOAD':
        context.push('/loads/$referenceId');
        break;
      case 'BID':
        context.push('/bids/$referenceId');
        break;
      case 'PAYMENT':
        context.push('/payments/$referenceId');
        break;
      case 'DOCUMENT':
        context.push('/documents/$referenceId');
        break;
      case 'TRACKING':
        context.push('/tracking/$referenceId');
        break;
      case 'COMPANY':
        context.push('/companies/detail/$referenceId');
        break;
      default:
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to navigate to referenced item'),
            backgroundColor: Colors.orange,
          ),
        );
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Notification'),
        content: const Text(
            'Are you sure you want to delete this notification? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<NotificationBloc>().add(
                    DeleteNotification(widget.notificationId),
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(NotificationType type) {
    switch (type) {
      case NotificationType.bidReceived:
      case NotificationType.bidAccepted:
        return Colors.green;
      case NotificationType.bidRejected:
        return Colors.red;
      case NotificationType.loadPosted:
      case NotificationType.loadStatusUpdate:
        return Colors.blue;
      case NotificationType.paymentReceived:
        return Colors.purple;
      case NotificationType.documentRequired:
        return Colors.orange;
      case NotificationType.systemAnnouncement:
        return Colors.indigo;
      case NotificationType.accountVerification:
        return Colors.teal;
      case NotificationType.securityAlert:
        return Colors.red;
      case NotificationType.trackingUpdate:
        return Colors.cyan;
    }
  }

  IconData _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.bidReceived:
        return Icons.gavel;
      case NotificationType.bidAccepted:
        return Icons.check_circle;
      case NotificationType.bidRejected:
        return Icons.cancel;
      case NotificationType.loadPosted:
        return Icons.local_shipping;
      case NotificationType.loadStatusUpdate:
        return Icons.update;
      case NotificationType.paymentReceived:
        return Icons.payment;
      case NotificationType.documentRequired:
        return Icons.description;
      case NotificationType.systemAnnouncement:
        return Icons.announcement;
      case NotificationType.accountVerification:
        return Icons.verified_user;
      case NotificationType.securityAlert:
        return Icons.security;
      case NotificationType.trackingUpdate:
        return Icons.location_on;
    }
  }

  Color _getPriorityColor(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Colors.grey;
      case NotificationPriority.medium:
        return Colors.blue;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.urgent:
        return Colors.red;
    }
  }

  IconData _getPriorityIcon(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Icons.keyboard_arrow_down;
      case NotificationPriority.medium:
        return Icons.remove;
      case NotificationPriority.high:
        return Icons.keyboard_arrow_up;
      case NotificationPriority.urgent:
        return Icons.priority_high;
    }
  }

  IconData _getReferenceIcon(String referenceType) {
    switch (referenceType) {
      case 'LOAD':
        return Icons.local_shipping;
      case 'BID':
        return Icons.gavel;
      case 'PAYMENT':
        return Icons.payment;
      case 'DOCUMENT':
        return Icons.description;
      case 'TRACKING':
        return Icons.location_on;
      case 'COMPANY':
        return Icons.business;
      default:
        return Icons.open_in_new;
    }
  }

  String _getReferenceButtonText(String referenceType) {
    switch (referenceType) {
      case 'LOAD':
        return 'View Load Details';
      case 'BID':
        return 'View Bid Details';
      case 'PAYMENT':
        return 'View Payment Details';
      case 'DOCUMENT':
        return 'View Document';
      case 'TRACKING':
        return 'View Tracking';
      case 'COMPANY':
        return 'View Company';
      default:
        return 'View Details';
    }
  }
}
