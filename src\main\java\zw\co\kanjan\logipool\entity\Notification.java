package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "notifications")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Notification {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @Column(nullable = false)
    private String title;
    
    @Column(columnDefinition = "TEXT")
    private String message;
    
    @Column(nullable = false)
    private String type;
    
    @Column(name = "is_read", nullable = false)
    private boolean read = false;
    
    @Column(nullable = false)
    private String priority = "MEDIUM";
    
    @Column(name = "reference_id")
    private Long referenceId;
    
    @Column(name = "reference_type")
    private String referenceType;
    
    @Column(columnDefinition = "TEXT")
    private String data; // JSON string for additional data
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "read_at")
    private LocalDateTime readAt;
    
    @Column(name = "expires_at")
    private LocalDateTime expiresAt;
    
    public enum NotificationType {
        LOAD_POSTED,
        BID_RECEIVED,
        BID_ACCEPTED,
        BID_REJECTED,
        LOAD_STATUS_UPDATE,
        PAYMENT_RECEIVED,
        DOCUMENT_REQUIRED,
        SYSTEM_ANNOUNCEMENT,
        ACCOUNT_VERIFICATION,
        SECURITY_ALERT,
        ADMIN_NEW_LOAD,
        ADMIN_DAILY_REPORT
    }
    
    public enum Priority {
        LOW,
        MEDIUM,
        HIGH,
        URGENT
    }
}
