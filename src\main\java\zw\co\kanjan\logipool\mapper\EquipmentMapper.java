package zw.co.kanjan.logipool.mapper;

import org.mapstruct.*;
import zw.co.kanjan.logipool.dto.EquipmentDto;
import zw.co.kanjan.logipool.entity.Equipment;
import zw.co.kanjan.logipool.entity.Company;

import java.util.Arrays;
import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface EquipmentMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "documents", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "approvedAt", ignore = true)
    @Mapping(target = "approvedBy", ignore = true)
    @Mapping(target = "publicApprovalStatus", constant = "PENDING")
    @Mapping(target = "status", constant = "AVAILABLE")
    Equipment toEntity(EquipmentDto.EquipmentCreateRequest request);
    
    @Mapping(source = "company", target = "company", qualifiedByName = "mapCompanyInfo")
    @Mapping(source = "features", target = "features", qualifiedByName = "parseFeatures")
    EquipmentDto.EquipmentResponse toResponse(Equipment equipment);
    
    @Mapping(source = "company", target = "company", qualifiedByName = "mapPublicCompanyInfo")
    @Mapping(source = "features", target = "features", qualifiedByName = "parseFeatures")
    @Mapping(target = "serialNumber", ignore = true) // Hide serial number for public
    EquipmentDto.EquipmentResponse toPublicResponse(Equipment equipment);
    
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "documents", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "approvedAt", ignore = true)
    @Mapping(target = "approvedBy", ignore = true)
    void updateEntity(EquipmentDto.EquipmentUpdateRequest request, @MappingTarget Equipment equipment);
    
    @Named("mapCompanyInfo")
    default EquipmentDto.CompanyInfo mapCompanyInfo(Company company) {
        if (company == null) {
            return null;
        }
        
        return EquipmentDto.CompanyInfo.builder()
                .id(company.getId())
                .name(company.getName())
                .rating(company.getRating())
                .verificationStatus(company.getVerificationStatus() != null ? 
                    company.getVerificationStatus().toString() : null)
                .location(company.getCity())
                .phoneNumber(company.getPhoneNumber())
                .email(company.getEmail())
                .build();
    }
    
    @Named("mapPublicCompanyInfo")
    default EquipmentDto.CompanyInfo mapPublicCompanyInfo(Company company) {
        if (company == null) {
            return null;
        }
        
        return EquipmentDto.CompanyInfo.builder()
                .id(company.getId())
                .name(company.getName())
                .rating(company.getRating())
                .verificationStatus(company.getVerificationStatus() != null ? 
                    company.getVerificationStatus().toString() : null)
                .location(company.getCity())
                // Hide sensitive information for public view
                .phoneNumber(null)
                .email(null)
                .build();
    }
    
    @Named("parseFeatures")
    default List<String> parseFeatures(String featuresJson) {
        if (featuresJson == null || featuresJson.trim().isEmpty()) {
            return List.of();
        }
        
        try {
            // Simple parsing - in production, use proper JSON parsing
            String cleaned = featuresJson.replaceAll("[\\[\\]\"]", "");
            if (cleaned.trim().isEmpty()) {
                return List.of();
            }
            return Arrays.asList(cleaned.split(",\\s*"));
        } catch (Exception e) {
            return List.of();
        }
    }
}
