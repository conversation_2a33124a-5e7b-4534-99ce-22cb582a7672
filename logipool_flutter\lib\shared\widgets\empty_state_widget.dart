import 'package:flutter/material.dart';

class EmptyStateWidget extends StatelessWidget {
  final String? title;
  final String message;
  final String? buttonText;
  final VoidCallback? onAction;
  final Widget? icon;
  final bool showActionButton;

  const EmptyStateWidget({
    Key? key,
    this.title,
    required this.message,
    this.buttonText,
    this.onAction,
    this.icon,
    this.showActionButton = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            icon ??
                Icon(
                  Icons.inbox_outlined,
                  size: 64,
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
            const SizedBox(height: 16),
            if (title != null) ...[
              Text(
                title!,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
            ],
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (showActionButton && onAction != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: onAction,
                icon: const Icon(Icons.add),
                label: Text(buttonText ?? 'Add New'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class EmptyLoadsWidget extends StatelessWidget {
  final VoidCallback? onCreateLoad;

  const EmptyLoadsWidget({
    Key? key,
    this.onCreateLoad,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      title: 'No Loads Found',
      message: 'There are no loads available at the moment. Create a new load to get started.',
      buttonText: 'Create Load',
      icon: Icon(
        Icons.local_shipping_outlined,
        size: 64,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
      ),
      showActionButton: onCreateLoad != null,
      onAction: onCreateLoad,
    );
  }
}

class EmptyBidsWidget extends StatelessWidget {
  final VoidCallback? onCreateBid;

  const EmptyBidsWidget({
    Key? key,
    this.onCreateBid,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      title: 'No Bids Found',
      message: 'You haven\'t placed any bids yet. Browse available loads and place your first bid.',
      buttonText: 'Browse Loads',
      icon: Icon(
        Icons.gavel_outlined,
        size: 64,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
      ),
      showActionButton: onCreateBid != null,
      onAction: onCreateBid,
    );
  }
}

class EmptyCompaniesWidget extends StatelessWidget {
  final VoidCallback? onAddCompany;

  const EmptyCompaniesWidget({
    Key? key,
    this.onAddCompany,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      title: 'No Companies Found',
      message: 'No companies match your search criteria. Try adjusting your filters.',
      icon: Icon(
        Icons.business_outlined,
        size: 64,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
      ),
      showActionButton: false,
    );
  }
}

class EmptyDocumentsWidget extends StatelessWidget {
  final VoidCallback? onUploadDocument;

  const EmptyDocumentsWidget({
    Key? key,
    this.onUploadDocument,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      title: 'No Documents Found',
      message: 'You haven\'t uploaded any documents yet. Upload your first document to get started.',
      buttonText: 'Upload Document',
      icon: Icon(
        Icons.description_outlined,
        size: 64,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
      ),
      showActionButton: onUploadDocument != null,
      onAction: onUploadDocument,
    );
  }
}

class EmptySearchWidget extends StatelessWidget {
  final String searchTerm;

  const EmptySearchWidget({
    Key? key,
    required this.searchTerm,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      title: 'No Results Found',
      message: 'No results found for "$searchTerm". Try using different keywords.',
      icon: Icon(
        Icons.search_off_outlined,
        size: 64,
        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
      ),
    );
  }
}
