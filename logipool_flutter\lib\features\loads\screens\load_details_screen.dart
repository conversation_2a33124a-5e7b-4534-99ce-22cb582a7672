import 'package:flutter/material.dart';

class LoadDetailsScreen extends StatelessWidget {
  final String loadId;
  
  const LoadDetailsScreen({super.key, required this.loadId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Load Details - $loadId')),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.info, size: 64),
            SizedBox(height: 16),
            Text('Load Details Screen'),
            Text('Coming Soon...'),
          ],
        ),
      ),
    );
  }
}
