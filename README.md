# LogiPool - Logistics Marketplace Platform

LogiPool is a comprehensive logistics marketplace application that connects logistics companies with clients needing transportation services. The platform features bid-based job postings, document management, real-time tracking, and payment processing.

## Technology Stack

### Backend
- **Java 21** with **Spring Boot 3.5.3**
- **PostgreSQL** for production database
- **H2** for testing
- **Redis** for caching and session management
- **JWT** for authentication and authorization
- **Hibernate/JPA** for ORM
- **MapStruct** for entity-DTO mapping
- **Maven** for dependency management

### Frontend (Planned)
- **Flutter** for cross-platform mobile and web application

## Features

- **User Management**: Registration, authentication, role-based access control
- **Company Profiles**: Company registration, verification, document management
- **Load Management**: Post loads, browse available loads, bid management
- **Real-time Tracking**: GPS tracking, status updates, delivery confirmation
- **Document Management**: Upload, verify, and manage required documents
- **Payment Processing**: Secure payment handling with commission management
- **Admin Dashboard**: Platform moderation, user verification, system management

## Quick Start

### Prerequisites
- Java 21 or higher
- Maven 3.6+
- Docker and Docker Compose (for database)

### 1. <PERSON>lone the Repository
```bash
git clone <repository-url>
cd logipool
```

### 2. Start Database Services
```bash
docker-compose up -d
```

This will start:
- PostgreSQL on port 5432
- Redis on port 6379

### 3. Run the Application
```bash
# For development with PostgreSQL
mvn spring-boot:run -Dspring-boot.run.profiles=dev

# For testing with H2 in-memory database
mvn spring-boot:run -Dspring-boot.run.profiles=test
```

### 4. Test the API
The application will be available at `http://localhost:8080`

#### Test User Registration
```bash
curl -X POST http://localhost:8080/api/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "firstName": "Test",
    "lastName": "User"
  }'
```

## API Documentation

Once the application is running, you can access:
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **API Docs**: http://localhost:8080/v3/api-docs

## Testing

### Run All Tests
```bash
mvn test
```

### Run Specific Test
```bash
mvn test -Dtest=AuthControllerTest
```

## Database Schema

The application automatically creates the following main entities:
- **Users**: User accounts with role-based access
- **Companies**: Logistics companies and clients
- **Loads**: Transportation jobs/loads
- **Bids**: Bids on loads from logistics companies
- **Vehicles**: Company vehicle fleet management
- **Documents**: Document management and verification
- **Load Tracking**: Real-time tracking and status updates

## Configuration

### Environment Profiles
- **dev**: Development with PostgreSQL
- **test**: Testing with H2 in-memory database
- **prod**: Production configuration (to be configured)

### Key Configuration Files
- `application.properties`: Default configuration
- `application-dev.properties`: Development configuration
- `application-test.properties`: Test configuration

## Development

### Project Structure
```
src/
├── main/java/zw/co/kanjan/logipool/
│   ├── config/          # Configuration classes
│   ├── controller/      # REST controllers
│   ├── dto/            # Data Transfer Objects
│   ├── entity/         # JPA entities
│   ├── repository/     # Data repositories
│   ├── security/       # Security configuration
│   ├── service/        # Business logic
│   └── mapper/         # Entity-DTO mappers
└── test/               # Test classes
```

### Key Features Implemented
- ✅ Complete entity model with relationships
- ✅ JWT-based authentication and authorization
- ✅ Role-based access control (ADMIN, CLIENT, TRANSPORTER)
- ✅ User registration and authentication endpoints
- ✅ Database schema with proper constraints
- ✅ Test configuration with H2
- ✅ Docker Compose for development environment

### Next Steps
1. Complete remaining business logic services
2. Implement file upload functionality
3. Add comprehensive API tests
4. Create admin dashboard APIs
5. Implement notification system
6. Build Flutter frontend
7. Add payment integration
8. Implement real-time tracking

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

[License information to be added]


## Test logins
<EMAIL>
<EMAIL>
Secret1234!


pgadmin.api-logistics.kanjan.co.zw → *************
traefik.api-logistics.kanjan.co.zw → *************
erp.kanjan.co.zw → *************