package zw.co.kanjan.logipool.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import zw.co.kanjan.logipool.exception.BusinessException;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

@Slf4j
@Service
public class PasswordValidationService {
    
    @Value("${app.security.password.min-length:8}")
    private int minLength;
    
    @Value("${app.security.password.require-uppercase:true}")
    private boolean requireUppercase;
    
    @Value("${app.security.password.require-lowercase:true}")
    private boolean requireLowercase;
    
    @Value("${app.security.password.require-numbers:true}")
    private boolean requireNumbers;
    
    @Value("${app.security.password.require-special-chars:true}")
    private boolean requireSpecialChars;
    
    private static final Pattern UPPERCASE_PATTERN = Pattern.compile(".*[A-Z].*");
    private static final Pattern LOWERCASE_PATTERN = Pattern.compile(".*[a-z].*");
    private static final Pattern NUMBERS_PATTERN = Pattern.compile(".*[0-9].*");
    private static final Pattern SPECIAL_CHARS_PATTERN = Pattern.compile(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*");
    
    public void validatePassword(String password) {
        List<String> errors = new ArrayList<>();
        
        if (password == null || password.trim().isEmpty()) {
            throw new BusinessException("Password cannot be empty");
        }
        
        if (password.length() < minLength) {
            errors.add(String.format("Password must be at least %d characters long", minLength));
        }
        
        if (requireUppercase && !UPPERCASE_PATTERN.matcher(password).matches()) {
            errors.add("Password must contain at least one uppercase letter");
        }
        
        if (requireLowercase && !LOWERCASE_PATTERN.matcher(password).matches()) {
            errors.add("Password must contain at least one lowercase letter");
        }
        
        if (requireNumbers && !NUMBERS_PATTERN.matcher(password).matches()) {
            errors.add("Password must contain at least one number");
        }
        
        if (requireSpecialChars && !SPECIAL_CHARS_PATTERN.matcher(password).matches()) {
            errors.add("Password must contain at least one special character");
        }
        
        // Check for common weak passwords
        if (isCommonPassword(password)) {
            errors.add("Password is too common. Please choose a more secure password");
        }
        
        if (!errors.isEmpty()) {
            throw new BusinessException("Password validation failed: " + String.join(", ", errors));
        }
        
        log.debug("Password validation passed for password of length: {}", password.length());
    }
    
    public boolean isPasswordStrong(String password) {
        try {
            validatePassword(password);
            return true;
        } catch (BusinessException e) {
            return false;
        }
    }
    
    public List<String> getPasswordRequirements() {
        List<String> requirements = new ArrayList<>();
        
        requirements.add(String.format("At least %d characters long", minLength));
        
        if (requireUppercase) {
            requirements.add("At least one uppercase letter");
        }
        
        if (requireLowercase) {
            requirements.add("At least one lowercase letter");
        }
        
        if (requireNumbers) {
            requirements.add("At least one number");
        }
        
        if (requireSpecialChars) {
            requirements.add("At least one special character (!@#$%^&*()_+-=[]{}|;':\"\\,.<>?/)");
        }
        
        requirements.add("Not a common password");
        
        return requirements;
    }
    
    private boolean isCommonPassword(String password) {
        // List of common weak passwords
        String[] commonPasswords = {
            "password", "123456", "123456789", "12345678", "12345", "1234567",
            "password123", "admin", "qwerty", "abc123", "Password1", "welcome",
            "monkey", "1234567890", "dragon", "princess", "letmein", "654321",
            "sunshine", "master", "shadow", "123123", "666666", "qwertyuiop",
            "123321", "mustang", "1234567", "michael", "654321", "superman"
        };
        
        String lowerPassword = password.toLowerCase();
        for (String common : commonPasswords) {
            if (lowerPassword.equals(common.toLowerCase()) || 
                lowerPassword.contains(common.toLowerCase())) {
                return true;
            }
        }
        
        // Check for simple patterns
        if (isSequentialPattern(password) || isRepeatingPattern(password)) {
            return true;
        }
        
        return false;
    }
    
    private boolean isSequentialPattern(String password) {
        String lowerPassword = password.toLowerCase();
        
        // Check for sequential letters (abc, def, etc.)
        for (int i = 0; i < lowerPassword.length() - 2; i++) {
            char c1 = lowerPassword.charAt(i);
            char c2 = lowerPassword.charAt(i + 1);
            char c3 = lowerPassword.charAt(i + 2);
            
            if (c2 == c1 + 1 && c3 == c2 + 1) {
                return true;
            }
        }
        
        // Check for sequential numbers (123, 456, etc.)
        for (int i = 0; i < password.length() - 2; i++) {
            if (Character.isDigit(password.charAt(i)) && 
                Character.isDigit(password.charAt(i + 1)) && 
                Character.isDigit(password.charAt(i + 2))) {
                
                int n1 = Character.getNumericValue(password.charAt(i));
                int n2 = Character.getNumericValue(password.charAt(i + 1));
                int n3 = Character.getNumericValue(password.charAt(i + 2));
                
                if (n2 == n1 + 1 && n3 == n2 + 1) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    private boolean isRepeatingPattern(String password) {
        // Check for repeating characters (aaa, 111, etc.)
        for (int i = 0; i < password.length() - 2; i++) {
            if (password.charAt(i) == password.charAt(i + 1) && 
                password.charAt(i + 1) == password.charAt(i + 2)) {
                return true;
            }
        }
        
        return false;
    }
}
