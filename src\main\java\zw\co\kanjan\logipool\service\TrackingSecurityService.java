package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.entity.TrackingAuditLog;
import zw.co.kanjan.logipool.entity.TrackingVerification;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.repository.TrackingAuditLogRepository;
import zw.co.kanjan.logipool.repository.TrackingVerificationRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrackingSecurityService {
    
    private final TrackingVerificationRepository trackingVerificationRepository;
    private final TrackingAuditLogRepository auditLogRepository;
    
    @Value("${app.tracking.verification.max-requests-per-hour:3}")
    private int maxRequestsPerHour;
    
    @Value("${app.tracking.verification.max-ip-requests-per-hour:10}")
    private int maxIpRequestsPerHour;
    
    @Value("${app.tracking.verification.max-access-count:5}")
    private int maxAccessCount;
    
    @Value("${app.tracking.verification.monitoring-enabled:true}")
    private boolean monitoringEnabled;
    
    // In-memory rate limiting (for production, consider using Redis)
    private final ConcurrentHashMap<String, AtomicInteger> ipRequestCounts = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, LocalDateTime> ipLastReset = new ConcurrentHashMap<>();
    
    /**
     * Check if the request should be rate limited
     */
    public void checkRateLimit(String trackingNumber, String contact, String ipAddress) {
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        
        // Check database-based rate limits
        checkDatabaseRateLimit(trackingNumber, contact, oneHourAgo);
        checkIpRateLimit(ipAddress, oneHourAgo);
        
        // Check in-memory rate limits for additional protection
        checkInMemoryIpRateLimit(ipAddress);
        
        // Log the request for monitoring
        logSecurityEvent("RATE_LIMIT_CHECK", trackingNumber, contact, ipAddress, "PASSED");
    }
    
    /**
     * Validate tracking access and check for suspicious activity
     */
    public void validateTrackingAccess(TrackingVerification verification, String ipAddress) {
        // Check if verification is still valid
        if (!verification.isValid()) {
            logSecurityEvent("ACCESS_DENIED", verification.getTrackingNumber(), 
                           verification.getEmail() != null ? verification.getEmail() : verification.getPhoneNumber(), 
                           ipAddress, "INVALID_VERIFICATION");
            throw new BusinessException("Invalid or expired tracking access");
        }
        
        // Check access count limit
        if (verification.getAccessCount() >= maxAccessCount) {
            logSecurityEvent("ACCESS_DENIED", verification.getTrackingNumber(), 
                           verification.getEmail() != null ? verification.getEmail() : verification.getPhoneNumber(), 
                           ipAddress, "MAX_ACCESS_EXCEEDED");
            throw new BusinessException("Maximum access limit reached for this tracking session");
        }
        
        // Check for IP address changes (potential security concern)
        if (verification.getIpAddress() != null && !verification.getIpAddress().equals(ipAddress)) {
            logSecurityEvent("IP_CHANGE_DETECTED", verification.getTrackingNumber(), 
                           verification.getEmail() != null ? verification.getEmail() : verification.getPhoneNumber(), 
                           ipAddress, "IP_CHANGED_FROM_" + verification.getIpAddress());
            
            // Allow but log for monitoring (you might want to be more restrictive)
            log.warn("IP address changed for tracking verification. Original: {}, Current: {}, Token: {}", 
                    verification.getIpAddress(), ipAddress, verification.getToken());
        }
        
        logSecurityEvent("ACCESS_GRANTED", verification.getTrackingNumber(), 
                       verification.getEmail() != null ? verification.getEmail() : verification.getPhoneNumber(), 
                       ipAddress, "SUCCESS");
    }
    
    /**
     * Check for suspicious patterns and potential abuse
     */
    public void detectSuspiciousActivity() {
        if (!monitoringEnabled) {
            return;
        }
        
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        LocalDateTime oneDayAgo = LocalDateTime.now().minusDays(1);
        
        // Find verifications with high access counts
        List<TrackingVerification> highAccessVerifications = 
                trackingVerificationRepository.findHighAccessVerifications(maxAccessCount - 1);
        
        for (TrackingVerification verification : highAccessVerifications) {
            logSecurityEvent("SUSPICIOUS_HIGH_ACCESS", verification.getTrackingNumber(), 
                           verification.getEmail() != null ? verification.getEmail() : verification.getPhoneNumber(), 
                           verification.getIpAddress(), "ACCESS_COUNT_" + verification.getAccessCount());
        }
        
        // Check for rapid verification requests from same IP
        // This would require additional tracking - implement based on your needs
        
        log.info("Suspicious activity detection completed. Found {} high-access verifications", 
                highAccessVerifications.size());
    }
    
    /**
     * Clean up expired verifications and reset rate limiting counters
     */
    @Transactional
    public void performSecurityMaintenance() {
        try {
            // Clean up expired verifications
            int deletedCount = trackingVerificationRepository.findExpired(LocalDateTime.now()).size();
            trackingVerificationRepository.deleteExpired(LocalDateTime.now());
            
            // Reset in-memory rate limiting counters
            LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
            ipLastReset.entrySet().removeIf(entry -> entry.getValue().isBefore(oneHourAgo));
            ipRequestCounts.entrySet().removeIf(entry -> {
                String ip = entry.getKey();
                LocalDateTime lastReset = ipLastReset.get(ip);
                return lastReset == null || lastReset.isBefore(oneHourAgo);
            });
            
            log.info("Security maintenance completed. Deleted {} expired verifications, reset {} IP counters", 
                    deletedCount, ipRequestCounts.size());
            
        } catch (Exception e) {
            log.error("Error during security maintenance", e);
        }
    }
    
    private void checkDatabaseRateLimit(String trackingNumber, String contact, LocalDateTime oneHourAgo) {
        Long contactRequests = trackingVerificationRepository
                .countRecentRequestsByTrackingNumberAndContact(trackingNumber, contact, oneHourAgo);
        
        if (contactRequests >= maxRequestsPerHour) {
            auditLogRepository.save(TrackingAuditLog.rateLimitExceeded(trackingNumber, contact, null, "CONTACT_REQUESTS"));
            logSecurityEvent("RATE_LIMIT_EXCEEDED", trackingNumber, contact, null,
                           "CONTACT_REQUESTS_" + contactRequests);
            throw new BusinessException("Too many verification requests. Please try again later.");
        }
    }
    
    private void checkIpRateLimit(String ipAddress, LocalDateTime oneHourAgo) {
        Long ipRequests = trackingVerificationRepository
                .countRecentRequestsByIpAddress(ipAddress, oneHourAgo);
        
        if (ipRequests >= maxIpRequestsPerHour) {
            auditLogRepository.save(TrackingAuditLog.rateLimitExceeded(null, null, ipAddress, "IP_REQUESTS"));
            logSecurityEvent("RATE_LIMIT_EXCEEDED", null, null, ipAddress,
                           "IP_REQUESTS_" + ipRequests);
            throw new BusinessException("Too many requests from this location. Please try again later.");
        }
    }
    
    private void checkInMemoryIpRateLimit(String ipAddress) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime lastReset = ipLastReset.get(ipAddress);
        
        // Reset counter if more than an hour has passed
        if (lastReset == null || lastReset.isBefore(now.minusHours(1))) {
            ipRequestCounts.put(ipAddress, new AtomicInteger(1));
            ipLastReset.put(ipAddress, now);
            return;
        }
        
        // Increment and check counter
        AtomicInteger counter = ipRequestCounts.computeIfAbsent(ipAddress, k -> new AtomicInteger(0));
        int currentCount = counter.incrementAndGet();
        
        if (currentCount > maxIpRequestsPerHour) {
            logSecurityEvent("RATE_LIMIT_EXCEEDED", null, null, ipAddress, 
                           "IN_MEMORY_IP_REQUESTS_" + currentCount);
            throw new BusinessException("Too many requests from this location. Please try again later.");
        }
    }
    
    private void logSecurityEvent(String eventType, String trackingNumber, String contact, 
                                 String ipAddress, String details) {
        if (monitoringEnabled) {
            log.info("TRACKING_SECURITY_EVENT: {} | TrackingNumber: {} | Contact: {} | IP: {} | Details: {}", 
                    eventType, trackingNumber, contact, ipAddress, details);
        }
    }
}
