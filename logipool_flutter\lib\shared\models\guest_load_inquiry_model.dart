class GuestLoadInquiryModel {
  // Contact Information
  final String name;
  final String email;
  final String phoneNumber;
  final String? company;
  
  // Load Information
  final String title;
  final String description;
  final String loadType;
  final double weight;
  final String? dimensions;
  
  // Location and Timing
  final String pickupLocation;
  final String deliveryLocation;
  final DateTime pickupDate;
  final DateTime? deliveryDate;
  
  // Additional Options
  final bool requiresSpecialHandling;
  final bool isUrgent;
  final String? specialInstructions;

  GuestLoadInquiryModel({
    required this.name,
    required this.email,
    required this.phoneNumber,
    this.company,
    required this.title,
    required this.description,
    required this.loadType,
    required this.weight,
    this.dimensions,
    required this.pickupLocation,
    required this.deliveryLocation,
    required this.pickupDate,
    this.deliveryDate,
    this.requiresSpecialHandling = false,
    this.isUrgent = false,
    this.specialInstructions,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'email': email,
      'phoneNumber': phoneNumber,
      'company': company,
      'title': title,
      'description': description,
      'loadType': loadType,
      'weight': weight,
      'dimensions': dimensions,
      'pickupLocation': pickupLocation,
      'deliveryLocation': deliveryLocation,
      'pickupDate': pickupDate.toIso8601String().split('T')[0], // Send as date only
      'deliveryDate': deliveryDate?.toIso8601String().split('T')[0],
      'requiresSpecialHandling': requiresSpecialHandling,
      'isUrgent': isUrgent,
      'specialInstructions': specialInstructions,
    };
  }

  factory GuestLoadInquiryModel.fromJson(Map<String, dynamic> json) {
    return GuestLoadInquiryModel(
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      company: json['company'],
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      loadType: json['loadType'] ?? 'GENERAL',
      weight: (json['weight'] ?? 0).toDouble(),
      dimensions: json['dimensions'],
      pickupLocation: json['pickupLocation'] ?? '',
      deliveryLocation: json['deliveryLocation'] ?? '',
      pickupDate: DateTime.parse(json['pickupDate']),
      deliveryDate: json['deliveryDate'] != null ? DateTime.parse(json['deliveryDate']) : null,
      requiresSpecialHandling: json['requiresSpecialHandling'] ?? false,
      isUrgent: json['isUrgent'] ?? false,
      specialInstructions: json['specialInstructions'],
    );
  }

  GuestLoadInquiryModel copyWith({
    String? name,
    String? email,
    String? phoneNumber,
    String? company,
    String? title,
    String? description,
    String? loadType,
    double? weight,
    String? dimensions,
    String? pickupLocation,
    String? deliveryLocation,
    DateTime? pickupDate,
    DateTime? deliveryDate,
    bool? requiresSpecialHandling,
    bool? isUrgent,
    String? specialInstructions,
  }) {
    return GuestLoadInquiryModel(
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      company: company ?? this.company,
      title: title ?? this.title,
      description: description ?? this.description,
      loadType: loadType ?? this.loadType,
      weight: weight ?? this.weight,
      dimensions: dimensions ?? this.dimensions,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      deliveryLocation: deliveryLocation ?? this.deliveryLocation,
      pickupDate: pickupDate ?? this.pickupDate,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      requiresSpecialHandling: requiresSpecialHandling ?? this.requiresSpecialHandling,
      isUrgent: isUrgent ?? this.isUrgent,
      specialInstructions: specialInstructions ?? this.specialInstructions,
    );
  }

  @override
  String toString() {
    return 'GuestLoadInquiryModel(name: $name, email: $email, title: $title, loadType: $loadType, weight: $weight, pickupLocation: $pickupLocation, deliveryLocation: $deliveryLocation, pickupDate: $pickupDate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is GuestLoadInquiryModel &&
      other.name == name &&
      other.email == email &&
      other.phoneNumber == phoneNumber &&
      other.company == company &&
      other.title == title &&
      other.description == description &&
      other.loadType == loadType &&
      other.weight == weight &&
      other.dimensions == dimensions &&
      other.pickupLocation == pickupLocation &&
      other.deliveryLocation == deliveryLocation &&
      other.pickupDate == pickupDate &&
      other.deliveryDate == deliveryDate &&
      other.requiresSpecialHandling == requiresSpecialHandling &&
      other.isUrgent == isUrgent &&
      other.specialInstructions == specialInstructions;
  }

  @override
  int get hashCode {
    return name.hashCode ^
      email.hashCode ^
      phoneNumber.hashCode ^
      company.hashCode ^
      title.hashCode ^
      description.hashCode ^
      loadType.hashCode ^
      weight.hashCode ^
      dimensions.hashCode ^
      pickupLocation.hashCode ^
      deliveryLocation.hashCode ^
      pickupDate.hashCode ^
      deliveryDate.hashCode ^
      requiresSpecialHandling.hashCode ^
      isUrgent.hashCode ^
      specialInstructions.hashCode;
  }
}
