import 'package:flutter/material.dart';

import '../../../shared/models/load_model.dart';

class LoadFilterBottomSheet extends StatefulWidget {
  final LoadType? selectedLoadType;
  final LoadStatus? selectedStatus;
  final Priority? selectedPriority;
  final bool verifiedOnly;
  final Function(LoadType?, LoadStatus?, Priority?, bool) onApplyFilters;

  const LoadFilterBottomSheet({
    super.key,
    this.selectedLoadType,
    this.selectedStatus,
    this.selectedPriority,
    this.verifiedOnly = false,
    required this.onApplyFilters,
  });

  @override
  State<LoadFilterBottomSheet> createState() => _LoadFilterBottomSheetState();
}

class _LoadFilterBottomSheetState extends State<LoadFilterBottomSheet> {
  LoadType? _selectedLoadType;
  LoadStatus? _selectedStatus;
  Priority? _selectedPriority;
  bool _verifiedOnly = false;

  @override
  void initState() {
    super.initState();
    _selectedLoadType = widget.selectedLoadType;
    _selectedStatus = widget.selectedStatus;
    _selectedPriority = widget.selectedPriority;
    _verifiedOnly = widget.verifiedOnly;
  }

  void _clearFilters() {
    setState(() {
      _selectedLoadType = null;
      _selectedStatus = null;
      _selectedPriority = null;
      _verifiedOnly = false;
    });
  }

  void _applyFilters() {
    widget.onApplyFilters(
      _selectedLoadType,
      _selectedStatus,
      _selectedPriority,
      _verifiedOnly,
    );
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.7,
        minChildSize: 0.5,
        maxChildSize: 0.9,
        expand: false,
        builder: (context, scrollController) {
          return Container(
            decoration: BoxDecoration(
              color: theme.scaffoldBackgroundColor,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // Handle
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                
                // Header
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Text(
                        'Filter Loads',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: _clearFilters,
                        child: const Text('Clear All'),
                      ),
                    ],
                  ),
                ),
                
                const Divider(height: 1),
                
                // Filter options
                Expanded(
                  child: ListView(
                    controller: scrollController,
                    padding: const EdgeInsets.all(16),
                    children: [
                      // Load Type
                      Text(
                        'Load Type',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: LoadType.values.map((type) {
                          final isSelected = _selectedLoadType == type;
                          return FilterChip(
                            label: Text(type.displayName),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                _selectedLoadType = selected ? type : null;
                              });
                            },
                          );
                        }).toList(),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Status
                      Text(
                        'Status',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: LoadStatus.values.map((status) {
                          final isSelected = _selectedStatus == status;
                          return FilterChip(
                            label: Text(status.displayName),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                _selectedStatus = selected ? status : null;
                              });
                            },
                          );
                        }).toList(),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Priority
                      Text(
                        'Priority',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Wrap(
                        spacing: 8,
                        children: Priority.values.map((priority) {
                          final isSelected = _selectedPriority == priority;
                          return FilterChip(
                            label: Text(priority.displayName),
                            selected: isSelected,
                            onSelected: (selected) {
                              setState(() {
                                _selectedPriority = selected ? priority : null;
                              });
                            },
                          );
                        }).toList(),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // Additional Options
                      Text(
                        'Additional Options',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      
                      SwitchListTile(
                        title: const Text('Verified Loads Only'),
                        subtitle: const Text('Show only verified load postings'),
                        value: _verifiedOnly,
                        onChanged: (value) {
                          setState(() {
                            _verifiedOnly = value;
                          });
                        },
                        contentPadding: EdgeInsets.zero,
                      ),
                      
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
                
                // Apply button
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: theme.scaffoldBackgroundColor,
                    border: Border(
                      top: BorderSide(
                        color: theme.dividerColor,
                        width: 1,
                      ),
                    ),
                  ),
                  child: SafeArea(
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _applyFilters,
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text('Apply Filters'),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
