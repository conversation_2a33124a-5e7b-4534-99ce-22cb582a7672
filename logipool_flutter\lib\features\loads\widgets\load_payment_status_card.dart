import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/load_model.dart';

class LoadPaymentStatusCard extends StatelessWidget {
  final LoadModel load;

  const LoadPaymentStatusCard({
    super.key,
    required this.load,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.payment,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Payment Status',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildPaymentOverview(context),
            const SizedBox(height: 16),
            _buildPaymentDetails(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentOverview(BuildContext context) {
    final paymentStatus = _getPaymentStatus();
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: paymentStatus.backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            paymentStatus.icon,
            color: paymentStatus.iconColor,
            size: 32,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  paymentStatus.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: paymentStatus.textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  paymentStatus.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: paymentStatus.textColor.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          if (load.estimatedValue != null)
            Text(
              '\$${load.estimatedValue!.toStringAsFixed(2)}',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: paymentStatus.textColor,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPaymentDetails(BuildContext context) {
    return Column(
      children: [
        _buildPaymentDetail(
          'Payment Type',
          load.paymentType?.displayName ?? 'Not specified',
          Icons.category,
        ),
        const SizedBox(height: 12),
        _buildPaymentDetail(
          'Payment Rate',
          load.paymentRate != null 
              ? '\$${load.paymentRate!.toStringAsFixed(2)} ${load.paymentUnit ?? ''}'
              : 'Not specified',
          Icons.attach_money,
        ),
        const SizedBox(height: 12),
        _buildPaymentDetail(
          'Estimated Value',
          load.estimatedValue != null 
              ? '\$${load.estimatedValue!.toStringAsFixed(2)}'
              : 'Not specified',
          Icons.calculate,
        ),
        const SizedBox(height: 12),
        _buildPaymentDetail(
          'Commission (5-10%)',
          load.estimatedValue != null 
              ? '\$${(load.estimatedValue! * 0.075).toStringAsFixed(2)} (est.)'
              : 'TBD',
          Icons.percent,
        ),
        const SizedBox(height: 12),
        _buildPaymentDetail(
          'Net Amount',
          load.estimatedValue != null 
              ? '\$${(load.estimatedValue! * 0.925).toStringAsFixed(2)} (est.)'
              : 'TBD',
          Icons.account_balance_wallet,
        ),
      ],
    );
  }

  Widget _buildPaymentDetail(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: const TextStyle(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  PaymentStatusInfo _getPaymentStatus() {
    switch (load.status) {
      case LoadStatus.posted:
      case LoadStatus.assigned:
      case LoadStatus.inTransit:
        return PaymentStatusInfo(
          title: 'Payment Pending',
          description: 'Payment will be processed after delivery',
          icon: Icons.schedule,
          backgroundColor: Colors.orange.shade50,
          iconColor: Colors.orange.shade600,
          textColor: Colors.orange.shade800,
        );
      
      case LoadStatus.delivered:
        return PaymentStatusInfo(
          title: 'Payment Processing',
          description: 'Load delivered, payment being processed',
          icon: Icons.hourglass_empty,
          backgroundColor: Colors.blue.shade50,
          iconColor: Colors.blue.shade600,
          textColor: Colors.blue.shade800,
        );
      
      case LoadStatus.completed:
        return PaymentStatusInfo(
          title: 'Payment Completed',
          description: 'Payment has been successfully processed',
          icon: Icons.check_circle,
          backgroundColor: Colors.green.shade50,
          iconColor: Colors.green.shade600,
          textColor: Colors.green.shade800,
        );
      
      case LoadStatus.cancelled:
        return PaymentStatusInfo(
          title: 'Payment Cancelled',
          description: 'Load was cancelled, no payment required',
          icon: Icons.cancel,
          backgroundColor: Colors.red.shade50,
          iconColor: Colors.red.shade600,
          textColor: Colors.red.shade800,
        );
      
      default:
        return PaymentStatusInfo(
          title: 'Payment Status Unknown',
          description: 'Unable to determine payment status',
          icon: Icons.help_outline,
          backgroundColor: Colors.grey.shade50,
          iconColor: Colors.grey.shade600,
          textColor: Colors.grey.shade800,
        );
    }
  }
}

class PaymentStatusInfo {
  final String title;
  final String description;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;
  final Color textColor;

  PaymentStatusInfo({
    required this.title,
    required this.description,
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
    required this.textColor,
  });
}
