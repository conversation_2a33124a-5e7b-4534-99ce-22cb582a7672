import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../shared/models/load_model.dart';
import '../../../shared/models/paginated_response.dart';
import '../../../shared/services/load_service.dart';
import '../../../shared/exceptions/api_exception.dart';

// Events
abstract class LoadEvent extends Equatable {
  const LoadEvent();

  @override
  List<Object?> get props => [];
}

class LoadCreateRequested extends LoadEvent {
  final LoadModel load;

  const LoadCreateRequested({required this.load});

  @override
  List<Object> get props => [load];
}

class LoadFetchAllRequested extends LoadEvent {
  final int page;
  final int size;
  final String sortBy;
  final String sortDir;
  final bool isRefresh;

  const LoadFetchAllRequested({
    this.page = 0,
    this.size = 10,
    this.sortBy = 'createdAt',
    this.sortDir = 'desc',
    this.isRefresh = false,
  });

  @override
  List<Object> get props => [page, size, sortBy, sortDir, isRefresh];
}

class LoadFetchByStatusRequested extends LoadEvent {
  final LoadStatus status;
  final int page;
  final int size;
  final String sortBy;
  final String sortDir;

  const LoadFetchByStatusRequested({
    required this.status,
    this.page = 0,
    this.size = 10,
    this.sortBy = 'createdAt',
    this.sortDir = 'desc',
  });

  @override
  List<Object> get props => [status, page, size, sortBy, sortDir];
}

class LoadFetchVerifiedRequested extends LoadEvent {
  final int page;
  final int size;
  final String sortBy;
  final String sortDir;

  const LoadFetchVerifiedRequested({
    this.page = 0,
    this.size = 10,
    this.sortBy = 'createdAt',
    this.sortDir = 'desc',
  });

  @override
  List<Object> get props => [page, size, sortBy, sortDir];
}

class LoadFetchMyLoadsRequested extends LoadEvent {
  final int page;
  final int size;
  final String sortBy;
  final String sortDir;

  const LoadFetchMyLoadsRequested({
    this.page = 0,
    this.size = 10,
    this.sortBy = 'createdAt',
    this.sortDir = 'desc',
  });

  @override
  List<Object> get props => [page, size, sortBy, sortDir];
}

class LoadFetchByIdRequested extends LoadEvent {
  final int id;

  const LoadFetchByIdRequested({required this.id});

  @override
  List<Object> get props => [id];
}

class LoadUpdateRequested extends LoadEvent {
  final int id;
  final LoadModel load;

  const LoadUpdateRequested({
    required this.id,
    required this.load,
  });

  @override
  List<Object> get props => [id, load];
}

class LoadUpdateStatusRequested extends LoadEvent {
  final int id;
  final LoadStatus status;

  const LoadUpdateStatusRequested({
    required this.id,
    required this.status,
  });

  @override
  List<Object> get props => [id, status];
}

class LoadUpdateStatusWithNotesRequested extends LoadEvent {
  final int id;
  final LoadStatus status;
  final String? notes;

  const LoadUpdateStatusWithNotesRequested({
    required this.id,
    required this.status,
    this.notes,
  });

  @override
  List<Object?> get props => [id, status, notes];
}

class LoadDeleteRequested extends LoadEvent {
  final int id;

  const LoadDeleteRequested({required this.id});

  @override
  List<Object> get props => [id];
}

class LoadAssignRequested extends LoadEvent {
  final int loadId;
  final int companyId;

  const LoadAssignRequested({
    required this.loadId,
    required this.companyId,
  });

  @override
  List<Object> get props => [loadId, companyId];
}

class LoadSearchRequested extends LoadEvent {
  final String? query;
  final LoadType? loadType;
  final LoadStatus? status;
  final Priority? priority;
  final String? pickupLocation;
  final String? deliveryLocation;
  final DateTime? pickupDateFrom;
  final DateTime? pickupDateTo;
  final DateTime? deliveryDateFrom;
  final DateTime? deliveryDateTo;
  final double? minWeight;
  final double? maxWeight;
  final bool? requiresInsurance;
  final bool? requiresSpecialHandling;
  final bool? verifiedOnly;
  final int page;
  final int size;
  final String sortBy;
  final String sortDir;

  const LoadSearchRequested({
    this.query,
    this.loadType,
    this.status,
    this.priority,
    this.pickupLocation,
    this.deliveryLocation,
    this.pickupDateFrom,
    this.pickupDateTo,
    this.deliveryDateFrom,
    this.deliveryDateTo,
    this.minWeight,
    this.maxWeight,
    this.requiresInsurance,
    this.requiresSpecialHandling,
    this.verifiedOnly,
    this.page = 0,
    this.size = 10,
    this.sortBy = 'createdAt',
    this.sortDir = 'desc',
  });

  @override
  List<Object?> get props => [
        query,
        loadType,
        status,
        priority,
        pickupLocation,
        deliveryLocation,
        pickupDateFrom,
        pickupDateTo,
        deliveryDateFrom,
        deliveryDateTo,
        minWeight,
        maxWeight,
        requiresInsurance,
        requiresSpecialHandling,
        verifiedOnly,
        page,
        size,
        sortBy,
        sortDir,
      ];
}

class LoadRefreshRequested extends LoadEvent {}

// States
abstract class LoadState extends Equatable {
  const LoadState();

  @override
  List<Object?> get props => [];
}

class LoadInitial extends LoadState {}

class LoadLoading extends LoadState {}

class LoadLoadingMore extends LoadState {}

class LoadListLoaded extends LoadState {
  final PaginatedResponse<LoadModel> response;

  const LoadListLoaded({required this.response});

  @override
  List<Object> get props => [response];
}

class LoadDetailLoaded extends LoadState {
  final LoadModel load;

  const LoadDetailLoaded({required this.load});

  @override
  List<Object> get props => [load];
}

class LoadCreateSuccess extends LoadState {
  final LoadModel load;

  const LoadCreateSuccess({required this.load});

  @override
  List<Object> get props => [load];
}

class LoadUpdateSuccess extends LoadState {
  final LoadModel load;

  const LoadUpdateSuccess({required this.load});

  @override
  List<Object> get props => [load];
}

class LoadDeleteSuccess extends LoadState {
  final int id;

  const LoadDeleteSuccess({required this.id});

  @override
  List<Object> get props => [id];
}

class LoadError extends LoadState {
  final String message;
  final String? errorCode;

  const LoadError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

class LoadBloc extends Bloc<LoadEvent, LoadState> {
  final LoadService _loadService;

  LoadBloc(this._loadService) : super(LoadInitial()) {
    on<LoadCreateRequested>(_onCreateRequested);
    on<LoadFetchAllRequested>(_onFetchAllRequested);
    on<LoadFetchByStatusRequested>(_onFetchByStatusRequested);
    on<LoadFetchVerifiedRequested>(_onFetchVerifiedRequested);
    on<LoadFetchMyLoadsRequested>(_onFetchMyLoadsRequested);
    on<LoadFetchByIdRequested>(_onFetchByIdRequested);
    on<LoadUpdateRequested>(_onUpdateRequested);
    on<LoadUpdateStatusRequested>(_onUpdateStatusRequested);
    on<LoadUpdateStatusWithNotesRequested>(_onUpdateStatusWithNotesRequested);
    on<LoadDeleteRequested>(_onDeleteRequested);
    on<LoadAssignRequested>(_onAssignRequested);
    on<LoadSearchRequested>(_onSearchRequested);
    on<LoadRefreshRequested>(_onRefreshRequested);
  }

  Future<void> _onCreateRequested(
    LoadCreateRequested event,
    Emitter<LoadState> emit,
  ) async {
    emit(LoadLoading());

    try {
      final load = await _loadService.createLoad(event.load);
      emit(LoadCreateSuccess(load: load));
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(message: 'Failed to create load: ${e.toString()}'));
    }
  }

  Future<void> _onFetchAllRequested(
    LoadFetchAllRequested event,
    Emitter<LoadState> emit,
  ) async {
    if (event.isRefresh) {
      emit(LoadLoading());
    } else {
      emit(LoadLoadingMore());
    }

    try {
      final response = await _loadService.getAllLoads(
        page: event.page,
        size: event.size,
        sortBy: event.sortBy,
        sortDir: event.sortDir,
      );

      if (event.isRefresh || event.page == 0) {
        emit(LoadListLoaded(response: response));
      } else {
        // Load more - append to existing list
        if (state is LoadListLoaded) {
          final currentState = state as LoadListLoaded;
          final updatedContent = [
            ...currentState.response.content,
            ...response.content,
          ];
          final updatedResponse = PaginatedResponse<LoadModel>(
            content: updatedContent,
            totalElements: response.totalElements,
            totalPages: response.totalPages,
            size: response.size,
            number: response.number,
            first: response.first,
            last: response.last,
            empty: response.empty,
          );
          emit(LoadListLoaded(response: updatedResponse));
        } else {
          emit(LoadListLoaded(response: response));
        }
      }
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(message: 'Failed to fetch loads: ${e.toString()}'));
    }
  }

  Future<void> _onFetchByStatusRequested(
    LoadFetchByStatusRequested event,
    Emitter<LoadState> emit,
  ) async {
    emit(LoadLoading());

    try {
      final response = await _loadService.getLoadsByStatus(
        event.status,
        page: event.page,
        size: event.size,
        sortBy: event.sortBy,
        sortDir: event.sortDir,
      );
      emit(LoadListLoaded(response: response));
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(
          message: 'Failed to fetch loads by status: ${e.toString()}'));
    }
  }

  Future<void> _onFetchVerifiedRequested(
    LoadFetchVerifiedRequested event,
    Emitter<LoadState> emit,
  ) async {
    emit(LoadLoading());

    try {
      final response = await _loadService.getVerifiedLoads(
        page: event.page,
        size: event.size,
        sortBy: event.sortBy,
        sortDir: event.sortDir,
      );
      emit(LoadListLoaded(response: response));
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(
          message: 'Failed to fetch verified loads: ${e.toString()}'));
    }
  }

  Future<void> _onFetchMyLoadsRequested(
    LoadFetchMyLoadsRequested event,
    Emitter<LoadState> emit,
  ) async {
    emit(LoadLoading());

    try {
      final response = await _loadService.getMyLoads(
        page: event.page,
        size: event.size,
        sortBy: event.sortBy,
        sortDir: event.sortDir,
      );
      emit(LoadListLoaded(response: response));
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(message: 'Failed to fetch my loads: ${e.toString()}'));
    }
  }

  Future<void> _onFetchByIdRequested(
    LoadFetchByIdRequested event,
    Emitter<LoadState> emit,
  ) async {
    emit(LoadLoading());

    try {
      final load = await _loadService.getLoadById(event.id);
      emit(LoadDetailLoaded(load: load));
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(message: 'Failed to fetch load details: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateRequested(
    LoadUpdateRequested event,
    Emitter<LoadState> emit,
  ) async {
    emit(LoadLoading());

    try {
      final load = await _loadService.updateLoad(event.id, event.load);
      emit(LoadUpdateSuccess(load: load));
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(message: 'Failed to update load: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateStatusRequested(
    LoadUpdateStatusRequested event,
    Emitter<LoadState> emit,
  ) async {
    emit(LoadLoading());

    try {
      final load = await _loadService.updateLoadStatus(event.id, event.status);
      emit(LoadUpdateSuccess(load: load));
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(message: 'Failed to update load status: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateStatusWithNotesRequested(
    LoadUpdateStatusWithNotesRequested event,
    Emitter<LoadState> emit,
  ) async {
    emit(LoadLoading());

    try {
      final load = await _loadService.updateLoadStatusWithNotes(
        event.id,
        event.status,
        event.notes,
      );
      emit(LoadUpdateSuccess(load: load));
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(message: 'Failed to update load status: ${e.toString()}'));
    }
  }

  Future<void> _onDeleteRequested(
    LoadDeleteRequested event,
    Emitter<LoadState> emit,
  ) async {
    emit(LoadLoading());

    try {
      await _loadService.deleteLoad(event.id);
      emit(LoadDeleteSuccess(id: event.id));
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(message: 'Failed to delete load: ${e.toString()}'));
    }
  }

  Future<void> _onAssignRequested(
    LoadAssignRequested event,
    Emitter<LoadState> emit,
  ) async {
    emit(LoadLoading());

    try {
      final load = await _loadService.assignLoad(event.loadId, event.companyId);
      emit(LoadDetailLoaded(load: load));
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(message: 'Failed to assign load: ${e.toString()}'));
    }
  }

  Future<void> _onSearchRequested(
    LoadSearchRequested event,
    Emitter<LoadState> emit,
  ) async {
    emit(LoadLoading());

    try {
      final response = await _loadService.searchLoads(
        query: event.query,
        loadType: event.loadType,
        status: event.status,
        priority: event.priority,
        pickupLocation: event.pickupLocation,
        deliveryLocation: event.deliveryLocation,
        pickupDateFrom: event.pickupDateFrom,
        pickupDateTo: event.pickupDateTo,
        deliveryDateFrom: event.deliveryDateFrom,
        deliveryDateTo: event.deliveryDateTo,
        minWeight: event.minWeight,
        maxWeight: event.maxWeight,
        requiresInsurance: event.requiresInsurance,
        requiresSpecialHandling: event.requiresSpecialHandling,
        verifiedOnly: event.verifiedOnly,
        page: event.page,
        size: event.size,
        sortBy: event.sortBy,
        sortDir: event.sortDir,
      );
      emit(LoadListLoaded(response: response));
    } on ApiException catch (e) {
      emit(LoadError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(LoadError(message: 'Failed to search loads: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshRequested(
    LoadRefreshRequested event,
    Emitter<LoadState> emit,
  ) async {
    // Refresh current data based on current state
    if (state is LoadListLoaded) {
      add(LoadFetchAllRequested(isRefresh: true));
    } else if (state is LoadDetailLoaded) {
      final currentState = state as LoadDetailLoaded;
      if (currentState.load.id != null) {
        add(LoadFetchByIdRequested(id: currentState.load.id!));
      }
    }
  }
}
