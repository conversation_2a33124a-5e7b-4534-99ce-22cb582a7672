package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.entity.GuestLoadInquiry;
import zw.co.kanjan.logipool.service.GuestLoadInquiryService;

import java.util.Map;

@RestController
@RequestMapping("/api/admin/guest-inquiries")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Admin Guest Inquiries", description = "Admin endpoints for managing guest load inquiries")
@PreAuthorize("hasRole('ADMIN')")
public class AdminGuestInquiryController {
    
    private final GuestLoadInquiryService guestLoadInquiryService;
    
    @GetMapping
    @Operation(summary = "Get all guest load inquiries", 
               description = "Get paginated list of all guest load inquiries")
    public ResponseEntity<Page<GuestLoadInquiry>> getAllInquiries(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<GuestLoadInquiry> inquiries = guestLoadInquiryService.getAllInquiries(pageable);
        return ResponseEntity.ok(inquiries);
    }
    
    @GetMapping("/status/{status}")
    @Operation(summary = "Get inquiries by status", 
               description = "Get paginated list of inquiries filtered by status")
    public ResponseEntity<Page<GuestLoadInquiry>> getInquiriesByStatus(
            @Parameter(description = "Inquiry status") @PathVariable GuestLoadInquiry.InquiryStatus status,
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<GuestLoadInquiry> inquiries = guestLoadInquiryService.getInquiriesByStatus(status, pageable);
        return ResponseEntity.ok(inquiries);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "Get inquiry by ID", 
               description = "Get detailed information about a specific inquiry")
    public ResponseEntity<GuestLoadInquiry> getInquiryById(
            @Parameter(description = "Inquiry ID") @PathVariable Long id) {
        
        GuestLoadInquiry inquiry = guestLoadInquiryService.getInquiryById(id);
        return ResponseEntity.ok(inquiry);
    }
    
    @PutMapping("/{id}/status")
    @Operation(summary = "Update inquiry status", 
               description = "Update the status of a guest load inquiry")
    public ResponseEntity<GuestLoadInquiry> updateInquiryStatus(
            @Parameter(description = "Inquiry ID") @PathVariable Long id,
            @RequestBody @Valid StatusUpdateRequest request) {
        
        GuestLoadInquiry inquiry = guestLoadInquiryService.updateInquiryStatus(
            id, request.status, request.notes);
        return ResponseEntity.ok(inquiry);
    }
    
    @PutMapping("/{id}/assign")
    @Operation(summary = "Assign inquiry to admin", 
               description = "Assign a guest load inquiry to an admin user")
    public ResponseEntity<GuestLoadInquiry> assignInquiry(
            @Parameter(description = "Inquiry ID") @PathVariable Long id,
            @RequestBody @Valid AssignmentRequest request,
            Authentication authentication) {
        
        // If no admin ID provided, assign to current user
        Long adminId = request.adminUserId;
        if (adminId == null) {
            // Get current user ID from authentication
            // This would need to be implemented based on your auth setup
            adminId = getCurrentUserId(authentication);
        }
        
        GuestLoadInquiry inquiry = guestLoadInquiryService.assignInquiry(id, adminId);
        return ResponseEntity.ok(inquiry);
    }
    
    @GetMapping("/stats")
    @Operation(summary = "Get inquiry statistics", 
               description = "Get statistics about guest load inquiries")
    public ResponseEntity<Map<String, Object>> getInquiryStats() {
        Map<String, Object> stats = Map.of(
            "totalInquiries", guestLoadInquiryService.getAllInquiries(Pageable.unpaged()).getTotalElements(),
            "newInquiries", guestLoadInquiryService.getInquiriesByStatus(
                GuestLoadInquiry.InquiryStatus.NEW, Pageable.unpaged()).getTotalElements(),
            "reviewedInquiries", guestLoadInquiryService.getInquiriesByStatus(
                GuestLoadInquiry.InquiryStatus.REVIEWED, Pageable.unpaged()).getTotalElements(),
            "contactedInquiries", guestLoadInquiryService.getInquiriesByStatus(
                GuestLoadInquiry.InquiryStatus.CONTACTED, Pageable.unpaged()).getTotalElements(),
            "convertedInquiries", guestLoadInquiryService.getInquiriesByStatus(
                GuestLoadInquiry.InquiryStatus.CONVERTED, Pageable.unpaged()).getTotalElements()
        );
        
        return ResponseEntity.ok(stats);
    }
    
    private Long getCurrentUserId(Authentication authentication) {
        // This would need to be implemented based on your authentication setup
        // For now, return null and let the service handle it
        return null;
    }
    
    // Inner classes for request bodies
    public static class StatusUpdateRequest {
        public GuestLoadInquiry.InquiryStatus status;
        public String notes;
    }
    
    public static class AssignmentRequest {
        public Long adminUserId;
    }
}
