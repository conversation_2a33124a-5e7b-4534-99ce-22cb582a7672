package zw.co.kanjan.logipool.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import zw.co.kanjan.logipool.dto.company.CompanyCreateRequest;
import zw.co.kanjan.logipool.dto.company.CompanyResponse;
import zw.co.kanjan.logipool.entity.Company;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface CompanyMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "user", ignore = true)
    @Mapping(target = "verificationStatus", ignore = true)
    @Mapping(target = "rating", ignore = true)
    @Mapping(target = "totalJobs", ignore = true)
    @Mapping(target = "completedJobs", ignore = true)
    @Mapping(target = "vehicles", ignore = true)
    @Mapping(target = "documents", ignore = true)
    @Mapping(target = "bids", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Company toEntity(CompanyCreateRequest request);
    
    @Mapping(source = "user.id", target = "userId")
    @Mapping(source = "user.username", target = "userName")
    CompanyResponse toResponse(Company company);
}
