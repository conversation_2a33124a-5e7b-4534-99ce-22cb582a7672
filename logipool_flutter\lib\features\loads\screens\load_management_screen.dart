import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/load_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/widgets/unified_header.dart';
import '../bloc/load_bloc.dart';
import '../widgets/load_status_update_card.dart';
import '../widgets/load_documents_section.dart';
import '../widgets/load_tracking_section.dart';
import '../widgets/load_invoice_section.dart';

class LoadManagementScreen extends StatefulWidget {
  final int loadId;

  const LoadManagementScreen({
    super.key,
    required this.loadId,
  });

  @override
  State<LoadManagementScreen> createState() => _LoadManagementScreenState();
}

class _LoadManagementScreenState extends State<LoadManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  LoadModel? _currentLoad;
  UserModel? _currentUser;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _currentUser = context.read<AuthService>().currentUser;
    _loadLoadDetails();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadLoadDetails() {
    context.read<LoadBloc>().add(LoadFetchByIdRequested(id: widget.loadId));
  }

  bool _canManageLoad() {
    if (_currentLoad == null ||
        _currentUser == null ||
        _currentUser!.company == null) return false;

    // Check if user's company is assigned to this load
    final userCompanyId = int.tryParse(_currentUser!.company!.id);
    final assignedCompanyId = _currentLoad?.assignedCompanyId;

    return userCompanyId != null &&
        assignedCompanyId != null &&
        userCompanyId == assignedCompanyId;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedHeader(
        title: 'Load Management',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadLoadDetails,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: BlocListener<LoadBloc, LoadState>(
        listener: (context, state) {
          if (state is LoadDetailLoaded) {
            setState(() {
              _currentLoad = state.load;
            });
          } else if (state is LoadUpdateSuccess) {
            // Refresh load details to show updated information
            _loadLoadDetails();
          } else if (state is LoadError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: BlocBuilder<LoadBloc, LoadState>(
          builder: (context, state) {
            if (state is LoadLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state is LoadDetailLoaded) {
              final load = state.load;

              if (!_canManageLoad()) {
                return _buildUnauthorizedView();
              }

              return Column(
                children: [
                  _buildLoadHeader(load),
                  _buildTabBar(),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        LoadStatusUpdateCard(load: load),
                        LoadDocumentsSection(load: load),
                        LoadTrackingSection(load: load),
                        LoadInvoiceSection(load: load),
                      ],
                    ),
                  ),
                ],
              );
            }

            if (state is LoadError) {
              return _buildErrorView(state.message);
            }

            return const Center(
              child: Text('Load not found'),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLoadHeader(LoadModel load) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  load.title,
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
              _buildStatusChip(load.status),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Load #${load.id}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  '${load.pickupLocation} → ${load.deliveryLocation}',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                'Pickup: ${DateFormat('MMM dd, yyyy').format(load.pickupDate)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(width: 16),
              Icon(Icons.flag, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                'Delivery: ${DateFormat('MMM dd, yyyy').format(load.deliveryDate)}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(LoadStatus status) {
    Color backgroundColor;
    Color textColor;

    switch (status) {
      case LoadStatus.assigned:
        backgroundColor = Colors.blue.shade100;
        textColor = Colors.blue.shade800;
        break;
      case LoadStatus.inTransit:
        backgroundColor = Colors.orange.shade100;
        textColor = Colors.orange.shade800;
        break;
      case LoadStatus.delivered:
        backgroundColor = Colors.green.shade100;
        textColor = Colors.green.shade800;
        break;
      case LoadStatus.cancelled:
        backgroundColor = Colors.red.shade100;
        textColor = Colors.red.shade800;
        break;
      default:
        backgroundColor = Colors.grey.shade100;
        textColor = Colors.grey.shade800;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade300,
            width: 1,
          ),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Theme.of(context).primaryColor,
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: Theme.of(context).primaryColor,
        tabs: const [
          Tab(
            icon: Icon(Icons.update),
            text: 'Status',
          ),
          Tab(
            icon: Icon(Icons.folder),
            text: 'Documents',
          ),
          Tab(
            icon: Icon(Icons.location_on),
            text: 'Tracking',
          ),
          Tab(
            icon: Icon(Icons.receipt),
            text: 'Invoice',
          ),
        ],
      ),
    );
  }

  Widget _buildUnauthorizedView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.lock,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Access Denied',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'You do not have permission to manage this load.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => context.pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Error',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.red[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: _loadLoadDetails,
                child: const Text('Retry'),
              ),
              const SizedBox(width: 16),
              OutlinedButton(
                onPressed: () => context.pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
