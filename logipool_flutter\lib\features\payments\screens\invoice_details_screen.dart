import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/invoice_model.dart';
import '../../../shared/services/file_download_service.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../../invoices/bloc/invoice_bloc.dart';

class InvoiceDetailsScreen extends StatefulWidget {
  final String invoiceId;

  const InvoiceDetailsScreen({
    super.key,
    required this.invoiceId,
  });

  @override
  State<InvoiceDetailsScreen> createState() => _InvoiceDetailsScreenState();
}

class _InvoiceDetailsScreenState extends State<InvoiceDetailsScreen> {
  @override
  void initState() {
    super.initState();
    // Load the invoice details
    context.read<InvoiceBloc>().add(
          InvoiceGetById(invoiceId: int.parse(widget.invoiceId)),
        );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Invoice Details',
      ),
      body: BlocListener<InvoiceBloc, InvoiceState>(
        listener: (context, state) {
          if (state is InvoicePdfDownloaded) {
            _handlePdfDownload(state.pdfBytes);
          } else if (state is InvoiceError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: BlocBuilder<InvoiceBloc, InvoiceState>(
          builder: (context, state) {
            if (state is InvoiceLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state is InvoiceError) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.error_outline,
                      size: 64,
                      color: Colors.red[300],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Error loading invoice',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.message,
                      style: Theme.of(context).textTheme.bodyMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => context.pop(),
                      child: const Text('Go Back'),
                    ),
                  ],
                ),
              );
            }

            if (state is InvoiceLoaded) {
              return _buildInvoiceDetails(state.invoice);
            }

            return const Center(
              child: Text('No invoice data available'),
            );
          },
        ),
      ),
    );
  }

  Widget _buildInvoiceDetails(InvoiceResponse invoice) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInvoiceHeader(invoice),
          const SizedBox(height: 16),
          _buildInvoiceInfo(invoice),
          const SizedBox(height: 16),
          _buildInvoiceItems(invoice),
          const SizedBox(height: 16),
          _buildInvoiceTotals(invoice),
          const SizedBox(height: 24),
          _buildActionButtons(invoice),
        ],
      ),
    );
  }

  Widget _buildInvoiceHeader(InvoiceResponse invoice) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Invoice #${invoice.invoiceNumber ?? 'N/A'}',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                _buildStatusChip(invoice.status),
              ],
            ),
            const SizedBox(height: 8),
            if (invoice.createdAt != null)
              Text(
                'Created: ${DateFormat('MMM dd, yyyy').format(invoice.createdAt!)}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            Text(
              'Due: ${DateFormat('MMM dd, yyyy').format(invoice.dueDate)}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(InvoiceStatus status) {
    Color color;
    String label;

    switch (status) {
      case InvoiceStatus.draft:
        color = Colors.grey;
        label = 'Draft';
        break;
      case InvoiceStatus.sent:
        color = Colors.blue;
        label = 'Sent';
        break;
      case InvoiceStatus.viewed:
        color = Colors.lightBlue;
        label = 'Viewed';
        break;
      case InvoiceStatus.paid:
        color = Colors.green;
        label = 'Paid';
        break;
      case InvoiceStatus.overdue:
        color = Colors.red;
        label = 'Overdue';
        break;
      case InvoiceStatus.cancelled:
        color = Colors.orange;
        label = 'Cancelled';
        break;
      case InvoiceStatus.refunded:
        color = Colors.purple;
        label = 'Refunded';
        break;
    }

    return Chip(
      label: Text(
        label,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: color,
    );
  }

  Widget _buildInvoiceInfo(InvoiceResponse invoice) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invoice Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            if (invoice.description != null && invoice.description!.isNotEmpty)
              _buildInfoRow('Description', invoice.description!),
            if (invoice.notes != null && invoice.notes!.isNotEmpty)
              _buildInfoRow('Notes', invoice.notes!),
            if (invoice.loadId != null) ...[
              _buildInfoRow('Load ID', invoice.loadId.toString()),
              if (invoice.loadTitle != null && invoice.loadTitle!.isNotEmpty)
                _buildInfoRow('Load Title', invoice.loadTitle!),
            ],
            if (invoice.clientName != null && invoice.clientName!.isNotEmpty)
              _buildInfoRow('Client', invoice.clientName!),
            if (invoice.transporterName != null &&
                invoice.transporterName!.isNotEmpty)
              _buildInfoRow('Transporter', invoice.transporterName!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceItems(InvoiceResponse invoice) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invoice Items',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            if (invoice.items != null && invoice.items!.isNotEmpty)
              ...invoice.items!.map((item) => _buildInvoiceItem(item))
            else
              const Text('No items available'),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceItem(InvoiceItemModel item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(item.description),
          ),
          Expanded(
            child: Text(
              '${item.quantity}',
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: Text(
              '\$${item.unitPrice.toStringAsFixed(2)}',
              textAlign: TextAlign.right,
            ),
          ),
          Expanded(
            child: Text(
              '\$${item.totalPrice.toStringAsFixed(2)}',
              textAlign: TextAlign.right,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceTotals(InvoiceResponse invoice) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Totals',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            _buildTotalRow('Subtotal', invoice.subtotal ?? 0.0),
            _buildTotalRow('Tax', invoice.taxAmount ?? 0.0),
            _buildTotalRow('Discount', invoice.discountAmount ?? 0.0),
            const Divider(),
            _buildTotalRow('Total Amount', invoice.totalAmount ?? 0.0,
                isTotal: true),
            _buildTotalRow('Commission', invoice.commissionAmount ?? 0.0),
            _buildTotalRow('Net Amount', invoice.netAmount ?? 0.0,
                isTotal: true),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalRow(String label, double amount, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                ),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(InvoiceResponse invoice) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _downloadInvoice(invoice),
            icon: const Icon(Icons.download),
            label: const Text('Download PDF'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => context.pop(),
            icon: const Icon(Icons.arrow_back),
            label: const Text('Go Back'),
          ),
        ),
      ],
    );
  }

  void _downloadInvoice(InvoiceResponse invoice) {
    if (invoice.id == null) return;

    context.read<InvoiceBloc>().add(
          InvoiceDownloadPdf(invoiceId: invoice.id!),
        );
  }

  Future<void> _handlePdfDownload(List<int> pdfBytes) async {
    try {
      // Generate a filename for the invoice using invoice number if available
      var fileName = 'Invoice.pdf';

      // Try to get the current invoice from the bloc state
      final currentState = context.read<InvoiceBloc>().state;
      if (currentState is InvoiceLoaded &&
          currentState.invoice.invoiceNumber != null) {
        fileName = 'Invoice_${currentState.invoice.invoiceNumber}.pdf';
      } else {
        // Fallback to timestamp if invoice number is not available
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        fileName = 'Invoice_$timestamp.pdf';
      }

      // Convert List<int> to Uint8List
      final uint8List = Uint8List.fromList(pdfBytes);

      // Save the PDF file
      final filePath = await FileDownloadService.savePdfBytes(
        pdfBytes: uint8List,
        fileName: fileName,
      );

      if (!mounted) {
        return;
      }

      if (filePath != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Invoice PDF saved to: ${filePath.split('/').last}'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save invoice PDF'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } on Exception catch (e) {
      if (!mounted) {
        return;
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving PDF: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
