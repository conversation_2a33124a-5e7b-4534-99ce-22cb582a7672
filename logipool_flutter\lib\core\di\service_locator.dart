import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Services
import '../../shared/services/auth_service.dart';
import '../../shared/services/load_service.dart';
import '../../shared/services/bid_service.dart';
import '../../shared/services/company_service.dart';
import '../../shared/services/document_service.dart';
import '../../shared/services/tracking_service.dart';
import '../../shared/services/notification_service.dart';
import '../../shared/services/notification_api_service.dart';
import '../../shared/services/admin_api_service.dart';
import '../../shared/services/storage_service.dart';
import '../../shared/services/payment_service.dart';
import '../../shared/services/company_member_service.dart';
import '../../shared/services/invoice_service.dart';
import '../../shared/services/location_tracking_service.dart';

// Network
import '../network/api_client.dart';
import '../constants/app_constants.dart';

// Utils
import '../utils/logger.dart';

final GetIt getIt = GetIt.instance;

class ServiceLocator {
  static Future<void> init() async {
    // External dependencies
    final sharedPreferences = await SharedPreferences.getInstance();
    getIt.registerSingleton<SharedPreferences>(sharedPreferences);

    // Initialize storage service
    await StorageService.init();

    // Dio instance
    final dio = Dio();
    getIt.registerSingleton<Dio>(dio);

    // Core services
    getIt.registerSingleton<Logger>(Logger());
    getIt
        .registerSingleton<ApiClient>(ApiClient(baseUrl: AppConstants.baseUrl));
    getIt.registerSingleton<StorageService>(StorageService());

    // App services
    getIt.registerLazySingleton<AuthService>(
      () => AuthService(
        apiClient: getIt<ApiClient>(),
        storageService: getIt<StorageService>(),
      ),
    );

    getIt.registerLazySingleton<LoadService>(
      () => LoadService(getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<BidService>(
      () => BidService(getIt<AuthService>()),
    );

    getIt.registerLazySingleton<CompanyService>(
      () => CompanyService(apiClient: getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<DocumentService>(
      () => DocumentService(getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<TrackingService>(
      () => TrackingService(apiClient: getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<NotificationService>(
      () => NotificationService(),
    );

    getIt.registerLazySingleton<NotificationApiService>(
      () => NotificationApiService(getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<AdminApiService>(
      () => AdminApiService(getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<PaymentService>(
      () => PaymentService(apiClient: getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<CompanyMemberService>(
      () => CompanyMemberService(getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<InvoiceService>(
      () => InvoiceService(getIt<ApiClient>()),
    );

    getIt.registerLazySingleton<LocationTrackingService>(
      () => LocationTrackingService(getIt<ApiClient>()),
    );
  }

  static void reset() {
    getIt.reset();
  }
}

// Convenience getters
T get<T extends Object>() => getIt<T>();

// Service getters
AuthService get authService => getIt<AuthService>();
LoadService get loadService => getIt<LoadService>();
BidService get bidService => getIt<BidService>();
CompanyService get companyService => getIt<CompanyService>();
DocumentService get documentService => getIt<DocumentService>();
TrackingService get trackingService => getIt<TrackingService>();
NotificationService get notificationService => getIt<NotificationService>();
NotificationApiService get notificationApiService =>
    getIt<NotificationApiService>();
AdminApiService get adminApiService => getIt<AdminApiService>();
PaymentService get paymentService => getIt<PaymentService>();
CompanyMemberService get companyMemberService => getIt<CompanyMemberService>();
InvoiceService get invoiceService => getIt<InvoiceService>();
LocationTrackingService get locationTrackingService => getIt<LocationTrackingService>();
ApiClient get apiClient => getIt<ApiClient>();
Logger get logger => getIt<Logger>();
