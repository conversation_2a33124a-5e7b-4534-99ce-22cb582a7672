// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LocationUpdateRequest _$LocationUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    LocationUpdateRequest(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      accuracy: (json['accuracy'] as num?)?.toDouble(),
      speed: (json['speed'] as num?)?.toDouble(),
      heading: (json['heading'] as num?)?.toDouble(),
      altitude: (json['altitude'] as num?)?.toDouble(),
      address: json['address'] as String?,
      source: $enumDecodeNullable(_$LocationSourceEnumMap, json['source']),
      loadId: (json['loadId'] as num?)?.toInt(),
      isShared: json['isShared'] as bool? ?? true,
      isOnDuty: json['isOnDuty'] as bool? ?? false,
      deviceId: json['deviceId'] as String?,
      sessionId: json['sessionId'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$LocationUpdateRequestToJson(
        LocationUpdateRequest instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'accuracy': instance.accuracy,
      'speed': instance.speed,
      'heading': instance.heading,
      'altitude': instance.altitude,
      'address': instance.address,
      'source': _$LocationSourceEnumMap[instance.source],
      'loadId': instance.loadId,
      'isShared': instance.isShared,
      'isOnDuty': instance.isOnDuty,
      'deviceId': instance.deviceId,
      'sessionId': instance.sessionId,
      'notes': instance.notes,
    };

const _$LocationSourceEnumMap = {
  LocationSource.gps: 'GPS',
  LocationSource.network: 'NETWORK',
  LocationSource.manual: 'MANUAL',
  LocationSource.estimated: 'ESTIMATED',
};

LocationResponse _$LocationResponseFromJson(Map<String, dynamic> json) =>
    LocationResponse(
      id: (json['id'] as num).toInt(),
      driverId: (json['driverId'] as num).toInt(),
      driverName: json['driverName'] as String,
      loadId: (json['loadId'] as num?)?.toInt(),
      loadTitle: json['loadTitle'] as String?,
      companyId: (json['companyId'] as num?)?.toInt(),
      companyName: json['companyName'] as String?,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      accuracy: (json['accuracy'] as num?)?.toDouble(),
      speed: (json['speed'] as num?)?.toDouble(),
      heading: (json['heading'] as num?)?.toDouble(),
      altitude: (json['altitude'] as num?)?.toDouble(),
      address: json['address'] as String?,
      source: $enumDecode(_$LocationSourceEnumMap, json['source']),
      status: $enumDecode(_$LocationStatusEnumMap, json['status']),
      isShared: json['isShared'] as bool,
      isOnDuty: json['isOnDuty'] as bool,
      deviceId: json['deviceId'] as String?,
      sessionId: json['sessionId'] as String?,
      notes: json['notes'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
      expiresAt: json['expiresAt'] == null
          ? null
          : DateTime.parse(json['expiresAt'] as String),
    );

Map<String, dynamic> _$LocationResponseToJson(LocationResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'driverId': instance.driverId,
      'driverName': instance.driverName,
      'loadId': instance.loadId,
      'loadTitle': instance.loadTitle,
      'companyId': instance.companyId,
      'companyName': instance.companyName,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'accuracy': instance.accuracy,
      'speed': instance.speed,
      'heading': instance.heading,
      'altitude': instance.altitude,
      'address': instance.address,
      'source': _$LocationSourceEnumMap[instance.source]!,
      'status': _$LocationStatusEnumMap[instance.status]!,
      'isShared': instance.isShared,
      'isOnDuty': instance.isOnDuty,
      'deviceId': instance.deviceId,
      'sessionId': instance.sessionId,
      'notes': instance.notes,
      'timestamp': instance.timestamp.toIso8601String(),
      'expiresAt': instance.expiresAt?.toIso8601String(),
    };

const _$LocationStatusEnumMap = {
  LocationStatus.active: 'ACTIVE',
  LocationStatus.historical: 'HISTORICAL',
  LocationStatus.estimated: 'ESTIMATED',
  LocationStatus.offline: 'OFFLINE',
};

TrackingSummary _$TrackingSummaryFromJson(Map<String, dynamic> json) =>
    TrackingSummary(
      totalActiveDrivers: (json['totalActiveDrivers'] as num).toInt(),
      driversOnDuty: (json['driversOnDuty'] as num).toInt(),
      driversWithSharedLocation:
          (json['driversWithSharedLocation'] as num).toInt(),
      activeTrackedLoads: (json['activeTrackedLoads'] as num).toInt(),
      recentLocationUpdates: (json['recentLocationUpdates'] as num).toInt(),
      activeDrivers: (json['activeDrivers'] as List<dynamic>)
          .map((e) => LocationResponse.fromJson(e as Map<String, dynamic>))
          .toList(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$TrackingSummaryToJson(TrackingSummary instance) =>
    <String, dynamic>{
      'totalActiveDrivers': instance.totalActiveDrivers,
      'driversOnDuty': instance.driversOnDuty,
      'driversWithSharedLocation': instance.driversWithSharedLocation,
      'activeTrackedLoads': instance.activeTrackedLoads,
      'recentLocationUpdates': instance.recentLocationUpdates,
      'activeDrivers': instance.activeDrivers,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
    };

LocationPermissionRequest _$LocationPermissionRequestFromJson(
        Map<String, dynamic> json) =>
    LocationPermissionRequest(
      driverId: (json['driverId'] as num).toInt(),
      companyId: (json['companyId'] as num).toInt(),
      loadId: (json['loadId'] as num?)?.toInt(),
      permissionType:
          $enumDecode(_$PermissionTypeEnumMap, json['permissionType']),
      allowRealTimeTracking: json['allowRealTimeTracking'] as bool? ?? false,
      allowHistoricalData: json['allowHistoricalData'] as bool? ?? false,
      allowClientAccess: json['allowClientAccess'] as bool? ?? false,
      allowEmergencyAccess: json['allowEmergencyAccess'] as bool? ?? true,
      validFrom: json['validFrom'] == null
          ? null
          : DateTime.parse(json['validFrom'] as String),
      validUntil: json['validUntil'] == null
          ? null
          : DateTime.parse(json['validUntil'] as String),
      conditions: json['conditions'] as String?,
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$LocationPermissionRequestToJson(
        LocationPermissionRequest instance) =>
    <String, dynamic>{
      'driverId': instance.driverId,
      'companyId': instance.companyId,
      'loadId': instance.loadId,
      'permissionType': _$PermissionTypeEnumMap[instance.permissionType]!,
      'allowRealTimeTracking': instance.allowRealTimeTracking,
      'allowHistoricalData': instance.allowHistoricalData,
      'allowClientAccess': instance.allowClientAccess,
      'allowEmergencyAccess': instance.allowEmergencyAccess,
      'validFrom': instance.validFrom?.toIso8601String(),
      'validUntil': instance.validUntil?.toIso8601String(),
      'conditions': instance.conditions,
      'notes': instance.notes,
    };

const _$PermissionTypeEnumMap = {
  PermissionType.general: 'GENERAL',
  PermissionType.loadSpecific: 'LOAD_SPECIFIC',
  PermissionType.emergencyOnly: 'EMERGENCY_ONLY',
  PermissionType.companyWide: 'COMPANY_WIDE',
};

LocationPermissionResponse _$LocationPermissionResponseFromJson(
        Map<String, dynamic> json) =>
    LocationPermissionResponse(
      id: (json['id'] as num).toInt(),
      driverId: (json['driverId'] as num).toInt(),
      driverName: json['driverName'] as String,
      companyId: (json['companyId'] as num).toInt(),
      companyName: json['companyName'] as String,
      loadId: (json['loadId'] as num?)?.toInt(),
      loadTitle: json['loadTitle'] as String?,
      permissionType:
          $enumDecode(_$PermissionTypeEnumMap, json['permissionType']),
      status: $enumDecode(_$PermissionStatusEnumMap, json['status']),
      isActive: json['isActive'] as bool,
      allowRealTimeTracking: json['allowRealTimeTracking'] as bool,
      allowHistoricalData: json['allowHistoricalData'] as bool,
      allowClientAccess: json['allowClientAccess'] as bool,
      allowEmergencyAccess: json['allowEmergencyAccess'] as bool,
      validFrom: json['validFrom'] == null
          ? null
          : DateTime.parse(json['validFrom'] as String),
      validUntil: json['validUntil'] == null
          ? null
          : DateTime.parse(json['validUntil'] as String),
      conditions: json['conditions'] as String?,
      notes: json['notes'] as String?,
      grantedBy: json['grantedBy'] as String?,
      grantedAt: json['grantedAt'] == null
          ? null
          : DateTime.parse(json['grantedAt'] as String),
      revokedBy: json['revokedBy'] as String?,
      revokedAt: json['revokedAt'] == null
          ? null
          : DateTime.parse(json['revokedAt'] as String),
      revokeReason: json['revokeReason'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$LocationPermissionResponseToJson(
        LocationPermissionResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'driverId': instance.driverId,
      'driverName': instance.driverName,
      'companyId': instance.companyId,
      'companyName': instance.companyName,
      'loadId': instance.loadId,
      'loadTitle': instance.loadTitle,
      'permissionType': _$PermissionTypeEnumMap[instance.permissionType]!,
      'status': _$PermissionStatusEnumMap[instance.status]!,
      'isActive': instance.isActive,
      'allowRealTimeTracking': instance.allowRealTimeTracking,
      'allowHistoricalData': instance.allowHistoricalData,
      'allowClientAccess': instance.allowClientAccess,
      'allowEmergencyAccess': instance.allowEmergencyAccess,
      'validFrom': instance.validFrom?.toIso8601String(),
      'validUntil': instance.validUntil?.toIso8601String(),
      'conditions': instance.conditions,
      'notes': instance.notes,
      'grantedBy': instance.grantedBy,
      'grantedAt': instance.grantedAt?.toIso8601String(),
      'revokedBy': instance.revokedBy,
      'revokedAt': instance.revokedAt?.toIso8601String(),
      'revokeReason': instance.revokeReason,
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$PermissionStatusEnumMap = {
  PermissionStatus.pending: 'PENDING',
  PermissionStatus.granted: 'GRANTED',
  PermissionStatus.denied: 'DENIED',
  PermissionStatus.revoked: 'REVOKED',
  PermissionStatus.expired: 'EXPIRED',
};
