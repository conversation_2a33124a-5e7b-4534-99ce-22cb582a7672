import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/di/service_locator.dart';
import '../../../shared/models/load_model.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../bloc/load_bloc.dart';

class LoadsScreen extends StatefulWidget {
  const LoadsScreen({super.key});

  @override
  State<LoadsScreen> createState() => _LoadsScreenState();
}

class _LoadsScreenState extends State<LoadsScreen> {
  final TextEditingController _searchController = TextEditingController();
  LoadStatus? _selectedStatus;
  String _sortBy = 'createdAt';
  String _sortDir = 'desc';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Trigger initial load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<LoadBloc>().add(const LoadFetchAllRequested());
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Loads'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => context.push('/loads/create'),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildStatusFilter(),
          Expanded(
            child: BlocBuilder<LoadBloc, LoadState>(
              builder: (context, state) {
                if (state is LoadLoading) {
                  return const LoadingWidget();
                }

                if (state is LoadError) {
                  return CustomErrorWidget(
                    message: state.message,
                    onRetry: () => context.read<LoadBloc>().add(
                          const LoadFetchAllRequested(isRefresh: true),
                        ),
                  );
                }

                if (state is LoadListLoaded) {
                  final loads = state.response.content;

                  if (loads.isEmpty) {
                    return const EmptyStateWidget(
                      icon: Icon(Icons.local_shipping),
                      title: 'No Loads Found',
                      message: 'Create your first load to get started',
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      context.read<LoadBloc>().add(
                            const LoadFetchAllRequested(isRefresh: true),
                          );
                    },
                    child: ListView.builder(
                      itemCount: loads.length,
                      itemBuilder: (context, index) {
                        final load = loads[index];
                        return _buildLoadCard(context, load);
                      },
                    ),
                  );
                }

                return const EmptyStateWidget(
                  icon: Icon(Icons.local_shipping),
                  title: 'No Loads Found',
                  message: 'Create your first load to get started',
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search loads...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _performSearch();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: (value) => _performSearch(),
      ),
    );
  }

  Widget _buildStatusFilter() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildStatusChip('All', null),
          const SizedBox(width: 8),
          _buildStatusChip('Posted', LoadStatus.posted),
          const SizedBox(width: 8),
          _buildStatusChip('Assigned', LoadStatus.assigned),
          const SizedBox(width: 8),
          _buildStatusChip('In Transit', LoadStatus.inTransit),
          const SizedBox(width: 8),
          _buildStatusChip('Delivered', LoadStatus.delivered),
          const SizedBox(width: 8),
          _buildStatusChip('Cancelled', LoadStatus.cancelled),
        ],
      ),
    );
  }

  Widget _buildStatusChip(String label, LoadStatus? status) {
    final isSelected = _selectedStatus == status;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedStatus = selected ? status : null;
        });
        _filterByStatus();
      },
      backgroundColor: Colors.grey[200],
      selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
    );
  }

  Widget _buildLoadCard(BuildContext context, LoadModel load) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => context.push('/loads/${load.id}'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      load.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                  _buildStatusBadge(load.status),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      '${load.pickupLocation} → ${load.deliveryLocation}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Pickup: ${_formatDate(load.pickupDate)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.scale, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${load.weight} ${load.weightUnit}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.category, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    load.cargoType,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              if (load.bidCount > 0) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.gavel, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${load.bidCount} bid${load.bidCount == 1 ? '' : 's'}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusBadge(LoadStatus status) {
    Color color;
    String text;

    switch (status) {
      case LoadStatus.posted:
        color = Colors.green;
        text = 'Posted';
        break;
      case LoadStatus.biddingClosed:
        color = Colors.orange;
        text = 'Bidding Closed';
        break;
      case LoadStatus.assigned:
        color = Colors.blue;
        text = 'Assigned';
        break;
      case LoadStatus.inTransit:
        color = Colors.purple;
        text = 'In Transit';
        break;
      case LoadStatus.delivered:
        color = Colors.grey;
        text = 'Delivered';
        break;
      case LoadStatus.completed:
        color = Colors.teal;
        text = 'Completed';
        break;
      case LoadStatus.cancelled:
        color = Colors.red;
        text = 'Cancelled';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _performSearch() {
    final query = _searchController.text.trim();
    if (query.isNotEmpty) {
      context.read<LoadBloc>().add(LoadSearchRequested(query: query));
    } else {
      context
          .read<LoadBloc>()
          .add(const LoadFetchAllRequested(isRefresh: true));
    }
  }

  void _filterByStatus() {
    if (_selectedStatus != null) {
      context.read<LoadBloc>().add(
            LoadFetchByStatusRequested(status: _selectedStatus!),
          );
    } else {
      context
          .read<LoadBloc>()
          .add(const LoadFetchAllRequested(isRefresh: true));
    }
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Sort & Filter'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Sort by'),
              subtitle: DropdownButton<String>(
                value: _sortBy,
                isExpanded: true,
                items: const [
                  DropdownMenuItem(
                      value: 'createdAt', child: Text('Created Date')),
                  DropdownMenuItem(
                      value: 'pickupDate', child: Text('Pickup Date')),
                  DropdownMenuItem(
                      value: 'deliveryDate', child: Text('Delivery Date')),
                  DropdownMenuItem(value: 'weight', child: Text('Weight')),
                  DropdownMenuItem(value: 'title', child: Text('Title')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _sortBy = value;
                    });
                  }
                },
              ),
            ),
            ListTile(
              title: const Text('Sort direction'),
              subtitle: DropdownButton<String>(
                value: _sortDir,
                isExpanded: true,
                items: const [
                  DropdownMenuItem(value: 'desc', child: Text('Descending')),
                  DropdownMenuItem(value: 'asc', child: Text('Ascending')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _sortDir = value;
                    });
                  }
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<LoadBloc>().add(
                    LoadFetchAllRequested(
                      isRefresh: true,
                      sortBy: _sortBy,
                      sortDir: _sortDir,
                    ),
                  );
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }
}
