import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/models/payment_model.dart';
import '../../../shared/models/invoice_model.dart';
import '../../../shared/models/paginated_response.dart';
import '../../../shared/services/payment_service.dart';

// Events
abstract class PaymentEvent extends Equatable {
  const PaymentEvent();

  @override
  List<Object?> get props => [];
}

class LoadPayments extends PaymentEvent {
  final bool refresh;
  final PaymentStatus? status;
  final int? loadId;
  final DateTime? startDate;
  final DateTime? endDate;

  const LoadPayments({
    this.refresh = false,
    this.status,
    this.loadId,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [refresh, status, loadId, startDate, endDate];
}

class LoadMorePayments extends PaymentEvent {
  const LoadMorePayments();
}

class CreatePayment extends PaymentEvent {
  final PaymentCreateRequest request;

  const CreatePayment(this.request);

  @override
  List<Object> get props => [request];
}

class ProcessPayment extends PaymentEvent {
  final PaymentProcessRequest request;

  const ProcessPayment(this.request);

  @override
  List<Object> get props => [request];
}

class UpdatePayment extends PaymentEvent {
  final int paymentId;
  final PaymentUpdateRequest request;

  const UpdatePayment(this.paymentId, this.request);

  @override
  List<Object> get props => [paymentId, request];
}

class CancelPayment extends PaymentEvent {
  final int paymentId;

  const CancelPayment(this.paymentId);

  @override
  List<Object> get props => [paymentId];
}

class DeletePayment extends PaymentEvent {
  final int paymentId;

  const DeletePayment(this.paymentId);

  @override
  List<Object> get props => [paymentId];
}

class LoadPaymentDetails extends PaymentEvent {
  final int paymentId;

  const LoadPaymentDetails(this.paymentId);

  @override
  List<Object> get props => [paymentId];
}

class LoadPaymentStatus extends PaymentEvent {
  final int paymentId;

  const LoadPaymentStatus(this.paymentId);

  @override
  List<Object> get props => [paymentId];
}

class LoadPaymentSummary extends PaymentEvent {
  final bool isAdmin;

  const LoadPaymentSummary({this.isAdmin = false});

  @override
  List<Object> get props => [isAdmin];
}

class LoadInvoices extends PaymentEvent {
  final bool refresh;

  const LoadInvoices({this.refresh = false});

  @override
  List<Object> get props => [refresh];
}

class LoadMoreInvoices extends PaymentEvent {
  const LoadMoreInvoices();
}

class CreateInvoice extends PaymentEvent {
  final InvoiceCreateRequest request;

  const CreateInvoice(this.request);

  @override
  List<Object> get props => [request];
}

class UpdateInvoice extends PaymentEvent {
  final int invoiceId;
  final InvoiceUpdateRequest request;

  const UpdateInvoice(this.invoiceId, this.request);

  @override
  List<Object> get props => [invoiceId, request];
}

class SendInvoice extends PaymentEvent {
  final int invoiceId;

  const SendInvoice(this.invoiceId);

  @override
  List<Object> get props => [invoiceId];
}

class MarkInvoiceAsPaid extends PaymentEvent {
  final int invoiceId;

  const MarkInvoiceAsPaid(this.invoiceId);

  @override
  List<Object> get props => [invoiceId];
}

class DownloadInvoicePdf extends PaymentEvent {
  final int invoiceId;

  const DownloadInvoicePdf(this.invoiceId);

  @override
  List<Object> get props => [invoiceId];
}

class LoadInvoiceDetails extends PaymentEvent {
  final int invoiceId;

  const LoadInvoiceDetails(this.invoiceId);

  @override
  List<Object> get props => [invoiceId];
}

class LoadInvoiceSummary extends PaymentEvent {
  const LoadInvoiceSummary();
}

// States
abstract class PaymentState extends Equatable {
  const PaymentState();

  @override
  List<Object?> get props => [];
}

class PaymentInitial extends PaymentState {
  const PaymentInitial();
}

class PaymentLoading extends PaymentState {
  const PaymentLoading();
}

class PaymentLoaded extends PaymentState {
  final List<PaymentModel> payments;
  final bool hasReachedMax;
  final int currentPage;
  final PaymentStatus? currentStatus;
  final int? currentLoadId;
  final DateTime? currentStartDate;
  final DateTime? currentEndDate;

  const PaymentLoaded({
    required this.payments,
    required this.hasReachedMax,
    required this.currentPage,
    this.currentStatus,
    this.currentLoadId,
    this.currentStartDate,
    this.currentEndDate,
  });

  @override
  List<Object?> get props => [
        payments,
        hasReachedMax,
        currentPage,
        currentStatus,
        currentLoadId,
        currentStartDate,
        currentEndDate,
      ];

  PaymentLoaded copyWith({
    List<PaymentModel>? payments,
    bool? hasReachedMax,
    int? currentPage,
    PaymentStatus? currentStatus,
    int? currentLoadId,
    DateTime? currentStartDate,
    DateTime? currentEndDate,
  }) {
    return PaymentLoaded(
      payments: payments ?? this.payments,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      currentStatus: currentStatus ?? this.currentStatus,
      currentLoadId: currentLoadId ?? this.currentLoadId,
      currentStartDate: currentStartDate ?? this.currentStartDate,
      currentEndDate: currentEndDate ?? this.currentEndDate,
    );
  }
}

class PaymentDetailsLoaded extends PaymentState {
  final PaymentModel payment;

  const PaymentDetailsLoaded(this.payment);

  @override
  List<Object> get props => [payment];
}

class PaymentStatusLoaded extends PaymentState {
  final PaymentStatusResponse status;

  const PaymentStatusLoaded(this.status);

  @override
  List<Object> get props => [status];
}

class PaymentSummaryLoaded extends PaymentState {
  final PaymentSummary summary;

  const PaymentSummaryLoaded(this.summary);

  @override
  List<Object> get props => [summary];
}

class InvoiceLoaded extends PaymentState {
  final List<InvoiceModel> invoices;
  final bool hasReachedMax;
  final int currentPage;

  const InvoiceLoaded({
    required this.invoices,
    required this.hasReachedMax,
    required this.currentPage,
  });

  @override
  List<Object> get props => [invoices, hasReachedMax, currentPage];

  InvoiceLoaded copyWith({
    List<InvoiceModel>? invoices,
    bool? hasReachedMax,
    int? currentPage,
  }) {
    return InvoiceLoaded(
      invoices: invoices ?? this.invoices,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

class InvoiceDetailsLoaded extends PaymentState {
  final InvoiceModel invoice;

  const InvoiceDetailsLoaded(this.invoice);

  @override
  List<Object> get props => [invoice];
}

class InvoiceSummaryLoaded extends PaymentState {
  final InvoiceSummary summary;

  const InvoiceSummaryLoaded(this.summary);

  @override
  List<Object> get props => [summary];
}

class PaymentOperationSuccess extends PaymentState {
  final String message;
  final PaymentModel? payment;
  final InvoiceModel? invoice;

  const PaymentOperationSuccess({
    required this.message,
    this.payment,
    this.invoice,
  });

  @override
  List<Object?> get props => [message, payment, invoice];
}

class PaymentError extends PaymentState {
  final String message;

  const PaymentError(this.message);

  @override
  List<Object> get props => [message];
}

// BLoC Implementation
class PaymentBloc extends Bloc<PaymentEvent, PaymentState> {
  final PaymentService _paymentService;

  PaymentBloc({required PaymentService paymentService})
      : _paymentService = paymentService,
        super(const PaymentInitial()) {
    on<LoadPayments>(_onLoadPayments);
    on<LoadMorePayments>(_onLoadMorePayments);
    on<CreatePayment>(_onCreatePayment);
    on<ProcessPayment>(_onProcessPayment);
    on<UpdatePayment>(_onUpdatePayment);
    on<CancelPayment>(_onCancelPayment);
    on<DeletePayment>(_onDeletePayment);
    on<LoadPaymentDetails>(_onLoadPaymentDetails);
    on<LoadPaymentStatus>(_onLoadPaymentStatus);
    on<LoadPaymentSummary>(_onLoadPaymentSummary);
    on<LoadInvoices>(_onLoadInvoices);
    on<LoadMoreInvoices>(_onLoadMoreInvoices);
    on<CreateInvoice>(_onCreateInvoice);
    on<UpdateInvoice>(_onUpdateInvoice);
    on<SendInvoice>(_onSendInvoice);
    on<MarkInvoiceAsPaid>(_onMarkInvoiceAsPaid);
    on<DownloadInvoicePdf>(_onDownloadInvoicePdf);
    on<LoadInvoiceDetails>(_onLoadInvoiceDetails);
    on<LoadInvoiceSummary>(_onLoadInvoiceSummary);
  }

  Future<void> _onLoadPayments(LoadPayments event, Emitter<PaymentState> emit) async {
    try {
      if (event.refresh || state is! PaymentLoaded) {
        emit(const PaymentLoading());
      }

      PaginatedResponse<PaymentModel> response;

      if (event.status != null) {
        response = await _paymentService.getPaymentsByStatus(
          status: event.status!,
          page: 0,
          size: 20,
        );
      } else if (event.loadId != null) {
        response = await _paymentService.getPaymentsByLoad(
          loadId: event.loadId!,
          page: 0,
          size: 20,
        );
      } else if (event.startDate != null && event.endDate != null) {
        response = await _paymentService.getPaymentsByDateRange(
          startDate: event.startDate!,
          endDate: event.endDate!,
          page: 0,
          size: 20,
        );
      } else {
        response = await _paymentService.getUserPayments(page: 0, size: 20);
      }

      emit(PaymentLoaded(
        payments: response.content,
        hasReachedMax: response.last,
        currentPage: 0,
        currentStatus: event.status,
        currentLoadId: event.loadId,
        currentStartDate: event.startDate,
        currentEndDate: event.endDate,
      ));
    } catch (e) {
      emit(PaymentError('Failed to load payments: ${e.toString()}'));
    }
  }

  Future<void> _onLoadMorePayments(LoadMorePayments event, Emitter<PaymentState> emit) async {
    if (state is PaymentLoaded) {
      final currentState = state as PaymentLoaded;
      if (currentState.hasReachedMax) return;

      try {
        final nextPage = currentState.currentPage + 1;
        PaginatedResponse<PaymentModel> response;

        if (currentState.currentStatus != null) {
          response = await _paymentService.getPaymentsByStatus(
            status: currentState.currentStatus!,
            page: nextPage,
            size: 20,
          );
        } else if (currentState.currentLoadId != null) {
          response = await _paymentService.getPaymentsByLoad(
            loadId: currentState.currentLoadId!,
            page: nextPage,
            size: 20,
          );
        } else if (currentState.currentStartDate != null && currentState.currentEndDate != null) {
          response = await _paymentService.getPaymentsByDateRange(
            startDate: currentState.currentStartDate!,
            endDate: currentState.currentEndDate!,
            page: nextPage,
            size: 20,
          );
        } else {
          response = await _paymentService.getUserPayments(page: nextPage, size: 20);
        }

        emit(currentState.copyWith(
          payments: [...currentState.payments, ...response.content],
          hasReachedMax: response.last,
          currentPage: nextPage,
        ));
      } catch (e) {
        emit(PaymentError('Failed to load more payments: ${e.toString()}'));
      }
    }
  }

  Future<void> _onCreatePayment(CreatePayment event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final payment = await _paymentService.createPayment(event.request);
      emit(PaymentOperationSuccess(
        message: 'Payment created successfully',
        payment: payment,
      ));
    } catch (e) {
      emit(PaymentError('Failed to create payment: ${e.toString()}'));
    }
  }

  Future<void> _onProcessPayment(ProcessPayment event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final status = await _paymentService.processPayment(event.request);
      emit(PaymentStatusLoaded(status));
    } catch (e) {
      emit(PaymentError('Failed to process payment: ${e.toString()}'));
    }
  }

  Future<void> _onUpdatePayment(UpdatePayment event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final payment = await _paymentService.updatePayment(event.paymentId, event.request);
      emit(PaymentOperationSuccess(
        message: 'Payment updated successfully',
        payment: payment,
      ));
    } catch (e) {
      emit(PaymentError('Failed to update payment: ${e.toString()}'));
    }
  }

  Future<void> _onCancelPayment(CancelPayment event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final payment = await _paymentService.cancelPayment(event.paymentId);
      emit(PaymentOperationSuccess(
        message: 'Payment cancelled successfully',
        payment: payment,
      ));
    } catch (e) {
      emit(PaymentError('Failed to cancel payment: ${e.toString()}'));
    }
  }

  Future<void> _onDeletePayment(DeletePayment event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      await _paymentService.deletePayment(event.paymentId);
      emit(const PaymentOperationSuccess(message: 'Payment deleted successfully'));
    } catch (e) {
      emit(PaymentError('Failed to delete payment: ${e.toString()}'));
    }
  }

  Future<void> _onLoadPaymentDetails(LoadPaymentDetails event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final payment = await _paymentService.getPayment(event.paymentId);
      emit(PaymentDetailsLoaded(payment));
    } catch (e) {
      emit(PaymentError('Failed to load payment details: ${e.toString()}'));
    }
  }

  Future<void> _onLoadPaymentStatus(LoadPaymentStatus event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final status = await _paymentService.getPaymentStatus(event.paymentId);
      emit(PaymentStatusLoaded(status));
    } catch (e) {
      emit(PaymentError('Failed to load payment status: ${e.toString()}'));
    }
  }

  Future<void> _onLoadPaymentSummary(LoadPaymentSummary event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final summary = event.isAdmin
          ? await _paymentService.getPaymentSummary()
          : await _paymentService.getUserPaymentSummary();
      emit(PaymentSummaryLoaded(summary));
    } catch (e) {
      emit(PaymentError('Failed to load payment summary: ${e.toString()}'));
    }
  }

  Future<void> _onLoadInvoices(LoadInvoices event, Emitter<PaymentState> emit) async {
    try {
      if (event.refresh || state is! InvoiceLoaded) {
        emit(const PaymentLoading());
      }

      final response = await _paymentService.getUserInvoices(page: 0, size: 20);

      emit(InvoiceLoaded(
        invoices: response.content,
        hasReachedMax: response.last,
        currentPage: 0,
      ));
    } catch (e) {
      emit(PaymentError('Failed to load invoices: ${e.toString()}'));
    }
  }

  Future<void> _onLoadMoreInvoices(LoadMoreInvoices event, Emitter<PaymentState> emit) async {
    if (state is InvoiceLoaded) {
      final currentState = state as InvoiceLoaded;
      if (currentState.hasReachedMax) return;

      try {
        final nextPage = currentState.currentPage + 1;
        final response = await _paymentService.getUserInvoices(page: nextPage, size: 20);

        emit(currentState.copyWith(
          invoices: [...currentState.invoices, ...response.content],
          hasReachedMax: response.last,
          currentPage: nextPage,
        ));
      } catch (e) {
        emit(PaymentError('Failed to load more invoices: ${e.toString()}'));
      }
    }
  }

  Future<void> _onCreateInvoice(CreateInvoice event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final invoice = await _paymentService.createInvoice(event.request);
      emit(PaymentOperationSuccess(
        message: 'Invoice created successfully',
        invoice: invoice,
      ));
    } catch (e) {
      emit(PaymentError('Failed to create invoice: ${e.toString()}'));
    }
  }

  Future<void> _onUpdateInvoice(UpdateInvoice event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final invoice = await _paymentService.updateInvoice(event.invoiceId, event.request);
      emit(PaymentOperationSuccess(
        message: 'Invoice updated successfully',
        invoice: invoice,
      ));
    } catch (e) {
      emit(PaymentError('Failed to update invoice: ${e.toString()}'));
    }
  }

  Future<void> _onSendInvoice(SendInvoice event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final invoice = await _paymentService.sendInvoice(event.invoiceId);
      emit(PaymentOperationSuccess(
        message: 'Invoice sent successfully',
        invoice: invoice,
      ));
    } catch (e) {
      emit(PaymentError('Failed to send invoice: ${e.toString()}'));
    }
  }

  Future<void> _onMarkInvoiceAsPaid(MarkInvoiceAsPaid event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final invoice = await _paymentService.markInvoiceAsPaid(event.invoiceId);
      emit(PaymentOperationSuccess(
        message: 'Invoice marked as paid',
        invoice: invoice,
      ));
    } catch (e) {
      emit(PaymentError('Failed to mark invoice as paid: ${e.toString()}'));
    }
  }

  Future<void> _onDownloadInvoicePdf(DownloadInvoicePdf event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final downloadUrl = await _paymentService.downloadInvoicePdf(event.invoiceId);
      emit(PaymentOperationSuccess(
        message: 'Invoice PDF ready for download: $downloadUrl',
      ));
    } catch (e) {
      emit(PaymentError('Failed to download invoice PDF: ${e.toString()}'));
    }
  }

  Future<void> _onLoadInvoiceDetails(LoadInvoiceDetails event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final invoice = await _paymentService.getInvoice(event.invoiceId);
      emit(InvoiceDetailsLoaded(invoice));
    } catch (e) {
      emit(PaymentError('Failed to load invoice details: ${e.toString()}'));
    }
  }

  Future<void> _onLoadInvoiceSummary(LoadInvoiceSummary event, Emitter<PaymentState> emit) async {
    try {
      emit(const PaymentLoading());
      final summary = await _paymentService.getInvoiceSummary();
      emit(InvoiceSummaryLoaded(summary));
    } catch (e) {
      emit(PaymentError('Failed to load invoice summary: ${e.toString()}'));
    }
  }
}
