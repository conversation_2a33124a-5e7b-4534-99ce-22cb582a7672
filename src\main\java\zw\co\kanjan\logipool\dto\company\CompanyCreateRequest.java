package zw.co.kanjan.logipool.dto.company;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import zw.co.kanjan.logipool.entity.Company;

@Data
public class CompanyCreateRequest {
    
    @NotBlank
    @Size(max = 100)
    private String name;
    
    @Size(max = 1000)
    private String description;
    
    @NotBlank
    @Size(max = 50)
    private String registrationNumber;
    
    @Size(max = 50)
    private String taxNumber;
    
    @NotNull
    private Company.CompanyType type;
    
    @NotBlank
    @Size(max = 200)
    private String address;
    
    @NotBlank
    @Size(max = 50)
    private String city;
    
    @NotBlank
    @Size(max = 50)
    private String country;
    
    @Size(max = 20)
    private String phoneNumber;
    
    @Email
    @Size(max = 100)
    private String email;
    
    @Size(max = 100)
    private String website;
}
