import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/notification_model.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../bloc/notification_bloc.dart';
import '../widgets/notification_card.dart';
import '../widgets/notification_filter_sheet.dart';

class NotificationListScreen extends StatefulWidget {
  const NotificationListScreen({super.key});

  @override
  State<NotificationListScreen> createState() => _NotificationListScreenState();
}

class _NotificationListScreenState extends State<NotificationListScreen> {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  final Set<int> _selectedNotifications = <int>{};
  bool _isSelectionMode = false;
  NotificationType? _selectedType;
  NotificationPriority? _selectedPriority;
  bool? _selectedReadStatus;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    context.read<NotificationBloc>().add(const LoadNotifications());
    context.read<NotificationBloc>().add(const LoadUnreadCount());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<NotificationBloc>().add(const LoadMoreNotifications());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  void _toggleSelection(int notificationId) {
    setState(() {
      if (_selectedNotifications.contains(notificationId)) {
        _selectedNotifications.remove(notificationId);
      } else {
        _selectedNotifications.add(notificationId);
      }

      if (_selectedNotifications.isEmpty) {
        _isSelectionMode = false;
      }
    });
  }

  void _enterSelectionMode(int notificationId) {
    setState(() {
      _isSelectionMode = true;
      _selectedNotifications.add(notificationId);
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedNotifications.clear();
    });
  }

  void _selectAll(List<NotificationModel> notifications) {
    setState(() {
      _selectedNotifications.clear();
      _selectedNotifications.addAll(
        notifications.map((n) => n.id!).where((id) => id != null),
      );
    });
  }

  void _markSelectedAsRead() {
    if (_selectedNotifications.isNotEmpty) {
      context.read<NotificationBloc>().add(
            MarkNotificationsAsRead(_selectedNotifications.toList()),
          );
      _exitSelectionMode();
    }
  }

  void _deleteSelected() {
    if (_selectedNotifications.isNotEmpty) {
      _showDeleteConfirmation(() {
        context.read<NotificationBloc>().add(
              DeleteNotifications(_selectedNotifications.toList()),
            );
        _exitSelectionMode();
      });
    }
  }

  void _showDeleteConfirmation(VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Notifications'),
        content: Text(
          'Are you sure you want to delete ${_selectedNotifications.length} notification(s)?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onConfirm();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => NotificationFilterSheet(
        selectedType: _selectedType,
        selectedPriority: _selectedPriority,
        selectedReadStatus: _selectedReadStatus,
        onApplyFilter: (type, priority, readStatus) {
          setState(() {
            _selectedType = type;
            _selectedPriority = priority;
            _selectedReadStatus = readStatus;
          });
          _applyFilters();
        },
        onClearFilter: () {
          setState(() {
            _selectedType = null;
            _selectedPriority = null;
            _selectedReadStatus = null;
          });
          context.read<NotificationBloc>().add(const LoadNotifications());
        },
      ),
    );
  }

  void _applyFilters() {
    if (_searchController.text.isNotEmpty ||
        _selectedType != null ||
        _selectedPriority != null ||
        _selectedReadStatus != null) {
      context.read<NotificationBloc>().add(
            SearchNotifications(
              query: _searchController.text,
              type: _selectedType,
              priority: _selectedPriority,
              read: _selectedReadStatus,
            ),
          );
    } else {
      context.read<NotificationBloc>().add(const LoadNotifications());
    }
  }

  void _onSearch(String query) {
    if (query.isEmpty &&
        _selectedType == null &&
        _selectedPriority == null &&
        _selectedReadStatus == null) {
      context.read<NotificationBloc>().add(const LoadNotifications());
    } else {
      context.read<NotificationBloc>().add(
            SearchNotifications(
              query: query,
              type: _selectedType,
              priority: _selectedPriority,
              read: _selectedReadStatus,
            ),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _isSelectionMode ? _buildSelectionAppBar() : _buildNormalAppBar(),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildFilterChips(),
          Expanded(
            child: BlocConsumer<NotificationBloc, NotificationState>(
              listener: (context, state) {
                if (state is NotificationError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(state.message)),
                  );
                } else if (state is NotificationOperationSuccess) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(state.message)),
                  );
                  // Refresh unread count
                  context.read<NotificationBloc>().add(const LoadUnreadCount());
                }
              },
              builder: (context, state) {
                if (state is NotificationLoading) {
                  return const LoadingWidget();
                } else if (state is NotificationLoaded) {
                  return _buildNotificationList(state);
                } else if (state is NotificationError) {
                  return CustomErrorWidget(
                    message: state.message,
                    onRetry: () => context
                        .read<NotificationBloc>()
                        .add(const LoadNotifications()),
                  );
                }
                return EmptyStateWidget(
                  title: 'No Notifications',
                  message: 'You have no notifications at the moment.',
                  icon: Icon(
                    Icons.notifications_none,
                    size: 64,
                    color: Theme.of(context)
                        .colorScheme
                        .onSurface
                        .withOpacity(0.5),
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildNormalAppBar() {
    return CustomAppBar(
      title: 'Notifications',
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFilterSheet,
        ),
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'mark_all_read':
                context
                    .read<NotificationBloc>()
                    .add(const MarkAllNotificationsAsRead());
                break;
              case 'clear_expired':
                context
                    .read<NotificationBloc>()
                    .add(const ClearExpiredNotifications());
                break;
              case 'test_notification':
                context
                    .read<NotificationBloc>()
                    .add(const SendTestNotification());
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'mark_all_read',
              child: Text('Mark All as Read'),
            ),
            const PopupMenuItem(
              value: 'clear_expired',
              child: Text('Clear Expired'),
            ),
            const PopupMenuItem(
              value: 'test_notification',
              child: Text('Send Test Notification'),
            ),
          ],
        ),
      ],
    );
  }

  PreferredSizeWidget _buildSelectionAppBar() {
    return AppBar(
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: _exitSelectionMode,
      ),
      title: Text('${_selectedNotifications.length} selected'),
      actions: [
        IconButton(
          icon: const Icon(Icons.mark_email_read),
          onPressed: _markSelectedAsRead,
        ),
        IconButton(
          icon: const Icon(Icons.delete),
          onPressed: _deleteSelected,
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search notifications...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _onSearch('');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: _onSearch,
      ),
    );
  }

  Widget _buildFilterChips() {
    final hasFilters = _selectedType != null ||
        _selectedPriority != null ||
        _selectedReadStatus != null;

    if (!hasFilters) return const SizedBox.shrink();

    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          if (_selectedType != null)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text(_selectedType!.displayName),
                onDeleted: () {
                  setState(() {
                    _selectedType = null;
                  });
                  _applyFilters();
                },
              ),
            ),
          if (_selectedPriority != null)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text(_selectedPriority!.displayName),
                onDeleted: () {
                  setState(() {
                    _selectedPriority = null;
                  });
                  _applyFilters();
                },
              ),
            ),
          if (_selectedReadStatus != null)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text(_selectedReadStatus! ? 'Read' : 'Unread'),
                onDeleted: () {
                  setState(() {
                    _selectedReadStatus = null;
                  });
                  _applyFilters();
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNotificationList(NotificationLoaded state) {
    if (state.notifications.isEmpty) {
      return EmptyStateWidget(
        title: 'No Notifications',
        message: 'You have no notifications matching your criteria.',
        icon: Icon(
          Icons.notifications_none,
          size: 64,
          color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<NotificationBloc>().add(const RefreshNotifications());
      },
      child: ListView.builder(
        controller: _scrollController,
        itemCount: state.notifications.length + (state.isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= state.notifications.length) {
            return const Padding(
              padding: EdgeInsets.all(16.0),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          final notification = state.notifications[index];
          final isSelected = _selectedNotifications.contains(notification.id);

          return NotificationCard(
            notification: notification,
            isSelected: isSelected,
            isSelectionMode: _isSelectionMode,
            onTap: () {
              if (_isSelectionMode) {
                _toggleSelection(notification.id!);
              } else {
                _handleNotificationTap(notification);
              }
            },
            onLongPress: () {
              if (!_isSelectionMode) {
                _enterSelectionMode(notification.id!);
              }
            },
            onMarkAsRead: () {
              context.read<NotificationBloc>().add(
                    MarkNotificationAsRead(notification.id!),
                  );
            },
            onDelete: () {
              _showDeleteConfirmation(() {
                context.read<NotificationBloc>().add(
                      DeleteNotification(notification.id!),
                    );
              });
            },
          );
        },
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    if (_isSelectionMode) {
      return FloatingActionButton.extended(
        onPressed: () => _selectAll(
          (context.read<NotificationBloc>().state as NotificationLoaded)
              .notifications,
        ),
        label: const Text('Select All'),
        icon: const Icon(Icons.select_all),
      );
    }
    return null;
  }

  void _handleNotificationTap(NotificationModel notification) {
    // Navigate to notification details screen
    // The details screen will handle marking as read
    context.push('/notifications/${notification.id}');
  }

  void _navigateToReference(NotificationModel notification) {
    final referenceType = notification.referenceType;
    final referenceId = notification.referenceId;

    switch (referenceType) {
      case 'LOAD':
        context.pushNamed(
          'load-details',
          pathParameters: {'id': referenceId},
        );
        break;
      case 'BID':
        Navigator.pushNamed(
          context,
          '/bid-details',
          arguments: referenceId,
        );
        break;
      case 'PAYMENT':
        Navigator.pushNamed(
          context,
          '/payment-details',
          arguments: referenceId,
        );
        break;
      case 'DOCUMENT':
        Navigator.pushNamed(
          context,
          '/document-details',
          arguments: referenceId,
        );
        break;
      case 'TRACKING':
        Navigator.pushNamed(
          context,
          '/tracking-details',
          arguments: referenceId,
        );
        break;
      default:
        // Show notification details in a dialog
        _showNotificationDetails(notification);
        break;
    }
  }

  void _showNotificationDetails(NotificationModel notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.message),
            const SizedBox(height: 16),
            Text(
              'Type: ${notification.type.displayName}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              'Priority: ${notification.priority.displayName}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            Text(
              'Time: ${notification.timestamp.toString()}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
