-- Create live_tracking_sessions table for managing live location tracking sessions
CREATE TABLE live_tracking_sessions (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    load_id BIGINT NOT NULL,
    tracking_user_id BIGINT NOT NULL,
    device_identifier VARCHAR(100),
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    started_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ended_at TIMESTAMP NULL,
    last_update_at TIMESTAMP NULL,
    current_latitude DECIMAL(10, 8) NULL,
    current_longitude DECIMAL(11, 8) NULL,
    current_location VARCHAR(200) NULL,
    total_updates INT NOT NULL DEFAULT 0,
    session_status ENUM('ACTIVE', 'ENDED_BY_USER', 'ENDED_BY_SYSTEM', 'ENDED_BY_CONFLICT', 'ENDED_BY_LOAD_COMPLETION') NOT NULL DEFAULT 'ACTIVE',
    end_reason VARCHAR(100) NULL,
    
    -- Foreign key constraints
    CONSTRAINT fk_live_tracking_load FOREIGN KEY (load_id) REFERENCES loads(id) ON DELETE CASCADE,
    CONS<PERSON>AINT fk_live_tracking_user FOREIGN KEY (tracking_user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Indexes for performance
    INDEX idx_live_tracking_load_id (load_id),
    INDEX idx_live_tracking_user_id (tracking_user_id),
    INDEX idx_live_tracking_active (is_active),
    INDEX idx_live_tracking_started_at (started_at),
    INDEX idx_live_tracking_last_update (last_update_at),
    
    -- Unique constraint to ensure only one active session per load
    UNIQUE KEY uk_active_load_tracking (load_id, is_active)
);

-- Add comments for documentation
ALTER TABLE live_tracking_sessions COMMENT = 'Tracks live location sharing sessions for loads';
ALTER TABLE live_tracking_sessions MODIFY COLUMN load_id BIGINT NOT NULL COMMENT 'Reference to the load being tracked';
ALTER TABLE live_tracking_sessions MODIFY COLUMN tracking_user_id BIGINT NOT NULL COMMENT 'User who is sharing their location';
ALTER TABLE live_tracking_sessions MODIFY COLUMN device_identifier VARCHAR(100) COMMENT 'Identifier for the device/app instance';
ALTER TABLE live_tracking_sessions MODIFY COLUMN is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Whether the tracking session is currently active';
ALTER TABLE live_tracking_sessions MODIFY COLUMN started_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'When the tracking session started';
ALTER TABLE live_tracking_sessions MODIFY COLUMN ended_at TIMESTAMP NULL COMMENT 'When the tracking session ended';
ALTER TABLE live_tracking_sessions MODIFY COLUMN last_update_at TIMESTAMP NULL COMMENT 'Last time location was updated';
ALTER TABLE live_tracking_sessions MODIFY COLUMN current_latitude DECIMAL(10, 8) NULL COMMENT 'Current latitude coordinate';
ALTER TABLE live_tracking_sessions MODIFY COLUMN current_longitude DECIMAL(11, 8) NULL COMMENT 'Current longitude coordinate';
ALTER TABLE live_tracking_sessions MODIFY COLUMN current_location VARCHAR(200) NULL COMMENT 'Human-readable current location';
ALTER TABLE live_tracking_sessions MODIFY COLUMN total_updates INT NOT NULL DEFAULT 0 COMMENT 'Total number of location updates received';
ALTER TABLE live_tracking_sessions MODIFY COLUMN session_status ENUM('ACTIVE', 'ENDED_BY_USER', 'ENDED_BY_SYSTEM', 'ENDED_BY_CONFLICT', 'ENDED_BY_LOAD_COMPLETION') NOT NULL DEFAULT 'ACTIVE' COMMENT 'Status of the tracking session';
ALTER TABLE live_tracking_sessions MODIFY COLUMN end_reason VARCHAR(100) NULL COMMENT 'Reason why the session ended';
