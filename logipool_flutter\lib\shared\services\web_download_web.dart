// Web-specific implementation for file downloads
import 'dart:html' as html;
import 'dart:typed_data';
import 'package:flutter/foundation.dart';

/// Download file using web-specific blob URL approach
void downloadFileWithBlob(List<int> bytes, String fileName) {
  try {
    // Convert List<int> to Uint8List
    final uint8List = Uint8List.fromList(bytes);
    
    // Determine MIME type from file extension
    String mimeType = 'application/octet-stream';
    final lowerFileName = fileName.toLowerCase();
    
    if (lowerFileName.endsWith('.pdf')) {
      mimeType = 'application/pdf';
    } else if (lowerFileName.endsWith('.png')) {
      mimeType = 'image/png';
    } else if (lowerFileName.endsWith('.jpg') || lowerFileName.endsWith('.jpeg')) {
      mimeType = 'image/jpeg';
    } else if (lowerFileName.endsWith('.txt')) {
      mimeType = 'text/plain';
    } else if (lowerFileName.endsWith('.doc') || lowerFileName.endsWith('.docx')) {
      mimeType = 'application/msword';
    } else if (lowerFileName.endsWith('.xls') || lowerFileName.endsWith('.xlsx')) {
      mimeType = 'application/vnd.ms-excel';
    }

    // Create blob with proper MIME type
    final blob = html.Blob([uint8List], mimeType);
    
    // Create object URL
    final url = html.Url.createObjectUrlFromBlob(blob);
    
    // Create anchor element with download attribute
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', fileName)
      ..style.display = 'none';
    
    // Add to DOM, click, and remove
    html.document.body?.children.add(anchor);
    anchor.click();
    html.document.body?.children.remove(anchor);
    
    // Clean up object URL
    html.Url.revokeObjectUrl(url);
    
    if (kDebugMode) {
      print('Web download completed for file: $fileName');
    }
  } catch (e) {
    if (kDebugMode) {
      print('Error in web download: $e');
    }
    rethrow;
  }
}
