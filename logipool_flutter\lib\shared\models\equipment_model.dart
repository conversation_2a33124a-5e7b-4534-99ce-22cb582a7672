import 'package:json_annotation/json_annotation.dart';
import 'vehicle_model.dart'; // For CompanyInfo and PublicApprovalStatus

part 'equipment_model.g.dart';

@JsonSerializable()
class EquipmentModel {
  final int? id;
  final String? name;
  final String? make;
  final String? model;
  final int? year;
  final EquipmentType? type;
  final String? serialNumber;
  final double? capacity;
  final String? capacityUnit;
  final EquipmentStatus? status;
  final String? description;
  final double? dailyRate;
  final double? hourlyRate;
  final String? currency;
  final List<String>? features;
  final String? imageUrl;
  final String? location;
  final double? latitude;
  final double? longitude;
  final bool? requiresOperator;
  final bool? operatorIncluded;
  final bool? isAvailableForRent;
  final bool? isPubliclyVisible;
  final PublicApprovalStatus? publicApprovalStatus;
  final CompanyInfo? company;
  final bool? hasInsurance;
  final bool? hasCertification;
  final DateTime? insuranceExpiryDate;
  final DateTime? certificationExpiryDate;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  EquipmentModel({
    this.id,
    this.name,
    this.make,
    this.model,
    this.year,
    this.type,
    this.serialNumber,
    this.capacity,
    this.capacityUnit,
    this.status,
    this.description,
    this.dailyRate,
    this.hourlyRate,
    this.currency,
    this.features,
    this.imageUrl,
    this.location,
    this.latitude,
    this.longitude,
    this.requiresOperator,
    this.operatorIncluded,
    this.isAvailableForRent,
    this.isPubliclyVisible,
    this.publicApprovalStatus,
    this.company,
    this.hasInsurance,
    this.hasCertification,
    this.insuranceExpiryDate,
    this.certificationExpiryDate,
    this.createdAt,
    this.updatedAt,
  });

  factory EquipmentModel.fromJson(Map<String, dynamic> json) =>
      _$EquipmentModelFromJson(json);

  Map<String, dynamic> toJson() => _$EquipmentModelToJson(this);
}

enum EquipmentType {
  @JsonValue('CRANE')
  crane,
  @JsonValue('FORKLIFT')
  forklift,
  @JsonValue('EXCAVATOR')
  excavator,
  @JsonValue('BULLDOZER')
  bulldozer,
  @JsonValue('LOADER')
  loader,
  @JsonValue('TRAILER')
  trailer,
  @JsonValue('CONTAINER')
  container,
  @JsonValue('PALLET_JACK')
  palletJack,
  @JsonValue('CONVEYOR')
  conveyor,
  @JsonValue('GENERATOR')
  generator,
  @JsonValue('COMPRESSOR')
  compressor,
  @JsonValue('WELDING_EQUIPMENT')
  weldingEquipment,
  @JsonValue('LIFTING_EQUIPMENT')
  liftingEquipment,
  @JsonValue('MATERIAL_HANDLING')
  materialHandling,
  @JsonValue('OTHER')
  other,
}

enum EquipmentStatus {
  @JsonValue('AVAILABLE')
  available,
  @JsonValue('IN_USE')
  inUse,
  @JsonValue('MAINTENANCE')
  maintenance,
  @JsonValue('OUT_OF_SERVICE')
  outOfService,
  @JsonValue('RESERVED')
  reserved,
}
