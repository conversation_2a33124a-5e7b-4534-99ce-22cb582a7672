package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.NotificationDto;
import zw.co.kanjan.logipool.security.UserPrincipal;
import zw.co.kanjan.logipool.service.PersistentNotificationService;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/notifications")
@RequiredArgsConstructor
@Tag(name = "Notifications", description = "Notification management endpoints")
public class NotificationController {
    
    private final PersistentNotificationService persistentNotificationService;
    
    @GetMapping
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get user notifications", description = "Retrieve paginated notifications for the authenticated user")
    public ResponseEntity<Page<NotificationDto>> getUserNotifications(
            Authentication authentication,
            Pageable pageable) {
        
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        Page<NotificationDto> notifications = persistentNotificationService.getUserNotifications(
            userPrincipal.getId(), pageable);
        
        return ResponseEntity.ok(notifications);
    }
    
    @GetMapping("/unread")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get unread notifications", description = "Retrieve all unread notifications for the authenticated user")
    public ResponseEntity<List<NotificationDto>> getUnreadNotifications(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        List<NotificationDto> notifications = persistentNotificationService.getUnreadNotifications(
            userPrincipal.getId());
        
        return ResponseEntity.ok(notifications);
    }
    
    @GetMapping("/unread/count")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get unread notification count", description = "Get the count of unread notifications for the authenticated user")
    public ResponseEntity<Map<String, Long>> getUnreadCount(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        long count = persistentNotificationService.getUnreadCount(userPrincipal.getId());
        
        return ResponseEntity.ok(Map.of("unreadCount", count));
    }
    
    @GetMapping("/urgent")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get urgent notifications", description = "Retrieve urgent unread notifications for the authenticated user")
    public ResponseEntity<List<NotificationDto>> getUrgentNotifications(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        List<NotificationDto> notifications = persistentNotificationService.getUrgentNotifications(
            userPrincipal.getId());
        
        return ResponseEntity.ok(notifications);
    }
    
    @GetMapping("/type/{type}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get notifications by type", description = "Retrieve notifications of a specific type for the authenticated user")
    public ResponseEntity<List<NotificationDto>> getNotificationsByType(
            Authentication authentication,
            @PathVariable String type) {
        
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        List<NotificationDto> notifications = persistentNotificationService.getNotificationsByType(
            userPrincipal.getId(), type);
        
        return ResponseEntity.ok(notifications);
    }
    
    @PutMapping("/{id}/read")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Mark notification as read", description = "Mark a specific notification as read")
    public ResponseEntity<Map<String, String>> markAsRead(@PathVariable Long id) {
        persistentNotificationService.markAsRead(id);
        return ResponseEntity.ok(Map.of("message", "Notification marked as read"));
    }
    
    @PutMapping("/read-all")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Mark all notifications as read", description = "Mark all notifications as read for the authenticated user")
    public ResponseEntity<Map<String, String>> markAllAsRead(Authentication authentication) {
        UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();
        persistentNotificationService.markAllAsRead(userPrincipal.getId());
        
        return ResponseEntity.ok(Map.of("message", "All notifications marked as read"));
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Delete notification", description = "Delete a specific notification")
    public ResponseEntity<Map<String, String>> deleteNotification(@PathVariable Long id) {
        persistentNotificationService.deleteNotification(id);
        return ResponseEntity.ok(Map.of("message", "Notification deleted successfully"));
    }
}
