package zw.co.kanjan.logipool.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.Equipment;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class EquipmentDto {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Equipment creation request")
    public static class EquipmentCreateRequest {
        
        @NotBlank(message = "Equipment name is required")
        @Size(max = 100, message = "Equipment name must not exceed 100 characters")
        @Schema(description = "Equipment name", example = "Mobile Crane 50T")
        private String name;
        
        @NotBlank(message = "Make is required")
        @Size(max = 50, message = "Make must not exceed 50 characters")
        @Schema(description = "Equipment make", example = "Liebherr")
        private String make;
        
        @NotBlank(message = "Model is required")
        @Size(max = 50, message = "Model must not exceed 50 characters")
        @Schema(description = "Equipment model", example = "LTM 1050-3.1")
        private String model;
        
        @Schema(description = "Manufacture year", example = "2020")
        private Integer year;
        
        @NotNull(message = "Equipment type is required")
        @Schema(description = "Equipment type")
        private Equipment.EquipmentType type;
        
        @Size(max = 50, message = "Serial number must not exceed 50 characters")
        @Schema(description = "Serial number", example = "SN123456789")
        private String serialNumber;
        
        @Schema(description = "Equipment capacity", example = "50.0")
        private BigDecimal capacity;
        
        @Size(max = 20, message = "Capacity unit must not exceed 20 characters")
        @Schema(description = "Capacity unit", example = "tons")
        private String capacityUnit;
        
        @Schema(description = "Equipment description")
        private String description;
        
        @Schema(description = "Daily rental rate", example = "500.00")
        private BigDecimal dailyRate;
        
        @Schema(description = "Hourly rental rate", example = "50.00")
        private BigDecimal hourlyRate;
        
        @Size(max = 10, message = "Currency must not exceed 10 characters")
        @Schema(description = "Currency", example = "USD")
        private String currency = "USD";
        
        @Schema(description = "Equipment features (JSON string)")
        private String features;
        
        @Size(max = 500, message = "Image URL must not exceed 500 characters")
        @Schema(description = "Equipment image URL")
        private String imageUrl;
        
        @Schema(description = "Equipment location", example = "Harare")
        private String location;
        
        @Schema(description = "Latitude coordinate", example = "-17.8252")
        private BigDecimal latitude;
        
        @Schema(description = "Longitude coordinate", example = "31.0335")
        private BigDecimal longitude;
        
        @Schema(description = "Whether equipment requires operator", example = "true")
        private Boolean requiresOperator = false;
        
        @Schema(description = "Whether operator is included", example = "false")
        private Boolean operatorIncluded = false;
        
        @Schema(description = "Whether available for rent", example = "true")
        private Boolean isAvailableForRent = false;
        
        @Schema(description = "Whether publicly visible", example = "false")
        private Boolean isPubliclyVisible = false;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Equipment update request")
    public static class EquipmentUpdateRequest {
        
        @Size(max = 100, message = "Equipment name must not exceed 100 characters")
        @Schema(description = "Equipment name", example = "Mobile Crane 50T")
        private String name;
        
        @Size(max = 50, message = "Make must not exceed 50 characters")
        @Schema(description = "Equipment make", example = "Liebherr")
        private String make;
        
        @Size(max = 50, message = "Model must not exceed 50 characters")
        @Schema(description = "Equipment model", example = "LTM 1050-3.1")
        private String model;
        
        @Schema(description = "Manufacture year", example = "2020")
        private Integer year;
        
        @Schema(description = "Equipment type")
        private Equipment.EquipmentType type;
        
        @Schema(description = "Equipment capacity", example = "50.0")
        private BigDecimal capacity;
        
        @Size(max = 20, message = "Capacity unit must not exceed 20 characters")
        @Schema(description = "Capacity unit", example = "tons")
        private String capacityUnit;
        
        @Schema(description = "Equipment status")
        private Equipment.EquipmentStatus status;
        
        @Schema(description = "Equipment description")
        private String description;
        
        @Schema(description = "Daily rental rate", example = "500.00")
        private BigDecimal dailyRate;
        
        @Schema(description = "Hourly rental rate", example = "50.00")
        private BigDecimal hourlyRate;
        
        @Schema(description = "Equipment features (JSON string)")
        private String features;
        
        @Size(max = 500, message = "Image URL must not exceed 500 characters")
        @Schema(description = "Equipment image URL")
        private String imageUrl;
        
        @Schema(description = "Equipment location", example = "Harare")
        private String location;
        
        @Schema(description = "Latitude coordinate", example = "-17.8252")
        private BigDecimal latitude;
        
        @Schema(description = "Longitude coordinate", example = "31.0335")
        private BigDecimal longitude;
        
        @Schema(description = "Whether equipment requires operator", example = "true")
        private Boolean requiresOperator;
        
        @Schema(description = "Whether operator is included", example = "false")
        private Boolean operatorIncluded;
        
        @Schema(description = "Whether available for rent", example = "true")
        private Boolean isAvailableForRent;
        
        @Schema(description = "Whether publicly visible", example = "false")
        private Boolean isPubliclyVisible;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Equipment response")
    public static class EquipmentResponse {
        
        @Schema(description = "Equipment ID", example = "1")
        private Long id;
        
        @Schema(description = "Equipment name", example = "Mobile Crane 50T")
        private String name;
        
        @Schema(description = "Equipment make", example = "Liebherr")
        private String make;
        
        @Schema(description = "Equipment model", example = "LTM 1050-3.1")
        private String model;
        
        @Schema(description = "Manufacture year", example = "2020")
        private Integer year;
        
        @Schema(description = "Equipment type")
        private Equipment.EquipmentType type;
        
        @Schema(description = "Serial number", example = "SN123456789")
        private String serialNumber;
        
        @Schema(description = "Equipment capacity", example = "50.0")
        private BigDecimal capacity;
        
        @Schema(description = "Capacity unit", example = "tons")
        private String capacityUnit;
        
        @Schema(description = "Equipment status")
        private Equipment.EquipmentStatus status;
        
        @Schema(description = "Equipment description")
        private String description;
        
        @Schema(description = "Daily rental rate", example = "500.00")
        private BigDecimal dailyRate;
        
        @Schema(description = "Hourly rental rate", example = "50.00")
        private BigDecimal hourlyRate;
        
        @Schema(description = "Currency", example = "USD")
        private String currency;
        
        @Schema(description = "Equipment features")
        private List<String> features;
        
        @Schema(description = "Equipment image URL")
        private String imageUrl;
        
        @Schema(description = "Equipment location", example = "Harare")
        private String location;
        
        @Schema(description = "Latitude coordinate", example = "-17.8252")
        private BigDecimal latitude;
        
        @Schema(description = "Longitude coordinate", example = "31.0335")
        private BigDecimal longitude;
        
        @Schema(description = "Whether equipment requires operator", example = "true")
        private Boolean requiresOperator;
        
        @Schema(description = "Whether operator is included", example = "false")
        private Boolean operatorIncluded;
        
        @Schema(description = "Whether available for rent", example = "true")
        private Boolean isAvailableForRent;
        
        @Schema(description = "Whether publicly visible", example = "false")
        private Boolean isPubliclyVisible;
        
        @Schema(description = "Public approval status")
        private Equipment.PublicApprovalStatus publicApprovalStatus;
        
        @Schema(description = "Company information")
        private CompanyInfo company;
        
        @Schema(description = "Has insurance", example = "true")
        private Boolean hasInsurance;
        
        @Schema(description = "Has certification", example = "true")
        private Boolean hasCertification;
        
        @Schema(description = "Insurance expiry date")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime insuranceExpiryDate;
        
        @Schema(description = "Certification expiry date")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime certificationExpiryDate;
        
        @Schema(description = "Creation timestamp")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
        
        @Schema(description = "Last update timestamp")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Company information")
    public static class CompanyInfo {
        @Schema(description = "Company ID", example = "1")
        private Long id;
        
        @Schema(description = "Company name", example = "ABC Logistics")
        private String name;
        
        @Schema(description = "Company rating", example = "4.5")
        private BigDecimal rating;
        
        @Schema(description = "Company verification status")
        private String verificationStatus;
        
        @Schema(description = "Company location", example = "Harare")
        private String location;
        
        @Schema(description = "Company phone number")
        private String phoneNumber;
        
        @Schema(description = "Company email")
        private String email;
    }
}
