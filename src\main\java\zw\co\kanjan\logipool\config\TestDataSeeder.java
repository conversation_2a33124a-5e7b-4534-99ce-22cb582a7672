package zw.co.kanjan.logipool.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.repository.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Component
@RequiredArgsConstructor
@Slf4j
@ConditionalOnProperty(name = "app.test-data.enabled", havingValue = "true")
public class TestDataSeeder implements CommandLineRunner {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final CompanyRepository companyRepository;
    private final VehicleRepository vehicleRepository;
    private final EquipmentRepository equipmentRepository;
    private final LoadRepository loadRepository;
    private final BidRepository bidRepository;
    private final LoadTrackingRepository loadTrackingRepository;
    private final CompanyMemberRepository companyMemberRepository;
    private final PasswordEncoder passwordEncoder;
    
    private final Random random = new Random();
    
    // Data arrays for realistic dummy data
    private final String[] firstNames = {
        "John", "Jane", "Michael", "Sarah", "David", "Emily", "Robert", "Lisa", 
        "James", "Mary", "William", "Jennifer", "Richard", "Patricia", "Charles", "Linda",
        "Joseph", "Elizabeth", "Thomas", "Barbara", "Christopher", "Susan", "Daniel", "Jessica",
        "Matthew", "Karen", "Anthony", "Nancy", "Mark", "Betty", "Donald", "Helen"
    };
    
    private final String[] lastNames = {
        "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
        "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson", "Thomas",
        "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson", "White",
        "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson", "Walker", "Young"
    };
    
    private final String[] companyNames = {
        "Swift Transport Solutions", "Global Logistics Corp", "Express Freight Services", 
        "Prime Cargo Systems", "Elite Transport Group", "Rapid Delivery Network",
        "Continental Shipping Co", "Metro Freight Lines", "Advanced Logistics Partners",
        "Reliable Transport Services", "Dynamic Cargo Solutions", "Premier Freight Corp",
        "Integrated Transport Systems", "Fast Track Logistics", "Universal Shipping Lines",
        "Professional Transport Co", "Efficient Cargo Services", "Strategic Freight Solutions"
    };
    
    private final String[] cities = {
        "Harare", "Bulawayo", "Chitungwiza", "Mutare", "Epworth", "Gweru", "Kwekwe",
        "Kadoma", "Masvingo", "Chinhoyi", "Norton", "Marondera", "Ruwa", "Chegutu",
        "Zvishavane", "Bindura", "Beitbridge", "Redcliff", "Victoria Falls", "Hwange"
    };
    
    private final String[] cargoTypes = {
        "General Cargo", "Electronics", "Furniture", "Machinery", "Food Products",
        "Textiles", "Construction Materials", "Automotive Parts", "Medical Supplies",
        "Agricultural Products", "Raw Materials", "Consumer Goods", "Industrial Equipment",
        "Chemicals", "Pharmaceuticals", "Beverages", "Paper Products", "Steel Products"
    };
    
    private final String[] vehicleMakes = {
        "Mercedes-Benz", "Volvo", "Scania", "MAN", "DAF", "Iveco", "Renault", "Ford",
        "Isuzu", "Mitsubishi", "Hino", "Freightliner", "Peterbilt", "Kenworth"
    };
    
    private final String[] vehicleModels = {
        "Actros", "FH", "R-Series", "TGX", "XF", "Stralis", "T-Series", "F-MAX",
        "NPR", "Fuso", "500 Series", "Cascadia", "579", "T680"
    };

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        if (userRepository.count() > 10) { // Only seed if database is relatively empty
            log.info("Database already contains data. Skipping test data seeding.");
            return;
        }

        log.info("🌱 Starting test data seeding...");

        // Ensure roles exist first
        initializeRoles();

        // Create users and companies
        List<User> adminUsers = createAdminUsers();
        List<User> clientUsers = createClientUsers();
        List<User> transporterUsers = createTransporterUsers();
        
        // Create companies for transporter users
        List<Company> companies = createCompanies(transporterUsers);
        
        // Create company members
        createCompanyMembers(companies);
        
        // Create vehicles and equipment
        createVehicles(companies);
        createEquipment(companies);
        
        // Create loads
        List<Load> loads = createLoads(clientUsers, companies);
        
        // Create bids
        createBids(loads, companies);
        
        // Create tracking data
        createTrackingData(loads);
        
        log.info("✅ Test data seeding completed successfully!");
        log.info("📊 Created: {} users, {} companies, {} loads",
                userRepository.count(), companyRepository.count(), loadRepository.count());
    }

    private void initializeRoles() {
        log.info("Initializing roles...");
        for (Role.RoleName roleName : Role.RoleName.values()) {
            if (!roleRepository.existsByName(roleName)) {
                Role role = Role.builder()
                        .name(roleName)
                        .description(getDescriptionForRole(roleName))
                        .build();
                roleRepository.save(role);
                log.info("Created role: {}", roleName);
            }
        }
    }

    private String getDescriptionForRole(Role.RoleName roleName) {
        return switch (roleName) {
            case ADMIN -> "System administrator with full access";
            case CLIENT -> "Client who posts loads for transportation";
            case TRANSPORTER -> "Logistics company that provides transportation services";
        };
    }

    private List<User> createAdminUsers() {
        log.info("Creating admin users...");
        List<User> adminUsers = new ArrayList<>();
        Role adminRole = roleRepository.findByName(Role.RoleName.ADMIN)
                .orElseThrow(() -> new RuntimeException("Admin role not found"));
        
        // Create main admin user
        User admin = User.builder()
                .username("<EMAIL>") // Use email as username for backward compatibility
                .email("<EMAIL>")
                .password(passwordEncoder.encode("admin123"))
                .firstName("System")
                .lastName("Administrator")
                .phoneNumber("+263774483751")
                .status(User.UserStatus.ACTIVE)
                .emailVerified(true)
                .phoneVerified(true)
                .roles(Set.of(adminRole))
                .build();
        
        adminUsers.add(userRepository.save(admin));
        
        // Create additional admin users
        for (int i = 0; i < 2; i++) {
            String email = "admin" + (i + 2) + "@logipool.com";
            User user = User.builder()
                    .username(email) // Use email as username for backward compatibility
                    .email(email)
                    .password(passwordEncoder.encode("password123"))
                    .firstName(getRandomFirstName())
                    .lastName(getRandomLastName())
                    .phoneNumber(generatePhoneNumber())
                    .status(User.UserStatus.ACTIVE)
                    .emailVerified(true)
                    .phoneVerified(random.nextBoolean())
                    .roles(Set.of(adminRole))
                    .build();
            
            adminUsers.add(userRepository.save(user));
        }
        
        log.info("Created {} admin users", adminUsers.size());
        return adminUsers;
    }
    
    private List<User> createClientUsers() {
        log.info("Creating client users...");
        List<User> clientUsers = new ArrayList<>();
        Role clientRole = roleRepository.findByName(Role.RoleName.CLIENT)
                .orElseThrow(() -> new RuntimeException("Client role not found"));
        
        for (int i = 0; i < 15; i++) {
            String email = "client" + (i + 1) + "@example.com";
            User user = User.builder()
                    .username(email) // Use email as username for backward compatibility
                    .email(email)
                    .password(passwordEncoder.encode("password123"))
                    .firstName(getRandomFirstName())
                    .lastName(getRandomLastName())
                    .phoneNumber(generatePhoneNumber())
                    .status(User.UserStatus.ACTIVE)
                    .emailVerified(random.nextBoolean())
                    .phoneVerified(random.nextBoolean())
                    .roles(Set.of(clientRole))
                    .build();
            
            clientUsers.add(userRepository.save(user));
        }
        
        log.info("Created {} client users", clientUsers.size());
        return clientUsers;
    }
    
    private List<User> createTransporterUsers() {
        log.info("Creating transporter users...");
        List<User> transporterUsers = new ArrayList<>();
        Role transporterRole = roleRepository.findByName(Role.RoleName.TRANSPORTER)
                .orElseThrow(() -> new RuntimeException("Transporter role not found"));
        
        for (int i = 0; i < 12; i++) {
            String email = "transporter" + (i + 1) + "@example.com";
            User user = User.builder()
                    .username(email) // Use email as username for backward compatibility
                    .email(email)
                    .password(passwordEncoder.encode("password123"))
                    .firstName(getRandomFirstName())
                    .lastName(getRandomLastName())
                    .phoneNumber(generatePhoneNumber())
                    .status(User.UserStatus.ACTIVE)
                    .emailVerified(random.nextBoolean())
                    .phoneVerified(random.nextBoolean())
                    .roles(Set.of(transporterRole))
                    .build();
            
            transporterUsers.add(userRepository.save(user));
        }
        
        log.info("Created {} transporter users", transporterUsers.size());
        return transporterUsers;
    }
    
    // Helper methods
    private String getRandomFirstName() {
        return firstNames[random.nextInt(firstNames.length)];
    }
    
    private String getRandomLastName() {
        return lastNames[random.nextInt(lastNames.length)];
    }
    
    private String generatePhoneNumber() {
        return "+263" + (70 + random.nextInt(9)) + String.format("%07d", random.nextInt(10000000));
    }
    
    private String getRandomCity() {
        return cities[random.nextInt(cities.length)];
    }
    
    private String getRandomCargoType() {
        return cargoTypes[random.nextInt(cargoTypes.length)];
    }
    
    private String getRandomCompanyName() {
        return companyNames[random.nextInt(companyNames.length)];
    }
    
    private String getRandomVehicleMake() {
        return vehicleMakes[random.nextInt(vehicleMakes.length)];
    }
    
    private String getRandomVehicleModel() {
        return vehicleModels[random.nextInt(vehicleModels.length)];
    }

    private List<Company> createCompanies(List<User> transporterUsers) {
        log.info("Creating companies...");
        List<Company> companies = new ArrayList<>();

        for (User user : transporterUsers) {
            Company company = Company.builder()
                    .name(getRandomCompanyName())
                    .registrationNumber("REG" + String.format("%06d", random.nextInt(1000000)))
                    .taxNumber("TAX" + String.format("%08d", random.nextInt(*********)))
                    .description("Professional logistics and transportation services provider")
                    .address(generateAddress())
                    .city(getRandomCity())
                    .country("Zimbabwe")
                    .phoneNumber(generatePhoneNumber())
                    .email(user.getEmail().replace("@example.com", "@company.com"))
                    .website("www." + user.getEmail().split("@")[0] + ".com")
                    .type(getRandomCompanyType())
                    .verificationStatus(getRandomVerificationStatus())
                    .rating(BigDecimal.valueOf(3.0 + random.nextDouble() * 2.0)) // 3.0 to 5.0
                    .totalJobs(random.nextInt(100))
                    .completedJobs(random.nextInt(80))
                    .user(user)
                    .build();

            companies.add(companyRepository.save(company));
        }

        log.info("Created {} companies", companies.size());
        return companies;
    }

    private void createCompanyMembers(List<Company> companies) {
        log.info("Creating company members...");
        int totalMembers = 0;

        for (Company company : companies) {
            // Create owner membership for the company user
            CompanyMember owner = CompanyMember.builder()
                    .company(company)
                    .user(company.getUser())
                    .role(CompanyMember.CompanyRole.OWNER)
                    .status(CompanyMember.MemberStatus.ACTIVE)
                    .canManageMembers(true)
                    .canManageLoads(true)
                    .canUpdateLoadStatus(true)
                    .canUploadDocuments(true)
                    .canGenerateInvoices(true)
                    .canViewFinancials(true)
                    .canTrackLocation(true)
                    .joinedAt(LocalDateTime.now().minusDays(random.nextInt(365)))
                    .build();

            companyMemberRepository.save(owner);
            totalMembers++;

            // Add 1-3 additional members per company
            int additionalMembers = random.nextInt(3) + 1;
            for (int i = 0; i < additionalMembers; i++) {
                // Create a new user for this member
                Role transporterRole = roleRepository.findByName(Role.RoleName.TRANSPORTER)
                        .orElseThrow(() -> new RuntimeException("Transporter role not found"));

                String memberEmail = company.getUser().getEmail().split("@")[0] + "_member" + (i + 1) + "@example.com";
                User memberUser = User.builder()
                        .username(memberEmail) // Use email as username for backward compatibility
                        .email(memberEmail)
                        .password(passwordEncoder.encode("password123"))
                        .firstName(getRandomFirstName())
                        .lastName(getRandomLastName())
                        .phoneNumber(generatePhoneNumber())
                        .status(User.UserStatus.ACTIVE)
                        .emailVerified(random.nextBoolean())
                        .phoneVerified(random.nextBoolean())
                        .roles(Set.of(transporterRole))
                        .build();

                memberUser = userRepository.save(memberUser);

                CompanyMember.CompanyRole role = getRandomCompanyRole();
                CompanyMember member = CompanyMember.builder()
                        .company(company)
                        .user(memberUser)
                        .role(role)
                        .status(CompanyMember.MemberStatus.ACTIVE)
                        .canManageMembers(role == CompanyMember.CompanyRole.MANAGER)
                        .canManageLoads(role != CompanyMember.CompanyRole.VIEWER)
                        .canUpdateLoadStatus(role == CompanyMember.CompanyRole.DRIVER || role == CompanyMember.CompanyRole.DISPATCHER)
                        .canUploadDocuments(role != CompanyMember.CompanyRole.VIEWER)
                        .canGenerateInvoices(role == CompanyMember.CompanyRole.ACCOUNTANT || role == CompanyMember.CompanyRole.MANAGER)
                        .canViewFinancials(role == CompanyMember.CompanyRole.ACCOUNTANT || role == CompanyMember.CompanyRole.MANAGER)
                        .canTrackLocation(role == CompanyMember.CompanyRole.DRIVER)
                        .invitedBy(company.getUser())
                        .invitedAt(LocalDateTime.now().minusDays(random.nextInt(30)))
                        .joinedAt(LocalDateTime.now().minusDays(random.nextInt(25)))
                        .build();

                companyMemberRepository.save(member);
                totalMembers++;
            }
        }

        log.info("Created {} company members", totalMembers);
    }

    private void createVehicles(List<Company> companies) {
        log.info("Creating vehicles...");
        int totalVehicles = 0;

        for (Company company : companies) {
            int vehicleCount = random.nextInt(5) + 2; // 2-6 vehicles per company

            for (int i = 0; i < vehicleCount; i++) {
                Vehicle vehicle = Vehicle.builder()
                        .registrationNumber(generateRegistrationNumber())
                        .make(getRandomVehicleMake())
                        .model(getRandomVehicleModel())
                        .year(2015 + random.nextInt(9)) // 2015-2023
                        .type(getRandomVehicleType())
                        .maxWeight(BigDecimal.valueOf(5000 + random.nextInt(20000))) // 5-25 tons
                        .maxVolume(BigDecimal.valueOf(20 + random.nextInt(80))) // 20-100 m³
                        .weightUnit("kg")
                        .volumeUnit("m3")
                        .status(getRandomVehicleStatus())
                        .description("Well-maintained commercial vehicle suitable for various cargo types")
                        .isPubliclyVisible(random.nextBoolean())
                        .publicApprovalStatus(getRandomPublicApprovalStatus())
                        .dailyRate(BigDecimal.valueOf(100 + random.nextInt(400))) // $100-500 per day
                        .currency("USD")
                        .features(generateVehicleFeatures())
                        .isAvailableForRent(random.nextBoolean())
                        .hasInsurance(random.nextBoolean())
                        .hasFitnessCertificate(random.nextBoolean())
                        .hasPermits(random.nextBoolean())
                        .insuranceExpiryDate(LocalDateTime.now().plusDays(random.nextInt(365)))
                        .fitnessExpiryDate(LocalDateTime.now().plusDays(random.nextInt(365)))
                        .permitExpiryDate(LocalDateTime.now().plusDays(random.nextInt(365)))
                        .company(company)
                        .build();

                vehicleRepository.save(vehicle);
                totalVehicles++;
            }
        }

        log.info("Created {} vehicles", totalVehicles);
    }

    private void createEquipment(List<Company> companies) {
        log.info("Creating equipment...");
        int totalEquipment = 0;

        for (Company company : companies) {
            int equipmentCount = random.nextInt(4) + 1; // 1-4 equipment per company

            for (int i = 0; i < equipmentCount; i++) {
                Equipment equipment = Equipment.builder()
                        .name(getRandomEquipmentName())
                        .make(getRandomVehicleMake()) // Reuse vehicle makes for equipment
                        .model("Model-" + (random.nextInt(900) + 100))
                        .year(2010 + random.nextInt(13)) // 2010-2022
                        .type(getRandomEquipmentType())
                        .serialNumber("SN" + String.format("%010d", random.nextInt(*********0)))
                        .capacity(BigDecimal.valueOf(1000 + random.nextInt(9000))) // 1-10 tons capacity
                        .capacityUnit("kg")
                        .status(getRandomEquipmentStatus())
                        .description("Professional grade equipment for logistics operations")
                        .isPubliclyVisible(random.nextBoolean())
                        .publicApprovalStatus(getRandomEquipmentPublicApprovalStatus())
                        .dailyRate(BigDecimal.valueOf(50 + random.nextInt(200))) // $50-250 per day
                        .currency("USD")
                        .features(generateEquipmentFeatures())
                        .isAvailableForRent(random.nextBoolean())
                        .hasInsurance(random.nextBoolean())
                        .hasCertification(random.nextBoolean())
                        .hasOperatorLicense(random.nextBoolean())
                        .insuranceExpiryDate(LocalDateTime.now().plusDays(random.nextInt(365)))
                        .certificationExpiryDate(LocalDateTime.now().plusDays(random.nextInt(365)))
                        .lastMaintenanceDate(LocalDateTime.now().minusDays(random.nextInt(90)))
                        .nextMaintenanceDate(LocalDateTime.now().plusDays(random.nextInt(90)))
                        .location(getRandomCity())
                        .latitude(generateLatitude())
                        .longitude(generateLongitude())
                        .requiresOperator(random.nextBoolean())
                        .operatorIncluded(random.nextBoolean())
                        .company(company)
                        .build();

                equipmentRepository.save(equipment);
                totalEquipment++;
            }
        }

        log.info("Created {} equipment items", totalEquipment);
    }

    private List<Load> createLoads(List<User> clientUsers, List<Company> companies) {
        log.info("Creating loads...");
        List<Load> loads = new ArrayList<>();

        for (int i = 0; i < 50; i++) {
            User client = clientUsers.get(random.nextInt(clientUsers.size()));

            LocalDateTime pickupDate = LocalDateTime.now().plusDays(random.nextInt(30) + 1);
            LocalDateTime deliveryDate = pickupDate.plusDays(random.nextInt(7) + 1);

            Load load = Load.builder()
                    .trackingNumber(generateTrackingNumber())
                    .title(generateLoadTitle())
                    .description(generateLoadDescription())
                    .cargoType(getRandomCargoType())
                    .weight(BigDecimal.valueOf(100 + random.nextInt(9900))) // 100-10000 kg
                    .weightUnit("kg")
                    .volume(BigDecimal.valueOf(1 + random.nextInt(49))) // 1-50 m³
                    .volumeUnit("m3")
                    .pickupLocation(generateLocation())
                    .pickupLatitude(generateLatitude())
                    .pickupLongitude(generateLongitude())
                    .deliveryLocation(generateLocation())
                    .deliveryLatitude(generateLatitude())
                    .deliveryLongitude(generateLongitude())
                    .pickupDate(pickupDate)
                    .deliveryDate(deliveryDate)
                    .estimatedDistance(BigDecimal.valueOf(50 + random.nextInt(950))) // 50-1000 km
                    .distanceUnit("km")
                    .loadType(getRandomLoadType())
                    .paymentType(getRandomPaymentType())
                    .paymentRate(BigDecimal.valueOf(500 + random.nextInt(4500))) // $500-5000
                    .paymentUnit("USD")
                    .estimatedValue(BigDecimal.valueOf(1000 + random.nextInt(49000))) // $1000-50000
                    .status(getRandomLoadStatus())
                    .priority(getRandomPriority())
                    .isVerified(random.nextBoolean())
                    .requiresInsurance(random.nextBoolean())
                    .requiresSpecialHandling(random.nextBoolean())
                    .specialInstructions(generateSpecialInstructions())
                    .client(client)
                    .assignedCompany(random.nextBoolean() ? companies.get(random.nextInt(companies.size())) : null)
                    .biddingClosesAt(LocalDateTime.now().plusDays(random.nextInt(5) + 1))
                    .documentsVerified(random.nextBoolean())
                    .requiresDocumentVerification(random.nextBoolean())
                    .build();

            loads.add(loadRepository.save(load));
        }

        log.info("Created {} loads", loads.size());
        return loads;
    }

    private void createBids(List<Load> loads, List<Company> companies) {
        log.info("Creating bids...");
        int totalBids = 0;

        for (Load load : loads) {
            if (load.getStatus() == Load.LoadStatus.POSTED || load.getStatus() == Load.LoadStatus.BIDDING_CLOSED) {
                int bidCount = random.nextInt(5) + 1; // 1-5 bids per load

                for (int i = 0; i < bidCount; i++) {
                    Company company = companies.get(random.nextInt(companies.size()));

                    // Avoid duplicate bids from same company
                    if (bidRepository.findByLoadAndCompany(load, company).isPresent()) {
                        continue;
                    }

                    LocalDateTime estimatedPickup = load.getPickupDate().minusHours(random.nextInt(24));
                    LocalDateTime estimatedDelivery = load.getDeliveryDate().minusHours(random.nextInt(48));

                    Bid bid = Bid.builder()
                            .amount(load.getPaymentRate().multiply(BigDecimal.valueOf(0.8 + random.nextDouble() * 0.4))) // 80%-120% of asking price
                            .proposal(generateBidProposal())
                            .estimatedPickupTime(estimatedPickup)
                            .estimatedDeliveryTime(estimatedDelivery)
                            .status(getRandomBidStatus())
                            .notes(generateBidNotes())
                            .load(load)
                            .company(company)
                            .build();

                    bidRepository.save(bid);
                    totalBids++;
                }
            }
        }

        log.info("Created {} bids", totalBids);
    }

    private void createTrackingData(List<Load> loads) {
        log.info("Creating tracking data...");
        int totalTrackingEntries = 0;

        for (Load load : loads) {
            if (load.getStatus() == Load.LoadStatus.IN_TRANSIT ||
                load.getStatus() == Load.LoadStatus.DELIVERED ||
                load.getStatus() == Load.LoadStatus.ASSIGNED) {

                int trackingCount = random.nextInt(5) + 2; // 2-6 tracking entries per load

                for (int i = 0; i < trackingCount; i++) {
                    LoadTracking tracking = LoadTracking.builder()
                            .location(generateLocation())
                            .latitude(generateLatitude())
                            .longitude(generateLongitude())
                            .status(getRandomTrackingStatus())
                            .notes(generateTrackingNotes())
                            .isAutomated(random.nextBoolean())
                            .load(load)
                            .updatedBy(load.getAssignedCompany() != null ? load.getAssignedCompany().getUser() : null)
                            .timestamp(LocalDateTime.now().minusHours(random.nextInt(72)))
                            .build();

                    loadTrackingRepository.save(tracking);
                    totalTrackingEntries++;
                }
            }
        }

        log.info("Created {} tracking entries", totalTrackingEntries);
    }

    // Helper methods for generating random data
    private String generateAddress() {
        String[] streetNames = {"Main Street", "Industrial Road", "Commerce Avenue", "Business Park", "Trade Center"};
        return (random.nextInt(999) + 1) + " " + streetNames[random.nextInt(streetNames.length)];
    }

    private Company.CompanyType getRandomCompanyType() {
        Company.CompanyType[] types = Company.CompanyType.values();
        return types[random.nextInt(types.length)];
    }

    private Company.VerificationStatus getRandomVerificationStatus() {
        Company.VerificationStatus[] statuses = Company.VerificationStatus.values();
        // Bias towards VERIFIED status
        if (random.nextDouble() < 0.7) {
            return Company.VerificationStatus.VERIFIED;
        }
        return statuses[random.nextInt(statuses.length)];
    }

    private CompanyMember.CompanyRole getRandomCompanyRole() {
        CompanyMember.CompanyRole[] roles = {
            CompanyMember.CompanyRole.MANAGER,
            CompanyMember.CompanyRole.DRIVER,
            CompanyMember.CompanyRole.DISPATCHER,
            CompanyMember.CompanyRole.ACCOUNTANT,
            CompanyMember.CompanyRole.VIEWER
        };
        return roles[random.nextInt(roles.length)];
    }

    private String generateRegistrationNumber() {
        String[] prefixes = {"AAA", "AAB", "AAC", "AAD", "AAE", "AAF"};
        return prefixes[random.nextInt(prefixes.length)] + String.format("%04d", random.nextInt(10000));
    }

    private Vehicle.VehicleType getRandomVehicleType() {
        Vehicle.VehicleType[] types = Vehicle.VehicleType.values();
        return types[random.nextInt(types.length)];
    }

    private Vehicle.VehicleStatus getRandomVehicleStatus() {
        Vehicle.VehicleStatus[] statuses = Vehicle.VehicleStatus.values();
        return statuses[random.nextInt(statuses.length)];
    }

    private Vehicle.PublicApprovalStatus getRandomPublicApprovalStatus() {
        Vehicle.PublicApprovalStatus[] statuses = Vehicle.PublicApprovalStatus.values();
        return statuses[random.nextInt(statuses.length)];
    }

    private Equipment.PublicApprovalStatus getRandomEquipmentPublicApprovalStatus() {
        Equipment.PublicApprovalStatus[] statuses = Equipment.PublicApprovalStatus.values();
        return statuses[random.nextInt(statuses.length)];
    }

    private String generateVehicleFeatures() {
        String[] features = {"GPS Tracking", "Air Conditioning", "Hydraulic Lift", "Refrigeration", "Security System"};
        List<String> selectedFeatures = new ArrayList<>();
        for (String feature : features) {
            if (random.nextBoolean()) {
                selectedFeatures.add(feature);
            }
        }
        return String.join(", ", selectedFeatures);
    }

    private String getRandomEquipmentName() {
        String[] names = {"Heavy Duty Crane", "Forklift", "Container Loader", "Pallet Jack", "Conveyor System", "Loading Dock"};
        return names[random.nextInt(names.length)];
    }

    private Equipment.EquipmentType getRandomEquipmentType() {
        Equipment.EquipmentType[] types = Equipment.EquipmentType.values();
        return types[random.nextInt(types.length)];
    }

    private Equipment.EquipmentStatus getRandomEquipmentStatus() {
        Equipment.EquipmentStatus[] statuses = Equipment.EquipmentStatus.values();
        return statuses[random.nextInt(statuses.length)];
    }

    private String generateEquipmentFeatures() {
        String[] features = {"Remote Control", "Safety Sensors", "Digital Display", "Automatic Operation", "Emergency Stop"};
        List<String> selectedFeatures = new ArrayList<>();
        for (String feature : features) {
            if (random.nextBoolean()) {
                selectedFeatures.add(feature);
            }
        }
        return String.join(", ", selectedFeatures);
    }

    private BigDecimal generateLatitude() {
        // Zimbabwe latitude range: approximately -22.4 to -15.6
        return BigDecimal.valueOf(-22.4 + random.nextDouble() * 6.8);
    }

    private BigDecimal generateLongitude() {
        // Zimbabwe longitude range: approximately 25.2 to 33.1
        return BigDecimal.valueOf(25.2 + random.nextDouble() * 7.9);
    }

    private String generateTrackingNumber() {
        return "LP-" + LocalDateTime.now().getYear() +
               String.format("%02d", LocalDateTime.now().getMonthValue()) +
               String.format("%02d", LocalDateTime.now().getDayOfMonth()) + "-" +
               String.format("%05d", random.nextInt(100000));
    }

    private String generateLoadTitle() {
        String[] adjectives = {"Urgent", "Priority", "Standard", "Express", "Scheduled"};
        String[] items = {"Cargo Delivery", "Freight Transport", "Goods Shipment", "Material Transfer", "Product Delivery"};
        return adjectives[random.nextInt(adjectives.length)] + " " + items[random.nextInt(items.length)];
    }

    private String generateLoadDescription() {
        String[] descriptions = {
            "Professional transportation of commercial goods with careful handling and timely delivery.",
            "Secure freight service for valuable cargo requiring special attention and tracking.",
            "Standard logistics service for general merchandise with reliable pickup and delivery.",
            "Express delivery service for time-sensitive materials with priority handling.",
            "Bulk cargo transportation with appropriate equipment and experienced drivers."
        };
        return descriptions[random.nextInt(descriptions.length)];
    }

    private String generateLocation() {
        return getRandomCity() + " Industrial Area, " + generateAddress();
    }

    private Load.LoadType getRandomLoadType() {
        Load.LoadType[] types = Load.LoadType.values();
        return types[random.nextInt(types.length)];
    }

    private Load.PaymentType getRandomPaymentType() {
        Load.PaymentType[] types = Load.PaymentType.values();
        return types[random.nextInt(types.length)];
    }

    private Load.LoadStatus getRandomLoadStatus() {
        Load.LoadStatus[] statuses = Load.LoadStatus.values();
        // Bias towards more common statuses
        double rand = random.nextDouble();
        if (rand < 0.3) return Load.LoadStatus.POSTED;
        if (rand < 0.5) return Load.LoadStatus.BIDDING_CLOSED;
        if (rand < 0.7) return Load.LoadStatus.ASSIGNED;
        if (rand < 0.85) return Load.LoadStatus.IN_TRANSIT;
        if (rand < 0.95) return Load.LoadStatus.DELIVERED;
        return Load.LoadStatus.CANCELLED;
    }

    private Load.Priority getRandomPriority() {
        Load.Priority[] priorities = Load.Priority.values();
        // Bias towards NORMAL priority
        double rand = random.nextDouble();
        if (rand < 0.6) return Load.Priority.NORMAL;
        if (rand < 0.8) return Load.Priority.HIGH;
        if (rand < 0.9) return Load.Priority.URGENT;
        return Load.Priority.LOW;
    }

    private String generateSpecialInstructions() {
        String[] instructions = {
            "Handle with care - fragile items",
            "Temperature controlled transport required",
            "Requires signature upon delivery",
            "Call recipient 30 minutes before arrival",
            "Use rear loading dock for pickup",
            "No special requirements",
            "Hazardous materials - follow safety protocols",
            "Oversized cargo - check route clearances"
        };
        return instructions[random.nextInt(instructions.length)];
    }

    private Bid.BidStatus getRandomBidStatus() {
        Bid.BidStatus[] statuses = Bid.BidStatus.values();
        // Bias towards PENDING status
        double rand = random.nextDouble();
        if (rand < 0.6) return Bid.BidStatus.PENDING;
        if (rand < 0.75) return Bid.BidStatus.ACCEPTED;
        if (rand < 0.9) return Bid.BidStatus.REJECTED;
        return Bid.BidStatus.WITHDRAWN;
    }

    private String generateBidProposal() {
        String[] proposals = {
            "We offer competitive pricing with guaranteed on-time delivery and full insurance coverage.",
            "Our experienced team ensures safe handling of your cargo with real-time tracking updates.",
            "Professional service with modern fleet and certified drivers for reliable transportation.",
            "Cost-effective solution with flexible scheduling to meet your delivery requirements.",
            "Premium service with dedicated support and comprehensive cargo protection."
        };
        return proposals[random.nextInt(proposals.length)];
    }

    private String generateBidNotes() {
        String[] notes = {
            "Available for immediate pickup",
            "Can provide additional insurance if needed",
            "Flexible on delivery time",
            "Experienced with this type of cargo",
            "Can offer volume discounts for future loads",
            "GPS tracking included",
            "24/7 customer support available"
        };
        return notes[random.nextInt(notes.length)];
    }

    private LoadTracking.TrackingStatus getRandomTrackingStatus() {
        LoadTracking.TrackingStatus[] statuses = LoadTracking.TrackingStatus.values();
        return statuses[random.nextInt(statuses.length)];
    }

    private String generateTrackingNotes() {
        String[] notes = {
            "On schedule for delivery",
            "Minor delay due to traffic",
            "Cargo secured and in good condition",
            "Driver taking mandatory rest break",
            "Arrived at checkpoint",
            "Loading/unloading in progress",
            "Weather conditions causing slight delay",
            "All documentation verified"
        };
        return notes[random.nextInt(notes.length)];
    }
}
