groups:
  - name: logipool-backend
    rules:
      # Application Health
      - alert: LogiPoolBackendDown
        expr: up{job="logipool-backend"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "LogiPool Backend is down"
          description: "LogiPool Backend has been down for more than 1 minute."

      # High Response Time
      - alert: HighResponseTime
        expr: http_request_duration_seconds{quantile="0.95"} > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s for {{ $labels.uri }}"

      # High Error Rate
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} errors per second"

      # Database Connection Pool
      - alert: DatabaseConnectionPoolHigh
        expr: hikaricp_connections_active / hikaricp_connections_max > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Database connection pool usage is high"
          description: "Connection pool usage is {{ $value | humanizePercentage }}"

      # Memory Usage
      - alert: HighMemoryUsage
        expr: jvm_memory_used_bytes / jvm_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High JVM memory usage"
          description: "JVM memory usage is {{ $value | humanizePercentage }}"

      # Disk Space
      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space"
          description: "Disk space is {{ $value | humanizePercentage }} full"

  - name: database
    rules:
      # PostgreSQL Down
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database is down"

      # High Database Connections
      - alert: HighDatabaseConnections
        expr: pg_stat_activity_count / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High database connection usage"
          description: "Database connection usage is {{ $value | humanizePercentage }}"

      # Long Running Queries
      - alert: LongRunningQueries
        expr: pg_stat_activity_max_tx_duration > 300
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Long running database queries detected"
          description: "Longest running query is {{ $value }}s"

  - name: redis
    rules:
      # Redis Down
      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis cache server is down"

      # High Redis Memory Usage
      - alert: HighRedisMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High Redis memory usage"
          description: "Redis memory usage is {{ $value | humanizePercentage }}"

  - name: system
    rules:
      # High CPU Usage
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage"
          description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"

      # High Load Average
      - alert: HighLoadAverage
        expr: node_load15 > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High load average"
          description: "Load average is {{ $value }} on {{ $labels.instance }}"

      # Out of Memory
      - alert: OutOfMemory
        expr: (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes) < 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "Out of memory"
          description: "Available memory is {{ $value | humanizePercentage }} on {{ $labels.instance }}"

  - name: business-metrics
    rules:
      # High Failed Login Attempts
      - alert: HighFailedLoginAttempts
        expr: rate(logipool_login_attempts_total{status="failed"}[5m]) > 10
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High failed login attempts"
          description: "Failed login rate is {{ $value }} per second"

      # Payment Processing Failures
      - alert: PaymentProcessingFailures
        expr: rate(logipool_payments_total{status="failed"}[5m]) > 0.1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Payment processing failures detected"
          description: "Payment failure rate is {{ $value }} per second"

      # Load Creation Rate Drop
      - alert: LoadCreationRateDrop
        expr: rate(logipool_loads_created_total[1h]) < 0.5 * rate(logipool_loads_created_total[24h] offset 24h)
        for: 30m
        labels:
          severity: warning
        annotations:
          summary: "Load creation rate has dropped significantly"
          description: "Current load creation rate is 50% below yesterday's rate"
