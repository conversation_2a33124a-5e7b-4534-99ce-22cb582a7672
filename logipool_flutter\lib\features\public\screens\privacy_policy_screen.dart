import 'package:flutter/material.dart';
import '../../../shared/widgets/public_footer.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildHeader(context),
          _buildContent(context),
          const PublicFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background image
          Positioned.fill(
            child: Image.asset(
              'assets/images/marcin-jozwiak-kGoPcmpPT7c-unsplash.jpg',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
          ),
          // Overlay
          Positioned.fill(
            child: Container(
              color: Theme.of(context).primaryColor.withOpacity(0.7),
            ),
          ),
          // Content
          Positioned.fill(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 64),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.privacy_tip,
                    size: 64,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Privacy Policy',
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 48,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Last updated: December 2024',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white70,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSection(
            context,
            'Introduction',
            'LogiPool ("we," "our," or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our logistics marketplace platform.',
          ),
          _buildSection(
            context,
            'Information We Collect',
            '''We collect information you provide directly to us, such as:
• Personal identification information (name, email, phone number)
• Business information (company name, address, tax ID)
• Payment information (processed securely through third-party providers)
• Shipment details and tracking information
• Communication records and support interactions''',
          ),
          _buildSection(
            context,
            'How We Use Your Information',
            '''We use the information we collect to:
• Provide, maintain, and improve our services
• Process transactions and send related information
• Send technical notices, updates, and support messages
• Respond to your comments, questions, and customer service requests
• Monitor and analyze trends, usage, and activities
• Detect, investigate, and prevent fraudulent transactions''',
          ),
          _buildSection(
            context,
            'Information Sharing',
            '''We may share your information in the following situations:
• With service providers to facilitate transactions
• With third-party vendors who perform services on our behalf
• To comply with legal obligations or protect our rights
• In connection with a merger, sale, or acquisition
• With your consent or at your direction''',
          ),
          _buildSection(
            context,
            'Data Security',
            'We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.',
          ),
          _buildSection(
            context,
            'Location Information',
            'We may collect and use location information to provide tracking services and optimize logistics routes. You can control location sharing through your device settings.',
          ),
          _buildSection(
            context,
            'Cookies and Tracking',
            'We use cookies and similar tracking technologies to collect and use personal information about you. You can control cookies through your browser settings.',
          ),
          _buildSection(
            context,
            'Your Rights',
            '''You have the right to:
• Access and update your personal information
• Request deletion of your personal information
• Object to processing of your personal information
• Request restriction of processing
• Data portability
• Withdraw consent at any time''',
          ),
          _buildSection(
            context,
            'Data Retention',
            'We retain your personal information for as long as necessary to provide our services and comply with legal obligations. We will delete or anonymize your information when it is no longer needed.',
          ),
          _buildSection(
            context,
            'Children\'s Privacy',
            'Our services are not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13.',
          ),
          _buildSection(
            context,
            'International Transfers',
            'Your information may be transferred to and processed in countries other than your own. We ensure appropriate safeguards are in place for such transfers.',
          ),
          _buildSection(
            context,
            'Changes to Privacy Policy',
            'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.',
          ),
          _buildSection(
            context,
            'Contact Us',
            '''If you have questions about this Privacy Policy, please contact us:
• Email: <EMAIL>
• Phone: +263 4 123 4567
• Address: 123 Business District, Harare, Zimbabwe''',
          ),
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.security,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Your privacy is important to us. We are committed to protecting your personal information and being transparent about how we use it.',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }
}
