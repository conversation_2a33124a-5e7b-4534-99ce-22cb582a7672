package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.entity.Document;
import zw.co.kanjan.logipool.repository.DocumentRepository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Service for scheduled document maintenance tasks
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentMaintenanceService {
    
    private final DocumentRepository documentRepository;
    private final FileStorageService fileStorageService;
    private final NotificationService notificationService;
    
    /**
     * Check for expired documents and update their status
     * Runs every day at 2 AM
     */
    @Scheduled(cron = "0 0 2 * * *")
    @Transactional
    public void checkExpiredDocuments() {
        log.info("Starting expired documents check");
        
        LocalDateTime now = LocalDateTime.now();
        List<Document> expiredDocuments = documentRepository.findExpiredDocuments(now);
        
        for (Document document : expiredDocuments) {
            document.setStatus(Document.DocumentStatus.EXPIRED);
            documentRepository.save(document);
            
            // Send notification about expired document
            sendExpirationNotification(document);
            
            log.info("Document expired: {} (ID: {})", document.getName(), document.getId());
        }
        
        log.info("Expired documents check completed. {} documents expired", expiredDocuments.size());
    }
    
    /**
     * Check for documents expiring soon and send notifications
     * Runs every day at 9 AM
     */
    @Scheduled(cron = "0 0 9 * * *")
    @Transactional(readOnly = true)
    public void checkDocumentsExpiringSoon() {
        log.info("Starting documents expiring soon check");
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime futureDate = now.plusDays(30); // Check for documents expiring in next 30 days
        
        List<Document> expiringSoonDocuments = documentRepository.findDocumentsExpiringSoon(now, futureDate);
        
        for (Document document : expiringSoonDocuments) {
            sendExpirationWarningNotification(document);
            log.info("Expiration warning sent for document: {} (ID: {}), expires: {}", 
                    document.getName(), document.getId(), document.getExpiryDate());
        }
        
        log.info("Documents expiring soon check completed. {} warnings sent", expiringSoonDocuments.size());
    }
    
    /**
     * Clean up orphaned files (files without corresponding database records)
     * Runs every Sunday at 3 AM
     */
    @Scheduled(cron = "0 0 3 * * SUN")
    @Transactional(readOnly = true)
    public void cleanupOrphanedFiles() {
        log.info("Starting orphaned files cleanup");
        
        // This is a placeholder for file cleanup logic
        // In a real implementation, you would:
        // 1. List all files in the upload directory
        // 2. Check if each file has a corresponding database record
        // 3. Delete files that don't have database records
        // 4. Be careful not to delete recently uploaded files that might not be processed yet
        
        log.info("Orphaned files cleanup completed");
    }
    
    /**
     * Generate document compliance reports
     * Runs every Monday at 8 AM
     */
    @Scheduled(cron = "0 0 8 * * MON")
    @Transactional(readOnly = true)
    public void generateComplianceReports() {
        log.info("Starting document compliance report generation");
        
        // Count documents by status
        long pendingCount = documentRepository.countByStatus(Document.DocumentStatus.PENDING);
        long verifiedCount = documentRepository.countByStatus(Document.DocumentStatus.VERIFIED);
        long rejectedCount = documentRepository.countByStatus(Document.DocumentStatus.REJECTED);
        long expiredCount = documentRepository.countByStatus(Document.DocumentStatus.EXPIRED);
        
        log.info("Document Status Summary:");
        log.info("- Pending: {}", pendingCount);
        log.info("- Verified: {}", verifiedCount);
        log.info("- Rejected: {}", rejectedCount);
        log.info("- Expired: {}", expiredCount);
        
        // TODO: Send compliance report to administrators
        // This could be implemented as an email report or dashboard notification
        
        log.info("Document compliance report generation completed");
    }
    
    /**
     * Clean up large files that exceed storage limits
     * Runs every first day of the month at 4 AM
     */
    @Scheduled(cron = "0 0 4 1 * *")
    @Transactional(readOnly = true)
    public void cleanupLargeFiles() {
        log.info("Starting large files cleanup check");
        
        // Find files larger than 50MB (configurable threshold)
        long sizeLimit = 50 * 1024 * 1024; // 50MB in bytes
        List<Document> largeFiles = documentRepository.findLargeFiles(sizeLimit);
        
        for (Document document : largeFiles) {
            log.warn("Large file detected: {} (ID: {}, Size: {} bytes)", 
                    document.getName(), document.getId(), document.getFileSize());
            
            // TODO: Implement large file handling strategy
            // Options:
            // 1. Compress the file
            // 2. Move to cold storage
            // 3. Notify admin for manual review
            // 4. Archive old large files
        }
        
        log.info("Large files cleanup check completed. {} large files found", largeFiles.size());
    }
    
    private void sendExpirationNotification(Document document) {
        try {
            if (document.getCompany() != null && document.getCompany().getUser() != null) {
                String subject = "Document Expired: " + document.getName();
                String message = String.format(
                    "Your document '%s' has expired on %s. Please upload a new version to maintain compliance.",
                    document.getName(),
                    document.getExpiryDate()
                );
                
                notificationService.sendNotification(
                    document.getCompany().getUser(),
                    subject,
                    message,
                    "DOCUMENT_EXPIRED"
                );
            }
        } catch (Exception e) {
            log.error("Failed to send expiration notification for document ID: {}", document.getId(), e);
        }
    }
    
    private void sendExpirationWarningNotification(Document document) {
        try {
            if (document.getCompany() != null && document.getCompany().getUser() != null) {
                long daysUntilExpiry = java.time.temporal.ChronoUnit.DAYS.between(
                    LocalDateTime.now().toLocalDate(),
                    document.getExpiryDate().toLocalDate()
                );
                
                String subject = "Document Expiring Soon: " + document.getName();
                String message = String.format(
                    "Your document '%s' will expire in %d days on %s. Please renew it to avoid compliance issues.",
                    document.getName(),
                    daysUntilExpiry,
                    document.getExpiryDate().toLocalDate()
                );
                
                notificationService.sendNotification(
                    document.getCompany().getUser(),
                    subject,
                    message,
                    "DOCUMENT_EXPIRING_SOON"
                );
            }
        } catch (Exception e) {
            log.error("Failed to send expiration warning notification for document ID: {}", document.getId(), e);
        }
    }
}
