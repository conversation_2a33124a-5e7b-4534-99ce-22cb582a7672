import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/invoice_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/widgets/unified_header.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../../../core/constants/app_constants.dart';
import '../bloc/payment_bloc.dart';
import '../widgets/invoice_card.dart';
import '../widgets/invoice_filter_sheet.dart';

/// Invoice management screen for creating, viewing, and managing invoices
class InvoiceManagementScreen extends StatefulWidget {
  const InvoiceManagementScreen({super.key});

  @override
  State<InvoiceManagementScreen> createState() =>
      _InvoiceManagementScreenState();
}

class _InvoiceManagementScreenState extends State<InvoiceManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  InvoiceStatus? _selectedStatus;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadInvoices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadInvoices() {
    context.read<PaymentBloc>().add(LoadInvoices(
          refresh: true,
          status: _selectedStatus,
          searchQuery: _searchQuery,
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedHeader(
        title: 'Invoice Management',
        actions: [
          IconButton(
            icon: Icon(
              Icons.filter_list,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            onPressed: _showFilterSheet,
            tooltip: 'Filter Invoices',
          ),
          IconButton(
            icon: Icon(
              Icons.search,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            onPressed: _showSearchDialog,
            tooltip: 'Search Invoices',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildTabBar(),
          if (_searchQuery.isNotEmpty) _buildSearchBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildInvoiceList(null), // All invoices
                _buildInvoiceList(InvoiceStatus.draft),
                _buildInvoiceList(InvoiceStatus.sent),
                _buildInvoiceList(InvoiceStatus.paid),
                _buildInvoiceList(InvoiceStatus.overdue),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabs: const [
          Tab(text: 'All', icon: Icon(Icons.receipt_long)),
          Tab(text: 'Draft', icon: Icon(Icons.edit)),
          Tab(text: 'Sent', icon: Icon(Icons.send)),
          Tab(text: 'Paid', icon: Icon(Icons.check_circle)),
          Tab(text: 'Overdue', icon: Icon(Icons.warning)),
        ],
        onTap: (index) {
          InvoiceStatus? status;
          switch (index) {
            case 1:
              status = InvoiceStatus.draft;
              break;
            case 2:
              status = InvoiceStatus.sent;
              break;
            case 3:
              status = InvoiceStatus.paid;
              break;
            case 4:
              status = InvoiceStatus.overdue;
              break;
          }
          setState(() {
            _selectedStatus = status;
          });
          _loadInvoices();
        },
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Theme.of(context).colorScheme.surface,
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.search,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Searching: "$_searchQuery"',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            onPressed: () {
              setState(() {
                _searchQuery = '';
              });
              _loadInvoices();
            },
            icon: const Icon(Icons.clear),
            tooltip: 'Clear Search',
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceList(InvoiceStatus? status) {
    return BlocBuilder<PaymentBloc, PaymentState>(
      builder: (context, state) {
        if (state is PaymentLoading) {
          return const LoadingWidget();
        }

        if (state is PaymentError) {
          return CustomErrorWidget(
            message: state.message,
            onRetry: _loadInvoices,
          );
        }

        // For now, show placeholder content since we don't have invoice data in the state
        return RefreshIndicator(
          onRefresh: () async => _loadInvoices(),
          child: _buildPlaceholderInvoiceList(status),
        );
      },
    );
  }

  Widget _buildPlaceholderInvoiceList(InvoiceStatus? status) {
    // Generate some sample invoices for demonstration
    final sampleInvoices = _generateSampleInvoices(status);

    if (sampleInvoices.isEmpty) {
      return EmptyStateWidget(
        icon: Icons.receipt_long,
        title: 'No Invoices',
        message: status == null
            ? 'No invoices found. Create your first invoice to get started.'
            : 'No ${status.displayName.toLowerCase()} invoices found.',
        actionLabel: 'Create Invoice',
        onAction: () => _createInvoice(),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sampleInvoices.length,
      itemBuilder: (context, index) {
        final invoice = sampleInvoices[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildInvoiceCard(invoice),
        );
      },
    );
  }

  Widget _buildInvoiceCard(InvoiceModel invoice) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '\$');
    final dateFormat = DateFormat('MMM dd, yyyy');

    return Card(
      child: InkWell(
        onTap: () => _viewInvoice(invoice),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          invoice.invoiceNumber ?? 'INV-${invoice.id}',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          invoice.clientName ?? 'Unknown Client',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(invoice.status),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Amount',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          currencyFormat.format(invoice.totalAmountValue),
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Due Date',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          dateFormat.format(invoice.dueDate),
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: _getDueDateColor(invoice),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _editInvoice(invoice),
                      icon: const Icon(Icons.edit, size: 16),
                      label: const Text('Edit'),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _sendInvoice(invoice),
                      icon: const Icon(Icons.send, size: 16),
                      label: const Text('Send'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(InvoiceStatus status) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (status) {
      case InvoiceStatus.draft:
        backgroundColor = Colors.grey[200]!;
        textColor = Colors.grey[700]!;
        icon = Icons.edit;
        break;
      case InvoiceStatus.sent:
        backgroundColor = Colors.blue[100]!;
        textColor = Colors.blue[700]!;
        icon = Icons.send;
        break;
      case InvoiceStatus.viewed:
        backgroundColor = Colors.orange[100]!;
        textColor = Colors.orange[700]!;
        icon = Icons.visibility;
        break;
      case InvoiceStatus.paid:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[700]!;
        icon = Icons.check_circle;
        break;
      case InvoiceStatus.overdue:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[700]!;
        icon = Icons.warning;
        break;
      case InvoiceStatus.cancelled:
        backgroundColor = Colors.grey[200]!;
        textColor = Colors.grey[700]!;
        icon = Icons.cancel;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: textColor,
          ),
          const SizedBox(width: 4),
          Text(
            status.displayName,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Color _getDueDateColor(InvoiceModel invoice) {
    final now = DateTime.now();
    final dueDate = invoice.dueDate;

    if (invoice.status == InvoiceStatus.paid) {
      return Colors.green;
    } else if (dueDate.isBefore(now)) {
      return Colors.red;
    } else if (dueDate.difference(now).inDays <= 3) {
      return Colors.orange;
    } else {
      return Colors.grey[600]!;
    }
  }

  List<InvoiceModel> _generateSampleInvoices(InvoiceStatus? status) {
    final now = DateTime.now();
    final sampleInvoices = [
      InvoiceModel(
        id: 1,
        invoiceNumber: 'INV-2024-001',
        subtotal: 1500.0,
        taxAmount: 150.0,
        discountAmount: 0.0,
        totalAmount: 1650.0,
        commissionAmount: 82.5,
        netAmount: 1567.5,
        status: InvoiceStatus.sent,
        type: InvoiceType.load,
        dueDate: now.add(const Duration(days: 15)),
        clientName: 'ABC Logistics',
        loadTitle: 'Electronics Shipment - NY to CA',
        createdAt: now.subtract(const Duration(days: 5)),
        updatedAt: now.subtract(const Duration(days: 5)),
      ),
      InvoiceModel(
        id: 2,
        invoiceNumber: 'INV-2024-002',
        subtotal: 2200.0,
        taxAmount: 220.0,
        discountAmount: 100.0,
        totalAmount: 2320.0,
        commissionAmount: 116.0,
        netAmount: 2204.0,
        status: InvoiceStatus.paid,
        type: InvoiceType.load,
        dueDate: now.subtract(const Duration(days: 2)),
        clientName: 'XYZ Transport',
        loadTitle: 'Furniture Delivery - TX to FL',
        paidAt: now.subtract(const Duration(days: 1)),
        createdAt: now.subtract(const Duration(days: 10)),
        updatedAt: now.subtract(const Duration(days: 1)),
      ),
    ];

    if (status == null) {
      return sampleInvoices;
    }

    return sampleInvoices.where((invoice) => invoice.status == status).toList();
  }

  Widget? _buildFloatingActionButton() {
    return FutureBuilder<UserModel?>(
      future: context.read<AuthService>().getCurrentUser(),
      builder: (context, snapshot) {
        final user = snapshot.data;
        if (user?.role == AppConstants.roleTransporter ||
            user?.role == AppConstants.roleAdmin) {
          return FloatingActionButton.extended(
            onPressed: _createInvoice,
            icon: const Icon(Icons.add),
            label: const Text('Create Invoice'),
          );
        }
        return null;
      },
    );
  }

  // Action methods
  void _createInvoice() {
    // TODO: Implement invoice creation screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Invoice creation feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _viewInvoice(InvoiceModel invoice) {
    context.push('/payments/invoices/${invoice.id}');
  }

  void _editInvoice(InvoiceModel invoice) {
    // TODO: Implement invoice edit screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Invoice editing feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _sendInvoice(InvoiceModel invoice) {
    // Show send confirmation dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Send Invoice'),
        content: Text(
            'Send invoice ${invoice.invoiceNumber} to ${invoice.clientName}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement send invoice logic
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                      'Invoice ${invoice.invoiceNumber} sent successfully'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('Send'),
          ),
        ],
      ),
    );
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Filter Invoices',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 20),
            const Text('Filter options coming soon...'),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Close'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Invoices'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'Enter invoice number or client name...',
            border: OutlineInputBorder(),
          ),
          onSubmitted: (value) {
            Navigator.pop(context);
            setState(() {
              _searchQuery = value;
            });
            _loadInvoices();
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
