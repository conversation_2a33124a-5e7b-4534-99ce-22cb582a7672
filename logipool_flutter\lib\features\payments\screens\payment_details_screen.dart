import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/payment_model.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../core/utils/date_formatter.dart';
import '../bloc/payment_bloc.dart';

class PaymentDetailsScreen extends StatefulWidget {
  final int paymentId;

  const PaymentDetailsScreen({
    super.key,
    required this.paymentId,
  });

  @override
  State<PaymentDetailsScreen> createState() => _PaymentDetailsScreenState();
}

class _PaymentDetailsScreenState extends State<PaymentDetailsScreen> {
  @override
  void initState() {
    super.initState();
    context.read<PaymentBloc>().add(LoadPaymentDetails(widget.paymentId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Payment #${widget.paymentId}'),
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  context.go('/payments/${widget.paymentId}/edit');
                  break;
                case 'process':
                  context.go('/payments/${widget.paymentId}/process');
                  break;
                case 'cancel':
                  _showCancelDialog();
                  break;
                case 'delete':
                  _showDeleteDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text('Edit Payment'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'process',
                child: ListTile(
                  leading: Icon(Icons.payment),
                  title: Text('Process Payment'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'cancel',
                child: ListTile(
                  leading: Icon(Icons.cancel, color: Colors.orange),
                  title: Text('Cancel Payment'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: ListTile(
                  leading: Icon(Icons.delete, color: Colors.red),
                  title: Text('Delete Payment'),
                  contentPadding: EdgeInsets.zero,
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocConsumer<PaymentBloc, PaymentState>(
        listener: (context, state) {
          if (state is PaymentError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          } else if (state is PaymentOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
            if (state.message.contains('deleted')) {
              Navigator.of(context).pop();
            } else {
              // Refresh payment details
              context.read<PaymentBloc>().add(LoadPaymentDetails(widget.paymentId));
            }
          }
        },
        builder: (context, state) {
          if (state is PaymentLoading) {
            return const LoadingWidget();
          }

          if (state is PaymentDetailsLoaded) {
            return _buildPaymentDetails(context, state.payment);
          }

          if (state is PaymentError) {
            return AppErrorWidget(
              message: state.message,
              onRetry: () {
                context.read<PaymentBloc>().add(LoadPaymentDetails(widget.paymentId));
              },
            );
          }

          return const LoadingWidget();
        },
      ),
    );
  }

  Widget _buildPaymentDetails(BuildContext context, PaymentModel payment) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatusCard(context, payment),
          const SizedBox(height: 16),
          _buildAmountCard(context, payment),
          const SizedBox(height: 16),
          _buildPaymentInfoCard(context, payment),
          const SizedBox(height: 16),
          _buildParticipantsCard(context, payment),
          if (payment.loadId != null) ...[
            const SizedBox(height: 16),
            _buildLoadInfoCard(context, payment),
          ],
          if (payment.transactionId != null || payment.paymentGatewayReference != null) ...[
            const SizedBox(height: 16),
            _buildTransactionCard(context, payment),
          ],
          if (payment.description != null || payment.notes != null) ...[
            const SizedBox(height: 16),
            _buildNotesCard(context, payment),
          ],
          const SizedBox(height: 16),
          _buildTimestampsCard(context, payment),
          if (payment.status.isPending) ...[
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  context.go('/payments/${payment.id}/process');
                },
                icon: const Icon(Icons.payment),
                label: const Text('Process Payment'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
          if (payment.status.isActive) ...[
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _showCancelDialog,
                icon: const Icon(Icons.cancel),
                label: const Text('Cancel Payment'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  foregroundColor: Colors.orange,
                  side: const BorderSide(color: Colors.orange),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusCard(BuildContext context, PaymentModel payment) {
    Color statusColor;
    IconData statusIcon;

    switch (payment.status) {
      case PaymentStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.schedule;
        break;
      case PaymentStatus.processing:
        statusColor = Colors.blue;
        statusIcon = Icons.sync;
        break;
      case PaymentStatus.completed:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case PaymentStatus.failed:
        statusColor = Colors.red;
        statusIcon = Icons.error;
        break;
      case PaymentStatus.cancelled:
        statusColor = Colors.grey;
        statusIcon = Icons.cancel;
        break;
      case PaymentStatus.refunded:
        statusColor = Colors.purple;
        statusIcon = Icons.undo;
        break;
      case PaymentStatus.disputed:
        statusColor = Colors.amber;
        statusIcon = Icons.warning;
        break;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                statusIcon,
                color: statusColor,
                size: 32,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    payment.status.displayName,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: statusColor,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    payment.status.description,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountCard(BuildContext context, PaymentModel payment) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Amount',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Total Amount:'),
                Text(
                  '\$${payment.amount.toStringAsFixed(2)}',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                ),
              ],
            ),
            if (payment.commissionAmount > 0) ...[
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('Commission (${(payment.commissionRate * 100).toStringAsFixed(1)}%):'),
                  Text(
                    '\$${payment.commissionAmount.toStringAsFixed(2)}',
                    style: const TextStyle(color: Colors.red),
                  ),
                ],
              ),
              const Divider(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Net Amount:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '\$${payment.netAmount.toStringAsFixed(2)}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentInfoCard(BuildContext context, PaymentModel payment) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Payment Method', payment.method.displayName),
            _buildInfoRow('Payment Type', payment.type.displayName),
            if (payment.dueDate != null)
              _buildInfoRow('Due Date', DateFormatter.formatDate(payment.dueDate!)),
          ],
        ),
      ),
    );
  }

  Widget _buildParticipantsCard(BuildContext context, PaymentModel payment) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Participants',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            if (payment.payerName != null)
              _buildInfoRow('Payer', payment.payerName!),
            if (payment.payeeName != null)
              _buildInfoRow('Payee', payment.payeeName!),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadInfoCard(BuildContext context, PaymentModel payment) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Load Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            if (payment.loadTitle != null)
              _buildInfoRow('Load Title', payment.loadTitle!),
            _buildInfoRow('Load ID', payment.loadId.toString()),
            if (payment.bidId != null)
              _buildInfoRow('Bid ID', payment.bidId.toString()),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  context.go('/loads/${payment.loadId}');
                },
                icon: const Icon(Icons.visibility),
                label: const Text('View Load Details'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionCard(BuildContext context, PaymentModel payment) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Transaction Details',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            if (payment.transactionId != null)
              _buildInfoRow('Transaction ID', payment.transactionId!),
            if (payment.paymentGatewayReference != null)
              _buildInfoRow('Gateway Reference', payment.paymentGatewayReference!),
            if (payment.paidAt != null)
              _buildInfoRow('Paid At', DateFormatter.formatDateTime(payment.paidAt!)),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard(BuildContext context, PaymentModel payment) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notes & Description',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            if (payment.description != null) ...[
              Text(
                'Description:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                payment.description!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
            if (payment.notes != null) ...[
              if (payment.description != null) const SizedBox(height: 12),
              Text(
                'Notes:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                payment.notes!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTimestampsCard(BuildContext context, PaymentModel payment) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Timestamps',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 12),
            if (payment.createdAt != null)
              _buildInfoRow('Created', DateFormatter.formatDateTime(payment.createdAt!)),
            if (payment.updatedAt != null)
              _buildInfoRow('Last Updated', DateFormatter.formatDateTime(payment.updatedAt!)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  void _showCancelDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Payment'),
        content: Text(
          'Are you sure you want to cancel payment #${widget.paymentId}?\n\n'
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<PaymentBloc>().add(CancelPayment(widget.paymentId));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Payment'),
        content: Text(
          'Are you sure you want to delete payment #${widget.paymentId}?\n\n'
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<PaymentBloc>().add(DeletePayment(widget.paymentId));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Yes, Delete'),
          ),
        ],
      ),
    );
  }
}
