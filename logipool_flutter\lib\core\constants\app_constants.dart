class AppConstants {
  // API Configuration
  static const String baseUrlNoApi = 'https://api-logistics.kanjan.co.zw';
  static const String baseUrl = '$baseUrlNoApi/api';
  static const String wsUrl = 'ws://api-logistics.kanjan.co.zw/ws';
  
  // API Endpoints
  static const String authEndpoint = '/auth';
  static const String loadsEndpoint = '/loads';
  static const String bidsEndpoint = '/bids';
  static const String companiesEndpoint = '/companies';
  static const String trackingEndpoint = '/tracking';
  static const String documentsEndpoint = '/documents';
  static const String paymentsEndpoint = '/payments';
  static const String notificationsEndpoint = '/notifications';
  static const String adminEndpoint = '/admin';
  
  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userKey = 'user_data';
  static const String settingsKey = 'app_settings';
  
  // App Configuration
  static const String appName = 'LogiPool';
  static const String appVersion = '1.0.0';
  static const int apiTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;
  
  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = [
    'jpg', 'jpeg', 'png', 'gif', 'webp'
  ];
  static const List<String> allowedDocumentTypes = [
    'pdf', 'doc', 'docx', 'txt'
  ];
  
  // Map Configuration
  static const double defaultLatitude = -17.8292;
  static const double defaultLongitude = 31.0522; // Harare, Zimbabwe
  static const double defaultZoom = 12.0;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Validation
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 50;
  
  // Business Rules
  static const double commissionRate = 0.075; // 7.5%
  static const double minCommission = 5.0;
  static const double maxCommission = 500.0;
  
  // Notification Types
  static const String notificationTypeLoad = 'LOAD';
  static const String notificationTypeBid = 'BID';
  static const String notificationTypePayment = 'PAYMENT';
  static const String notificationTypeTracking = 'TRACKING';
  static const String notificationTypeSystem = 'SYSTEM';
  
  // User Roles
  static const String roleAdmin = 'ADMIN';
  static const String roleClient = 'CLIENT';
  static const String roleTransporter = 'TRANSPORTER';
  
  // Load Status
  static const String loadStatusPosted = 'POSTED';
  static const String loadStatusAssigned = 'ASSIGNED';
  static const String loadStatusInTransit = 'IN_TRANSIT';
  static const String loadStatusDelivered = 'DELIVERED';
  static const String loadStatusCancelled = 'CANCELLED';
  static const String loadStatusBiddingClosed = 'BIDDING_CLOSED';
  
  // Bid Status
  static const String bidStatusPending = 'PENDING';
  static const String bidStatusAccepted = 'ACCEPTED';
  static const String bidStatusRejected = 'REJECTED';
  static const String bidStatusWithdrawn = 'WITHDRAWN';
  
  // Payment Status
  static const String paymentStatusPending = 'PENDING';
  static const String paymentStatusProcessing = 'PROCESSING';
  static const String paymentStatusCompleted = 'COMPLETED';
  static const String paymentStatusFailed = 'FAILED';
  static const String paymentStatusCancelled = 'CANCELLED';
  static const String paymentStatusRefunded = 'REFUNDED';
  static const String paymentStatusDisputed = 'DISPUTED';
  
  // Company Verification Status
  static const String verificationStatusPending = 'PENDING';
  static const String verificationStatusVerified = 'VERIFIED';
  static const String verificationStatusRejected = 'REJECTED';
  static const String verificationStatusSuspended = 'SUSPENDED';
  
  // Document Status
  static const String documentStatusPending = 'PENDING';
  static const String documentStatusVerified = 'VERIFIED';
  static const String documentStatusRejected = 'REJECTED';
  static const String documentStatusExpired = 'EXPIRED';
  
  // Tracking Status
  static const String trackingStatusLoadPosted = 'LOAD_POSTED';
  static const String trackingStatusBidAccepted = 'BID_ACCEPTED';
  static const String trackingStatusPickupScheduled = 'PICKUP_SCHEDULED';
  static const String trackingStatusInTransitToPickup = 'IN_TRANSIT_TO_PICKUP';
  static const String trackingStatusArrivedAtPickup = 'ARRIVED_AT_PICKUP';
  static const String trackingStatusLoadingInProgress = 'LOADING_IN_PROGRESS';
  static const String trackingStatusLoaded = 'LOADED';
  static const String trackingStatusInTransitToDelivery = 'IN_TRANSIT_TO_DELIVERY';
  static const String trackingStatusArrivedAtDelivery = 'ARRIVED_AT_DELIVERY';
  static const String trackingStatusUnloadingInProgress = 'UNLOADING_IN_PROGRESS';
  static const String trackingStatusDelivered = 'DELIVERED';
  static const String trackingStatusDelayed = 'DELAYED';
  static const String trackingStatusIssueReported = 'ISSUE_REPORTED';
  
  // Error Messages
  static const String errorNetworkConnection = 'Network connection error';
  static const String errorServerError = 'Server error occurred';
  static const String errorUnauthorized = 'Unauthorized access';
  static const String errorNotFound = 'Resource not found';
  static const String errorValidation = 'Validation error';
  static const String errorUnknown = 'Unknown error occurred';
  
  // Success Messages
  static const String successLogin = 'Login successful';
  static const String successRegistration = 'Registration successful';
  static const String successLogout = 'Logout successful';
  static const String successUpdate = 'Update successful';
  static const String successDelete = 'Delete successful';
  static const String successCreate = 'Created successfully';

  // Contact Information
  static const String companyPhoneNumber = '+263 4 123 4567';
  static const String companyWhatsAppNumber = '+263 77 123 4567';
  static const String companyEmail = '<EMAIL>';
  static const String companySupportEmail = '<EMAIL>';
  static const String companyLegalEmail = '<EMAIL>';
  static const String companyAddress = '123 Business District\nHarare, Zimbabwe';
  static const String companyBusinessHours = 'Mon - Fri: 8:00 AM - 6:00 PM\nSat: 9:00 AM - 2:00 PM';
  static const String companyWebsiteUrl = 'https://logipool.com';

  // Live Chat Configuration
  static const bool liveChatEnabled = false; // Disabled as requested
  static const String tawkToUrl = 'https://tawk.to/chat/your-tawk-to-id/default'; // Kept for future use

  // Helper methods for contact information
  static String get formattedPhoneNumber => companyPhoneNumber;
  static String get formattedWhatsAppNumber => companyWhatsAppNumber;
  static String get phoneNumberForDialing => companyPhoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
  static String get whatsAppNumberForDialing => companyWhatsAppNumber.replaceAll(RegExp(r'[^\d+]'), '');
}
