import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/models/notification_model.dart';
import '../../../shared/models/pagination_model.dart';
import '../../../shared/services/notification_api_service.dart';

// Events
abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object?> get props => [];
}

class LoadNotifications extends NotificationEvent {
  const LoadNotifications();
}

class LoadMoreNotifications extends NotificationEvent {
  const LoadMoreNotifications();
}

class RefreshNotifications extends NotificationEvent {
  const RefreshNotifications();
}

class MarkNotificationAsRead extends NotificationEvent {
  final int notificationId;

  const MarkNotificationAsRead(this.notificationId);

  @override
  List<Object> get props => [notificationId];
}

class MarkNotificationsAsRead extends NotificationEvent {
  final List<int> notificationIds;

  const MarkNotificationsAsRead(this.notificationIds);

  @override
  List<Object> get props => [notificationIds];
}

class MarkAllNotificationsAsRead extends NotificationEvent {
  const MarkAllNotificationsAsRead();
}

class DeleteNotification extends NotificationEvent {
  final int notificationId;

  const DeleteNotification(this.notificationId);

  @override
  List<Object> get props => [notificationId];
}

class DeleteNotifications extends NotificationEvent {
  final List<int> notificationIds;

  const DeleteNotifications(this.notificationIds);

  @override
  List<Object> get props => [notificationIds];
}

class LoadNotificationsByType extends NotificationEvent {
  final NotificationType type;

  const LoadNotificationsByType(this.type);

  @override
  List<Object> get props => [type];
}

class LoadNotificationSummary extends NotificationEvent {
  const LoadNotificationSummary();
}

class LoadNotificationSettings extends NotificationEvent {
  const LoadNotificationSettings();
}

class UpdateNotificationSettings extends NotificationEvent {
  final NotificationSettings settings;

  const UpdateNotificationSettings(this.settings);

  @override
  List<Object> get props => [settings];
}

class SearchNotifications extends NotificationEvent {
  final String query;
  final NotificationType? type;
  final NotificationPriority? priority;
  final bool? read;
  final DateTime? startDate;
  final DateTime? endDate;

  const SearchNotifications({
    required this.query,
    this.type,
    this.priority,
    this.read,
    this.startDate,
    this.endDate,
  });

  @override
  List<Object?> get props => [query, type, priority, read, startDate, endDate];
}

class LoadUnreadCount extends NotificationEvent {
  const LoadUnreadCount();
}

class SendTestNotification extends NotificationEvent {
  const SendTestNotification();
}

class ClearExpiredNotifications extends NotificationEvent {
  const ClearExpiredNotifications();
}

// States
abstract class NotificationState extends Equatable {
  const NotificationState();

  @override
  List<Object?> get props => [];
}

class NotificationInitial extends NotificationState {}

class NotificationLoading extends NotificationState {}

class NotificationLoaded extends NotificationState {
  final List<NotificationModel> notifications;
  final bool hasReachedMax;
  final bool isLoadingMore;
  final int currentPage;

  const NotificationLoaded({
    required this.notifications,
    this.hasReachedMax = false,
    this.isLoadingMore = false,
    this.currentPage = 0,
  });

  @override
  List<Object> get props => [notifications, hasReachedMax, isLoadingMore, currentPage];

  NotificationLoaded copyWith({
    List<NotificationModel>? notifications,
    bool? hasReachedMax,
    bool? isLoadingMore,
    int? currentPage,
  }) {
    return NotificationLoaded(
      notifications: notifications ?? this.notifications,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

class NotificationsByTypeLoaded extends NotificationState {
  final List<NotificationModel> notifications;
  final NotificationType type;

  const NotificationsByTypeLoaded({
    required this.notifications,
    required this.type,
  });

  @override
  List<Object> get props => [notifications, type];
}

class NotificationSummaryLoaded extends NotificationState {
  final NotificationSummary summary;

  const NotificationSummaryLoaded(this.summary);

  @override
  List<Object> get props => [summary];
}

class NotificationSettingsLoaded extends NotificationState {
  final NotificationSettings settings;

  const NotificationSettingsLoaded(this.settings);

  @override
  List<Object> get props => [settings];
}

class UnreadCountLoaded extends NotificationState {
  final int count;

  const UnreadCountLoaded(this.count);

  @override
  List<Object> get props => [count];
}

class NotificationOperationSuccess extends NotificationState {
  final String message;

  const NotificationOperationSuccess(this.message);

  @override
  List<Object> get props => [message];
}

class NotificationError extends NotificationState {
  final String message;

  const NotificationError(this.message);

  @override
  List<Object> get props => [message];
}

// BLoC
class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final NotificationApiService _notificationService;

  NotificationBloc(this._notificationService) : super(NotificationInitial()) {
    on<LoadNotifications>(_onLoadNotifications);
    on<LoadMoreNotifications>(_onLoadMoreNotifications);
    on<RefreshNotifications>(_onRefreshNotifications);
    on<MarkNotificationAsRead>(_onMarkNotificationAsRead);
    on<MarkNotificationsAsRead>(_onMarkNotificationsAsRead);
    on<MarkAllNotificationsAsRead>(_onMarkAllNotificationsAsRead);
    on<DeleteNotification>(_onDeleteNotification);
    on<DeleteNotifications>(_onDeleteNotifications);
    on<LoadNotificationsByType>(_onLoadNotificationsByType);
    on<LoadNotificationSummary>(_onLoadNotificationSummary);
    on<LoadNotificationSettings>(_onLoadNotificationSettings);
    on<UpdateNotificationSettings>(_onUpdateNotificationSettings);
    on<SearchNotifications>(_onSearchNotifications);
    on<LoadUnreadCount>(_onLoadUnreadCount);
    on<SendTestNotification>(_onSendTestNotification);
    on<ClearExpiredNotifications>(_onClearExpiredNotifications);
  }

  Future<void> _onLoadNotifications(
    LoadNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(NotificationLoading());
      
      final response = await _notificationService.getNotifications(
        page: 0,
        size: 20,
      );

      emit(NotificationLoaded(
        notifications: response.content,
        hasReachedMax: response.last,
        currentPage: 0,
      ));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onLoadMoreNotifications(
    LoadMoreNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    final currentState = state;
    if (currentState is! NotificationLoaded || currentState.hasReachedMax) {
      return;
    }

    try {
      emit(currentState.copyWith(isLoadingMore: true));

      final nextPage = currentState.currentPage + 1;
      final response = await _notificationService.getNotifications(
        page: nextPage,
        size: 20,
      );

      final updatedNotifications = List<NotificationModel>.from(currentState.notifications)
        ..addAll(response.content);

      emit(NotificationLoaded(
        notifications: updatedNotifications,
        hasReachedMax: response.last,
        currentPage: nextPage,
        isLoadingMore: false,
      ));
    } catch (e) {
      emit(currentState.copyWith(isLoadingMore: false));
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onRefreshNotifications(
    RefreshNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    add(const LoadNotifications());
  }

  Future<void> _onMarkNotificationAsRead(
    MarkNotificationAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.markSingleAsRead(event.notificationId);
      
      // Update local state
      final currentState = state;
      if (currentState is NotificationLoaded) {
        final updatedNotifications = currentState.notifications.map((notification) {
          if (notification.id == event.notificationId) {
            return notification.copyWith(read: true, readAt: DateTime.now());
          }
          return notification;
        }).toList();

        emit(currentState.copyWith(notifications: updatedNotifications));
      }

      emit(const NotificationOperationSuccess('Notification marked as read'));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onMarkNotificationsAsRead(
    MarkNotificationsAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.markAsRead(event.notificationIds);

      // Update local state
      final currentState = state;
      if (currentState is NotificationLoaded) {
        final updatedNotifications = currentState.notifications.map((notification) {
          if (event.notificationIds.contains(notification.id)) {
            return notification.copyWith(read: true, readAt: DateTime.now());
          }
          return notification;
        }).toList();

        emit(currentState.copyWith(notifications: updatedNotifications));
      }

      emit(const NotificationOperationSuccess('Notifications marked as read'));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onMarkAllNotificationsAsRead(
    MarkAllNotificationsAsRead event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.markAllAsRead();

      // Update local state
      final currentState = state;
      if (currentState is NotificationLoaded) {
        final updatedNotifications = currentState.notifications.map((notification) {
          return notification.copyWith(read: true, readAt: DateTime.now());
        }).toList();

        emit(currentState.copyWith(notifications: updatedNotifications));
      }

      emit(const NotificationOperationSuccess('All notifications marked as read'));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onDeleteNotification(
    DeleteNotification event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.deleteSingleNotification(event.notificationId);

      // Update local state
      final currentState = state;
      if (currentState is NotificationLoaded) {
        final updatedNotifications = currentState.notifications
            .where((notification) => notification.id != event.notificationId)
            .toList();

        emit(currentState.copyWith(notifications: updatedNotifications));
      }

      emit(const NotificationOperationSuccess('Notification deleted'));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onDeleteNotifications(
    DeleteNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.deleteNotifications(event.notificationIds);

      // Update local state
      final currentState = state;
      if (currentState is NotificationLoaded) {
        final updatedNotifications = currentState.notifications
            .where((notification) => !event.notificationIds.contains(notification.id))
            .toList();

        emit(currentState.copyWith(notifications: updatedNotifications));
      }

      emit(const NotificationOperationSuccess('Notifications deleted'));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onLoadNotificationsByType(
    LoadNotificationsByType event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(NotificationLoading());

      final notifications = await _notificationService.getNotificationsByType(event.type);

      emit(NotificationsByTypeLoaded(
        notifications: notifications,
        type: event.type,
      ));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onLoadNotificationSummary(
    LoadNotificationSummary event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final summary = await _notificationService.getNotificationSummary();
      emit(NotificationSummaryLoaded(summary));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onLoadNotificationSettings(
    LoadNotificationSettings event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final settings = await _notificationService.getNotificationSettings();
      emit(NotificationSettingsLoaded(settings));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onUpdateNotificationSettings(
    UpdateNotificationSettings event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.updateNotificationSettings(event.settings);
      emit(NotificationSettingsLoaded(event.settings));
      emit(const NotificationOperationSuccess('Settings updated successfully'));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onSearchNotifications(
    SearchNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      emit(NotificationLoading());

      final response = await _notificationService.searchNotifications(
        query: event.query,
        type: event.type,
        priority: event.priority,
        read: event.read,
        startDate: event.startDate,
        endDate: event.endDate,
      );

      emit(NotificationLoaded(
        notifications: response.content,
        hasReachedMax: response.last,
        currentPage: 0,
      ));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onLoadUnreadCount(
    LoadUnreadCount event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      final count = await _notificationService.getUnreadCount();
      emit(UnreadCountLoaded(count));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onSendTestNotification(
    SendTestNotification event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.sendTestNotification();
      emit(const NotificationOperationSuccess('Test notification sent'));
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }

  Future<void> _onClearExpiredNotifications(
    ClearExpiredNotifications event,
    Emitter<NotificationState> emit,
  ) async {
    try {
      await _notificationService.clearExpiredNotifications();
      emit(const NotificationOperationSuccess('Expired notifications cleared'));
      add(const LoadNotifications()); // Refresh the list
    } catch (e) {
      emit(NotificationError(e.toString()));
    }
  }
}
