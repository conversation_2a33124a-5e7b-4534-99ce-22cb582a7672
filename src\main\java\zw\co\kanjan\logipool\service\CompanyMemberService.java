package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.company.CompanyMemberDto;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.CompanyMember;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.CompanyMemberMapper;
import zw.co.kanjan.logipool.repository.CompanyMemberRepository;
import zw.co.kanjan.logipool.repository.CompanyRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CompanyMemberService {
    
    private final CompanyMemberRepository companyMemberRepository;
    private final CompanyRepository companyRepository;
    private final UserRepository userRepository;
    private final CompanyMemberMapper companyMemberMapper;
    private final NotificationService notificationService;
    
    public CompanyMemberDto.MemberResponse inviteUser(Long companyId, CompanyMemberDto.InviteRequest request, String inviterUsername) {
        log.info("Inviting user {} to company {} by {}", request.getEmail(), companyId, inviterUsername);
        
        // Get the company
        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));
        
        // Get the inviter
        User inviter = userRepository.findByUsername(inviterUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Inviter not found"));
        
        // Validate inviter has permission to invite users
        validateInvitePermission(company, inviter);
        
        // Find the user to invite
        User userToInvite = userRepository.findByEmail(request.getEmail())
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + request.getEmail()));
        
        // Check if user is already a member
        if (companyMemberRepository.existsByCompanyAndUser(company, userToInvite)) {
            throw new BusinessException("User is already a member of this company");
        }
        
        // Create company member
        CompanyMember member = companyMemberMapper.toEntity(request);
        member.setCompany(company);
        member.setUser(userToInvite);
        member.setStatus(CompanyMember.MemberStatus.PENDING);
        member.setInvitedBy(inviter);
        member.setInvitedAt(LocalDateTime.now());
        
        // Set default permissions based on role
        setDefaultPermissions(member, request.getRole());
        
        // Override with custom permissions if provided
        if (request.getCanManageMembers() != null) member.setCanManageMembers(request.getCanManageMembers());
        if (request.getCanManageLoads() != null) member.setCanManageLoads(request.getCanManageLoads());
        if (request.getCanUpdateLoadStatus() != null) member.setCanUpdateLoadStatus(request.getCanUpdateLoadStatus());
        if (request.getCanUploadDocuments() != null) member.setCanUploadDocuments(request.getCanUploadDocuments());
        if (request.getCanGenerateInvoices() != null) member.setCanGenerateInvoices(request.getCanGenerateInvoices());
        if (request.getCanViewFinancials() != null) member.setCanViewFinancials(request.getCanViewFinancials());
        if (request.getCanTrackLocation() != null) member.setCanTrackLocation(request.getCanTrackLocation());
        
        CompanyMember savedMember = companyMemberRepository.save(member);
        
        // Send invitation notification
        sendInvitationNotification(savedMember, request.getInvitationMessage());
        
        log.info("User {} invited to company {} successfully", request.getEmail(), companyId);
        return companyMemberMapper.toResponse(savedMember);
    }
    
    public CompanyMemberDto.MemberResponse acceptInvitation(CompanyMemberDto.AcceptInvitationRequest request, String username) {
        log.info("User {} accepting invitation for membership {}", username, request.getMemberId());
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        CompanyMember member = companyMemberRepository.findById(request.getMemberId())
                .orElseThrow(() -> new ResourceNotFoundException("Membership not found"));
        
        // Validate user is the invited user
        if (!member.getUser().getId().equals(user.getId())) {
            throw new BusinessException("You can only accept your own invitations");
        }
        
        // Validate invitation is pending
        if (member.getStatus() != CompanyMember.MemberStatus.PENDING) {
            throw new BusinessException("Invitation is not pending");
        }
        
        if (request.getAccept()) {
            member.setStatus(CompanyMember.MemberStatus.ACTIVE);
            member.setJoinedAt(LocalDateTime.now());
            log.info("User {} accepted invitation to company {}", username, member.getCompany().getName());
        } else {
            companyMemberRepository.delete(member);
            log.info("User {} declined invitation to company {}", username, member.getCompany().getName());
            throw new BusinessException("Invitation declined");
        }
        
        CompanyMember savedMember = companyMemberRepository.save(member);
        
        // Send notification to company owners/managers
        notifyCompanyOfAcceptance(savedMember);
        
        return companyMemberMapper.toResponse(savedMember);
    }
    
    @Transactional(readOnly = true)
    public CompanyMemberDto.MemberListResponse getCompanyMembers(Long companyId, String username) {
        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate user has permission to view members
        validateViewMembersPermission(company, user);
        
        List<CompanyMember> members = companyMemberRepository.findByCompany(company);
        List<CompanyMemberDto.MemberResponse> memberResponses = members.stream()
                .map(companyMemberMapper::toResponse)
                .collect(Collectors.toList());
        
        Long totalMembers = (long) members.size();
        Long activeMembers = members.stream()
                .filter(m -> m.getStatus() == CompanyMember.MemberStatus.ACTIVE)
                .count();
        Long pendingInvitations = members.stream()
                .filter(m -> m.getStatus() == CompanyMember.MemberStatus.PENDING)
                .count();
        
        return CompanyMemberDto.MemberListResponse.builder()
                .members(memberResponses)
                .totalMembers(totalMembers)
                .activeMembers(activeMembers)
                .pendingInvitations(pendingInvitations)
                .build();
    }
    
    public CompanyMemberDto.MemberResponse updateMember(CompanyMemberDto.UpdateRequest request, String updaterUsername) {
        log.info("Updating member {} by {}", request.getMemberId(), updaterUsername);
        
        CompanyMember member = companyMemberRepository.findById(request.getMemberId())
                .orElseThrow(() -> new ResourceNotFoundException("Member not found"));
        
        User updater = userRepository.findByUsername(updaterUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Updater not found"));
        
        // Validate updater has permission
        validateUpdateMemberPermission(member.getCompany(), updater, member);
        
        // Update member
        companyMemberMapper.updateMemberFromRequest(request, member);
        
        // If role is being changed, update default permissions
        if (request.getRole() != null && request.getRole() != member.getRole()) {
            setDefaultPermissions(member, request.getRole());
        }
        
        CompanyMember savedMember = companyMemberRepository.save(member);
        
        log.info("Member {} updated successfully", request.getMemberId());
        return companyMemberMapper.toResponse(savedMember);
    }
    
    public void removeMember(Long memberId, String removerUsername) {
        log.info("Removing member {} by {}", memberId, removerUsername);
        
        CompanyMember member = companyMemberRepository.findById(memberId)
                .orElseThrow(() -> new ResourceNotFoundException("Member not found"));
        
        User remover = userRepository.findByUsername(removerUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Remover not found"));
        
        // Validate remover has permission
        validateRemoveMemberPermission(member.getCompany(), remover, member);
        
        // Cannot remove the last owner
        if (member.getRole() == CompanyMember.CompanyRole.OWNER) {
            Long ownerCount = companyMemberRepository.countActiveOwners(member.getCompany());
            if (ownerCount <= 1) {
                throw new BusinessException("Cannot remove the last owner of the company");
            }
        }
        
        companyMemberRepository.delete(member);
        
        // Send notification to removed user
        notifyUserOfRemoval(member);
        
        log.info("Member {} removed successfully", memberId);
    }
    
    // Private helper methods
    private void validateInvitePermission(Company company, User inviter) {
        CompanyMember inviterMember = companyMemberRepository.findByCompanyAndUser(company, inviter)
                .orElse(null);
        
        // Check if user is company owner (original owner relationship)
        if (company.getUser().getId().equals(inviter.getId())) {
            return;
        }
        
        // Check if user is a member with invite permissions
        if (inviterMember != null && 
            inviterMember.getStatus() == CompanyMember.MemberStatus.ACTIVE &&
            (inviterMember.getCanManageMembers() || 
             inviterMember.getRole() == CompanyMember.CompanyRole.OWNER ||
             inviterMember.getRole() == CompanyMember.CompanyRole.MANAGER)) {
            return;
        }
        
        throw new BusinessException("You don't have permission to invite users to this company");
    }
    
    private void validateViewMembersPermission(Company company, User user) {
        CompanyMember member = companyMemberRepository.findByCompanyAndUser(company, user)
                .orElse(null);
        
        // Check if user is company owner
        if (company.getUser().getId().equals(user.getId())) {
            return;
        }
        
        // Check if user is an active member
        if (member != null && member.getStatus() == CompanyMember.MemberStatus.ACTIVE) {
            return;
        }
        
        throw new BusinessException("You don't have permission to view members of this company");
    }
    
    private void validateUpdateMemberPermission(Company company, User updater, CompanyMember memberToUpdate) {
        // Company owner can update anyone
        if (company.getUser().getId().equals(updater.getId())) {
            return;
        }
        
        CompanyMember updaterMember = companyMemberRepository.findByCompanyAndUser(company, updater)
                .orElse(null);
        
        if (updaterMember == null || updaterMember.getStatus() != CompanyMember.MemberStatus.ACTIVE) {
            throw new BusinessException("You don't have permission to update members");
        }
        
        // Owners and managers with manage members permission can update
        if (updaterMember.getRole() == CompanyMember.CompanyRole.OWNER ||
            (updaterMember.getRole() == CompanyMember.CompanyRole.MANAGER && updaterMember.getCanManageMembers())) {
            
            // Cannot update another owner unless you're the company owner
            if (memberToUpdate.getRole() == CompanyMember.CompanyRole.OWNER) {
                throw new BusinessException("Only the company owner can update other owners");
            }
            return;
        }
        
        throw new BusinessException("You don't have permission to update members");
    }
    
    private void validateRemoveMemberPermission(Company company, User remover, CompanyMember memberToRemove) {
        // Company owner can remove anyone except themselves if they're the last owner
        if (company.getUser().getId().equals(remover.getId())) {
            return;
        }
        
        CompanyMember removerMember = companyMemberRepository.findByCompanyAndUser(company, remover)
                .orElse(null);
        
        if (removerMember == null || removerMember.getStatus() != CompanyMember.MemberStatus.ACTIVE) {
            throw new BusinessException("You don't have permission to remove members");
        }
        
        // Owners and managers with manage members permission can remove
        if (removerMember.getRole() == CompanyMember.CompanyRole.OWNER ||
            (removerMember.getRole() == CompanyMember.CompanyRole.MANAGER && removerMember.getCanManageMembers())) {
            
            // Cannot remove another owner unless you're the company owner
            if (memberToRemove.getRole() == CompanyMember.CompanyRole.OWNER) {
                throw new BusinessException("Only the company owner can remove other owners");
            }
            return;
        }
        
        throw new BusinessException("You don't have permission to remove members");
    }
    
    private void setDefaultPermissions(CompanyMember member, CompanyMember.CompanyRole role) {
        switch (role) {
            case OWNER:
                member.setCanManageMembers(true);
                member.setCanManageLoads(true);
                member.setCanUpdateLoadStatus(true);
                member.setCanUploadDocuments(true);
                member.setCanGenerateInvoices(true);
                member.setCanViewFinancials(true);
                member.setCanTrackLocation(false);
                break;
            case MANAGER:
                member.setCanManageMembers(true);
                member.setCanManageLoads(true);
                member.setCanUpdateLoadStatus(true);
                member.setCanUploadDocuments(true);
                member.setCanGenerateInvoices(true);
                member.setCanViewFinancials(true);
                member.setCanTrackLocation(false);
                break;
            case DISPATCHER:
                member.setCanManageMembers(false);
                member.setCanManageLoads(true);
                member.setCanUpdateLoadStatus(true);
                member.setCanUploadDocuments(true);
                member.setCanGenerateInvoices(false);
                member.setCanViewFinancials(false);
                member.setCanTrackLocation(false);
                break;
            case DRIVER:
                member.setCanManageMembers(false);
                member.setCanManageLoads(false);
                member.setCanUpdateLoadStatus(true);
                member.setCanUploadDocuments(true);
                member.setCanGenerateInvoices(false);
                member.setCanViewFinancials(false);
                member.setCanTrackLocation(true);
                break;
            case ACCOUNTANT:
                member.setCanManageMembers(false);
                member.setCanManageLoads(false);
                member.setCanUpdateLoadStatus(false);
                member.setCanUploadDocuments(true);
                member.setCanGenerateInvoices(true);
                member.setCanViewFinancials(true);
                member.setCanTrackLocation(false);
                break;
            case VIEWER:
                member.setCanManageMembers(false);
                member.setCanManageLoads(false);
                member.setCanUpdateLoadStatus(false);
                member.setCanUploadDocuments(false);
                member.setCanGenerateInvoices(false);
                member.setCanViewFinancials(false);
                member.setCanTrackLocation(false);
                break;
        }
    }
    
    private void sendInvitationNotification(CompanyMember member, String message) {
        // Implementation for sending invitation notification
        // This would integrate with your notification service
        log.info("Sending invitation notification to {}", member.getUser().getEmail());
    }
    
    private void notifyCompanyOfAcceptance(CompanyMember member) {
        // Implementation for notifying company of acceptance
        log.info("Notifying company {} of member acceptance", member.getCompany().getName());
    }
    
    private void notifyUserOfRemoval(CompanyMember member) {
        // Implementation for notifying user of removal
        log.info("Notifying user {} of removal from company", member.getUser().getEmail());
    }
}
