package zw.co.kanjan.logipool.dto.load;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.Load;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Request to update load status")
public class LoadStatusUpdateRequest {
    
    @NotNull(message = "Load ID is required")
    @Schema(description = "ID of the load to update", example = "1")
    private Long loadId;
    
    @NotNull(message = "Status is required")
    @Schema(description = "New status for the load", example = "IN_TRANSIT")
    private Load.LoadStatus status;
    
    @Size(max = 1000, message = "Notes should not exceed 1000 characters")
    @Schema(description = "Optional notes about the status update", 
            example = "Load picked up from warehouse. Driver: <PERSON>. Vehicle: ABC-123")
    private String notes;
    
    @Schema(description = "Current location when updating status", example = "Harare CBD")
    private String currentLocation;
    
    @Schema(description = "Latitude of current location", example = "-17.8292")
    private Double latitude;
    
    @Schema(description = "Longitude of current location", example = "31.0522")
    private Double longitude;
    
    @Schema(description = "Estimated time of arrival (for in-transit status)", example = "2024-01-20T15:30:00")
    private String estimatedArrival;
    
    @Schema(description = "Reason for status change (especially for cancellations)", 
            example = "Client requested cancellation")
    private String reason;
}
