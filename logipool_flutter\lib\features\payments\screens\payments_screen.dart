import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/payment_model.dart';
import '../../../shared/widgets/unified_header.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../../../core/utils/app_router.dart';
import '../bloc/payment_bloc.dart';
import '../widgets/payment_card.dart';
import '../widgets/payment_filter_sheet.dart';

class PaymentsScreen extends StatefulWidget {
  const PaymentsScreen({super.key});

  @override
  State<PaymentsScreen> createState() => _PaymentsScreenState();
}

class _PaymentsScreenState extends State<PaymentsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  PaymentStatus? _selectedStatus;
  DateTime? _startDate;
  DateTime? _endDate;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadPayments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadPayments() {
    context.read<PaymentBloc>().add(LoadPayments(
          refresh: true,
          status: _selectedStatus,
          startDate: _startDate,
          endDate: _endDate,
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedHeader(
        title: 'Payments',
        actions: [
          IconButton(
            icon: Icon(
              Icons.filter_list,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            onPressed: _showFilterSheet,
            tooltip: 'Filter Payments',
          ),
          IconButton(
            icon: Icon(
              Icons.search,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            onPressed: _showSearchDialog,
            tooltip: 'Search Payments',
          ),
        ],
      ),
      body: Column(
        children: [
          // Filters positioned just above content
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).dividerColor,
                  width: 0.5,
                ),
              ),
            ),
            child: Column(
              children: [
                _buildTabBar(),
                _buildSearchBar(),
              ],
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPaymentsList(null), // All payments
                _buildPaymentsList(PaymentStatus.pending),
                _buildPaymentsList(PaymentStatus.completed),
                _buildPaymentsList(PaymentStatus.failed),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.go('/payments/create');
        },
        tooltip: 'Create New Payment',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(text: 'All'),
          Tab(text: 'Pending'),
          Tab(text: 'Completed'),
          Tab(text: 'Failed'),
        ],
        onTap: (index) {
          PaymentStatus? status;
          switch (index) {
            case 1:
              status = PaymentStatus.pending;
              break;
            case 2:
              status = PaymentStatus.completed;
              break;
            case 3:
              status = PaymentStatus.failed;
              break;
          }
          setState(() {
            _selectedStatus = status;
          });
          _loadPayments();
        },
      ),
    );
  }

  Widget _buildSearchBar() {
    if (_searchQuery.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search payments...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              setState(() {
                _searchQuery = '';
              });
            },
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildPaymentsList(PaymentStatus? status) {
    return BlocBuilder<PaymentBloc, PaymentState>(
      builder: (context, state) {
        if (state is PaymentLoading) {
          return const LoadingWidget();
        }

        if (state is PaymentError) {
          return CustomErrorWidget(
            message: state.message,
            onRetry: () => _loadPayments(),
          );
        }

        if (state is PaymentLoaded) {
          final payments = _filterPayments(state.payments, status);

          if (payments.isEmpty) {
            return EmptyStateWidget(
              icon: const Icon(Icons.payment),
              title: 'No Payments Found',
              message: _getEmptyMessage(status),
            );
          }

          return RefreshIndicator(
            onRefresh: () async => _loadPayments(),
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: payments.length,
              itemBuilder: (context, index) {
                final payment = payments[index];
                return PaymentCard(
                  payment: payment,
                  onTap: () {
                    context.go('/payments/${payment.id}');
                  },
                );
              },
            ),
          );
        }

        return const EmptyStateWidget(
          icon: Icon(Icons.payment),
          title: 'No Payments',
          message: 'No payment data available',
        );
      },
    );
  }

  List<PaymentModel> _filterPayments(
      List<PaymentModel> payments, PaymentStatus? status) {
    var filtered = payments;

    if (status != null) {
      filtered = filtered.where((p) => p.status == status).toList();
    }

    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((p) =>
              p.id.toString().contains(_searchQuery.toLowerCase()) ||
              p.description
                      ?.toLowerCase()
                      .contains(_searchQuery.toLowerCase()) ==
                  true)
          .toList();
    }

    return filtered;
  }

  String _getEmptyMessage(PaymentStatus? status) {
    switch (status) {
      case PaymentStatus.pending:
        return 'No pending payments found';
      case PaymentStatus.completed:
        return 'No completed payments found';
      case PaymentStatus.failed:
        return 'No failed payments found';
      default:
        return 'No payments found. Create your first payment to get started.';
    }
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => PaymentFilterSheet(
        selectedStatus: _selectedStatus,
        startDate: _startDate,
        endDate: _endDate,
        onApplyFilter: (status, startDate, endDate) {
          setState(() {
            _selectedStatus = status;
            _startDate = startDate;
            _endDate = endDate;
          });
          Navigator.pop(context);
          _loadPayments();
        },
        onClearFilter: () {
          setState(() {
            _selectedStatus = null;
            _startDate = null;
            _endDate = null;
          });
          Navigator.pop(context);
          _loadPayments();
        },
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Payments'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'Enter payment ID or description...',
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _loadPayments();
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }
}
