# Production Configuration

# Database Configuration - PostgreSQL Production
spring.datasource.url=${DATABASE_URL:**********************************************}
spring.datasource.username=${DATABASE_USERNAME:logipool}
spring.datasource.password=${DATABASE_PASSWORD:}
spring.datasource.driver-class-name=org.postgresql.Driver

# Connection Pool Configuration
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.idle-timeout=300000
spring.datasource.hikari.max-lifetime=1200000
spring.datasource.hikari.connection-timeout=20000

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# Redis Configuration
spring.data.redis.host=${REDIS_HOST:localhost}
spring.data.redis.port=${REDIS_PORT:6379}
spring.data.redis.password=${REDIS_PASSWORD:}
spring.data.redis.timeout=60000
spring.data.redis.lettuce.pool.max-active=8
spring.data.redis.lettuce.pool.max-idle=8
spring.data.redis.lettuce.pool.min-idle=0

# JWT Configuration
app.jwt.secret=${JWT_SECRET:}
app.jwt.expiration=${JWT_EXPIRATION:86400000}
app.jwt.refresh-expiration=${JWT_REFRESH_EXPIRATION:604800000}

# Rate Limiting Configuration
app.rate-limit.enabled=true
app.rate-limit.requests-per-minute=${RATE_LIMIT_PER_MINUTE:100}
app.rate-limit.requests-per-hour=${RATE_LIMIT_PER_HOUR:5000}

# Security Configuration
app.security.password.min-length=8
app.security.password.require-uppercase=true
app.security.password.require-lowercase=true
app.security.password.require-numbers=true
app.security.password.require-special-chars=true

# File Upload Configuration
spring.servlet.multipart.max-file-size=${MAX_FILE_SIZE:50MB}
spring.servlet.multipart.max-request-size=${MAX_REQUEST_SIZE:50MB}
app.file.upload-dir=${UPLOAD_DIR:/app/uploads}
app.file.max-size=${MAX_FILE_SIZE_BYTES:52428800}
app.file.allowed-types=pdf,doc,docx,jpg,jpeg,png,gif,txt,csv,xlsx,xls

# Cloud Storage Configuration
app.cloud.storage.enabled=${CLOUD_STORAGE_ENABLED:true}
app.cloud.storage.provider=${CLOUD_STORAGE_PROVIDER:aws}
app.cloud.storage.bucket=${S3_BUCKET_NAME:logipool-documents-prod}
app.cloud.storage.region=${AWS_REGION:us-east-1}

# Mail Configuration
spring.mail.host=${MAIL_HOST:smtp.gmail.com}
spring.mail.port=${MAIL_PORT:587}
spring.mail.username=${MAIL_USERNAME:}
spring.mail.password=${MAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# API Documentation (disabled in production)
springdoc.api-docs.enabled=false
springdoc.swagger-ui.enabled=false

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics,prometheus
management.endpoint.health.show-details=never
management.endpoint.health.probes.enabled=true
management.health.livenessstate.enabled=true
management.health.readinessstate.enabled=true

# Notification Configuration
app.notification.email.enabled=${EMAIL_NOTIFICATIONS_ENABLED:true}
app.notification.sms.enabled=${SMS_NOTIFICATIONS_ENABLED:true}
app.notification.realtime.enabled=true
app.notification.persistent.enabled=true
app.base-url=${APP_BASE_URL:https://api.logipool.com}

# Admin Report Configuration
app.admin.daily-report.enabled=${ADMIN_DAILY_REPORT_ENABLED:true}

# SMS Configuration (Twilio)
app.sms.provider=${SMS_PROVIDER:twilio}
app.sms.enabled=${SMS_ENABLED:true}
app.sms.twilio.account-sid=${TWILIO_ACCOUNT_SID:}
app.sms.twilio.auth-token=${TWILIO_AUTH_TOKEN:}
app.sms.twilio.from-number=${TWILIO_FROM_NUMBER:}

# Payment Configuration
app.payment.commission.rate=${COMMISSION_RATE:0.075}
app.payment.commission.min=${MIN_COMMISSION:5.00}
app.payment.commission.max=${MAX_COMMISSION:500.00}
app.payment.gateway.stripe.enabled=${STRIPE_ENABLED:true}
app.payment.gateway.stripe.secret-key=${STRIPE_SECRET_KEY:}
app.payment.gateway.stripe.public-key=${STRIPE_PUBLIC_KEY:}
app.payment.gateway.paypal.enabled=${PAYPAL_ENABLED:false}
app.payment.gateway.mock.enabled=false

# Tracking Configuration
app.tracking.enabled=true
app.tracking.real-time.enabled=true
app.tracking.gps.enabled=true
app.tracking.stale-threshold-hours=6
app.tracking.cleanup.enabled=true
app.tracking.monitoring.enabled=true
app.tracking.location-update-interval-seconds=30
app.tracking.max-location-history=1000

# Logging Configuration
logging.level.zw.co.kanjan.logipool=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.springframework.web=WARN
logging.level.org.springframework.boot.actuate=INFO

# Server Configuration
server.port=${PORT:8080}
server.servlet.context-path=/
server.compression.enabled=true
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
server.compression.min-response-size=1024

# SSL Configuration (when using HTTPS)
server.ssl.enabled=${SSL_ENABLED:false}
server.ssl.key-store=${SSL_KEYSTORE_PATH:}
server.ssl.key-store-password=${SSL_KEYSTORE_PASSWORD:}
server.ssl.key-store-type=${SSL_KEYSTORE_TYPE:PKCS12}

# CORS Configuration
app.cors.allowed-origins=${CORS_ALLOWED_ORIGINS:https://app.logipool.com,https://admin.logipool.com}
app.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
app.cors.allowed-headers=*
app.cors.allow-credentials=true

# Cache Configuration
spring.cache.type=redis
spring.cache.redis.time-to-live=3600000
spring.cache.redis.cache-null-values=false

# Session Configuration
spring.session.store-type=redis
spring.session.redis.namespace=logipool:session
spring.session.timeout=1800

# H2 Console (disabled in production)
spring.h2.console.enabled=false
