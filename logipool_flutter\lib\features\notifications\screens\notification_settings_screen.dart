import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../shared/models/notification_model.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/services/notification_service.dart';
import '../bloc/notification_bloc.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  NotificationSettings? _settings;
  bool _isLoading = false;
  bool _permissionsGranted = false;

  @override
  void initState() {
    super.initState();
    context.read<NotificationBloc>().add(const LoadNotificationSettings());
    _checkPermissions();
  }

  Future<void> _checkPermissions() async {
    final granted = await NotificationService.instance.arePermissionsGranted();
    if (mounted) {
      setState(() {
        _permissionsGranted = granted;
      });
    }
  }

  void _updateSettings(NotificationSettings settings) {
    setState(() {
      _settings = settings;
      _isLoading = true;
    });
    
    context.read<NotificationBloc>().add(UpdateNotificationSettings(settings));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Notification Settings',
      ),
      body: BlocConsumer<NotificationBloc, NotificationState>(
        listener: (context, state) {
          if (state is NotificationError) {
            setState(() {
              _isLoading = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          } else if (state is NotificationOperationSuccess) {
            setState(() {
              _isLoading = false;
            });
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          } else if (state is NotificationSettingsLoaded) {
            setState(() {
              _settings = state.settings;
              _isLoading = false;
            });
          }
        },
        builder: (context, state) {
          if (state is NotificationLoading && _settings == null) {
            return const LoadingWidget();
          } else if (state is NotificationError && _settings == null) {
            return CustomErrorWidget(
              message: state.message,
              onRetry: () => context.read<NotificationBloc>().add(const LoadNotificationSettings()),
            );
          } else if (_settings != null) {
            return _buildSettingsContent();
          }
          return const LoadingWidget();
        },
      ),
    );
  }

  Widget _buildSettingsContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildGeneralSettings(),
          const SizedBox(height: 24),
          _buildNotificationChannels(),
          const SizedBox(height: 24),
          _buildNotificationTypes(),
          const SizedBox(height: 24),
          _buildQuietHours(),
          const SizedBox(height: 24),
          _buildAdvancedSettings(),
        ],
      ),
    );
  }

  Widget _buildGeneralSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'General Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            // Permission status and request button
            if (!_permissionsGranted) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  border: Border.all(color: Colors.orange.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.warning, color: Colors.orange.shade700, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Notifications Disabled',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'You need to allow notifications to receive important updates about your loads, bids, and account.',
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _requestPermissions,
                        icon: const Icon(Icons.notifications),
                        label: const Text('Allow Notifications'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange.shade600,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ] else ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  border: Border.all(color: Colors.green.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green.shade700, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Notifications Enabled',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade700,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            SwitchListTile(
              title: const Text('Do Not Disturb'),
              subtitle: const Text('Disable all notifications temporarily'),
              value: _settings!.doNotDisturb,
              onChanged: _isLoading ? null : (value) {
                _updateSettings(_settings!.copyWith(doNotDisturb: value));
              },
              secondary: const Icon(Icons.do_not_disturb),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationChannels() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Channels',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Push Notifications'),
              subtitle: const Text('Receive notifications on your device'),
              value: _settings!.pushNotifications,
              onChanged: _isLoading ? null : (value) {
                _updateSettings(_settings!.copyWith(pushNotifications: value));
              },
              secondary: const Icon(Icons.notifications),
            ),
            SwitchListTile(
              title: const Text('Email Notifications'),
              subtitle: const Text('Receive notifications via email'),
              value: _settings!.emailNotifications,
              onChanged: _isLoading ? null : (value) {
                _updateSettings(_settings!.copyWith(emailNotifications: value));
              },
              secondary: const Icon(Icons.email),
            ),
            SwitchListTile(
              title: const Text('SMS Notifications'),
              subtitle: const Text('Receive notifications via SMS'),
              value: _settings!.smsNotifications,
              onChanged: _isLoading ? null : (value) {
                _updateSettings(_settings!.copyWith(smsNotifications: value));
              },
              secondary: const Icon(Icons.sms),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationTypes() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Notification Types',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose which types of notifications you want to receive',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
            const SizedBox(height: 16),
            ...NotificationType.values.map((type) {
              final isEnabled = _settings!.typeSettings[type.name] ?? true;
              return SwitchListTile(
                title: Text(type.displayName),
                subtitle: Text(type.description),
                value: isEnabled,
                onChanged: _isLoading ? null : (value) {
                  final updatedTypeSettings = Map<String, bool>.from(_settings!.typeSettings);
                  updatedTypeSettings[type.name] = value;
                  _updateSettings(_settings!.copyWith(typeSettings: updatedTypeSettings));
                },
                secondary: Icon(_getTypeIcon(type)),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildQuietHours() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quiet Hours',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Set times when you don\'t want to receive notifications',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.bedtime),
              title: const Text('Configure Quiet Hours'),
              subtitle: _settings!.quietHours.isNotEmpty
                  ? Text('${_settings!.quietHours.length} time ranges set')
                  : const Text('No quiet hours set'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: _isLoading ? null : _showQuietHoursDialog,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Advanced Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.bug_report),
              title: const Text('Send Test Notification'),
              subtitle: const Text('Test your notification settings'),
              trailing: const Icon(Icons.send),
              onTap: _isLoading ? null : _sendTestNotification,
            ),
            ListTile(
              leading: const Icon(Icons.clear_all),
              title: const Text('Clear Expired Notifications'),
              subtitle: const Text('Remove old and expired notifications'),
              trailing: const Icon(Icons.delete_sweep),
              onTap: _isLoading ? null : () {
                context.read<NotificationBloc>().add(const ClearExpiredNotifications());
              },
            ),
            ListTile(
              leading: const Icon(Icons.restore),
              title: const Text('Reset to Defaults'),
              subtitle: const Text('Reset all notification settings'),
              trailing: const Icon(Icons.refresh),
              onTap: _isLoading ? null : _showResetDialog,
            ),
          ],
        ),
      ),
    );
  }

  void _showQuietHoursDialog() {
    // TODO: Implement quiet hours configuration dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Quiet Hours'),
        content: const Text('Quiet hours configuration will be implemented in a future update.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _requestPermissions() async {
    try {
      final granted = await NotificationService.instance.requestPermissions();
      if (mounted) {
        setState(() {
          _permissionsGranted = granted;
        });

        if (granted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Notifications enabled successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Notification permission denied. You can enable it later in your device settings.'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 5),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error requesting permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _sendTestNotification() async {
    try {
      // Check if permissions are granted first
      final permissionsGranted = await NotificationService.instance.arePermissionsGranted();

      if (!permissionsGranted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please allow notifications first to test them.'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      // Send a test notification
      await NotificationService.instance.showLocalNotification(
        title: 'LogiPool Test Notification',
        body: 'This is a test notification. Your notifications are working correctly!',
        payload: 'test_notification',
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Test notification sent! Check your notification panel.'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error sending test notification: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text('Are you sure you want to reset all notification settings to defaults?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _updateSettings(const NotificationSettings());
            },
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  IconData _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.loadPosted:
        return Icons.local_shipping;
      case NotificationType.bidReceived:
        return Icons.gavel;
      case NotificationType.bidAccepted:
        return Icons.check_circle;
      case NotificationType.bidRejected:
        return Icons.cancel;
      case NotificationType.loadStatusUpdate:
        return Icons.update;
      case NotificationType.paymentReceived:
        return Icons.payment;
      case NotificationType.documentRequired:
        return Icons.description;
      case NotificationType.systemAnnouncement:
        return Icons.announcement;
      case NotificationType.accountVerification:
        return Icons.verified_user;
      case NotificationType.securityAlert:
        return Icons.security;
      case NotificationType.trackingUpdate:
        return Icons.location_on;
    }
  }
}
