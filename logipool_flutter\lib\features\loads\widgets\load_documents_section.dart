import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/models/user_model.dart';
import '../../../shared/models/document_model.dart';
import '../../../shared/models/load_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../documents/bloc/document_bloc.dart';
import '../../documents/widgets/document_card.dart';
import 'load_document_upload_dialog.dart';

class LoadDocumentsSection extends StatefulWidget {
  final LoadModel load;

  const LoadDocumentsSection({
    super.key,
    required this.load,
  });

  @override
  State<LoadDocumentsSection> createState() => _LoadDocumentsSectionState();
}

class _LoadDocumentsSectionState extends State<LoadDocumentsSection>
    with TickerProviderStateMixin {
  UserModel? _currentUser;
  late TabController _tabController;
  DocumentType? _selectedDocumentType;

  // Load-specific document categories
  final List<DocumentType> _loadDocumentTypes = [
    DocumentType.proofOfDelivery,
    DocumentType.invoice,
    DocumentType.contract,
    DocumentType.waybill,
    DocumentType.customsDeclaration,
    DocumentType.other,
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _currentUser = context.read<AuthService>().currentUser;
    _loadDocuments();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadDocuments() {
    context.read<DocumentBloc>().add(
          LoadDocumentsRequested(
            loadId: widget.load.id!,
            refresh: true,
          ),
        );
  }

  bool _canUploadDocuments() {
    // Check if user has permission to upload documents
    // This would typically check company membership permissions
    return _currentUser != null;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<DocumentBloc, DocumentState>(
      listener: (context, state) {
        if (state is DocumentUploaded) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Document uploaded successfully'),
              backgroundColor: Colors.green,
            ),
          );
          _loadDocuments(); // Refresh the list
        } else if (state is DocumentError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        } else if (state is DocumentFileDownloaded) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.green,
            ),
          );
        }
      },
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: BlocBuilder<DocumentBloc, DocumentState>(
              builder: (context, state) {
                if (state is DocumentLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state is DocumentsLoaded) {
                  final documents = state.documents;

                  if (documents.isEmpty) {
                    return _buildEmptyState();
                  }

                  return RefreshIndicator(
                    onRefresh: () async => _loadDocuments(),
                    child: ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: documents.length,
                      itemBuilder: (context, index) {
                        final document = documents[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: DocumentCard(
                            document: document,
                            onTap: () => _viewDocument(document),
                            onDownload: () => _downloadDocument(document),
                          ),
                        );
                      },
                    ),
                  );
                }

                if (state is DocumentError) {
                  return _buildErrorState(state.message);
                }

                return const Center(
                  child: Text('No documents found'),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade300,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.folder,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Load Documents',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
          if (_canUploadDocuments())
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: _showUploadDialog,
              tooltip: 'Upload Document',
            ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDocuments,
            tooltip: 'Refresh',
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.folder_open,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No Documents',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'No documents have been uploaded for this load yet.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          if (_canUploadDocuments()) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _showUploadDialog,
              icon: const Icon(Icons.upload),
              label: const Text('Upload Document'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Error',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Colors.red[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadDocuments,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _viewDocument(dynamic document) {
    // Navigate to document viewer
    context.push('/documents/${document.id}');
  }

  void _downloadDocument(dynamic document) {
    // Use file download instead of URL opening
    if (document is DocumentModel) {
      context
          .read<DocumentBloc>()
          .add(DocumentFileDownloadRequested(document.id, document.name));
    }
  }

  void _showUploadDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => LoadDocumentUploadDialog(load: widget.load),
    );

    if (result == true) {
      _loadDocuments(); // Refresh the list
    }
  }
}
