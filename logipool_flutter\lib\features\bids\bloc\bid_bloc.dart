import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/models/bid_model.dart';
import '../../../shared/models/paginated_response.dart';
import '../../../shared/services/bid_service.dart';
import '../../../core/utils/logger.dart';

// Events
abstract class BidEvent extends Equatable {
  const BidEvent();

  @override
  List<Object?> get props => [];
}

class BidCreateRequested extends BidEvent {
  final BidCreateRequest request;

  const BidCreateRequested({required this.request});

  @override
  List<Object?> get props => [request];
}

class BidUpdateRequested extends BidEvent {
  final int bidId;
  final BidUpdateRequest request;

  const BidUpdateRequested({
    required this.bidId,
    required this.request,
  });

  @override
  List<Object?> get props => [bidId, request];
}

class BidWithdrawRequested extends BidEvent {
  final int bidId;

  const BidWithdrawRequested({required this.bidId});

  @override
  List<Object?> get props => [bidId];
}

class BidFetchRequested extends BidEvent {
  final int bidId;

  const BidFetchRequested({required this.bidId});

  @override
  List<Object?> get props => [bidId];
}

class BidsForLoadFetchRequested extends BidEvent {
  final int loadId;
  final int page;
  final int size;
  final String sortBy;
  final String sortDir;

  const BidsForLoadFetchRequested({
    required this.loadId,
    this.page = 0,
    this.size = 10,
    this.sortBy = 'createdAt',
    this.sortDir = 'desc',
  });

  @override
  List<Object?> get props => [loadId, page, size, sortBy, sortDir];
}

class BidsForLoadLoadMoreRequested extends BidEvent {
  final int loadId;
  final int page;
  final int size;
  final String sortBy;
  final String sortDir;

  const BidsForLoadLoadMoreRequested({
    required this.loadId,
    required this.page,
    this.size = 10,
    this.sortBy = 'createdAt',
    this.sortDir = 'desc',
  });

  @override
  List<Object?> get props => [loadId, page, size, sortBy, sortDir];
}

class MyBidsFetchRequested extends BidEvent {
  final int page;
  final int size;
  final String sortBy;
  final String sortDir;

  const MyBidsFetchRequested({
    this.page = 0,
    this.size = 10,
    this.sortBy = 'createdAt',
    this.sortDir = 'desc',
  });

  @override
  List<Object?> get props => [page, size, sortBy, sortDir];
}

class MyBidsLoadMoreRequested extends BidEvent {
  final int page;
  final int size;
  final String sortBy;
  final String sortDir;

  const MyBidsLoadMoreRequested({
    required this.page,
    this.size = 10,
    this.sortBy = 'createdAt',
    this.sortDir = 'desc',
  });

  @override
  List<Object?> get props => [page, size, sortBy, sortDir];
}

// States
abstract class BidState extends Equatable {
  const BidState();

  @override
  List<Object?> get props => [];
}

class BidInitial extends BidState {}

class BidLoading extends BidState {}

class BidLoadingMore extends BidState {}

class BidSuccess extends BidState {
  final BidModel bid;

  const BidSuccess({required this.bid});

  @override
  List<Object?> get props => [bid];
}

class BidsForLoadSuccess extends BidState {
  final List<BidModel> bids;
  final bool hasReachedMax;
  final int currentPage;

  const BidsForLoadSuccess({
    required this.bids,
    required this.hasReachedMax,
    required this.currentPage,
  });

  @override
  List<Object?> get props => [bids, hasReachedMax, currentPage];

  BidsForLoadSuccess copyWith({
    List<BidModel>? bids,
    bool? hasReachedMax,
    int? currentPage,
  }) {
    return BidsForLoadSuccess(
      bids: bids ?? this.bids,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

class MyBidsSuccess extends BidState {
  final List<BidModel> bids;
  final bool hasReachedMax;
  final int currentPage;

  const MyBidsSuccess({
    required this.bids,
    required this.hasReachedMax,
    required this.currentPage,
  });

  @override
  List<Object?> get props => [bids, hasReachedMax, currentPage];

  MyBidsSuccess copyWith({
    List<BidModel>? bids,
    bool? hasReachedMax,
    int? currentPage,
  }) {
    return MyBidsSuccess(
      bids: bids ?? this.bids,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

class BidOperationSuccess extends BidState {
  final String message;

  const BidOperationSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class BidError extends BidState {
  final String message;

  const BidError({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class BidBloc extends Bloc<BidEvent, BidState> {
  final BidService _bidService;

  BidBloc(this._bidService) : super(BidInitial()) {
    on<BidCreateRequested>(_onBidCreateRequested);
    on<BidUpdateRequested>(_onBidUpdateRequested);
    on<BidWithdrawRequested>(_onBidWithdrawRequested);
    on<BidFetchRequested>(_onBidFetchRequested);
    on<BidsForLoadFetchRequested>(_onBidsForLoadFetchRequested);
    on<BidsForLoadLoadMoreRequested>(_onBidsForLoadLoadMoreRequested);
    on<MyBidsFetchRequested>(_onMyBidsFetchRequested);
    on<MyBidsLoadMoreRequested>(_onMyBidsLoadMoreRequested);
  }

  Future<void> _onBidCreateRequested(
    BidCreateRequested event,
    Emitter<BidState> emit,
  ) async {
    try {
      emit(BidLoading());
      final bid = await _bidService.createBid(event.request);
      emit(BidSuccess(bid: bid));
      Logger.info('Bid created successfully');
    } catch (e) {
      Logger.error('Error creating bid: $e');
      emit(BidError(message: e.toString()));
    }
  }

  Future<void> _onBidUpdateRequested(
    BidUpdateRequested event,
    Emitter<BidState> emit,
  ) async {
    try {
      emit(BidLoading());
      final bid = await _bidService.updateBid(event.bidId, event.request);
      emit(BidSuccess(bid: bid));
      Logger.info('Bid updated successfully');
    } catch (e) {
      Logger.error('Error updating bid: $e');
      emit(BidError(message: e.toString()));
    }
  }

  Future<void> _onBidWithdrawRequested(
    BidWithdrawRequested event,
    Emitter<BidState> emit,
  ) async {
    try {
      emit(BidLoading());
      await _bidService.withdrawBid(event.bidId);
      emit(const BidOperationSuccess(message: 'Bid withdrawn successfully'));
      Logger.info('Bid withdrawn successfully');
    } catch (e) {
      Logger.error('Error withdrawing bid: $e');
      emit(BidError(message: e.toString()));
    }
  }

  Future<void> _onBidFetchRequested(
    BidFetchRequested event,
    Emitter<BidState> emit,
  ) async {
    try {
      emit(BidLoading());
      final bid = await _bidService.getBidById(event.bidId);
      emit(BidSuccess(bid: bid));
    } catch (e) {
      Logger.error('Error fetching bid: $e');
      emit(BidError(message: e.toString()));
    }
  }

  Future<void> _onBidsForLoadFetchRequested(
    BidsForLoadFetchRequested event,
    Emitter<BidState> emit,
  ) async {
    try {
      emit(BidLoading());
      final response = await _bidService.getBidsForLoad(
        event.loadId,
        page: event.page,
        size: event.size,
        sortBy: event.sortBy,
        sortDir: event.sortDir,
      );
      emit(BidsForLoadSuccess(
        bids: response.content,
        hasReachedMax: response.last,
        currentPage: response.number,
      ));
    } catch (e) {
      Logger.error('Error fetching bids for load: $e');
      emit(BidError(message: e.toString()));
    }
  }

  Future<void> _onBidsForLoadLoadMoreRequested(
    BidsForLoadLoadMoreRequested event,
    Emitter<BidState> emit,
  ) async {
    final currentState = state;
    if (currentState is! BidsForLoadSuccess || currentState.hasReachedMax) {
      return;
    }

    try {
      emit(BidLoadingMore());
      final response = await _bidService.getBidsForLoad(
        event.loadId,
        page: event.page,
        size: event.size,
        sortBy: event.sortBy,
        sortDir: event.sortDir,
      );
      
      final updatedBids = List<BidModel>.from(currentState.bids)
        ..addAll(response.content);
      
      emit(currentState.copyWith(
        bids: updatedBids,
        hasReachedMax: response.last,
        currentPage: response.number,
      ));
    } catch (e) {
      Logger.error('Error loading more bids: $e');
      emit(BidError(message: e.toString()));
    }
  }

  Future<void> _onMyBidsFetchRequested(
    MyBidsFetchRequested event,
    Emitter<BidState> emit,
  ) async {
    try {
      emit(BidLoading());
      final response = await _bidService.getMyBids(
        page: event.page,
        size: event.size,
        sortBy: event.sortBy,
        sortDir: event.sortDir,
      );
      emit(MyBidsSuccess(
        bids: response.content,
        hasReachedMax: response.last,
        currentPage: response.number,
      ));
    } catch (e) {
      Logger.error('Error fetching my bids: $e');
      emit(BidError(message: e.toString()));
    }
  }

  Future<void> _onMyBidsLoadMoreRequested(
    MyBidsLoadMoreRequested event,
    Emitter<BidState> emit,
  ) async {
    final currentState = state;
    if (currentState is! MyBidsSuccess || currentState.hasReachedMax) {
      return;
    }

    try {
      emit(BidLoadingMore());
      final response = await _bidService.getMyBids(
        page: event.page,
        size: event.size,
        sortBy: event.sortBy,
        sortDir: event.sortDir,
      );
      
      final updatedBids = List<BidModel>.from(currentState.bids)
        ..addAll(response.content);
      
      emit(currentState.copyWith(
        bids: updatedBids,
        hasReachedMax: response.last,
        currentPage: response.number,
      ));
    } catch (e) {
      Logger.error('Error loading more my bids: $e');
      emit(BidError(message: e.toString()));
    }
  }
}
