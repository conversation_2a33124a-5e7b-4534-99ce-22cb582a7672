enum UserRole {
  admin,
  company,
  client,
  driver,
  transporter,
}

extension UserRoleExtension on UserRole {
  String get displayName {
    switch (this) {
      case UserRole.admin:
        return 'Admin';
      case UserRole.company:
        return 'Company';
      case UserRole.client:
        return 'Client';
      case UserRole.driver:
        return 'Driver';
      case UserRole.transporter:
        return 'Transporter';
    }
  }

  String get value {
    switch (this) {
      case UserRole.admin:
        return 'ADMIN';
      case UserRole.company:
        return 'COMPANY';
      case UserRole.client:
        return 'CLIENT';
      case UserRole.driver:
        return 'DRIVER';
      case UserRole.transporter:
        return 'TRANSPORTER';
    }
  }

  static UserRole fromString(String value) {
    switch (value.toUpperCase()) {
      case 'ADMIN':
        return UserRole.admin;
      case 'COMPANY':
        return UserRole.company;
      case 'CLIENT':
        return UserRole.client;
      case 'DRIVER':
        return UserRole.driver;
      case 'TRANSPORTER':
        return UserRole.transporter;
      default:
        throw ArgumentError('Invalid user role: $value');
    }
  }
}
