import 'package:flutter/material.dart';
import '../../../shared/widgets/public_footer.dart';

class AboutUsScreen extends StatelessWidget {
  const AboutUsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildHeroSection(context),
          _buildMissionSection(context),
          _buildValuesSection(context),
          _buildStatsSection(context),
          const PublicFooter(),
        ],
      ),
    );
  }

  Widget _buildHeroSection(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background image
          Positioned.fill(
            child: Image.asset(
              'assets/images/pexels-nc-farm-bureau-mark-2255801.jpg',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
          ),
          // Overlay
          Positioned.fill(
            child: Container(
              color: Theme.of(context).primaryColor.withOpacity(0.7),
            ),
          ),
          // Content
          Positioned.fill(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 64),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.business,
                    size: 64,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'About LogiPool',
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 48,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Connecting businesses with trusted logistics partners across the region',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Colors.white70,
                      fontSize: 20,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMissionSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          Text(
            'Our Mission',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 24),
          Text(
            'LogiPool is revolutionizing the logistics industry by creating a transparent, efficient, and reliable marketplace that connects businesses with verified logistics service providers. We believe in making transportation accessible, affordable, and trustworthy for everyone.',
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          Row(
            children: [
              Expanded(
                child: _buildMissionCard(
                  context,
                  Icons.verified,
                  'Trust & Verification',
                  'All our logistics partners are thoroughly verified and insured.',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMissionCard(
                  context,
                  Icons.speed,
                  'Efficiency',
                  'Streamlined processes for faster and more efficient logistics.',
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildMissionCard(
                  context,
                  Icons.handshake,
                  'Reliability',
                  'Dependable service with real-time tracking and support.',
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMissionCard(
      BuildContext context, IconData icon, String title, String description) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Icon(
              icon,
              size: 48,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              description,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildValuesSection(BuildContext context) {
        final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth = screenWidth / (screenWidth > 768 ? 2 : 1);
    final itemHeight = 90 ; // desired height
    final aspectRatio = itemWidth / itemHeight;

    return Container(
      padding: const EdgeInsets.all(32),
      color: Theme.of(context).primaryColor.withOpacity(0.05),
      child: Column(
        children: [
          Text(
            'Our Values',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 32),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: MediaQuery.of(context).size.width > 768 ? 2 : 1,
            crossAxisSpacing: 24,
            mainAxisSpacing: 24,
            childAspectRatio: aspectRatio,
            children: [
              _buildValueItem(
                context,
                Icons.security,
                'Security First',
                'We prioritize the safety and security of all shipments.',
              ),
              _buildValueItem(
                context,
                Icons.visibility,
                'Transparency',
                'Clear pricing, real-time tracking, and honest communication.',
              ),
              _buildValueItem(
                context,
                Icons.support_agent,
                'Customer Support',
                '24/7 support to ensure smooth logistics operations.',
              ),
              _buildValueItem(
                context,
                Icons.eco,
                'Sustainability',
                'Promoting eco-friendly logistics solutions.',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildValueItem(
      BuildContext context, IconData icon, String title, String description) {
    return Row(
      children: [
        Icon(
          icon,
          size: 40,
          color: Theme.of(context).primaryColor,
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }



  Widget _buildStatsSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      color: Theme.of(context).primaryColor,
      child: Column(
        children: [
          Text(
            'LogiPool by the Numbers',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 32),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem(context, '500+', 'Verified Companies'),
              _buildStatItem(context, '10,000+', 'Successful Deliveries'),
              _buildStatItem(context, '1,200+', 'Available Vehicles'),
              _buildStatItem(context, '98%', 'Customer Satisfaction'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String number, String label) {
    return Column(
      children: [
        Text(
          number,
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white70,
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
