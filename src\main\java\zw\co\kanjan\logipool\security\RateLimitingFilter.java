package zw.co.kanjan.logipool.security;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.time.Duration;

@Slf4j
@Component
public class RateLimitingFilter extends OncePerRequestFilter {

    private final RedisTemplate<String, String> redisTemplate;
    
    @Value("${app.rate-limit.requests-per-minute:60}")
    private int requestsPerMinute;
    
    @Value("${app.rate-limit.requests-per-hour:1000}")
    private int requestsPerHour;
    
    @Value("${app.rate-limit.enabled:true}")
    private boolean rateLimitEnabled;
    
    public RateLimitingFilter(@Autowired(required = false) RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response,
                                  FilterChain filterChain) throws ServletException, IOException {

        if (!rateLimitEnabled || redisTemplate == null) {
            filterChain.doFilter(request, response);
            return;
        }
        
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        
        // Skip rate limiting for health checks and static resources
        String requestURI = request.getRequestURI();
        if (requestURI.startsWith("/actuator/health") || 
            requestURI.startsWith("/swagger-ui") || 
            requestURI.startsWith("/api-docs")) {
            filterChain.doFilter(request, response);
            return;
        }
        
        // Check rate limits
        if (isRateLimited(clientIp, userAgent)) {
            log.warn("Rate limit exceeded for IP: {} and User-Agent: {}", clientIp, userAgent);
            response.setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
            response.setContentType("application/json");
            response.getWriter().write("{\"error\":\"Rate limit exceeded. Please try again later.\"}");
            return;
        }
        
        filterChain.doFilter(request, response);
    }
    
    private boolean isRateLimited(String clientIp, String userAgent) {
        String minuteKey = "rate_limit:minute:" + clientIp;
        String hourKey = "rate_limit:hour:" + clientIp;
        
        try {
            // Check minute limit
            String minuteCount = redisTemplate.opsForValue().get(minuteKey);
            if (minuteCount != null && Integer.parseInt(minuteCount) >= requestsPerMinute) {
                return true;
            }
            
            // Check hour limit
            String hourCount = redisTemplate.opsForValue().get(hourKey);
            if (hourCount != null && Integer.parseInt(hourCount) >= requestsPerHour) {
                return true;
            }
            
            // Increment counters
            redisTemplate.opsForValue().increment(minuteKey);
            redisTemplate.expire(minuteKey, Duration.ofMinutes(1));
            
            redisTemplate.opsForValue().increment(hourKey);
            redisTemplate.expire(hourKey, Duration.ofHours(1));
            
            return false;
            
        } catch (Exception e) {
            log.error("Error checking rate limit for IP: {}", clientIp, e);
            // In case of Redis error, allow the request to proceed
            return false;
        }
    }
    
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
