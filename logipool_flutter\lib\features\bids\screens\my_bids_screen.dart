import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../shared/enums/user_role.dart';
import '../../../shared/models/bid_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/services/bid_service.dart';
import '../../../shared/widgets/unified_header.dart';
import '../../../core/di/service_locator.dart';
import '../bloc/bid_bloc.dart';
import '../widgets/bid_card.dart';
import 'bid_detail_screen.dart';

class MyBidsScreen extends StatefulWidget {
  const MyBidsScreen({super.key});

  @override
  State<MyBidsScreen> createState() => _MyBidsScreenState();
}

class _MyBidsScreenState extends State<MyBidsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;
  BidStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _scrollController.addListener(_onScroll);
    _fetchMyBids();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _fetchMyBids() {
    context.read<BidBloc>().add(const MyBidsFetchRequested());
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore) {
      _loadMore();
    }
  }

  void _loadMore() {
    final state = context.read<BidBloc>().state;
    if (state is MyBidsSuccess && !state.hasReachedMax) {
      setState(() {
        _isLoadingMore = true;
      });

      context.read<BidBloc>().add(
            MyBidsLoadMoreRequested(page: state.currentPage + 1),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const UnifiedHeader(
        title: 'My Bids',
      ),
      body: Column(
        children: [
          Container(
            color: Theme.of(context).colorScheme.surface,
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              onTap: (index) {
                setState(() {
                  switch (index) {
                    case 0:
                      _selectedStatus = null; // All
                      break;
                    case 1:
                      _selectedStatus = BidStatus.pending;
                      break;
                    case 2:
                      _selectedStatus = BidStatus.accepted;
                      break;
                    case 3:
                      _selectedStatus = BidStatus.rejected;
                      break;
                    case 4:
                      _selectedStatus = BidStatus.withdrawn;
                      break;
                  }
                });
              },
              tabs: const [
                Tab(text: 'All'),
                Tab(text: 'Pending'),
                Tab(text: 'Accepted'),
                Tab(text: 'Rejected'),
                Tab(text: 'Withdrawn'),
              ],
            ),
          ),
          Expanded(
            child: BlocConsumer<BidBloc, BidState>(
              listener: (context, state) {
                if (state is MyBidsSuccess) {
                  setState(() {
                    _isLoadingMore = false;
                  });
                } else if (state is BidError) {
                  setState(() {
                    _isLoadingMore = false;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                } else if (state is BidOperationSuccess) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _fetchMyBids(); // Refresh the list
                }
              },
              builder: (context, state) {
                if (state is BidLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                } else if (state is MyBidsSuccess) {
                  return _buildBidsList(context, state);
                } else if (state is BidError) {
                  return _buildErrorState(context, state.message);
                } else {
                  return const Center(
                    child: Text('No bids available'),
                  );
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBidsList(BuildContext context, MyBidsSuccess state) {
    // Filter bids based on selected status
    final filteredBids = _selectedStatus == null
        ? state.bids
        : state.bids.where((bid) => bid.status == _selectedStatus).toList();

    if (filteredBids.isEmpty) {
      return _buildEmptyState(context);
    }

    return RefreshIndicator(
      onRefresh: () async {
        _fetchMyBids();
      },
      child: ListView.builder(
        controller: _scrollController,
        itemCount: filteredBids.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= filteredBids.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final bid = filteredBids[index];
          return FutureBuilder<UserModel?>(
            future: context.read<AuthService>().getCurrentUser(),
            builder: (context, snapshot) {
              final currentUser = snapshot.data;

              return BidCard(
                bid: bid,
                currentUserRole: currentUser?.role != null
                    ? UserRoleExtension.fromString(currentUser!.role)
                    : null,
                isLoadOwner: false,
                onTap: () => _navigateToBidDetail(bid),
                onWithdraw: bid.status == BidStatus.pending
                    ? () => _withdrawBid(bid)
                    : null,
                onEdit: bid.status == BidStatus.pending
                    ? () => _editBid(bid)
                    : null,
                onViewDetails: () => _navigateToBidDetail(bid),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    String message;
    String subtitle;

    switch (_selectedStatus) {
      case BidStatus.pending:
        message = 'No pending bids';
        subtitle = 'Your pending bids will appear here';
        break;
      case BidStatus.accepted:
        message = 'No accepted bids';
        subtitle = 'Your accepted bids will appear here';
        break;
      case BidStatus.rejected:
        message = 'No rejected bids';
        subtitle = 'Your rejected bids will appear here';
        break;
      case BidStatus.withdrawn:
        message = 'No withdrawn bids';
        subtitle = 'Your withdrawn bids will appear here';
        break;
      default:
        message = 'No bids yet';
        subtitle = 'Start bidding on loads to see them here';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.local_offer_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: theme.textTheme.titleMedium?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading bids',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchMyBids,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _navigateToBidDetail(BidModel bid) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => BidBloc(getIt<BidService>()),
          child: BidDetailScreen(bid: bid),
        ),
      ),
    ).then((result) {
      if (result == true) {
        _fetchMyBids(); // Refresh if bid was modified
      }
    });
  }

  void _withdrawBid(BidModel bid) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Withdraw Bid'),
        content: Text(
          'Are you sure you want to withdraw your bid for "${bid.loadTitle}"?\n\nThis action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<BidBloc>().add(
                    BidWithdrawRequested(bidId: bid.id!),
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Withdraw'),
          ),
        ],
      ),
    );
  }

  void _editBid(BidModel bid) {
    // Navigate to edit bid screen (to be implemented)
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Edit bid functionality coming soon'),
        backgroundColor: Colors.orange,
      ),
    );
  }
}
