import 'package:equatable/equatable.dart';

class PaginatedResponse<T> extends Equatable {
  final List<T> content;
  final int totalElements;
  final int totalPages;
  final int size;
  final int number;
  final bool first;
  final bool last;
  final bool empty;

  const PaginatedResponse({
    required this.content,
    required this.totalElements,
    required this.totalPages,
    required this.size,
    required this.number,
    required this.first,
    required this.last,
    required this.empty,
  });

  factory PaginatedResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic) fromJsonT,
  ) {
    return PaginatedResponse<T>(
      content: (json['content'] as List<dynamic>?)
              ?.map((item) => fromJsonT(item))
              .toList() ??
          [],
      totalElements: (json['totalElements'] as int?) ?? 0,
      totalPages: (json['totalPages'] as int?) ?? 0,
      size: (json['size'] as int?) ?? 0,
      number: (json['number'] as int?) ?? 0,
      first: (json['first'] as bool?) ?? true,
      last: (json['last'] as bool?) ?? true,
      empty: (json['empty'] as bool?) ?? true,
    );
  }

  Map<String, dynamic> toJson(Map<String, dynamic> Function(T) toJsonT) {
    return {
      'content': content.map((item) => toJsonT(item)).toList(),
      'totalElements': totalElements,
      'totalPages': totalPages,
      'size': size,
      'number': number,
      'first': first,
      'last': last,
      'empty': empty,
    };
  }

  bool get hasNextPage => !last;
  bool get hasPreviousPage => !first;
  int get currentPage => number;

  @override
  List<Object?> get props => [
        content,
        totalElements,
        totalPages,
        size,
        number,
        first,
        last,
        empty,
      ];
}
