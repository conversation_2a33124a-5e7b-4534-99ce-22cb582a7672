import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../shared/models/document_model.dart';
import '../../../shared/widgets/app_bar.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../bloc/document_bloc.dart';

class DocumentViewScreen extends StatefulWidget {
  final int documentId;

  const DocumentViewScreen({
    super.key,
    required this.documentId,
  });

  @override
  State<DocumentViewScreen> createState() => _DocumentViewScreenState();
}

class _DocumentViewScreenState extends State<DocumentViewScreen> {
  @override
  void initState() {
    super.initState();
    context.read<DocumentBloc>().add(DocumentByIdRequested(widget.documentId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const LogiPoolAppBar(
        title: 'Document Details',
      ),
      body: BlocConsumer<DocumentBloc, DocumentState>(
        listener: (context, state) {
          if (state is DocumentError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          } else if (state is DocumentDownloadReady) {
            _launchUrl(state.downloadUrl);
          } else if (state is DocumentFileDownloaded) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
          } else if (state is DocumentDeleted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Document deleted successfully'),
                backgroundColor: Colors.green,
              ),
            );
            context.pop();
          } else if (state is DocumentVerified) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Document verification updated'),
                backgroundColor: Colors.green,
              ),
            );
            // Refresh the document
            context
                .read<DocumentBloc>()
                .add(DocumentByIdRequested(widget.documentId));
          }
        },
        builder: (context, state) {
          if (state is DocumentLoading) {
            return const LoadingWidget();
          }

          if (state is DocumentError) {
            return AppErrorWidget(
              message: state.message,
              onRetry: () => context.read<DocumentBloc>().add(
                    DocumentByIdRequested(widget.documentId),
                  ),
            );
          }

          if (state is DocumentLoaded) {
            return _buildDocumentDetails(context, state.document);
          }

          return const SizedBox.shrink();
        },
      ),
    );
  }

  Widget _buildDocumentDetails(BuildContext context, DocumentModel document) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header card with basic info
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          document.name,
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      _buildStatusChip(context, document),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    document.type.displayName,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (document.description != null) ...[
                    const SizedBox(height: 12),
                    Text(
                      document.description!,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // File information card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'File Information',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow(
                    context,
                    'File Name',
                    document.fileName,
                    Icons.insert_drive_file,
                  ),
                  _buildInfoRow(
                    context,
                    'File Type',
                    document.fileType,
                    Icons.category,
                  ),
                  _buildInfoRow(
                    context,
                    'File Size',
                    document.formattedFileSize,
                    Icons.storage,
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Dates and status card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Document Status',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildInfoRow(
                    context,
                    'Uploaded',
                    DateFormat('MMM dd, yyyy HH:mm').format(document.createdAt),
                    Icons.upload,
                  ),
                  _buildInfoRow(
                    context,
                    'Last Updated',
                    DateFormat('MMM dd, yyyy HH:mm').format(document.updatedAt),
                    Icons.update,
                  ),
                  if (document.expiryDate != null)
                    _buildInfoRow(
                      context,
                      'Expires',
                      DateFormat('MMM dd, yyyy').format(document.expiryDate!),
                      document.isExpired
                          ? Icons.error
                          : document.isExpiringSoon
                              ? Icons.warning
                              : Icons.schedule,
                      valueColor: document.isExpired
                          ? colorScheme.error
                          : document.isExpiringSoon
                              ? Colors.orange
                              : null,
                    ),
                  if (document.verifiedAt != null)
                    _buildInfoRow(
                      context,
                      'Verified',
                      DateFormat('MMM dd, yyyy HH:mm')
                          .format(document.verifiedAt!),
                      Icons.verified,
                    ),
                  if (document.isRequired)
                    _buildInfoRow(
                      context,
                      'Required',
                      'This is a required document',
                      Icons.star,
                      valueColor: Colors.amber.shade700,
                    ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _downloadDocument(document),
                  icon: const Icon(Icons.download),
                  label: const Text('Download'),
                ),
              ),
              const SizedBox(width: 12),
              if (_canEdit(document))
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _editDocument(document),
                    icon: const Icon(Icons.edit),
                    label: const Text('Edit'),
                  ),
                ),
            ],
          ),

          const SizedBox(height: 12),

          // Admin actions (if applicable)
          if (_canVerify(document)) ...[
            Row(
              children: [
                Expanded(
                  child: FilledButton.icon(
                    onPressed: () =>
                        _verifyDocument(document, DocumentStatus.verified),
                    icon: const Icon(Icons.check),
                    label: const Text('Approve'),
                    style: FilledButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: FilledButton.icon(
                    onPressed: () =>
                        _verifyDocument(document, DocumentStatus.rejected),
                    icon: const Icon(Icons.close),
                    label: const Text('Reject'),
                    style: FilledButton.styleFrom(
                      backgroundColor: colorScheme.error,
                      foregroundColor: colorScheme.onError,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
          ],

          // Delete button (if applicable)
          if (_canDelete(document))
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _deleteDocument(document),
                icon: const Icon(Icons.delete),
                label: const Text('Delete Document'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: colorScheme.error,
                  side: BorderSide(color: colorScheme.error),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    BuildContext context,
    String label,
    String value,
    IconData icon, {
    Color? valueColor,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: valueColor ?? colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, DocumentModel document) {
    final colorScheme = Theme.of(context).colorScheme;

    Color backgroundColor;
    Color foregroundColor;
    IconData icon;

    switch (document.status) {
      case DocumentStatus.verified:
        backgroundColor = Colors.green.shade100;
        foregroundColor = Colors.green.shade700;
        icon = Icons.verified;
        break;
      case DocumentStatus.rejected:
        backgroundColor = colorScheme.errorContainer;
        foregroundColor = colorScheme.onErrorContainer;
        icon = Icons.cancel;
        break;
      case DocumentStatus.expired:
        backgroundColor = Colors.red.shade100;
        foregroundColor = Colors.red.shade700;
        icon = Icons.event_busy;
        break;
      case DocumentStatus.pending:
      default:
        backgroundColor = Colors.orange.shade100;
        foregroundColor = Colors.orange.shade700;
        icon = Icons.pending;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: foregroundColor),
          const SizedBox(width: 6),
          Text(
            document.status.displayName,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: foregroundColor,
            ),
          ),
        ],
      ),
    );
  }

  bool _canEdit(DocumentModel document) {
    return document.status != DocumentStatus.verified;
  }

  bool _canDelete(DocumentModel document) {
    return !document.isRequired;
  }

  bool _canVerify(DocumentModel document) {
    // This would typically check user role/permissions
    return document.status == DocumentStatus.pending;
  }

  void _downloadDocument(DocumentModel document) {
    // Use file download instead of URL opening
    context
        .read<DocumentBloc>()
        .add(DocumentFileDownloadRequested(document.id, document.name));
  }

  void _editDocument(DocumentModel document) {
    context.push('/documents/${document.id}/edit');
  }

  void _deleteDocument(DocumentModel document) {
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Document'),
        content: Text('Are you sure you want to delete "${document.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    ).then((confirmed) {
      if (confirmed == true) {
        context.read<DocumentBloc>().add(DocumentDeleteRequested(document.id));
      }
    });
  }

  void _verifyDocument(DocumentModel document, DocumentStatus status) {
    context.read<DocumentBloc>().add(DocumentVerificationRequested(
          id: document.id,
          request: DocumentVerificationRequest(status: status),
        ));
  }

  Future<void> _launchUrl(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not open download link'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
