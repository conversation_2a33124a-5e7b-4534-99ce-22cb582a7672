import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../auth/bloc/auth_bloc.dart';
import '../bloc/admin_bloc.dart';

class AdminSettingsScreen extends StatefulWidget {
  const AdminSettingsScreen({super.key});

  @override
  State<AdminSettingsScreen> createState() => _AdminSettingsScreenState();
}

class _AdminSettingsScreenState extends State<AdminSettingsScreen> {
  @override
  void initState() {
    super.initState();
    context.read<AdminBloc>().add(const LoadSystemConfiguration());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'System Settings',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => context.read<AdminBloc>().add(const LoadSystemConfiguration()),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Logout', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocConsumer<AdminBloc, AdminState>(
        listener: (context, state) {
          if (state is AdminError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          } else if (state is AdminOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
        },
        builder: (context, state) {
          if (state is AdminLoading) {
            return const LoadingWidget();
          } else if (state is AdminError) {
            return CustomErrorWidget(
              message: state.message,
              onRetry: () => context.read<AdminBloc>().add(const LoadSystemConfiguration()),
            );
          } else if (state is SystemConfigurationLoaded) {
            return _buildSettingsContent(state);
          }
          return const LoadingWidget();
        },
      ),
    );
  }

  Widget _buildSettingsContent(SystemConfigurationLoaded state) {
    final config = state.configuration;
    
    return RefreshIndicator(
      onRefresh: () async {
        context.read<AdminBloc>().add(const LoadSystemConfiguration());
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildApplicationSettings(config.applicationSettings),
            const SizedBox(height: 24),
            _buildSecuritySettings(config.securitySettings),
            const SizedBox(height: 24),
            _buildNotificationSettings(config.notificationSettings),
            const SizedBox(height: 24),
            _buildPaymentSettings(config.paymentSettings),
            const SizedBox(height: 24),
            _buildTrackingSettings(config.trackingSettings),
            const SizedBox(height: 24),
            _buildFileSettings(config.fileSettings),
            const SizedBox(height: 24),
            _buildSystemActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildApplicationSettings(dynamic applicationSettings) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.settings, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'Application Settings',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSettingItem(
              'Application Name',
              (applicationSettings?['applicationName'] as String?) ?? 'LogiPool',
              Icons.apps,
            ),
            _buildSettingItem(
              'Version',
              (applicationSettings?['version'] as String?) ?? '1.0.0',
              Icons.info,
            ),
            _buildSettingItem(
              'Maintenance Mode',
              applicationSettings?.maintenanceMode == true ? 'Enabled' : 'Disabled',
              Icons.build,
              trailing: Switch(
                value: (applicationSettings?['maintenanceMode'] as bool?) ?? false,
                onChanged: (value) {
                  // TODO: Implement maintenance mode toggle
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecuritySettings(dynamic securitySettings) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.security, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'Security Settings',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSettingItem(
              'JWT Expiration',
              '${securitySettings?.jwtExpirationMinutes ?? 60} minutes',
              Icons.timer,
            ),
            _buildSettingItem(
              'Max Login Attempts',
              '${securitySettings?.maxLoginAttempts ?? 5}',
              Icons.lock,
            ),
            _buildSettingItem(
              'Password Min Length',
              '${securitySettings?.passwordMinLength ?? 8}',
              Icons.password,
            ),
            _buildSettingItem(
              'Two-Factor Auth',
              securitySettings?.twoFactorEnabled == true ? 'Enabled' : 'Disabled',
              Icons.verified_user,
              trailing: Switch(
                value: (securitySettings?['twoFactorEnabled'] as bool?) ?? false,
                onChanged: (value) {
                  // TODO: Implement 2FA toggle
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSettings(dynamic notificationSettings) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.notifications, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Notification Settings',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSettingItem(
              'Email Notifications',
              notificationSettings?.emailEnabled == true ? 'Enabled' : 'Disabled',
              Icons.email,
              trailing: Switch(
                value: (notificationSettings?['emailEnabled'] as bool?) ?? false,
                onChanged: (value) {
                  // TODO: Implement email notifications toggle
                },
              ),
            ),
            _buildSettingItem(
              'Push Notifications',
              notificationSettings?.pushEnabled == true ? 'Enabled' : 'Disabled',
              Icons.push_pin,
              trailing: Switch(
                value: (notificationSettings?['pushEnabled'] as bool?) ?? false,
                onChanged: (value) {
                  // TODO: Implement push notifications toggle
                },
              ),
            ),
            _buildSettingItem(
              'SMS Notifications',
              notificationSettings?.smsEnabled == true ? 'Enabled' : 'Disabled',
              Icons.sms,
              trailing: Switch(
                value: (notificationSettings?['smsEnabled'] as bool?) ?? false,
                onChanged: (value) {
                  // TODO: Implement SMS notifications toggle
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSettings(dynamic paymentSettings) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.payment, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Payment Settings',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSettingItem(
              'Commission Rate',
              '${paymentSettings?.commissionRate ?? 5.0}%',
              Icons.percent,
            ),
            _buildSettingItem(
              'Currency',
              (paymentSettings?['currency'] as String?) ?? 'USD',
              Icons.attach_money,
            ),
            _buildSettingItem(
              'Payment Gateway',
              (paymentSettings?['paymentGateway'] as String?) ?? 'Stripe',
              Icons.credit_card,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTrackingSettings(dynamic trackingSettings) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.location_on, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'Tracking Settings',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSettingItem(
              'GPS Tracking',
              trackingSettings?.gpsEnabled == true ? 'Enabled' : 'Disabled',
              Icons.gps_fixed,
              trailing: Switch(
                value: (trackingSettings?['gpsEnabled'] as bool?) ?? false,
                onChanged: (value) {
                  // TODO: Implement GPS tracking toggle
                },
              ),
            ),
            _buildSettingItem(
              'Update Interval',
              '${trackingSettings?.updateIntervalSeconds ?? 30} seconds',
              Icons.timer,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFileSettings(dynamic fileSettings) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.folder, color: Colors.teal),
                const SizedBox(width: 8),
                Text(
                  'File Settings',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSettingItem(
              'Max File Size',
              '${fileSettings?.maxFileSizeMB ?? 10} MB',
              Icons.file_upload,
            ),
            _buildSettingItem(
              'Allowed Types',
              (fileSettings?['allowedFileTypes'] as List<dynamic>?)?.cast<String>().join(', ') ?? 'PDF, JPG, PNG',
              Icons.file_present,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.admin_panel_settings, color: Colors.red),
                const SizedBox(width: 8),
                Text(
                  'System Actions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.backup, color: Colors.blue),
              title: const Text('Create System Backup'),
              subtitle: const Text('Create a full system backup'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showBackupDialog(),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.history, color: Colors.orange),
              title: const Text('View Audit Logs'),
              subtitle: const Text('View system audit logs'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _navigateToAuditLogs(),
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.warning, color: Colors.red),
              title: const Text('System Alerts'),
              subtitle: const Text('View and manage system alerts'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _navigateToSystemAlerts(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingItem(String title, String value, IconData icon, {Widget? trailing}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  value,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          if (trailing != null) trailing,
        ],
      ),
    );
  }

  void _showBackupDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create System Backup'),
        content: const Text('This will create a full system backup. This process may take several minutes.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AdminBloc>().add(const InitiateSystemBackup(backupType: 'FULL'));
            },
            child: const Text('Create Backup'),
          ),
        ],
      ),
    );
  }

  void _navigateToAuditLogs() {
    // TODO: Navigate to audit logs screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Audit logs screen coming soon')),
    );
  }

  void _navigateToSystemAlerts() {
    // TODO: Navigate to system alerts screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('System alerts screen coming soon')),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'logout':
        _showLogoutDialog();
        break;
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AuthBloc>().add(AuthLogoutRequested());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
