package zw.co.kanjan.logipool.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;

import zw.co.kanjan.logipool.security.JwtUtils;

import static org.mockito.Mockito.mock;

/**
 * Test security configuration that provides mocked security beans for testing
 */
@TestConfiguration
public class TestSecurityConfig {

    @Bean
    @Primary
    public JwtUtils jwtUtils() {
        return mock(JwtUtils.class);
    }



    @Bean
    @Primary
    @SuppressWarnings("unchecked")
    public RedisTemplate<String, String> redisTemplate() {
        return mock(RedisTemplate.class);
    }


}
