package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import zw.co.kanjan.logipool.dto.load.LoadCreateRequest;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.LoadMapper;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LoadServiceTest {

    @Mock
    private LoadRepository loadRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private LoadMapper loadMapper;

    @Mock
    private NotificationService notificationService;

    @InjectMocks
    private LoadService loadService;

    private User testUser;
    private Company testCompany;
    private Load testLoad;
    private LoadCreateRequest loadRequest;
    private LoadResponse loadResponse;

    @BeforeEach
    void setUp() {
        // Setup test user with CLIENT role
        Role clientRole = new Role();
        clientRole.setName(Role.RoleName.CLIENT);
        Set<Role> roles = new HashSet<>();
        roles.add(clientRole);

        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testclient");
        testUser.setEmail("<EMAIL>");
        testUser.setFirstName("Test");
        testUser.setLastName("Client");
        testUser.setRoles(roles);

        // Setup test company
        testCompany = new Company();
        testCompany.setId(1L);
        testCompany.setName("Test Company");
        testCompany.setRegistrationNumber("REG123");
        testCompany.setUser(testUser);
        testCompany.setVerificationStatus(Company.VerificationStatus.VERIFIED);

        // Setup test load
        testLoad = new Load();
        testLoad.setId(1L);
        testLoad.setTitle("Test Load");
        testLoad.setDescription("Test load description");
        testLoad.setPickupLocation("Pickup Location");
        testLoad.setDeliveryLocation("Delivery Location");
        testLoad.setEstimatedValue(BigDecimal.valueOf(1000));
        testLoad.setPaymentRate(BigDecimal.valueOf(500));
        testLoad.setWeight(BigDecimal.valueOf(100));
        testLoad.setPickupDate(LocalDateTime.now().plusDays(1));
        testLoad.setDeliveryDate(LocalDateTime.now().plusDays(3));
        testLoad.setStatus(Load.LoadStatus.POSTED);
        testLoad.setClient(testUser);
        testLoad.setCreatedAt(LocalDateTime.now());

        // Setup DTOs
        loadRequest = new LoadCreateRequest();
        loadRequest.setTitle("Test Load");
        loadRequest.setDescription("Test load description");
        loadRequest.setCargoType("General");
        loadRequest.setPickupLocation("Pickup Location");
        loadRequest.setDeliveryLocation("Delivery Location");
        loadRequest.setEstimatedValue(BigDecimal.valueOf(1000));
        loadRequest.setPaymentRate(BigDecimal.valueOf(500));
        loadRequest.setWeight(BigDecimal.valueOf(100));
        loadRequest.setPickupDate(LocalDateTime.now().plusDays(1));
        loadRequest.setDeliveryDate(LocalDateTime.now().plusDays(3));

        loadResponse = new LoadResponse();
        loadResponse.setId(1L);
        loadResponse.setTitle("Test Load");
        loadResponse.setDescription("Test load description");
        loadResponse.setPickupLocation("Pickup Location");
        loadResponse.setDeliveryLocation("Delivery Location");
        loadResponse.setEstimatedValue(BigDecimal.valueOf(1000));
        loadResponse.setPaymentRate(BigDecimal.valueOf(500));
        loadResponse.setWeight(BigDecimal.valueOf(100));
        loadResponse.setPickupDate(LocalDateTime.now().plusDays(1));
        loadResponse.setDeliveryDate(LocalDateTime.now().plusDays(3));
        loadResponse.setStatus(Load.LoadStatus.POSTED);
        loadResponse.setClientName("Test Client");
        loadResponse.setCreatedAt(LocalDateTime.now());
    }

    @Test
    void createLoad_WithValidData_ShouldCreateLoad() {
        // Arrange
        when(userRepository.findByUsername("testclient")).thenReturn(Optional.of(testUser));
        when(loadMapper.toEntity(loadRequest)).thenReturn(testLoad);
        when(loadRepository.save(any(Load.class))).thenReturn(testLoad);
        when(loadMapper.toResponse(testLoad)).thenReturn(loadResponse);

        // Act
        LoadResponse result = loadService.createLoad(loadRequest, "testclient");

        // Assert
        assertNotNull(result);
        assertEquals("Test Load", result.getTitle());
        assertEquals(Load.LoadStatus.POSTED, result.getStatus());
        verify(loadRepository).save(any(Load.class));
        verify(notificationService).sendLoadPostedNotification(testLoad);
        verify(notificationService).sendAdminNewLoadNotification(testLoad);
    }

    @Test
    void createLoad_WithInvalidUser_ShouldThrowException() {
        // Arrange
        when(userRepository.findByUsername("invaliduser")).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> {
            loadService.createLoad(loadRequest, "invaliduser");
        });
        verify(loadRepository, never()).save(any());
    }

    @Test
    void getLoadById_WithValidId_ShouldReturnLoad() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(loadMapper.toResponse(testLoad)).thenReturn(loadResponse);

        // Act
        LoadResponse result = loadService.getLoadById(1L);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("Test Load", result.getTitle());
    }

    @Test
    void getLoadById_WithInvalidId_ShouldThrowException() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> {
            loadService.getLoadById(1L);
        });
    }

    @Test
    void getAllLoads_ShouldReturnPagedLoads() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Load> loadPage = new PageImpl<>(Arrays.asList(testLoad), pageable, 1);
        when(loadRepository.findAll(pageable)).thenReturn(loadPage);
        when(loadMapper.toResponse(testLoad)).thenReturn(loadResponse);

        // Act
        Page<LoadResponse> result = loadService.getAllLoads(pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals("Test Load", result.getContent().get(0).getTitle());
    }

    @Test
    void updateLoadStatus_WithValidData_ShouldUpdateStatus() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(loadRepository.save(any(Load.class))).thenReturn(testLoad);
        when(loadMapper.toResponse(testLoad)).thenReturn(loadResponse);

        // Act
        LoadResponse result = loadService.updateLoadStatus(1L, Load.LoadStatus.IN_TRANSIT);

        // Assert
        assertNotNull(result);
        assertEquals(Load.LoadStatus.IN_TRANSIT, testLoad.getStatus());
        verify(loadRepository).save(testLoad);
    }

    @Test
    void deleteLoad_WithValidId_ShouldDeleteLoad() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));

        // Act
        loadService.deleteLoad(1L, "testclient");

        // Assert
        verify(loadRepository).delete(testLoad); // deleteLoad actually deletes the load
    }

    @Test
    void deleteLoad_WithInvalidId_ShouldThrowException() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> {
            loadService.deleteLoad(1L, "testclient");
        });
    }
}
