# HTTP server (redirects to HTTPS in production)
server {
    listen 80;
    server_name api.logipool.com localhost;

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # Redirect HTTP to HTTPS in production
    # Uncomment the following lines when SSL is configured
    # return 301 https://$server_name$request_uri;

    # For development/testing, proxy directly to backend
    location / {
        proxy_pass http://logipool_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;

        # Rate limiting for API endpoints
        limit_req zone=api burst=20 nodelay;
    }

    # Special rate limiting for authentication endpoints
    location /api/auth/ {
        proxy_pass http://logipool_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Stricter rate limiting for auth
        limit_req zone=auth burst=10 nodelay;
    }

    # File upload endpoints with larger body size
    location /api/documents/upload {
        proxy_pass http://logipool_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Increase body size for file uploads
        client_max_body_size 50M;
        proxy_request_buffering off;
        proxy_read_timeout 300s;
        proxy_send_timeout 300s;
    }

    # Actuator endpoints (restricted access)
    location /actuator/ {
        # Allow only from specific IPs in production
        # allow 10.0.0.0/8;
        # allow **********/12;
        # allow ***********/16;
        # deny all;

        proxy_pass http://logipool_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# HTTPS server (uncomment and configure when SSL certificates are available)
# server {
#     listen 443 ssl http2;
#     server_name api.logipool.com;
#
#     # SSL configuration
#     ssl_certificate /etc/nginx/ssl/logipool.crt;
#     ssl_certificate_key /etc/nginx/ssl/logipool.key;
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#
#     # HSTS
#     add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
#
#     # Same location blocks as HTTP server
#     location / {
#         proxy_pass http://logipool_backend;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto https;
#         proxy_set_header X-Forwarded-Host $host;
#         proxy_set_header X-Forwarded-Port $server_port;
#
#         proxy_connect_timeout 30s;
#         proxy_send_timeout 30s;
#         proxy_read_timeout 30s;
#
#         limit_req zone=api burst=20 nodelay;
#     }
#
#     location /api/auth/ {
#         proxy_pass http://logipool_backend;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto https;
#
#         limit_req zone=auth burst=10 nodelay;
#     }
#
#     location /api/documents/upload {
#         proxy_pass http://logipool_backend;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto https;
#
#         client_max_body_size 50M;
#         proxy_request_buffering off;
#         proxy_read_timeout 300s;
#         proxy_send_timeout 300s;
#     }
#
#     location /actuator/ {
#         # Restrict access in production
#         # allow 10.0.0.0/8;
#         # allow **********/12;
#         # allow ***********/16;
#         # deny all;
#
#         proxy_pass http://logipool_backend;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto https;
#     }
# }
