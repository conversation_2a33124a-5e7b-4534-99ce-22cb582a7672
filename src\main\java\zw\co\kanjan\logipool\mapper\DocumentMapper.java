package zw.co.kanjan.logipool.mapper;

import org.mapstruct.*;
import zw.co.kanjan.logipool.dto.DocumentDto;
import zw.co.kanjan.logipool.entity.Document;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DocumentMapper {
    
    @Mapping(target = "companyId", source = "company.id")
    @Mapping(target = "vehicleId", source = "vehicle.id")
    @Mapping(target = "loadId", source = "load.id")
    @Mapping(target = "verifiedById", source = "verifiedBy.id")
    @Mapping(target = "downloadUrl", ignore = true) // Will be set by service
    DocumentDto.DocumentResponse toResponse(Document document);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "filePath", ignore = true)
    @Mapping(target = "fileName", ignore = true)
    @Mapping(target = "fileType", ignore = true)
    @Mapping(target = "fileSize", ignore = true)
    @Mapping(target = "status", constant = "PENDING")
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "vehicle", ignore = true)
    @Mapping(target = "load", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "verifiedAt", ignore = true)
    @Mapping(target = "verifiedBy", ignore = true)
    @Mapping(target = "isRequired", ignore = true) // Will be determined by business logic
    Document toEntity(DocumentDto.DocumentUploadRequest request);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "type", ignore = true)
    @Mapping(target = "filePath", ignore = true)
    @Mapping(target = "fileName", ignore = true)
    @Mapping(target = "fileType", ignore = true)
    @Mapping(target = "fileSize", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "vehicle", ignore = true)
    @Mapping(target = "load", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "verifiedAt", ignore = true)
    @Mapping(target = "verifiedBy", ignore = true)
    @Mapping(target = "isRequired", ignore = true)
    void updateEntity(DocumentDto.DocumentUpdateRequest request, @MappingTarget Document document);
}
