package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.core.io.Resource;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.entity.Invoice.InvoiceStatus;
import zw.co.kanjan.logipool.repository.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class InvoiceServiceTest {

    @Mock
    private InvoiceRepository invoiceRepository;
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private LoadRepository loadRepository;
    
    @Mock
    private BidRepository bidRepository;
    
    @Mock
    private CompanyRepository companyRepository;

    @InjectMocks
    private InvoiceService invoiceService;

    private Invoice testInvoice;
    private User testUser;
    private Load testLoad;
    private Company testCompany;

    @BeforeEach
    void setUp() {
        // Create test user
        testUser = User.builder()
                .id(1L)
                .username("testuser")
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("User")
                .build();

        // Create test company
        testCompany = Company.builder()
                .id(1L)
                .name("Test Transport Company")
                .build();

        // Create test load
        testLoad = Load.builder()
                .id(1L)
                .title("Test Load")
                .pickupLocation("New York, NY")
                .deliveryLocation("Los Angeles, CA")
                .client(testUser)
                .build();

        // Create test invoice items
        List<InvoiceItem> items = new ArrayList<>();
        InvoiceItem item1 = InvoiceItem.builder()
                .id(1L)
                .description("Transportation Service")
                .quantity(BigDecimal.valueOf(1))
                .unitPrice(BigDecimal.valueOf(1000.00))
                .totalPrice(BigDecimal.valueOf(1000.00))
                .build();
        items.add(item1);

        // Create test invoice
        testInvoice = Invoice.builder()
                .id(1L)
                .invoiceNumber("INV-001")
                .client(testUser)
                .transporter(testUser)
                .company(testCompany)
                .load(testLoad)
                .subtotal(BigDecimal.valueOf(1000.00))
                .taxAmount(BigDecimal.valueOf(100.00))
                .totalAmount(BigDecimal.valueOf(1100.00))
                .status(InvoiceStatus.DRAFT)
                .dueDate(LocalDateTime.now().plusDays(30))
                .createdAt(LocalDateTime.now())
                .items(items)
                .notes("Test invoice notes")
                .build();
    }

    @Test
    void testGenerateInvoicePdf_Success() {
        // Given
        when(invoiceRepository.findById(1L)).thenReturn(Optional.of(testInvoice));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));

        // When
        Resource result = invoiceService.generateInvoicePdf(1L, "testuser");

        // Then
        assertNotNull(result);
        assertTrue(result.exists());
        verify(invoiceRepository).findById(1L);
        verify(userRepository).findByUsername("testuser");
    }

    @Test
    void testGenerateInvoiceFile_Success() {
        // Given
        when(invoiceRepository.findById(1L)).thenReturn(Optional.of(testInvoice));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));

        // When
        InvoiceService.InvoiceFileResponse result = invoiceService.generateInvoiceFile(1L, "testuser");

        // Then
        assertNotNull(result);
        assertNotNull(result.getResource());
        assertTrue(result.getFileName().contains("INV-001"));
        assertTrue(result.getFileName().endsWith(".pdf"));
        assertEquals("application/pdf", result.getContentType());
        verify(invoiceRepository).findById(1L);
        verify(userRepository).findByUsername("testuser");
    }

    @Test
    void testGenerateInvoiceFile_InvoiceNotFound() {
        // Given
        when(invoiceRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(Exception.class, () -> {
            invoiceService.generateInvoiceFile(1L, "testuser");
        });
        verify(invoiceRepository).findById(1L);
        verify(userRepository, never()).findByUsername(any());
    }

    @Test
    void testGenerateInvoiceFile_UserNotFound() {
        // Given
        when(invoiceRepository.findById(1L)).thenReturn(Optional.of(testInvoice));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.empty());

        // When & Then
        assertThrows(Exception.class, () -> {
            invoiceService.generateInvoiceFile(1L, "testuser");
        });
        verify(invoiceRepository).findById(1L);
        verify(userRepository).findByUsername("testuser");
    }

    @Test
    void testGenerateInvoiceFile_WithoutInvoiceNumber() {
        // Given
        testInvoice.setInvoiceNumber(null);
        when(invoiceRepository.findById(1L)).thenReturn(Optional.of(testInvoice));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));

        // When
        InvoiceService.InvoiceFileResponse result = invoiceService.generateInvoiceFile(1L, "testuser");

        // Then
        assertNotNull(result);
        assertTrue(result.getFileName().contains("Invoice_1"));
        assertTrue(result.getFileName().endsWith(".pdf"));
        assertEquals("application/pdf", result.getContentType());
    }
}
