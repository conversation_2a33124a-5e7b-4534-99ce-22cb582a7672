# Secure Tracking API Documentation

## Overview

The Secure Tracking API provides a two-step verification system for accessing shipment tracking information. This replaces the previous unsecured tracking endpoint and ensures that only authorized users can access tracking details.

## Security Features

- **Two-step verification**: Users must verify their identity via email or SMS before accessing tracking information
- **Rate limiting**: Prevents abuse with configurable limits per contact and IP address
- **Token-based access**: Secure tokens with configurable expiration times
- **Audit logging**: Comprehensive logging of all tracking access attempts
- **IP monitoring**: Tracks and monitors suspicious activity patterns

## API Endpoints

### 1. Request Tracking Verification

**Endpoint:** `POST /api/public/tracking/request-verification`

**Description:** Initiates the secure tracking process by sending a verification code or link to the user.

**Request Body:**
```json
{
  "trackingNumber": "LP123456789",
  "contact": "<EMAIL>",
  "verificationMethod": "EMAIL"
}
```

**Request Parameters:**
- `trackingNumber` (string, required): The shipment tracking number (max 20 characters)
- `contact` (string, required): Email address or phone number for verification (max 100 characters)
- `verificationMethod` (string, required): Either "EMAIL" or "SMS"

**Response (Success - 200 OK):**
```json
{
  "message": "Verification sent successfully",
  "token": "abc123def456...",
  "requiresCode": false,
  "expiryMinutes": 1440,
  "maskedContact": "us***@example.com"
}
```

**Response Parameters:**
- `message`: Success message
- `token`: Verification token (for email method, this is the access token; for SMS, this is used for code verification)
- `requiresCode`: Boolean indicating if a verification code is required (true for SMS, false for email)
- `expiryMinutes`: Token expiration time in minutes (1440 for email, 15 for SMS)
- `maskedContact`: Masked version of the contact information for privacy

**Error Responses:**
- `400 Bad Request`: Invalid input data or rate limit exceeded
- `404 Not Found`: Tracking number not found
- `500 Internal Server Error`: Server error

### 2. Verify SMS Code

**Endpoint:** `POST /api/public/tracking/verify-code`

**Description:** Verifies the SMS code and returns an access token for tracking information.

**Request Body:**
```json
{
  "trackingNumber": "LP123456789",
  "verificationCode": "123456"
}
```

**Request Parameters:**
- `trackingNumber` (string, required): The shipment tracking number
- `verificationCode` (string, required): 6-digit verification code received via SMS

**Response (Success - 200 OK):**
```json
{
  "message": "Verification successful",
  "token": "verified_abc123def456...",
  "validHours": 24
}
```

**Response Parameters:**
- `message`: Success message
- `token`: Access token for retrieving tracking details
- `validHours`: Token validity period in hours

**Error Responses:**
- `400 Bad Request`: Invalid verification code or expired code
- `404 Not Found`: Tracking number not found
- `500 Internal Server Error`: Server error

### 3. Get Secure Tracking Details

**Endpoint:** `GET /api/public/tracking/details?token={access_token}`

**Description:** Retrieves detailed tracking information using a verified access token.

**Query Parameters:**
- `token` (string, required): Valid access token obtained from verification process

**Response (Success - 200 OK):**
```json
{
  "id": 1,
  "trackingNumber": "LP123456789",
  "title": "Electronics Shipment",
  "description": "Laptop computers and accessories",
  "cargoType": "Electronics",
  "weight": 25.5,
  "weightUnit": "kg",
  "pickupLocation": "Harare, Zimbabwe",
  "deliveryLocation": "Bulawayo, Zimbabwe",
  "pickupDate": "2024-12-15T08:00:00",
  "deliveryDate": "2024-12-18T17:00:00",
  "estimatedDistance": 439.2,
  "distanceUnit": "km",
  "status": "IN_TRANSIT",
  "priority": "NORMAL",
  "isVerified": true,
  "requiresInsurance": true,
  "specialInstructions": "Handle with care - fragile items",
  "createdAt": "2024-12-14T10:30:00",
  "updatedAt": "2024-12-16T14:22:00",
  "clientName": "Tech Solutions Ltd",
  "assignedCompanyName": "Swift Logistics"
}
```

**Error Responses:**
- `400 Bad Request`: Invalid or expired token, or maximum access limit reached
- `404 Not Found`: Tracking information not found
- `500 Internal Server Error`: Server error

## Rate Limiting

The API implements multiple layers of rate limiting to prevent abuse:

### Contact-based Rate Limiting
- **Limit**: 3 verification requests per hour per tracking number and contact combination
- **Scope**: Prevents spam to specific email addresses or phone numbers

### IP-based Rate Limiting
- **Limit**: 10 verification requests per hour per IP address
- **Scope**: Prevents abuse from specific locations

### Access Count Limiting
- **Limit**: 5 tracking detail accesses per verification token
- **Scope**: Prevents excessive use of a single verification

## Security Considerations

### Token Security
- Tokens are cryptographically secure and include timestamps
- Email tokens are valid for 24 hours
- SMS verification codes are valid for 15 minutes
- Tokens become invalid after maximum access count is reached

### Data Privacy
- Contact information is masked in responses
- Sensitive shipment details are only available through verified access
- All access attempts are logged for security monitoring

### Monitoring and Alerts
- Suspicious activity patterns are automatically detected
- High-frequency requests trigger additional security measures
- Failed verification attempts are logged and monitored

## Usage Examples

### Email Verification Flow

1. **Request verification:**
```bash
curl -X POST "https://api.logipool.com/api/public/tracking/request-verification" \
  -H "Content-Type: application/json" \
  -d '{
    "trackingNumber": "LP123456789",
    "contact": "<EMAIL>",
    "verificationMethod": "EMAIL"
  }'
```

2. **User receives email with secure link**

3. **Access tracking details using token from email:**
```bash
curl "https://api.logipool.com/api/public/tracking/details?token=abc123def456..."
```

### SMS Verification Flow

1. **Request verification:**
```bash
curl -X POST "https://api.logipool.com/api/public/tracking/request-verification" \
  -H "Content-Type: application/json" \
  -d '{
    "trackingNumber": "LP123456789",
    "contact": "+263771234567",
    "verificationMethod": "SMS"
  }'
```

2. **Verify SMS code:**
```bash
curl -X POST "https://api.logipool.com/api/public/tracking/verify-code" \
  -H "Content-Type: application/json" \
  -d '{
    "trackingNumber": "LP123456789",
    "verificationCode": "123456"
  }'
```

3. **Access tracking details using verified token:**
```bash
curl "https://api.logipool.com/api/public/tracking/details?token=verified_abc123def456..."
```

## Migration from Legacy API

The legacy tracking endpoint (`GET /api/public/track/{trackingNumber}`) is deprecated and returns limited information. It will be removed in a future version. Please migrate to the secure tracking API for full functionality.

## Error Handling

All endpoints return consistent error responses:

```json
{
  "error": "RATE_LIMIT_EXCEEDED",
  "message": "Too many verification requests. Please try again later.",
  "timestamp": "2024-12-16T15:30:00Z"
}
```

Common error codes:
- `INVALID_TRACKING_NUMBER`: Tracking number not found
- `INVALID_CONTACT`: Invalid email or phone number format
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INVALID_VERIFICATION_CODE`: Wrong or expired verification code
- `INVALID_TOKEN`: Invalid or expired access token
- `MAX_ACCESS_EXCEEDED`: Maximum access limit reached for token
