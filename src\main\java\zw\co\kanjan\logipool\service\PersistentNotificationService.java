package zw.co.kanjan.logipool.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.NotificationDto;
import zw.co.kanjan.logipool.entity.Notification;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.repository.NotificationRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PersistentNotificationService {
    
    private final NotificationRepository notificationRepository;
    private final UserRepository userRepository;
    private final ObjectMapper objectMapper;
    
    @Transactional
    public Notification createNotification(Long userId, String title, String message, 
                                         String type, String priority, Map<String, Object> data,
                                         Long referenceId, String referenceType, LocalDateTime expiresAt) {
        
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with id: " + userId));
        
        String dataJson = null;
        if (data != null && !data.isEmpty()) {
            try {
                dataJson = objectMapper.writeValueAsString(data);
            } catch (JsonProcessingException e) {
                log.error("Failed to serialize notification data", e);
            }
        }
        
        Notification notification = Notification.builder()
                .user(user)
                .title(title)
                .message(message)
                .type(type)
                .priority(priority != null ? priority : "MEDIUM")
                .data(dataJson)
                .referenceId(referenceId)
                .referenceType(referenceType)
                .expiresAt(expiresAt)
                .read(false)
                .build();
        
        return notificationRepository.save(notification);
    }
    
    public Page<NotificationDto> getUserNotifications(Long userId, Pageable pageable) {
        Page<Notification> notifications = notificationRepository.findByUserIdOrderByCreatedAtDesc(userId, pageable);
        return notifications.map(this::convertToDto);
    }
    
    public List<NotificationDto> getUnreadNotifications(Long userId) {
        List<Notification> notifications = notificationRepository.findByUserIdAndReadFalseOrderByCreatedAtDesc(userId);
        return notifications.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
    
    public long getUnreadCount(Long userId) {
        return notificationRepository.countByUserIdAndReadFalse(userId);
    }
    
    @Transactional
    public void markAsRead(Long notificationId) {
        notificationRepository.markAsRead(notificationId, LocalDateTime.now());
    }
    
    @Transactional
    public void markAllAsRead(Long userId) {
        notificationRepository.markAllAsReadForUser(userId, LocalDateTime.now());
    }
    
    @Transactional
    public void deleteNotification(Long notificationId) {
        notificationRepository.deleteById(notificationId);
    }
    
    public List<NotificationDto> getNotificationsByType(Long userId, String type) {
        List<Notification> notifications = notificationRepository.findByUserIdAndType(userId, type);
        return notifications.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
    
    public List<NotificationDto> getUrgentNotifications(Long userId) {
        List<Notification> notifications = notificationRepository.findUnreadByUserIdAndPriority(userId, "URGENT");
        return notifications.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }
    
    @Scheduled(fixedRate = 3600000) // Run every hour
    @Transactional
    public void cleanupExpiredNotifications() {
        try {
            notificationRepository.deleteExpiredNotifications(LocalDateTime.now());
            log.debug("Cleaned up expired notifications");
        } catch (Exception e) {
            log.error("Failed to cleanup expired notifications", e);
        }
    }
    
    private NotificationDto convertToDto(Notification notification) {
        Map<String, Object> data = null;
        if (notification.getData() != null) {
            try {
                data = objectMapper.readValue(notification.getData(), Map.class);
            } catch (JsonProcessingException e) {
                log.error("Failed to deserialize notification data", e);
            }
        }
        
        return NotificationDto.builder()
                .title(notification.getTitle())
                .message(notification.getMessage())
                .type(notification.getType())
                .timestamp(notification.getCreatedAt())
                .data(data)
                .read(notification.isRead())
                .priority(notification.getPriority())
                .build();
    }
    
    // Convenience methods for common notification types
    public Notification createLoadPostedNotification(Long userId, String loadTitle, String location, Long loadId) {
        Map<String, Object> data = Map.of(
            "loadTitle", loadTitle,
            "location", location,
            "loadId", loadId
        );
        
        return createNotification(
            userId,
            "New Load Available",
            String.format("A new load '%s' is available near %s", loadTitle, location),
            "LOAD_POSTED",
            "MEDIUM",
            data,
            loadId,
            "LOAD",
            LocalDateTime.now().plusDays(7)
        );
    }
    
    public Notification createBidReceivedNotification(Long userId, String companyName, String bidAmount, 
                                                    String loadTitle, Long bidId, Long loadId) {
        Map<String, Object> data = Map.of(
            "companyName", companyName,
            "bidAmount", bidAmount,
            "loadTitle", loadTitle,
            "bidId", bidId,
            "loadId", loadId
        );
        
        return createNotification(
            userId,
            "New Bid Received",
            String.format("You received a bid of $%s from %s for '%s'", bidAmount, companyName, loadTitle),
            "BID_RECEIVED",
            "HIGH",
            data,
            bidId,
            "BID",
            LocalDateTime.now().plusDays(30)
        );
    }
    
    public Notification createBidAcceptedNotification(Long userId, String loadTitle, String bidAmount, Long bidId) {
        Map<String, Object> data = Map.of(
            "loadTitle", loadTitle,
            "bidAmount", bidAmount,
            "bidId", bidId
        );
        
        return createNotification(
            userId,
            "Bid Accepted!",
            String.format("Congratulations! Your bid of $%s for '%s' has been accepted", bidAmount, loadTitle),
            "BID_ACCEPTED",
            "HIGH",
            data,
            bidId,
            "BID",
            null // No expiration for important notifications
        );
    }
}
