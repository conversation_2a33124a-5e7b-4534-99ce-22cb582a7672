import 'package:flutter/material.dart';

class FinancialOverviewCard extends StatelessWidget {
  final double totalRevenue;
  final double totalExpenses;
  final double netProfit;
  final double pendingPayments;
  final int totalInvoices;
  final int paidInvoices;
  final int overdueInvoices;

  const FinancialOverviewCard({
    super.key,
    required this.totalRevenue,
    required this.totalExpenses,
    required this.netProfit,
    required this.pendingPayments,
    required this.totalInvoices,
    required this.paidInvoices,
    required this.overdueInvoices,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: theme.primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Financial Overview',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildFinancialMetrics(context),
            const SizedBox(height: 16),
            _buildInvoiceMetrics(context),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialMetrics(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildMetricItem(
                context,
                'Total Revenue',
                '\$${totalRevenue.toStringAsFixed(2)}',
                Icons.trending_up,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildMetricItem(
                context,
                'Total Expenses',
                '\$${totalExpenses.toStringAsFixed(2)}',
                Icons.trending_down,
                Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildMetricItem(
                context,
                'Net Profit',
                '\$${netProfit.toStringAsFixed(2)}',
                Icons.account_balance_wallet,
                netProfit >= 0 ? Colors.green : Colors.red,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildMetricItem(
                context,
                'Pending Payments',
                '\$${pendingPayments.toStringAsFixed(2)}',
                Icons.pending_actions,
                Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInvoiceMetrics(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildMetricItem(
            context,
            'Total Invoices',
            totalInvoices.toString(),
            Icons.receipt,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildMetricItem(
            context,
            'Paid',
            paidInvoices.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildMetricItem(
            context,
            'Overdue',
            overdueInvoices.toString(),
            Icons.warning,
            Colors.red,
          ),
        ),
      ],
    );
  }

  Widget _buildMetricItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
