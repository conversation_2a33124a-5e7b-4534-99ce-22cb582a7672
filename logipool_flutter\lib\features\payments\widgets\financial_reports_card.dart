import 'package:flutter/material.dart';

class FinancialReportsCard extends StatelessWidget {
  final VoidCallback? onGenerateReport;
  final VoidCallback? onViewReports;
  final List<FinancialReport>? recentReports;

  const FinancialReportsCard({
    super.key,
    this.onGenerateReport,
    this.onViewReports,
    this.recentReports,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.assessment,
                      color: theme.primaryColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Financial Reports',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'generate':
                        onGenerateReport?.call();
                        break;
                      case 'view_all':
                        onViewReports?.call();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'generate',
                      child: Row(
                        children: [
                          Icon(Icons.add_chart),
                          SizedBox(width: 8),
                          Text('Generate Report'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'view_all',
                      child: Row(
                        children: [
                          Icon(Icons.folder_open),
                          SizedBox(width: 8),
                          Text('View All Reports'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildQuickReports(context),
            const SizedBox(height: 16),
            if (recentReports?.isNotEmpty == true)
              _buildRecentReports(context)
            else
              _buildEmptyState(context),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickReports(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Reports',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickReportButton(
                context,
                'Monthly Revenue',
                Icons.trending_up,
                Colors.green,
                () => _generateQuickReport(context, 'monthly_revenue'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickReportButton(
                context,
                'Invoice Summary',
                Icons.receipt_long,
                Colors.blue,
                () => _generateQuickReport(context, 'invoice_summary'),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildQuickReportButton(
                context,
                'Client Analysis',
                Icons.people,
                Colors.orange,
                () => _generateQuickReport(context, 'client_analysis'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickReportButton(
                context,
                'Tax Report',
                Icons.account_balance,
                Colors.purple,
                () => _generateQuickReport(context, 'tax_report'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickReportButton(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: color.withOpacity(0.3)),
          borderRadius: BorderRadius.circular(8),
          color: color.withOpacity(0.05),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              title,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentReports(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Reports',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            TextButton(
              onPressed: onViewReports,
              child: const Text('View All'),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...recentReports!.take(3).map((report) {
          return _buildReportItem(context, report);
        }),
      ],
    );
  }

  Widget _buildReportItem(BuildContext context, FinancialReport report) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.description,
            color: theme.primaryColor,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  report.title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  'Generated on ${_formatDate(report.createdAt)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _downloadReport(context, report),
            icon: const Icon(Icons.download),
            iconSize: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.assessment,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No reports generated yet',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Generate your first financial report to track your business performance',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: onGenerateReport,
            icon: const Icon(Icons.add_chart),
            label: const Text('Generate Report'),
          ),
        ],
      ),
    );
  }

  void _generateQuickReport(BuildContext context, String reportType) {
    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Generating report...'),
          ],
        ),
      ),
    );

    // Simulate report generation
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('$reportType report generated successfully'),
          action: SnackBarAction(
            label: 'View',
            onPressed: () {
              // Navigate to report view
            },
          ),
        ),
      );
    });
  }

  void _downloadReport(BuildContext context, FinancialReport report) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Downloading ${report.title}...'),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// Simple model for financial reports
class FinancialReport {
  final String id;
  final String title;
  final String type;
  final DateTime createdAt;
  final String? filePath;

  const FinancialReport({
    required this.id,
    required this.title,
    required this.type,
    required this.createdAt,
    this.filePath,
  });
}
