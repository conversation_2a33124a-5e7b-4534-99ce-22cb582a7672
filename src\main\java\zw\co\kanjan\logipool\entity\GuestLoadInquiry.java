package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Entity
@Table(name = "guest_load_inquiries")
@Data
@EqualsAndHashCode(callSuper = false)
public class GuestLoadInquiry {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    // Contact Information
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(nullable = false, length = 100)
    private String email;
    
    @Column(nullable = false, length = 20)
    private String phoneNumber;
    
    @Column(length = 100)
    private String company;
    
    // Load Information
    @Column(nullable = false, length = 100)
    private String title;
    
    @Column(nullable = false, length = 1000)
    private String description;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private Load.LoadType loadType;
    
    @Column(nullable = false)
    private Double weight; // in kg
    
    @Column(length = 50)
    private String dimensions;
    
    // Location and Timing
    @Column(nullable = false, length = 200)
    private String pickupLocation;
    
    @Column(nullable = false, length = 200)
    private String deliveryLocation;
    
    @Column(nullable = false)
    private LocalDate pickupDate;
    
    private LocalDate deliveryDate;
    
    // Additional Options
    @Column(nullable = false)
    private Boolean requiresSpecialHandling = false;
    
    @Column(nullable = false)
    private Boolean isUrgent = false;
    
    @Column(length = 500)
    private String specialInstructions;
    
    // Status and Processing
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private InquiryStatus status = InquiryStatus.NEW;
    
    @Column(length = 500)
    private String adminNotes;
    
    @Column(name = "assigned_to")
    private Long assignedTo; // Admin user ID who is handling this inquiry
    
    // System fields
    @Column(length = 45)
    private String ipAddress;
    
    @Column(length = 500)
    private String userAgent;
    
    @CreationTimestamp
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(nullable = false)
    private LocalDateTime updatedAt;
    
    // Tracking
    private LocalDateTime processedAt;
    private LocalDateTime contactedAt;
    
    public enum InquiryStatus {
        NEW,           // Just submitted
        REVIEWED,      // Admin has reviewed
        CONTACTED,     // Customer has been contacted
        QUOTED,        // Quotes have been provided
        CONVERTED,     // Customer created account and posted load
        CLOSED         // Inquiry closed without conversion
    }
}
