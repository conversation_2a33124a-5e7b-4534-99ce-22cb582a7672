package zw.co.kanjan.logipool.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.regex.Pattern;

public class EmailValidator implements ConstraintValidator<ValidEmail, String> {
    
    private static final String EMAIL_PATTERN = 
        "^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$";
    
    private static final Pattern pattern = Pattern.compile(EMAIL_PATTERN);
    
    @Override
    public void initialize(ValidEmail constraintAnnotation) {
        // No initialization needed
    }
    
    @Override
    public boolean isValid(String email, ConstraintValidatorContext context) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        // Basic format validation
        if (!pattern.matcher(email).matches()) {
            return false;
        }
        
        // Additional checks
        if (email.length() > 254) { // RFC 5321 limit
            return false;
        }
        
        String[] parts = email.split("@");
        if (parts.length != 2) {
            return false;
        }
        
        String localPart = parts[0];
        String domainPart = parts[1];
        
        // Local part should not exceed 64 characters
        if (localPart.length() > 64) {
            return false;
        }
        
        // Domain part should not exceed 253 characters
        if (domainPart.length() > 253) {
            return false;
        }
        
        // Check for consecutive dots
        if (email.contains("..")) {
            return false;
        }
        
        // Check for leading/trailing dots in local part
        if (localPart.startsWith(".") || localPart.endsWith(".")) {
            return false;
        }
        
        return true;
    }
}
