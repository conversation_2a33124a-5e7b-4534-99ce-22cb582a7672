import 'package:flutter/material.dart';
import '../../../shared/widgets/public_footer.dart';

class FaqScreen extends StatefulWidget {
  const FaqScreen({super.key});

  @override
  State<FaqScreen> createState() => _FaqScreenState();
}

class _FaqScreenState extends State<FaqScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedCategory = 'All';

  final List<String> _categories = [
    'All',
    'General',
    'Booking',
    'Tracking',
    'Payments',
    'Support'
  ];

  final List<Map<String, dynamic>> _faqs = [
    {
      'category': 'General',
      'question': 'What is LogiPool?',
      'answer': 'LogiPool is a comprehensive logistics marketplace that connects businesses with verified logistics service providers. We offer a platform for freight transportation, real-time tracking, and secure logistics solutions.',
    },
    {
      'category': 'General',
      'question': 'How does LogiPool work?',
      'answer': 'Simply post your transportation requirements, receive bids from verified logistics companies, compare prices and services, select the best option, and track your shipment in real-time.',
    },
    {
      'category': 'Booking',
      'question': 'How do I book a shipment?',
      'answer': 'Create an account, post your load requirements including pickup and delivery locations, weight, and timeline. Logistics companies will bid on your shipment, and you can choose the best offer.',
    },
    {
      'category': 'Booking',
      'question': 'What information do I need to provide when booking?',
      'answer': 'You need to provide pickup and delivery addresses, cargo details (type, weight, dimensions), preferred pickup date, any special requirements, and contact information.',
    },
    {
      'category': 'Tracking',
      'question': 'How can I track my shipment?',
      'answer': 'Use your tracking number on our tracking page to get real-time updates on your shipment\'s location and status. You\'ll also receive SMS and email notifications.',
    },
    {
      'category': 'Tracking',
      'question': 'What if my tracking number doesn\'t work?',
      'answer': 'Ensure you\'ve entered the correct tracking number. If the issue persists, contact our support team with your order details for assistance.',
    },
    {
      'category': 'Payments',
      'question': 'What payment methods do you accept?',
      'answer': 'We accept major credit cards, bank transfers, and mobile money payments. Payment is typically made after successful delivery confirmation.',
    },
    {
      'category': 'Payments',
      'question': 'When do I need to pay?',
      'answer': 'Payment is usually required after successful delivery and confirmation. Some services may require partial payment upfront for high-value shipments.',
    },
    {
      'category': 'Support',
      'question': 'How can I contact customer support?',
      'answer': 'You can reach our support team through the contact form, WhatsApp chat, <NAME_EMAIL>, or phone during business hours.',
    },
    {
      'category': 'Support',
      'question': 'What are your business hours?',
      'answer': 'Our customer support is available Monday to Friday 8:00 AM - 6:00 PM, and Saturday 9:00 AM - 2:00 PM. Emergency support is available 24/7.',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildHeader(context),
          _buildSearchAndFilter(context),
          _buildFaqList(context),
          _buildContactSection(context),
          const PublicFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background image
          Positioned.fill(
            child: Image.asset(
              'assets/images/pexels-apasaric-1238864.jpg',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
          ),
          // Overlay
          Positioned.fill(
            child: Container(
              color: Theme.of(context).primaryColor.withOpacity(0.7),
            ),
          ),
          // Content
          Positioned.fill(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 64),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.help_center,
                    size: 64,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Frequently Asked Questions',
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 48,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Find answers to common questions about LogiPool services',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white70,
                      fontSize: 18,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Theme.of(context).primaryColor.withOpacity(0.05),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search FAQs...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  setState(() {
                    _searchQuery = '';
                  });
                },
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
            },
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: _categories.map((category) {
                final isSelected = _selectedCategory == category;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(category),
                    selected: isSelected,
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                    backgroundColor: Colors.white,
                    selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFaqList(BuildContext context) {
    final filteredFaqs = _getFilteredFaqs();

    if (filteredFaqs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No FAQs found',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filter criteria',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: filteredFaqs.map((faq) => _buildFaqItem(context, faq)).toList(),
      ),
    );
  }

  Widget _buildFaqItem(BuildContext context, Map<String, dynamic> faq) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: ExpansionTile(
        title: Text(
          faq['question'],
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(top: 4),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              faq['category'],
              style: TextStyle(
                fontSize: 10,
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              faq['answer'],
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      color: Theme.of(context).primaryColor.withOpacity(0.05),
      child: Column(
        children: [
          Text(
            'Still have questions?',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Our support team is here to help you',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: () => Navigator.pushNamed(context, '/contact'),
                icon: const Icon(Icons.contact_support),
                label: const Text('Contact Support'),
              ),
              const SizedBox(width: 16),
              OutlinedButton.icon(
                onPressed: () => _launchWhatsApp(),
                icon: const Icon(Icons.chat),
                label: const Text('WhatsApp'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredFaqs() {
    return _faqs.where((faq) {
      bool matchesCategory = _selectedCategory == 'All' || faq['category'] == _selectedCategory;
      bool matchesSearch = _searchQuery.isEmpty ||
          faq['question'].toLowerCase().contains(_searchQuery.toLowerCase()) ||
          faq['answer'].toLowerCase().contains(_searchQuery.toLowerCase());
      
      return matchesCategory && matchesSearch;
    }).toList();
  }

  void _launchWhatsApp() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Opening WhatsApp...')),
    );
  }
}
