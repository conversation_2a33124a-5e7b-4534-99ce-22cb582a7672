// Re-export all exception classes
export 'api_exception.dart';

// Base exception class
abstract class AppException implements Exception {
  final String message;
  final String? code;
  
  const AppException(this.message, {this.code});
  
  @override
  String toString() => message;
}

// Network related exceptions
class NetworkException extends AppException {
  const NetworkException(String message, {String? code}) : super(message, code: code);
}

class TimeoutException extends AppException {
  const TimeoutException(String message, {String? code}) : super(message, code: code);
}

// Authentication exceptions
class AuthenticationException extends AppException {
  const AuthenticationException(String message, {String? code}) : super(message, code: code);
}

class AuthorizationException extends AppException {
  const AuthorizationException(String message, {String? code}) : super(message, code: code);
}

// Validation exceptions
class ValidationException extends AppException {
  final Map<String, List<String>>? errors;
  
  const ValidationException(String message, {this.errors, String? code}) : super(message, code: code);
}

// Server exceptions
class ServerException extends AppException {
  const ServerException(String message, {String? code}) : super(message, code: code);
}

// Cache exceptions
class CacheException extends AppException {
  const CacheException(String message, {String? code}) : super(message, code: code);
}

// File handling exceptions
class FileException extends AppException {
  const FileException(String message, {String? code}) : super(message, code: code);
}

// Location exceptions
class LocationException extends AppException {
  const LocationException(String message, {String? code}) : super(message, code: code);
}
