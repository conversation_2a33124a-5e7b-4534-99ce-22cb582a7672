package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class EmailTemplateService {
    
    public String processTemplate(String templateName, Map<String, Object> variables) {
        try {
            String template = loadTemplate(templateName);
            return replaceVariables(template, variables);
        } catch (Exception e) {
            log.error("Error processing email template: {}", templateName, e);
            return generateFallbackContent(templateName, variables);
        }
    }
    
    private String loadTemplate(String templateName) throws IOException {
        String templatePath = "templates/email/" + templateName + ".html";
        ClassPathResource resource = new ClassPathResource(templatePath);
        
        if (!resource.exists()) {
            throw new IOException("Template not found: " + templatePath);
        }
        
        return new String(resource.getInputStream().readAllBytes(), StandardCharsets.UTF_8);
    }
    
    private String replaceVariables(String template, Map<String, Object> variables) {
        String result = template;
        
        for (Map.Entry<String, Object> entry : variables.entrySet()) {
            String placeholder = "{{" + entry.getKey() + "}}";
            String value = entry.getValue() != null ? entry.getValue().toString() : "";
            result = result.replace(placeholder, value);
        }
        
        return result;
    }
    
    private String generateFallbackContent(String templateName, Map<String, Object> variables) {
        StringBuilder content = new StringBuilder();
        content.append("<html><body>");
        content.append("<h2>LogiPool Notification</h2>");
        
        switch (templateName) {
            case "load-posted":
                content.append("<p>A new load has been posted that may interest you.</p>");
                break;
            case "bid-received":
                content.append("<p>You have received a new bid for your load.</p>");
                break;
            case "bid-accepted":
                content.append("<p>Congratulations! Your bid has been accepted.</p>");
                break;
            case "bid-rejected":
                content.append("<p>Your bid was not selected for this load.</p>");
                break;
            case "load-status-update":
                content.append("<p>Your load status has been updated.</p>");
                break;
            case "password-reset":
                content.append("<p>You have requested to reset your password.</p>");
                content.append("<p>Please use the link provided to reset your password.</p>");
                break;
            default:
                content.append("<p>You have a new notification from LogiPool.</p>");
        }
        
        content.append("<p>Please log in to your LogiPool account for more details.</p>");
        content.append("<p>Best regards,<br>The LogiPool Team</p>");
        content.append("</body></html>");
        
        return content.toString();
    }
    
    public Map<String, Object> createLoadPostedVariables(
            String transporterName, String loadTitle, String pickupLocation,
            String deliveryLocation, String weight, String pickupDate,
            String deliveryDate, String loadType, String description,
            String bidUrl, String unsubscribeUrl, String supportUrl) {
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("transporterName", transporterName);
        variables.put("loadTitle", loadTitle);
        variables.put("pickupLocation", pickupLocation);
        variables.put("deliveryLocation", deliveryLocation);
        variables.put("weight", weight);
        variables.put("pickupDate", pickupDate);
        variables.put("deliveryDate", deliveryDate);
        variables.put("loadType", loadType);
        variables.put("description", description);
        variables.put("bidUrl", bidUrl);
        variables.put("unsubscribeUrl", unsubscribeUrl);
        variables.put("supportUrl", supportUrl);
        return variables;
    }
    
    public Map<String, Object> createBidReceivedVariables(
            String clientName, String loadTitle, String bidAmount,
            String companyName, String companyRating, String reviewCount,
            String verificationStatus, String estimatedDelivery, String bidDate,
            String bidMessage, String acceptBidUrl, String viewBidsUrl,
            String unsubscribeUrl, String supportUrl) {
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("clientName", clientName);
        variables.put("loadTitle", loadTitle);
        variables.put("bidAmount", bidAmount);
        variables.put("companyName", companyName);
        variables.put("companyRating", companyRating);
        variables.put("reviewCount", reviewCount);
        variables.put("verificationStatus", verificationStatus);
        variables.put("estimatedDelivery", estimatedDelivery);
        variables.put("bidDate", bidDate);
        variables.put("bidMessage", bidMessage);
        variables.put("acceptBidUrl", acceptBidUrl);
        variables.put("viewBidsUrl", viewBidsUrl);
        variables.put("unsubscribeUrl", unsubscribeUrl);
        variables.put("supportUrl", supportUrl);
        return variables;
    }
    
    public Map<String, Object> createBidAcceptedVariables(
            String transporterName, String loadTitle, String bidAmount,
            String clientName, String pickupLocation, String deliveryLocation,
            String pickupDate, String deliveryDate, String weight,
            String clientEmail, String clientPhone, String jobDetailsUrl,
            String contactClientUrl, String supportUrl) {
        
        Map<String, Object> variables = new HashMap<>();
        variables.put("transporterName", transporterName);
        variables.put("loadTitle", loadTitle);
        variables.put("bidAmount", bidAmount);
        variables.put("clientName", clientName);
        variables.put("pickupLocation", pickupLocation);
        variables.put("deliveryLocation", deliveryLocation);
        variables.put("pickupDate", pickupDate);
        variables.put("deliveryDate", deliveryDate);
        variables.put("weight", weight);
        variables.put("clientEmail", clientEmail);
        variables.put("clientPhone", clientPhone);
        variables.put("jobDetailsUrl", jobDetailsUrl);
        variables.put("contactClientUrl", contactClientUrl);
        variables.put("supportUrl", supportUrl);
        return variables;
    }

    public Map<String, Object> createPasswordResetVariables(
            String userName, String resetUrl, String expiryHours,
            String supportUrl, String securityUrl) {

        Map<String, Object> variables = new HashMap<>();
        variables.put("userName", userName);
        variables.put("resetUrl", resetUrl);
        variables.put("expiryHours", expiryHours);
        variables.put("supportUrl", supportUrl);
        variables.put("securityUrl", securityUrl);
        return variables;
    }

    public Map<String, Object> createTrackingVerificationVariables(String trackingNumber, String loadTitle,
                                                                  String pickupLocation, String deliveryLocation,
                                                                  String trackingUrl, String expiryHours,
                                                                  String supportUrl) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("trackingNumber", trackingNumber);
        variables.put("loadTitle", loadTitle);
        variables.put("pickupLocation", pickupLocation);
        variables.put("deliveryLocation", deliveryLocation);
        variables.put("trackingUrl", trackingUrl);
        variables.put("expiryHours", expiryHours);
        variables.put("supportUrl", supportUrl);
        variables.put("currentYear", String.valueOf(java.time.Year.now().getValue()));
        return variables;
    }
}
