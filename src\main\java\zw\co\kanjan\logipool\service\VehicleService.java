package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import zw.co.kanjan.logipool.dto.VehicleDto;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.entity.Vehicle;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.exception.UnauthorizedException;
import zw.co.kanjan.logipool.exception.ValidationException;
import zw.co.kanjan.logipool.mapper.VehicleMapper;
import zw.co.kanjan.logipool.repository.CompanyRepository;
import zw.co.kanjan.logipool.repository.UserRepository;
import zw.co.kanjan.logipool.repository.VehicleRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class VehicleService {

    private final VehicleRepository vehicleRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final VehicleMapper vehicleMapper;
    private final FileStorageService fileStorageService;
    private final NotificationService notificationService;

    public VehicleDto.VehicleResponse createVehicle(VehicleDto.VehicleCreateRequest request, String username) {
        log.info("Creating vehicle for user: {}", username);
        
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        // Check if registration number already exists
        if (vehicleRepository.existsByRegistrationNumber(request.getRegistrationNumber())) {
            throw new ValidationException("Vehicle with registration number " + request.getRegistrationNumber() + " already exists");
        }
        
        Vehicle vehicle = vehicleMapper.toEntity(request);
        vehicle.setCompany(company);
        
        Vehicle savedVehicle = vehicleRepository.save(vehicle);
        log.info("Vehicle created with ID: {} for company: {}", savedVehicle.getId(), company.getName());
        
        return vehicleMapper.toResponse(savedVehicle);
    }

    @Transactional(readOnly = true)
    public Page<VehicleDto.VehicleResponse> getCompanyVehicles(
            String username, 
            Pageable pageable, 
            Vehicle.VehicleStatus status, 
            Vehicle.VehicleType type) {
        
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Page<Vehicle> vehicles;
        
        if (status != null && type != null) {
            vehicles = vehicleRepository.findByCompanyAndStatusAndType(company, status, type, pageable);
        } else if (status != null) {
            vehicles = vehicleRepository.findByCompanyAndStatus(company, status, pageable);
        } else if (type != null) {
            vehicles = vehicleRepository.findByCompanyAndType(company, type, pageable);
        } else {
            vehicles = vehicleRepository.findByCompany(company, pageable);
        }
        
        return vehicles.map(vehicleMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public VehicleDto.VehicleResponse getVehicle(Long vehicleId, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with ID: " + vehicleId));
        
        // Check if vehicle belongs to user's company
        if (!vehicle.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to access this vehicle");
        }
        
        return vehicleMapper.toResponse(vehicle);
    }

    public VehicleDto.VehicleResponse updateVehicle(Long vehicleId, VehicleDto.VehicleUpdateRequest request, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with ID: " + vehicleId));
        
        // Check if vehicle belongs to user's company
        if (!vehicle.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to update this vehicle");
        }
        
        vehicleMapper.updateEntity(request, vehicle);
        Vehicle updatedVehicle = vehicleRepository.save(vehicle);
        
        log.info("Vehicle {} updated by user: {}", vehicleId, username);
        return vehicleMapper.toResponse(updatedVehicle);
    }

    public void deleteVehicle(Long vehicleId, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with ID: " + vehicleId));
        
        // Check if vehicle belongs to user's company
        if (!vehicle.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to delete this vehicle");
        }
        
        // Check if vehicle is currently in use
        if (vehicle.getStatus() == Vehicle.VehicleStatus.IN_USE) {
            throw new ValidationException("Cannot delete vehicle that is currently in use");
        }
        
        vehicleRepository.delete(vehicle);
        log.info("Vehicle {} deleted by user: {}", vehicleId, username);
    }

    public String uploadVehicleImage(Long vehicleId, MultipartFile file, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with ID: " + vehicleId));
        
        // Check if vehicle belongs to user's company
        if (!vehicle.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to upload image for this vehicle");
        }
        
        String imageUrl = fileStorageService.storeFile(file, "vehicles");
        vehicle.setImageUrl(imageUrl);
        vehicleRepository.save(vehicle);
        
        log.info("Image uploaded for vehicle {} by user: {}", vehicleId, username);
        return imageUrl;
    }

    public void requestPublicApproval(Long vehicleId, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with ID: " + vehicleId));
        
        // Check if vehicle belongs to user's company
        if (!vehicle.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to request approval for this vehicle");
        }
        
        // Check if company is verified
        if (company.getVerificationStatus() != Company.VerificationStatus.VERIFIED) {
            throw new ValidationException("Company must be verified before requesting public approval for vehicles");
        }
        
        // Check if vehicle has required information
        if (vehicle.getImageUrl() == null || vehicle.getDescription() == null || vehicle.getDescription().trim().isEmpty()) {
            throw new ValidationException("Vehicle must have an image and description before requesting public approval");
        }
        
        vehicle.setPublicApprovalStatus(Vehicle.PublicApprovalStatus.PENDING);
        vehicleRepository.save(vehicle);
        
        // Notify admins about pending approval
        notificationService.notifyAdminsAboutPendingVehicleApproval(vehicle);
        
        log.info("Public approval requested for vehicle {} by user: {}", vehicleId, username);
    }

    public void togglePublicVisibility(Long vehicleId, boolean enable, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with ID: " + vehicleId));
        
        // Check if vehicle belongs to user's company
        if (!vehicle.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to modify this vehicle");
        }
        
        // Check if vehicle is approved for public visibility
        if (enable && vehicle.getPublicApprovalStatus() != Vehicle.PublicApprovalStatus.APPROVED) {
            throw new ValidationException("Vehicle must be approved before enabling public visibility");
        }
        
        vehicle.setIsPubliclyVisible(enable);
        vehicleRepository.save(vehicle);
        
        log.info("Public visibility for vehicle {} set to {} by user: {}", vehicleId, enable, username);
    }

    public void toggleRentalAvailability(Long vehicleId, boolean enable, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with ID: " + vehicleId));
        
        // Check if vehicle belongs to user's company
        if (!vehicle.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to modify this vehicle");
        }
        
        // Check if daily rate is set when enabling rental
        if (enable && vehicle.getDailyRate() == null) {
            throw new ValidationException("Daily rate must be set before enabling rental availability");
        }
        
        vehicle.setIsAvailableForRent(enable);
        vehicleRepository.save(vehicle);
        
        log.info("Rental availability for vehicle {} set to {} by user: {}", vehicleId, enable, username);
    }

    @Transactional(readOnly = true)
    public Vehicle.PublicApprovalStatus getApprovalStatus(Long vehicleId, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Vehicle vehicle = vehicleRepository.findById(vehicleId)
                .orElseThrow(() -> new ResourceNotFoundException("Vehicle not found with ID: " + vehicleId));
        
        // Check if vehicle belongs to user's company
        if (!vehicle.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to access this vehicle");
        }
        
        return vehicle.getPublicApprovalStatus();
    }

    private User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
    }

    private Company getCompanyByUser(User user) {
        return companyRepository.findByUser(user)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found for user: " + user.getUsername()));
    }
}
