# Guest Load Posting Feature

## Overview

The Guest Load Posting feature allows users to submit load requests without creating an account on the LogiPool platform. This feature makes the platform more accessible and user-friendly for potential customers who want to quickly post their shipping needs.

## Features Implemented

### 1. Enhanced Landing Page
- **Location**: `logipool_flutter/lib/features/public/screens/public_home_screen.dart`
- **Improvements**:
  - Added prominent "Post a Load" button in the hero section
  - Created dedicated guest load posting section with clear call-to-action
  - Integrated contact options (WhatsApp, phone, email, live chat)
  - Enhanced visual design with orange accent color for load posting

### 2. Guest Load Posting Form
- **Location**: `logipool_flutter/lib/features/public/screens/guest_load_posting_screen.dart`
- **Features**:
  - Comprehensive form with contact information, load details, and preferences
  - Form validation and error handling
  - Date pickers for pickup and delivery dates
  - Load type selection (General, Fragile, Hazardous, etc.)
  - Special handling options and urgent delivery flags
  - Alternative contact methods (WhatsApp, phone, email)
  - Success dialog with reference number

### 3. Contact Integration Components
- **Location**: `logipool_flutter/lib/shared/widgets/contact_integration_widget.dart`
- **Components**:
  - `ContactIntegrationWidget`: Reusable contact buttons
  - `QuickContactCard`: Card-style contact options
  - `FloatingContactButton`: Floating action button for specific contact types
  - Support for WhatsApp, phone, email, and live chat

### 4. Backend API Implementation
- **Guest Load Inquiry Entity**: `src/main/java/zw/co/kanjan/logipool/entity/GuestLoadInquiry.java`
- **Repository**: `src/main/java/zw/co/kanjan/logipool/repository/GuestLoadInquiryRepository.java`
- **Service**: `src/main/java/zw/co/kanjan/logipool/service/GuestLoadInquiryService.java`
- **Public Controller**: Updated `src/main/java/zw/co/kanjan/logipool/controller/PublicController.java`
- **Admin Controller**: `src/main/java/zw/co/kanjan/logipool/controller/AdminGuestInquiryController.java`

### 5. Enhanced Public Layout
- **Location**: `logipool_flutter/lib/shared/widgets/public_layout.dart`
- **Improvements**:
  - Dual floating action buttons (Post Load + WhatsApp)
  - Quick contact dialog in header
  - Enhanced footer with contact information
  - Improved contact accessibility

### 6. Updated Routing
- **Location**: `logipool_flutter/lib/core/utils/app_router.dart`
- **Changes**:
  - Added `/guest-load-posting` route
  - Updated public routes list

## API Endpoints

### Public Endpoints
- `POST /api/public/guest-load-inquiry` - Submit guest load inquiry
- `GET /api/public/guest-load-inquiry/{id}/status` - Check inquiry status (optional)

### Admin Endpoints (Requires ADMIN role)
- `GET /api/admin/guest-inquiries` - Get all inquiries (paginated)
- `GET /api/admin/guest-inquiries/status/{status}` - Get inquiries by status
- `GET /api/admin/guest-inquiries/{id}` - Get inquiry details
- `PUT /api/admin/guest-inquiries/{id}/status` - Update inquiry status
- `PUT /api/admin/guest-inquiries/{id}/assign` - Assign inquiry to admin
- `GET /api/admin/guest-inquiries/stats` - Get inquiry statistics

## Data Model

### GuestLoadInquiry Entity
```java
- Contact Information: name, email, phoneNumber, company
- Load Information: title, description, loadType, weight, dimensions
- Location & Timing: pickupLocation, deliveryLocation, pickupDate, deliveryDate
- Options: requiresSpecialHandling, isUrgent, specialInstructions
- Status: NEW, REVIEWED, CONTACTED, QUOTED, CONVERTED, CLOSED
- System: ipAddress, userAgent, createdAt, updatedAt
```

## Security Features

### Spam Prevention
- Maximum 3 inquiries per email per day
- 15-minute cooldown between inquiries from same email
- IP address and user agent tracking

### Data Validation
- Email format validation
- Required field validation
- Date validation (pickup date must be in future)
- Weight must be positive number

## Notification System

### Customer Notifications
- Confirmation email with inquiry details and reference number
- Contact information for follow-up

### Admin Notifications
- Email notifications to all admin users
- In-app notifications for online admins
- Detailed inquiry information for processing

## Contact Integration

### Supported Channels
1. **WhatsApp**: Direct link with pre-filled message
2. **Phone**: Direct dialing capability
3. **Email**: Pre-filled email with inquiry details
4. **Live Chat**: Tawk.to integration (placeholder for now)

### Contact Information
- Phone: +263 4 123 4567
- WhatsApp: +263 77 123 4567
- Email: <EMAIL>
- Business Hours: Mon-Fri 8AM-6PM, Sat 9AM-2PM

## User Experience Improvements

### Landing Page
- Clear value proposition for guest users
- Multiple entry points for load posting
- Prominent contact information
- Professional design with consistent branding

### Form Experience
- Progressive disclosure of form sections
- Clear field labels and help text
- Real-time validation feedback
- Success confirmation with next steps

### Mobile Responsiveness
- Responsive design for all screen sizes
- Touch-friendly buttons and inputs
- Optimized layout for mobile devices

## Future Enhancements

### Planned Features
1. **Load Tracking**: Allow guests to track their inquiry status
2. **Quote Comparison**: Show multiple quotes from different companies
3. **SMS Notifications**: Send SMS updates to customers
4. **File Uploads**: Allow customers to upload load images/documents
5. **Live Chat Integration**: Full Tawk.to or similar integration
6. **Analytics Dashboard**: Track conversion rates and inquiry sources

### Technical Improvements
1. **Rate Limiting**: Implement more sophisticated rate limiting
2. **Geolocation**: Auto-detect user location for pickup/delivery
3. **Load Templates**: Pre-filled forms for common load types
4. **Integration**: Connect with CRM systems for lead management

## Testing

### Manual Testing Checklist
- [ ] Form submission with valid data
- [ ] Form validation with invalid data
- [ ] Spam prevention (multiple submissions)
- [ ] Email notifications (customer and admin)
- [ ] Contact integration (WhatsApp, phone, email)
- [ ] Mobile responsiveness
- [ ] Admin inquiry management

### Automated Testing
- Unit tests for service layer
- Integration tests for API endpoints
- Widget tests for Flutter components
- End-to-end tests for complete flow

## Deployment Notes

### Database Migration
- New table: `guest_load_inquiries`
- Indexes on: email, status, createdAt, assignedTo

### Configuration
- Update email templates
- Configure notification settings
- Set up monitoring for inquiry volume
- Update contact information in constants

### Monitoring
- Track inquiry submission rates
- Monitor conversion from inquiry to account creation
- Alert on high inquiry volumes or errors
- Dashboard for admin inquiry management

## Support and Maintenance

### Admin Training
- How to process guest inquiries
- Status management workflow
- Customer communication best practices
- Conversion tracking and follow-up

### Customer Support
- FAQ updates for guest posting
- Help documentation
- Contact information updates
- Response time expectations

This feature significantly improves the accessibility of the LogiPool platform and provides a smooth onboarding experience for potential customers.
