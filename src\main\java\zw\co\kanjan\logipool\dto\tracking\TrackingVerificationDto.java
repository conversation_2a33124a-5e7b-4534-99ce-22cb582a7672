package zw.co.kanjan.logipool.dto.tracking;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.TrackingVerification;

public class TrackingVerificationDto {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerificationRequest {
        
        @NotBlank(message = "Tracking number is required")
        @Size(max = 20, message = "Tracking number must not exceed 20 characters")
        private String trackingNumber;
        
        @NotBlank(message = "Contact information is required")
        @Size(max = 100, message = "Contact information must not exceed 100 characters")
        private String contact;
        
        @NotBlank(message = "Verification method is required")
        @Pattern(regexp = "EMAIL|SMS", message = "Verification method must be EMAIL or SMS")
        private String verificationMethod;
        
        public TrackingVerification.VerificationMethod getVerificationMethodEnum() {
            return TrackingVerification.VerificationMethod.valueOf(verificationMethod);
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class VerificationResponse {
        
        private String message;
        private String token;
        private boolean requiresCode;
        private int expiryMinutes;
        private String maskedContact;
        
        public static VerificationResponse success(String token, boolean requiresCode, int expiryMinutes, String contact) {
            return VerificationResponse.builder()
                    .message("Verification sent successfully")
                    .token(token)
                    .requiresCode(requiresCode)
                    .expiryMinutes(expiryMinutes)
                    .maskedContact(maskContact(contact))
                    .build();
        }
        
        private static String maskContact(String contact) {
            if (contact == null || contact.length() < 3) {
                return "***";
            }
            
            if (contact.contains("@")) {
                // Email masking
                String[] parts = contact.split("@");
                String username = parts[0];
                String domain = parts[1];
                
                if (username.length() <= 2) {
                    return "**@" + domain;
                }
                
                return username.substring(0, 2) + "***@" + domain;
            } else {
                // Phone number masking
                if (contact.length() <= 4) {
                    return "***" + contact.substring(contact.length() - 1);
                }
                
                return "***" + contact.substring(contact.length() - 4);
            }
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CodeVerificationRequest {
        
        @NotBlank(message = "Tracking number is required")
        @Size(max = 20, message = "Tracking number must not exceed 20 characters")
        private String trackingNumber;
        
        @NotBlank(message = "Verification code is required")
        @Pattern(regexp = "\\d{6}", message = "Verification code must be 6 digits")
        private String verificationCode;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CodeVerificationResponse {
        
        private String message;
        private String token;
        private int validHours;
        
        public static CodeVerificationResponse success(String token, int validHours) {
            return CodeVerificationResponse.builder()
                    .message("Verification successful")
                    .token(token)
                    .validHours(validHours)
                    .build();
        }
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TrackingAccessRequest {
        
        @NotBlank(message = "Access token is required")
        private String token;
    }
}
