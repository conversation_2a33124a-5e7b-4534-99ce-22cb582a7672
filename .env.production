# LogiPool Production Environment Configuration for Traefik Deployment
# Copy this file to .env and fill in the actual values

# Domain Configuration
DOMAIN_NAME=api-logistics.kanjan.co.zw
ACME_EMAIL=<EMAIL>

# Database Configuration
DATABASE_NAME=logipool_prod
DATABASE_USERNAME=logipool
DATABASE_PASSWORD=SecureDbPass123!

# pgAdmin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=SecurePgAdminPass123!

# Redis Configuration
REDIS_PASSWORD=SecureRedisPass123!

# JWT Configuration
JWT_SECRET=YourVeryLongAndSecureJwtSecretKeyHereMinimum256BitsRecommendedForProductionUse123!
JWT_EXPIRATION=86400000
JWT_REFRESH_EXPIRATION=604800000

# File Upload Configuration
MAX_FILE_SIZE=50MB
MAX_REQUEST_SIZE=50MB

# Cloud Storage Configuration (AWS S3)
CLOUD_STORAGE_ENABLED=true
CLOUD_STORAGE_PROVIDER=aws
S3_BUCKET_NAME=logipool-documents-prod
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key

# Email Configuration
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password_here

# SMS Configuration (Twilio)
SMS_PROVIDER=twilio
SMS_ENABLED=true
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=+**********

# Payment Configuration (Stripe)
STRIPE_ENABLED=true
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_PUBLIC_KEY=pk_live_your_stripe_public_key
COMMISSION_RATE=0.075

# Rate Limiting Configuration
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_PER_HOUR=5000

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://app.api-logistics.kanjan.co.zw,https://admin.api-logistics.kanjan.co.zw

# Notification Configuration
EMAIL_NOTIFICATIONS_ENABLED=true
SMS_NOTIFICATIONS_ENABLED=true

# Monitoring Configuration
MONITORING_ENABLED=true
METRICS_ENABLED=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=logipool-backups-prod

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_PATH=/app/logs/logipool.log
LOG_MAX_FILE_SIZE=100MB
LOG_MAX_FILES=10

# Security Configuration
SECURITY_HEADERS_ENABLED=true
CSRF_PROTECTION_ENABLED=true
XSS_PROTECTION_ENABLED=true

# Performance Configuration
CONNECTION_POOL_SIZE=20
CACHE_TTL=3600
SESSION_TIMEOUT=1800

# External API Configuration
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
WEATHER_API_KEY=your_weather_api_key

# Traefik Dashboard (set to false in production)
TRAEFIK_DASHBOARD_ENABLED=false

# Additional Security
# Generate strong passwords using: openssl rand -base64 32
# Generate JWT secret using: openssl rand -base64 64
