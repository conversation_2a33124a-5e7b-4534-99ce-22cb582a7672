import 'package:dio/dio.dart';
import '../models/payment_model.dart';
import '../models/invoice_model.dart';
import '../models/paginated_response.dart';
import '../../core/network/api_client.dart';
import '../../core/errors/api_exception.dart';
import '../../core/errors/exceptions.dart';

class PaymentService {
  final ApiClient _apiClient;

  PaymentService({required ApiClient apiClient}) : _apiClient = apiClient;

  // Payment CRUD Operations
  Future<PaymentModel> createPayment(PaymentCreateRequest request) async {
    try {
      final response = await _apiClient.post(
        '/payments',
        data: request.toJson(),
      );
      return PaymentModel.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to create payment',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<PaymentModel> getPayment(int paymentId) async {
    try {
      final response = await _apiClient.get('/payments/$paymentId');
      return PaymentModel.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get payment',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<PaymentModel> updatePayment(int paymentId, PaymentUpdateRequest request) async {
    try {
      final response = await _apiClient.put(
        '/payments/$paymentId',
        data: request.toJson(),
      );
      return PaymentModel.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to update payment',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<void> deletePayment(int paymentId) async {
    try {
      await _apiClient.delete('/payments/$paymentId');
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to delete payment',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  // Payment Processing
  Future<PaymentStatusResponse> processPayment(PaymentProcessRequest request) async {
    try {
      final response = await _apiClient.post(
        '/payments/${request.paymentId}/process',
        data: request.toJson(),
      );
      return PaymentStatusResponse.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to process payment',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<PaymentStatusResponse> getPaymentStatus(int paymentId) async {
    try {
      final response = await _apiClient.get('/payments/$paymentId/status');
      return PaymentStatusResponse.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get payment status',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<PaymentModel> cancelPayment(int paymentId) async {
    try {
      final response = await _apiClient.post('/payments/$paymentId/cancel');
      return PaymentModel.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to cancel payment',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  // Payment Queries
  Future<PaginatedResponse<PaymentModel>> getAllPayments({
    int page = 0,
    int size = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        '/payments',
        queryParameters: {
          'page': page,
          'size': size,
        },
      );
      return PaginatedResponse.fromJson(
        response.data as Map<String, dynamic>,
        (json) => PaymentModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get all payments',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<PaginatedResponse<PaymentModel>> getUserPayments({
    int page = 0,
    int size = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        '/payments/user',
        queryParameters: {
          'page': page,
          'size': size,
        },
      );
      return PaginatedResponse.fromJson(
        response.data as Map<String, dynamic>,
        (json) => PaymentModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get user payments',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<PaginatedResponse<PaymentModel>> getPaymentsByStatus({
    required PaymentStatus status,
    int page = 0,
    int size = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        '/payments/status/${status.name.toUpperCase()}',
        queryParameters: {
          'page': page,
          'size': size,
        },
      );
      return PaginatedResponse.fromJson(
        response.data as Map<String, dynamic>,
        (json) => PaymentModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get payments by status',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<PaginatedResponse<PaymentModel>> getPaymentsByLoad({
    required int loadId,
    int page = 0,
    int size = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        '/payments/load/$loadId',
        queryParameters: {
          'page': page,
          'size': size,
        },
      );
      return PaginatedResponse.fromJson(
        response.data as Map<String, dynamic>,
        (json) => PaymentModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get payments by load',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<PaginatedResponse<PaymentModel>> getPaymentsByDateRange({
    required DateTime startDate,
    required DateTime endDate,
    int page = 0,
    int size = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        '/payments/date-range',
        queryParameters: {
          'startDate': startDate.toIso8601String(),
          'endDate': endDate.toIso8601String(),
          'page': page,
          'size': size,
        },
      );
      return PaginatedResponse.fromJson(
        response.data as Map<String, dynamic>,
        (json) => PaymentModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get payments by date range',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  // Payment Analytics
  Future<PaymentSummary> getPaymentSummary() async {
    try {
      final response = await _apiClient.get('/payments/summary');
      return PaymentSummary.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get payment summary',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<PaymentSummary> getUserPaymentSummary() async {
    try {
      final response = await _apiClient.get('/payments/user/summary');
      return PaymentSummary.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get user payment summary',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  // Invoice Operations
  Future<InvoiceModel> createInvoice(InvoiceCreateRequest request) async {
    try {
      final response = await _apiClient.post(
        '/invoices',
        data: request.toJson(),
      );
      return InvoiceModel.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to create invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<InvoiceModel> getInvoice(int invoiceId) async {
    try {
      final response = await _apiClient.get('/invoices/$invoiceId');
      return InvoiceModel.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<InvoiceModel> updateInvoice(int invoiceId, InvoiceUpdateRequest request) async {
    try {
      final response = await _apiClient.put(
        '/invoices/$invoiceId',
        data: request.toJson(),
      );
      return InvoiceModel.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to update invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<void> deleteInvoice(int invoiceId) async {
    try {
      await _apiClient.delete('/invoices/$invoiceId');
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to delete invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<PaginatedResponse<InvoiceModel>> getAllInvoices({
    int page = 0,
    int size = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        '/invoices',
        queryParameters: {
          'page': page,
          'size': size,
        },
      );
      return PaginatedResponse.fromJson(
        response.data as Map<String, dynamic>,
        (json) => InvoiceModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get invoices',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<PaginatedResponse<InvoiceModel>> getUserInvoices({
    int page = 0,
    int size = 20,
  }) async {
    try {
      final response = await _apiClient.get(
        '/invoices/user',
        queryParameters: {
          'page': page,
          'size': size,
        },
      );
      return PaginatedResponse.fromJson(
        response.data as Map<String, dynamic>,
        (json) => InvoiceModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get user invoices',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<InvoiceSummary> getInvoiceSummary() async {
    try {
      final response = await _apiClient.get('/invoices/summary');
      return InvoiceSummary.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get invoice summary',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  // Invoice Actions
  Future<InvoiceModel> sendInvoice(int invoiceId) async {
    try {
      final response = await _apiClient.post('/invoices/$invoiceId/send');
      return InvoiceModel.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to send invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<InvoiceModel> markInvoiceAsPaid(int invoiceId) async {
    try {
      final response = await _apiClient.post('/invoices/$invoiceId/mark-paid');
      return InvoiceModel.fromJson(response.data as Map<String, dynamic>);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to mark invoice as paid',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  Future<String> downloadInvoicePdf(int invoiceId) async {
    try {
      final response = await _apiClient.get('/invoices/$invoiceId/pdf');
      return response.data['downloadUrl'] as String;
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to download invoice PDF',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }
}
