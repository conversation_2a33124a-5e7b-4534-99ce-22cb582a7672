import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:hive_flutter/hive_flutter.dart';

import '../../core/constants/app_constants.dart';

class StorageService {
  static StorageService? _instance;
  static SharedPreferences? _prefs;
  static Box? _box;

  StorageService._();

  static StorageService get instance {
    _instance ??= StorageService._();
    return _instance!;
  }

  factory StorageService() => instance;

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _box = await Hive.openBox('logipool_storage');
  }

  // Token Management
  Future<void> saveToken(String token) async {
    await _prefs?.setString(AppConstants.tokenKey, token);
    await _box?.put(AppConstants.tokenKey, token);
  }

  Future<String?> getToken() async {
    return _prefs?.getString(AppConstants.tokenKey) ??
        _box?.get(AppConstants.tokenKey) as String?;
  }

  Future<void> saveRefreshToken(String refreshToken) async {
    await _prefs?.setString(AppConstants.refreshTokenKey, refreshToken);
    await _box?.put(AppConstants.refreshTokenKey, refreshToken);
  }

  Future<String?> getRefreshToken() async {
    return _prefs?.getString(AppConstants.refreshTokenKey) ??
        _box?.get(AppConstants.refreshTokenKey) as String?;
  }

  Future<void> clearTokens() async {
    await _prefs?.remove(AppConstants.tokenKey);
    await _prefs?.remove(AppConstants.refreshTokenKey);
    await _box?.delete(AppConstants.tokenKey);
    await _box?.delete(AppConstants.refreshTokenKey);
  }

  // User Data Management
  Future<void> saveUserData(Map<String, dynamic> userData) async {
    final userJson = jsonEncode(userData);
    await _prefs?.setString(AppConstants.userKey, userJson);
    await _box?.put(AppConstants.userKey, userData);
  }

  Future<Map<String, dynamic>?> getUserData() async {
    final userJson = _prefs?.getString(AppConstants.userKey);
    if (userJson != null) {
      return jsonDecode(userJson) as Map<String, dynamic>;
    }
    
    final userData = _box?.get(AppConstants.userKey);
    if (userData is Map) {
      return Map<String, dynamic>.from(userData);
    }
    
    return null;
  }

  Future<void> clearUserData() async {
    await _prefs?.remove(AppConstants.userKey);
    await _box?.delete(AppConstants.userKey);
  }

  // App Settings Management
  Future<void> saveSettings(Map<String, dynamic> settings) async {
    final settingsJson = jsonEncode(settings);
    await _prefs?.setString(AppConstants.settingsKey, settingsJson);
    await _box?.put(AppConstants.settingsKey, settings);
  }

  Future<Map<String, dynamic>?> getSettings() async {
    final settingsJson = _prefs?.getString(AppConstants.settingsKey);
    if (settingsJson != null) {
      return jsonDecode(settingsJson) as Map<String, dynamic>;
    }
    
    final settings = _box?.get(AppConstants.settingsKey);
    if (settings is Map) {
      return Map<String, dynamic>.from(settings);
    }
    
    return null;
  }

  // Generic Storage Methods
  Future<void> setString(String key, String value) async {
    await _prefs?.setString(key, value);
    await _box?.put(key, value);
  }

  Future<String?> getString(String key) async {
    return _prefs?.getString(key) ?? _box?.get(key) as String?;
  }

  Future<void> setInt(String key, int value) async {
    await _prefs?.setInt(key, value);
    await _box?.put(key, value);
  }

  Future<int?> getInt(String key) async {
    return _prefs?.getInt(key) ?? _box?.get(key) as int?;
  }

  Future<void> setBool(String key, bool value) async {
    await _prefs?.setBool(key, value);
    await _box?.put(key, value);
  }

  Future<bool?> getBool(String key) async {
    return _prefs?.getBool(key) ?? _box?.get(key) as bool?;
  }

  Future<void> setDouble(String key, double value) async {
    await _prefs?.setDouble(key, value);
    await _box?.put(key, value);
  }

  Future<double?> getDouble(String key) async {
    return _prefs?.getDouble(key) ?? _box?.get(key) as double?;
  }

  Future<void> setStringList(String key, List<String> value) async {
    await _prefs?.setStringList(key, value);
    await _box?.put(key, value);
  }

  Future<List<String>?> getStringList(String key) async {
    final prefsList = _prefs?.getStringList(key);
    if (prefsList != null) return prefsList;
    
    final boxList = _box?.get(key);
    if (boxList is List) {
      return boxList.cast<String>();
    }
    
    return null;
  }

  Future<void> remove(String key) async {
    await _prefs?.remove(key);
    await _box?.delete(key);
  }

  Future<void> clear() async {
    await _prefs?.clear();
    await _box?.clear();
  }

  // Cache Management
  Future<void> setCacheData(String key, Map<String, dynamic> data, {Duration? expiry}) async {
    final cacheData = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiry': expiry?.inMilliseconds,
    };
    await _box?.put('cache_$key', cacheData);
  }

  Future<Map<String, dynamic>?> getCacheData(String key) async {
    final cacheData = _box?.get('cache_$key');
    if (cacheData is Map) {
      final timestamp = cacheData['timestamp'] as int?;
      final expiry = cacheData['expiry'] as int?;
      
      if (timestamp != null && expiry != null) {
        final now = DateTime.now().millisecondsSinceEpoch;
        if (now - timestamp > expiry) {
          // Cache expired, remove it
          await _box?.delete('cache_$key');
          return null;
        }
      }
      
      return Map<String, dynamic>.from(cacheData['data'] as Map);
    }
    return null;
  }

  Future<void> clearCache() async {
    final keys = _box?.keys.where((key) => key.toString().startsWith('cache_')).toList();
    if (keys != null) {
      for (final key in keys) {
        await _box?.delete(key);
      }
    }
  }

  // Offline Data Management
  Future<void> saveOfflineData(String key, List<Map<String, dynamic>> data) async {
    await _box?.put('offline_$key', data);
  }

  Future<List<Map<String, dynamic>>?> getOfflineData(String key) async {
    final data = _box?.get('offline_$key');
    if (data is List) {
      return data.cast<Map<String, dynamic>>();
    }
    return null;
  }

  Future<void> clearOfflineData(String key) async {
    await _box?.delete('offline_$key');
  }

  Future<void> clearAllOfflineData() async {
    final keys = _box?.keys.where((key) => key.toString().startsWith('offline_')).toList();
    if (keys != null) {
      for (final key in keys) {
        await _box?.delete(key);
      }
    }
  }
}
