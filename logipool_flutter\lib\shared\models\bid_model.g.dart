// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'bid_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BidModel _$BidModelFromJson(Map<String, dynamic> json) => BidModel(
      id: (json['id'] as num?)?.toInt(),
      amount: (json['amount'] as num).toDouble(),
      proposal: json['proposal'] as String?,
      estimatedPickupTime:
          DateTime.parse(json['estimatedPickupTime'] as String),
      estimatedDeliveryTime:
          DateTime.parse(json['estimatedDeliveryTime'] as String),
      status: $enumDecode(_$BidStatusEnumMap, json['status']),
      notes: json['notes'] as String?,
      loadId: (json['loadId'] as num).toInt(),
      loadTitle: json['loadTitle'] as String?,
      companyId: (json['companyId'] as num).toInt(),
      companyName: json['companyName'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
      acceptedAt: json['acceptedAt'] == null
          ? null
          : DateTime.parse(json['acceptedAt'] as String),
      rejectedAt: json['rejectedAt'] == null
          ? null
          : DateTime.parse(json['rejectedAt'] as String),
    );

Map<String, dynamic> _$BidModelToJson(BidModel instance) => <String, dynamic>{
      'id': instance.id,
      'amount': instance.amount,
      'proposal': instance.proposal,
      'estimatedPickupTime': instance.estimatedPickupTime.toIso8601String(),
      'estimatedDeliveryTime': instance.estimatedDeliveryTime.toIso8601String(),
      'status': _$BidStatusEnumMap[instance.status]!,
      'notes': instance.notes,
      'loadId': instance.loadId,
      'loadTitle': instance.loadTitle,
      'companyId': instance.companyId,
      'companyName': instance.companyName,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
      'acceptedAt': instance.acceptedAt?.toIso8601String(),
      'rejectedAt': instance.rejectedAt?.toIso8601String(),
    };

const _$BidStatusEnumMap = {
  BidStatus.pending: 'PENDING',
  BidStatus.accepted: 'ACCEPTED',
  BidStatus.rejected: 'REJECTED',
  BidStatus.withdrawn: 'WITHDRAWN',
};

BidCreateRequest _$BidCreateRequestFromJson(Map<String, dynamic> json) =>
    BidCreateRequest(
      loadId: (json['loadId'] as num).toInt(),
      amount: (json['amount'] as num).toDouble(),
      proposal: json['proposal'] as String?,
      estimatedPickupTime:
          DateTime.parse(json['estimatedPickupTime'] as String),
      estimatedDeliveryTime:
          DateTime.parse(json['estimatedDeliveryTime'] as String),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$BidCreateRequestToJson(BidCreateRequest instance) =>
    <String, dynamic>{
      'loadId': instance.loadId,
      'amount': instance.amount,
      'proposal': instance.proposal,
      'estimatedPickupTime': instance.estimatedPickupTime.toIso8601String(),
      'estimatedDeliveryTime': instance.estimatedDeliveryTime.toIso8601String(),
      'notes': instance.notes,
    };

BidUpdateRequest _$BidUpdateRequestFromJson(Map<String, dynamic> json) =>
    BidUpdateRequest(
      amount: (json['amount'] as num?)?.toDouble(),
      proposal: json['proposal'] as String?,
      estimatedPickupTime: json['estimatedPickupTime'] == null
          ? null
          : DateTime.parse(json['estimatedPickupTime'] as String),
      estimatedDeliveryTime: json['estimatedDeliveryTime'] == null
          ? null
          : DateTime.parse(json['estimatedDeliveryTime'] as String),
      notes: json['notes'] as String?,
    );

Map<String, dynamic> _$BidUpdateRequestToJson(BidUpdateRequest instance) =>
    <String, dynamic>{
      'amount': instance.amount,
      'proposal': instance.proposal,
      'estimatedPickupTime': instance.estimatedPickupTime?.toIso8601String(),
      'estimatedDeliveryTime':
          instance.estimatedDeliveryTime?.toIso8601String(),
      'notes': instance.notes,
    };
