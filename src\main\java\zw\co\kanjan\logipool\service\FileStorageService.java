package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import zw.co.kanjan.logipool.exception.BusinessException;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class FileStorageService {
    
    @Value("${app.file.upload-dir:./uploads}")
    private String uploadDir;
    
    @Value("${app.file.max-size:10485760}") // 10MB default
    private long maxFileSize;
    
    @Value("${app.file.allowed-types:pdf,doc,docx,jpg,jpeg,png,gif}")
    private String allowedTypes;
    
    private Path fileStorageLocation;
    private List<String> allowedFileTypes;
    
    @PostConstruct
    public void init() {
        this.fileStorageLocation = Paths.get(uploadDir).toAbsolutePath().normalize();
        this.allowedFileTypes = Arrays.asList(allowedTypes.toLowerCase().split(","));
        
        try {
            Files.createDirectories(this.fileStorageLocation);
            log.info("File storage location initialized: {}", this.fileStorageLocation);
        } catch (Exception ex) {
            throw new BusinessException("Could not create the directory where the uploaded files will be stored.", ex);
        }
    }
    
    public String storeFile(MultipartFile file, String category) {
        validateFile(file);
        
        String fileName = generateFileName(file, category);
        
        try {
            // Check if the file's name contains invalid characters
            if (fileName.contains("..")) {
                throw new BusinessException("Sorry! Filename contains invalid path sequence " + fileName);
            }
            
            // Create category directory if it doesn't exist
            Path categoryPath = this.fileStorageLocation.resolve(category);
            Files.createDirectories(categoryPath);
            
            // Copy file to the target location (Replacing existing file with the same name)
            Path targetLocation = categoryPath.resolve(fileName);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
            
            String relativePath = category + "/" + fileName;
            log.info("File stored successfully: {}", relativePath);
            return relativePath;
            
        } catch (IOException ex) {
            throw new BusinessException("Could not store file " + fileName + ". Please try again!", ex);
        }
    }
    
    public Resource loadFileAsResource(String fileName) {
        try {
            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            
            if (resource.exists()) {
                return resource;
            } else {
                throw new BusinessException("File not found " + fileName);
            }
        } catch (MalformedURLException ex) {
            throw new BusinessException("File not found " + fileName, ex);
        }
    }
    
    public boolean deleteFile(String fileName) {
        try {
            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
            return Files.deleteIfExists(filePath);
        } catch (IOException ex) {
            log.error("Could not delete file: {}", fileName, ex);
            return false;
        }
    }
    
    public long getFileSize(String fileName) {
        try {
            Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
            return Files.size(filePath);
        } catch (IOException ex) {
            log.error("Could not get file size: {}", fileName, ex);
            return 0;
        }
    }
    
    public boolean fileExists(String fileName) {
        Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
        return Files.exists(filePath);
    }
    
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new BusinessException("Cannot store empty file");
        }
        
        if (file.getSize() > maxFileSize) {
            throw new BusinessException("File size exceeds maximum allowed size of " + (maxFileSize / 1024 / 1024) + "MB");
        }
        
        String fileExtension = getFileExtension(file.getOriginalFilename());
        if (!allowedFileTypes.contains(fileExtension.toLowerCase())) {
            throw new BusinessException("File type not allowed. Allowed types: " + String.join(", ", allowedFileTypes));
        }
    }
    
    private String generateFileName(MultipartFile file, String category) {
        String originalFileName = StringUtils.cleanPath(file.getOriginalFilename());
        String fileExtension = getFileExtension(originalFileName);
        String baseName = getBaseName(originalFileName);
        
        // Generate unique filename with timestamp and UUID
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String uniqueId = UUID.randomUUID().toString().substring(0, 8);
        
        return String.format("%s_%s_%s.%s", baseName, timestamp, uniqueId, fileExtension);
    }
    
    private String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex == -1 ? "" : fileName.substring(lastDotIndex + 1);
    }
    
    private String getBaseName(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "file";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        String baseName = lastDotIndex == -1 ? fileName : fileName.substring(0, lastDotIndex);
        
        // Clean the base name - remove special characters and spaces
        baseName = baseName.replaceAll("[^a-zA-Z0-9_-]", "_");
        
        // Limit length
        if (baseName.length() > 50) {
            baseName = baseName.substring(0, 50);
        }
        
        return baseName.isEmpty() ? "file" : baseName;
    }
    
    public String getCategoryForDocumentType(String documentType) {
        if (documentType == null) {
            return "general";
        }
        
        switch (documentType.toUpperCase()) {
            case "COMPANY_REGISTRATION":
            case "TAX_CLEARANCE":
            case "BUSINESS_LICENSE":
            case "INSURANCE_CERTIFICATE":
                return "company";
            case "VEHICLE_REGISTRATION":
            case "FITNESS_CERTIFICATE":
            case "ROAD_PERMIT":
            case "ZINARA_PERMIT":
            case "VEHICLE_INSURANCE":
            case "VEHICLE_PHOTOS":
                return "vehicle";
            case "PROOF_OF_DELIVERY":
            case "INVOICE":
            case "CONTRACT":
            case "WAYBILL":
            case "CUSTOMS_DECLARATION":
                return "load";
            case "PROFILE_PHOTO":
                return "profile";
            default:
                return "general";
        }
    }
}
