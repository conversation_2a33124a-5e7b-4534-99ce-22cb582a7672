import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../shared/models/tracking_model.dart';
import '../../../shared/models/paginated_response.dart';
import '../../../shared/services/tracking_service.dart';

// Events
abstract class TrackingEvent extends Equatable {
  const TrackingEvent();

  @override
  List<Object?> get props => [];
}

class TrackingUpdateRequested extends TrackingEvent {
  final TrackingUpdateRequest request;

  const TrackingUpdateRequested({required this.request});

  @override
  List<Object?> get props => [request];
}

class LocationUpdateRequested extends TrackingEvent {
  final LocationUpdateRequest request;

  const LocationUpdateRequested({required this.request});

  @override
  List<Object?> get props => [request];
}

class TrackingHistoryFetched extends TrackingEvent {
  final int loadId;
  final int page;
  final int size;

  const TrackingHistoryFetched({
    required this.loadId,
    this.page = 0,
    this.size = 20,
  });

  @override
  List<Object?> get props => [loadId, page, size];
}

class LatestTrackingFetched extends TrackingEvent {
  final int loadId;

  const LatestTrackingFetched({required this.loadId});

  @override
  List<Object?> get props => [loadId];
}

class ActiveLocationsRequested extends TrackingEvent {
  const ActiveLocationsRequested();
}

class TrackingStatisticsRequested extends TrackingEvent {
  const TrackingStatisticsRequested();
}

class DeliveryConfirmationRequested extends TrackingEvent {
  final DeliveryConfirmationRequest request;

  const DeliveryConfirmationRequested({required this.request});

  @override
  List<Object?> get props => [request];
}

class DelayedLoadsRequested extends TrackingEvent {
  const DelayedLoadsRequested();
}

class LoadsWithIssuesRequested extends TrackingEvent {
  const LoadsWithIssuesRequested();
}

class CompanyLoadsTrackingRequested extends TrackingEvent {
  final int companyId;

  const CompanyLoadsTrackingRequested({required this.companyId});

  @override
  List<Object?> get props => [companyId];
}

class TrackingSearchRequested extends TrackingEvent {
  final String? location;
  final TrackingStatus? status;
  final DateTime? startDate;
  final DateTime? endDate;
  final int page;
  final int size;

  const TrackingSearchRequested({
    this.location,
    this.status,
    this.startDate,
    this.endDate,
    this.page = 0,
    this.size = 20,
  });

  @override
  List<Object?> get props => [location, status, startDate, endDate, page, size];
}

class TrackingLoadMoreRequested extends TrackingEvent {
  const TrackingLoadMoreRequested();
}

class TrackingRefreshRequested extends TrackingEvent {
  const TrackingRefreshRequested();
}

// States
abstract class TrackingState extends Equatable {
  const TrackingState();

  @override
  List<Object?> get props => [];
}

class TrackingInitial extends TrackingState {
  const TrackingInitial();
}

class TrackingLoading extends TrackingState {
  const TrackingLoading();
}

class TrackingLoadingMore extends TrackingState {
  final List<TrackingModel> currentTracking;

  const TrackingLoadingMore({required this.currentTracking});

  @override
  List<Object?> get props => [currentTracking];
}

class TrackingUpdated extends TrackingState {
  final TrackingModel tracking;

  const TrackingUpdated({required this.tracking});

  @override
  List<Object?> get props => [tracking];
}

class LocationUpdated extends TrackingState {
  final TrackingModel tracking;

  const LocationUpdated({required this.tracking});

  @override
  List<Object?> get props => [tracking];
}

class TrackingHistoryLoaded extends TrackingState {
  final List<TrackingModel> tracking;
  final bool hasMore;
  final int currentPage;

  const TrackingHistoryLoaded({
    required this.tracking,
    required this.hasMore,
    required this.currentPage,
  });

  @override
  List<Object?> get props => [tracking, hasMore, currentPage];
}

class LatestTrackingLoaded extends TrackingState {
  final TrackingModel? tracking;

  const LatestTrackingLoaded({this.tracking});

  @override
  List<Object?> get props => [tracking];
}

class ActiveLocationsLoaded extends TrackingState {
  final List<RealTimeLocation> locations;

  const ActiveLocationsLoaded({required this.locations});

  @override
  List<Object?> get props => [locations];
}

class TrackingStatisticsLoaded extends TrackingState {
  final TrackingStatistics statistics;

  const TrackingStatisticsLoaded({required this.statistics});

  @override
  List<Object?> get props => [statistics];
}

class DeliveryConfirmed extends TrackingState {
  final TrackingModel tracking;

  const DeliveryConfirmed({required this.tracking});

  @override
  List<Object?> get props => [tracking];
}

class DelayedLoadsLoaded extends TrackingState {
  final List<TrackingModel> delayedLoads;

  const DelayedLoadsLoaded({required this.delayedLoads});

  @override
  List<Object?> get props => [delayedLoads];
}

class LoadsWithIssuesLoaded extends TrackingState {
  final List<TrackingModel> loadsWithIssues;

  const LoadsWithIssuesLoaded({required this.loadsWithIssues});

  @override
  List<Object?> get props => [loadsWithIssues];
}

class CompanyLoadsTrackingLoaded extends TrackingState {
  final List<TrackingModel> companyTracking;

  const CompanyLoadsTrackingLoaded({required this.companyTracking});

  @override
  List<Object?> get props => [companyTracking];
}

class TrackingSearchLoaded extends TrackingState {
  final List<TrackingModel> searchResults;
  final bool hasMore;
  final int currentPage;

  const TrackingSearchLoaded({
    required this.searchResults,
    required this.hasMore,
    required this.currentPage,
  });

  @override
  List<Object?> get props => [searchResults, hasMore, currentPage];
}

class TrackingError extends TrackingState {
  final String message;

  const TrackingError({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class TrackingBloc extends Bloc<TrackingEvent, TrackingState> {
  final TrackingService _trackingService;
  
  List<TrackingModel> _currentTracking = [];
  int _currentPage = 0;
  bool _hasMore = true;
  String? _currentLocation;
  TrackingStatus? _currentStatus;
  DateTime? _currentStartDate;
  DateTime? _currentEndDate;

  TrackingBloc({required TrackingService trackingService})
      : _trackingService = trackingService,
        super(const TrackingInitial()) {
    on<TrackingUpdateRequested>(_onTrackingUpdateRequested);
    on<LocationUpdateRequested>(_onLocationUpdateRequested);
    on<TrackingHistoryFetched>(_onTrackingHistoryFetched);
    on<LatestTrackingFetched>(_onLatestTrackingFetched);
    on<ActiveLocationsRequested>(_onActiveLocationsRequested);
    on<TrackingStatisticsRequested>(_onTrackingStatisticsRequested);
    on<DeliveryConfirmationRequested>(_onDeliveryConfirmationRequested);
    on<DelayedLoadsRequested>(_onDelayedLoadsRequested);
    on<LoadsWithIssuesRequested>(_onLoadsWithIssuesRequested);
    on<CompanyLoadsTrackingRequested>(_onCompanyLoadsTrackingRequested);
    on<TrackingSearchRequested>(_onTrackingSearchRequested);
    on<TrackingLoadMoreRequested>(_onTrackingLoadMoreRequested);
    on<TrackingRefreshRequested>(_onTrackingRefreshRequested);
  }

  Future<void> _onTrackingUpdateRequested(
    TrackingUpdateRequested event,
    Emitter<TrackingState> emit,
  ) async {
    try {
      emit(const TrackingLoading());
      final tracking = await _trackingService.updateTracking(event.request);
      emit(TrackingUpdated(tracking: tracking));
    } catch (e) {
      emit(TrackingError(message: e.toString()));
    }
  }

  Future<void> _onLocationUpdateRequested(
    LocationUpdateRequested event,
    Emitter<TrackingState> emit,
  ) async {
    try {
      emit(const TrackingLoading());
      final tracking = await _trackingService.updateLocation(event.request);
      emit(LocationUpdated(tracking: tracking));
    } catch (e) {
      emit(TrackingError(message: e.toString()));
    }
  }

  Future<void> _onTrackingHistoryFetched(
    TrackingHistoryFetched event,
    Emitter<TrackingState> emit,
  ) async {
    try {
      if (event.page == 0) {
        emit(const TrackingLoading());
        _currentTracking.clear();
      } else {
        emit(TrackingLoadingMore(currentTracking: _currentTracking));
      }

      final response = await _trackingService.getTrackingHistory(
        event.loadId,
        page: event.page,
        size: event.size,
      );

      if (event.page == 0) {
        _currentTracking = response.content;
      } else {
        _currentTracking.addAll(response.content);
      }

      _currentPage = event.page;
      _hasMore = !response.last;

      emit(TrackingHistoryLoaded(
        tracking: List.from(_currentTracking),
        hasMore: _hasMore,
        currentPage: _currentPage,
      ));
    } catch (e) {
      emit(TrackingError(message: e.toString()));
    }
  }

  Future<void> _onLatestTrackingFetched(
    LatestTrackingFetched event,
    Emitter<TrackingState> emit,
  ) async {
    try {
      emit(const TrackingLoading());
      final tracking = await _trackingService.getLatestTracking(event.loadId);
      emit(LatestTrackingLoaded(tracking: tracking));
    } catch (e) {
      emit(TrackingError(message: e.toString()));
    }
  }

  Future<void> _onActiveLocationsRequested(
    ActiveLocationsRequested event,
    Emitter<TrackingState> emit,
  ) async {
    try {
      emit(const TrackingLoading());
      final locations = await _trackingService.getActiveLoadsLocation();
      emit(ActiveLocationsLoaded(locations: locations));
    } catch (e) {
      emit(TrackingError(message: e.toString()));
    }
  }

  Future<void> _onTrackingStatisticsRequested(
    TrackingStatisticsRequested event,
    Emitter<TrackingState> emit,
  ) async {
    try {
      emit(const TrackingLoading());
      final statistics = await _trackingService.getTrackingStatistics();
      emit(TrackingStatisticsLoaded(statistics: statistics));
    } catch (e) {
      emit(TrackingError(message: e.toString()));
    }
  }

  Future<void> _onDeliveryConfirmationRequested(
    DeliveryConfirmationRequested event,
    Emitter<TrackingState> emit,
  ) async {
    try {
      emit(const TrackingLoading());
      final tracking = await _trackingService.confirmDelivery(event.request);
      emit(DeliveryConfirmed(tracking: tracking));
    } catch (e) {
      emit(TrackingError(message: e.toString()));
    }
  }

  Future<void> _onDelayedLoadsRequested(
    DelayedLoadsRequested event,
    Emitter<TrackingState> emit,
  ) async {
    try {
      emit(const TrackingLoading());
      final delayedLoads = await _trackingService.getDelayedLoads();
      emit(DelayedLoadsLoaded(delayedLoads: delayedLoads));
    } catch (e) {
      emit(TrackingError(message: e.toString()));
    }
  }

  Future<void> _onLoadsWithIssuesRequested(
    LoadsWithIssuesRequested event,
    Emitter<TrackingState> emit,
  ) async {
    try {
      emit(const TrackingLoading());
      final loadsWithIssues = await _trackingService.getLoadsWithIssues();
      emit(LoadsWithIssuesLoaded(loadsWithIssues: loadsWithIssues));
    } catch (e) {
      emit(TrackingError(message: e.toString()));
    }
  }

  Future<void> _onCompanyLoadsTrackingRequested(
    CompanyLoadsTrackingRequested event,
    Emitter<TrackingState> emit,
  ) async {
    try {
      emit(const TrackingLoading());
      final companyTracking = await _trackingService.getCompanyLoadsTracking(event.companyId);
      emit(CompanyLoadsTrackingLoaded(companyTracking: companyTracking));
    } catch (e) {
      emit(TrackingError(message: e.toString()));
    }
  }

  Future<void> _onTrackingSearchRequested(
    TrackingSearchRequested event,
    Emitter<TrackingState> emit,
  ) async {
    try {
      if (event.page == 0) {
        emit(const TrackingLoading());
        _currentTracking.clear();
      } else {
        emit(TrackingLoadingMore(currentTracking: _currentTracking));
      }

      _currentLocation = event.location;
      _currentStatus = event.status;
      _currentStartDate = event.startDate;
      _currentEndDate = event.endDate;

      final response = await _trackingService.searchTracking(
        location: event.location,
        status: event.status,
        startDate: event.startDate,
        endDate: event.endDate,
        page: event.page,
        size: event.size,
      );

      if (event.page == 0) {
        _currentTracking = response.content;
      } else {
        _currentTracking.addAll(response.content);
      }

      _currentPage = event.page;
      _hasMore = !response.last;

      emit(TrackingSearchLoaded(
        searchResults: List.from(_currentTracking),
        hasMore: _hasMore,
        currentPage: _currentPage,
      ));
    } catch (e) {
      emit(TrackingError(message: e.toString()));
    }
  }

  Future<void> _onTrackingLoadMoreRequested(
    TrackingLoadMoreRequested event,
    Emitter<TrackingState> emit,
  ) async {
    if (!_hasMore) return;

    add(TrackingSearchRequested(
      location: _currentLocation,
      status: _currentStatus,
      startDate: _currentStartDate,
      endDate: _currentEndDate,
      page: _currentPage + 1,
    ));
  }

  Future<void> _onTrackingRefreshRequested(
    TrackingRefreshRequested event,
    Emitter<TrackingState> emit,
  ) async {
    _currentPage = 0;
    _hasMore = true;
    _currentTracking.clear();

    add(TrackingSearchRequested(
      location: _currentLocation,
      status: _currentStatus,
      startDate: _currentStartDate,
      endDate: _currentEndDate,
      page: 0,
    ));
  }
}
