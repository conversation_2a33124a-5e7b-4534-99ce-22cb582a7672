apiVersion: v1
kind: ConfigMap
metadata:
  name: logipool-config
  namespace: logipool
data:
  # Application Configuration
  APP_BASE_URL: "https://api.logipool.com"
  PORT: "8080"
  
  # Database Configuration
  DATABASE_NAME: "logipool_prod"
  DATABASE_USERNAME: "logipool"
  DATABASE_PORT: "5432"
  
  # Redis Configuration
  REDIS_PORT: "6379"
  
  # File Upload Configuration
  UPLOAD_DIR: "/app/uploads"
  MAX_FILE_SIZE: "50MB"
  MAX_REQUEST_SIZE: "50MB"
  
  # Cloud Storage Configuration
  CLOUD_STORAGE_ENABLED: "true"
  CLOUD_STORAGE_PROVIDER: "aws"
  AWS_REGION: "us-east-1"
  
  # Email Configuration
  MAIL_HOST: "smtp.gmail.com"
  MAIL_PORT: "587"
  
  # SMS Configuration
  SMS_PROVIDER: "twilio"
  SMS_ENABLED: "true"
  
  # Payment Configuration
  STRIPE_ENABLED: "true"
  COMMISSION_RATE: "0.075"
  
  # Rate Limiting
  RATE_LIMIT_PER_MINUTE: "100"
  RATE_LIMIT_PER_HOUR: "5000"
  
  # CORS Configuration
  CORS_ALLOWED_ORIGINS: "https://app.logipool.com,https://admin.logipool.com"
  
  # JWT Configuration
  JWT_EXPIRATION: "86400000"
  JWT_REFRESH_EXPIRATION: "604800000"
  
  # Monitoring Configuration
  MONITORING_ENABLED: "true"
  METRICS_ENABLED: "true"
