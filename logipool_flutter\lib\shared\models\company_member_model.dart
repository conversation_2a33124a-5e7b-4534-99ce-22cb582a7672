import 'package:json_annotation/json_annotation.dart';
import 'user_model.dart';

part 'company_member_model.g.dart';

@JsonSerializable()
class CompanyMemberModel {
  final int id;
  final int userId;
  final String username;
  final String email;
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final CompanyRole role;
  final MemberStatus status;
  final bool canManageMembers;
  final bool canManageLoads;
  final bool canUpdateLoadStatus;
  final bool canUploadDocuments;
  final bool canGenerateInvoices;
  final bool canViewFinancials;
  final bool canTrackLocation;
  final String? invitedBy;
  final DateTime? invitedAt;
  final DateTime? joinedAt;
  final DateTime createdAt;

  const CompanyMemberModel({
    required this.id,
    required this.userId,
    required this.username,
    required this.email,
    this.firstName,
    this.lastName,
    this.phoneNumber,
    required this.role,
    required this.status,
    required this.canManageMembers,
    required this.canManageLoads,
    required this.canUpdateLoadStatus,
    required this.canUploadDocuments,
    required this.canGenerateInvoices,
    required this.canViewFinancials,
    required this.canTrackLocation,
    this.invitedBy,
    this.invitedAt,
    this.joinedAt,
    required this.createdAt,
  });

  factory CompanyMemberModel.fromJson(Map<String, dynamic> json) =>
      _$CompanyMemberModelFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyMemberModelToJson(this);

  String get displayName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    } else if (firstName != null) {
      return firstName!;
    } else if (lastName != null) {
      return lastName!;
    } else {
      return username;
    }
  }

  String get roleDisplayName {
    switch (role) {
      case CompanyRole.owner:
        return 'Owner';
      case CompanyRole.manager:
        return 'Manager';
      case CompanyRole.driver:
        return 'Driver';
      case CompanyRole.dispatcher:
        return 'Dispatcher';
      case CompanyRole.accountant:
        return 'Accountant';
      case CompanyRole.viewer:
        return 'Viewer';
    }
  }

  String get statusDisplayName {
    switch (status) {
      case MemberStatus.pending:
        return 'Pending';
      case MemberStatus.active:
        return 'Active';
      case MemberStatus.inactive:
        return 'Inactive';
      case MemberStatus.suspended:
        return 'Suspended';
    }
  }

  bool get isPending => status == MemberStatus.pending;
  bool get isActive => status == MemberStatus.active;
  bool get canManageTeam => canManageMembers || role == CompanyRole.owner;
}

@JsonSerializable()
class CompanyMemberListResponse {
  final List<CompanyMemberModel> members;
  final int totalMembers;
  final int activeMembers;
  final int pendingInvitations;

  const CompanyMemberListResponse({
    required this.members,
    required this.totalMembers,
    required this.activeMembers,
    required this.pendingInvitations,
  });

  factory CompanyMemberListResponse.fromJson(Map<String, dynamic> json) =>
      _$CompanyMemberListResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyMemberListResponseToJson(this);
}

@JsonSerializable()
class InviteUserRequest {
  final String email;
  final CompanyRole role;
  final bool canManageMembers;
  final bool canManageLoads;
  final bool canUpdateLoadStatus;
  final bool canUploadDocuments;
  final bool canGenerateInvoices;
  final bool canViewFinancials;
  final bool canTrackLocation;
  final String? invitationMessage;

  const InviteUserRequest({
    required this.email,
    required this.role,
    this.canManageMembers = false,
    this.canManageLoads = false,
    this.canUpdateLoadStatus = false,
    this.canUploadDocuments = false,
    this.canGenerateInvoices = false,
    this.canViewFinancials = false,
    this.canTrackLocation = false,
    this.invitationMessage,
  });

  factory InviteUserRequest.fromJson(Map<String, dynamic> json) =>
      _$InviteUserRequestFromJson(json);

  Map<String, dynamic> toJson() => _$InviteUserRequestToJson(this);
}

@JsonSerializable()
class UpdateMemberRequest {
  final int memberId;
  final CompanyRole? role;
  final MemberStatus? status;
  final bool? canManageMembers;
  final bool? canManageLoads;
  final bool? canUpdateLoadStatus;
  final bool? canUploadDocuments;
  final bool? canGenerateInvoices;
  final bool? canViewFinancials;
  final bool? canTrackLocation;

  const UpdateMemberRequest({
    required this.memberId,
    this.role,
    this.status,
    this.canManageMembers,
    this.canManageLoads,
    this.canUpdateLoadStatus,
    this.canUploadDocuments,
    this.canGenerateInvoices,
    this.canViewFinancials,
    this.canTrackLocation,
  });

  factory UpdateMemberRequest.fromJson(Map<String, dynamic> json) =>
      _$UpdateMemberRequestFromJson(json);

  Map<String, dynamic> toJson() => _$UpdateMemberRequestToJson(this);
}

enum CompanyRole {
  @JsonValue('OWNER')
  owner,
  @JsonValue('MANAGER')
  manager,
  @JsonValue('DRIVER')
  driver,
  @JsonValue('DISPATCHER')
  dispatcher,
  @JsonValue('ACCOUNTANT')
  accountant,
  @JsonValue('VIEWER')
  viewer,
}

enum MemberStatus {
  @JsonValue('PENDING')
  pending,
  @JsonValue('ACTIVE')
  active,
  @JsonValue('INACTIVE')
  inactive,
  @JsonValue('SUSPENDED')
  suspended,
}
