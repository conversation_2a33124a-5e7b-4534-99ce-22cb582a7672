import 'package:json_annotation/json_annotation.dart';

part 'document_model.g.dart';

@JsonSerializable()
class DocumentModel {
  final int id;
  final String name;
  final DocumentType type;
  final String fileName;
  final String fileType;
  final int fileSize;
  final DocumentStatus status;
  final String? description;
  final DateTime? expiryDate;
  final bool isRequired;
  final int? companyId;
  final int? vehicleId;
  final int? loadId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? verifiedAt;
  final int? verifiedById;
  final String? downloadUrl;

  const DocumentModel({
    required this.id,
    required this.name,
    required this.type,
    required this.fileName,
    required this.fileType,
    required this.fileSize,
    required this.status,
    this.description,
    this.expiryDate,
    required this.isRequired,
    this.companyId,
    this.vehicleId,
    this.loadId,
    required this.createdAt,
    required this.updatedAt,
    this.verifiedAt,
    this.verifiedById,
    this.downloadUrl,
  });

  factory DocumentModel.fromJson(Map<String, dynamic> json) =>
      _$DocumentModelFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentModelToJson(this);

  DocumentModel copyWith({
    int? id,
    String? name,
    DocumentType? type,
    String? fileName,
    String? fileType,
    int? fileSize,
    DocumentStatus? status,
    String? description,
    DateTime? expiryDate,
    bool? isRequired,
    int? companyId,
    int? vehicleId,
    int? loadId,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? verifiedAt,
    int? verifiedById,
    String? downloadUrl,
  }) {
    return DocumentModel(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      fileName: fileName ?? this.fileName,
      fileType: fileType ?? this.fileType,
      fileSize: fileSize ?? this.fileSize,
      status: status ?? this.status,
      description: description ?? this.description,
      expiryDate: expiryDate ?? this.expiryDate,
      isRequired: isRequired ?? this.isRequired,
      companyId: companyId ?? this.companyId,
      vehicleId: vehicleId ?? this.vehicleId,
      loadId: loadId ?? this.loadId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      verifiedById: verifiedById ?? this.verifiedById,
      downloadUrl: downloadUrl ?? this.downloadUrl,
    );
  }

  bool get isExpired {
    if (expiryDate == null) return false;
    return DateTime.now().isAfter(expiryDate!);
  }

  bool get isExpiringSoon {
    if (expiryDate == null) return false;
    final daysUntilExpiry = expiryDate!.difference(DateTime.now()).inDays;
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  }

  String get formattedFileSize {
    if (fileSize < 1024) return '${fileSize}B';
    if (fileSize < 1024 * 1024) return '${(fileSize / 1024).toStringAsFixed(1)}KB';
    if (fileSize < 1024 * 1024 * 1024) return '${(fileSize / (1024 * 1024)).toStringAsFixed(1)}MB';
    return '${(fileSize / (1024 * 1024 * 1024)).toStringAsFixed(1)}GB';
  }
}

enum DocumentType {
  @JsonValue('COMPANY_REGISTRATION')
  companyRegistration,
  @JsonValue('TAX_CLEARANCE')
  taxClearance,
  @JsonValue('BUSINESS_LICENSE')
  businessLicense,
  @JsonValue('INSURANCE_CERTIFICATE')
  insuranceCertificate,
  @JsonValue('VEHICLE_REGISTRATION')
  vehicleRegistration,
  @JsonValue('FITNESS_CERTIFICATE')
  fitnessCertificate,
  @JsonValue('ROAD_PERMIT')
  roadPermit,
  @JsonValue('ZINARA_PERMIT')
  zinaraPermit,
  @JsonValue('VEHICLE_INSURANCE')
  vehicleInsurance,
  @JsonValue('VEHICLE_PHOTOS')
  vehiclePhotos,
  @JsonValue('PROOF_OF_DELIVERY')
  proofOfDelivery,
  @JsonValue('INVOICE')
  invoice,
  @JsonValue('CONTRACT')
  contract,
  @JsonValue('WAYBILL')
  waybill,
  @JsonValue('CUSTOMS_DECLARATION')
  customsDeclaration,
  @JsonValue('PROFILE_PHOTO')
  profilePhoto,
  @JsonValue('OTHER')
  other;

  String get displayName {
    switch (this) {
      case DocumentType.companyRegistration:
        return 'Company Registration';
      case DocumentType.taxClearance:
        return 'Tax Clearance';
      case DocumentType.businessLicense:
        return 'Business License';
      case DocumentType.insuranceCertificate:
        return 'Insurance Certificate';
      case DocumentType.vehicleRegistration:
        return 'Vehicle Registration';
      case DocumentType.fitnessCertificate:
        return 'Fitness Certificate';
      case DocumentType.roadPermit:
        return 'Road Permit';
      case DocumentType.zinaraPermit:
        return 'ZINARA Permit';
      case DocumentType.vehicleInsurance:
        return 'Vehicle Insurance';
      case DocumentType.vehiclePhotos:
        return 'Vehicle Photos';
      case DocumentType.proofOfDelivery:
        return 'Proof of Delivery';
      case DocumentType.invoice:
        return 'Invoice';
      case DocumentType.contract:
        return 'Contract';
      case DocumentType.waybill:
        return 'Waybill';
      case DocumentType.customsDeclaration:
        return 'Customs Declaration';
      case DocumentType.profilePhoto:
        return 'Profile Photo';
      case DocumentType.other:
        return 'Other';
    }
  }

  String get description {
    switch (this) {
      case DocumentType.companyRegistration:
        return 'Official company registration certificate';
      case DocumentType.taxClearance:
        return 'Tax clearance certificate from revenue authority';
      case DocumentType.businessLicense:
        return 'Business operating license';
      case DocumentType.insuranceCertificate:
        return 'Company insurance certificate';
      case DocumentType.vehicleRegistration:
        return 'Vehicle registration document';
      case DocumentType.fitnessCertificate:
        return 'Vehicle fitness certificate';
      case DocumentType.roadPermit:
        return 'Road usage permit';
      case DocumentType.zinaraPermit:
        return 'ZINARA operating permit';
      case DocumentType.vehicleInsurance:
        return 'Vehicle insurance certificate';
      case DocumentType.vehiclePhotos:
        return 'Vehicle photographs';
      case DocumentType.proofOfDelivery:
        return 'Delivery confirmation document';
      case DocumentType.invoice:
        return 'Invoice document';
      case DocumentType.contract:
        return 'Contract agreement';
      case DocumentType.waybill:
        return 'Waybill document';
      case DocumentType.customsDeclaration:
        return 'Customs declaration form';
      case DocumentType.profilePhoto:
        return 'Profile photograph';
      case DocumentType.other:
        return 'Other document type';
    }
  }

  bool get isCompanyDocument {
    return [
      DocumentType.companyRegistration,
      DocumentType.taxClearance,
      DocumentType.businessLicense,
      DocumentType.insuranceCertificate,
    ].contains(this);
  }

  bool get isVehicleDocument {
    return [
      DocumentType.vehicleRegistration,
      DocumentType.fitnessCertificate,
      DocumentType.roadPermit,
      DocumentType.zinaraPermit,
      DocumentType.vehicleInsurance,
      DocumentType.vehiclePhotos,
    ].contains(this);
  }

  bool get isLoadDocument {
    return [
      DocumentType.proofOfDelivery,
      DocumentType.invoice,
      DocumentType.contract,
      DocumentType.waybill,
      DocumentType.customsDeclaration,
    ].contains(this);
  }
}

enum DocumentStatus {
  @JsonValue('PENDING')
  pending,
  @JsonValue('VERIFIED')
  verified,
  @JsonValue('REJECTED')
  rejected,
  @JsonValue('EXPIRED')
  expired;

  String get displayName {
    switch (this) {
      case DocumentStatus.pending:
        return 'Pending';
      case DocumentStatus.verified:
        return 'Verified';
      case DocumentStatus.rejected:
        return 'Rejected';
      case DocumentStatus.expired:
        return 'Expired';
    }
  }

  String get description {
    switch (this) {
      case DocumentStatus.pending:
        return 'Document is pending verification';
      case DocumentStatus.verified:
        return 'Document has been verified and approved';
      case DocumentStatus.rejected:
        return 'Document has been rejected';
      case DocumentStatus.expired:
        return 'Document has expired';
    }
  }
}

@JsonSerializable()
class DocumentUploadRequest {
  final String name;
  final DocumentType type;
  final String? description;
  final DateTime? expiryDate;
  final int? companyId;
  final int? vehicleId;
  final int? loadId;

  const DocumentUploadRequest({
    required this.name,
    required this.type,
    this.description,
    this.expiryDate,
    this.companyId,
    this.vehicleId,
    this.loadId,
  });

  factory DocumentUploadRequest.fromJson(Map<String, dynamic> json) =>
      _$DocumentUploadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentUploadRequestToJson(this);
}

@JsonSerializable()
class DocumentUpdateRequest {
  final String? name;
  final String? description;
  final DateTime? expiryDate;

  const DocumentUpdateRequest({
    this.name,
    this.description,
    this.expiryDate,
  });

  factory DocumentUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$DocumentUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentUpdateRequestToJson(this);
}

@JsonSerializable()
class DocumentVerificationRequest {
  final DocumentStatus status;
  final String? comments;

  const DocumentVerificationRequest({
    required this.status,
    this.comments,
  });

  factory DocumentVerificationRequest.fromJson(Map<String, dynamic> json) =>
      _$DocumentVerificationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DocumentVerificationRequestToJson(this);
}

@JsonSerializable()
class FileUploadResponse {
  final bool success;
  final String message;
  final String fileName;
  final int fileSize;
  final String fileType;
  final int documentId;
  final String downloadUrl;

  const FileUploadResponse({
    required this.success,
    required this.message,
    required this.fileName,
    required this.fileSize,
    required this.fileType,
    required this.documentId,
    required this.downloadUrl,
  });

  factory FileUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$FileUploadResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FileUploadResponseToJson(this);
}
