import 'package:flutter/material.dart';
import '../../../shared/models/invoice_model.dart';

class InvoiceManagementCard extends StatelessWidget {
  final List<InvoiceModel> recentInvoices;
  final VoidCallback? onCreateInvoice;
  final VoidCallback? onViewAllInvoices;
  final Function(InvoiceModel)? onInvoiceSelected;

  const InvoiceManagementCard({
    super.key,
    required this.recentInvoices,
    this.onCreateInvoice,
    this.onViewAllInvoices,
    this.onInvoiceSelected,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.receipt_long,
                      color: theme.primaryColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Invoice Management',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'create':
                        onCreateInvoice?.call();
                        break;
                      case 'view_all':
                        onViewAllInvoices?.call();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'create',
                      child: Row(
                        children: [
                          Icon(Icons.add),
                          SizedBox(width: 8),
                          Text('Create Invoice'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'view_all',
                      child: Row(
                        children: [
                          Icon(Icons.list),
                          SizedBox(width: 8),
                          Text('View All'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInvoiceStats(context),
            const SizedBox(height: 16),
            if (recentInvoices.isEmpty)
              _buildEmptyState(context)
            else
              _buildRecentInvoices(context),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceStats(BuildContext context) {
    final theme = Theme.of(context);

    final draftCount =
        recentInvoices.where((i) => i.status == InvoiceStatus.draft).length;
    final sentCount =
        recentInvoices.where((i) => i.status == InvoiceStatus.sent).length;
    final paidCount =
        recentInvoices.where((i) => i.status == InvoiceStatus.paid).length;
    final overdueCount =
        recentInvoices.where((i) => i.status == InvoiceStatus.overdue).length;

    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            context,
            'Draft',
            draftCount.toString(),
            Colors.grey,
          ),
        ),
        Expanded(
          child: _buildStatItem(
            context,
            'Sent',
            sentCount.toString(),
            Colors.blue,
          ),
        ),
        Expanded(
          child: _buildStatItem(
            context,
            'Paid',
            paidCount.toString(),
            Colors.green,
          ),
        ),
        Expanded(
          child: _buildStatItem(
            context,
            'Overdue',
            overdueCount.toString(),
            Colors.red,
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(
      BuildContext context, String label, String value, Color color) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.receipt_long,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No invoices yet',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first invoice to get started',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            onPressed: onCreateInvoice,
            icon: const Icon(Icons.add),
            label: const Text('Create Invoice'),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentInvoices(BuildContext context) {
    final displayInvoices = recentInvoices.take(3).toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Invoices',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
        ),
        const SizedBox(height: 8),
        ...displayInvoices.map((invoice) {
          return _buildInvoiceItem(context, invoice);
        }),
        if (recentInvoices.length > 3)
          TextButton(
            onPressed: onViewAllInvoices,
            child: Text('View all ${recentInvoices.length} invoices'),
          ),
      ],
    );
  }

  Widget _buildInvoiceItem(BuildContext context, InvoiceModel invoice) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: () => onInvoiceSelected?.call(invoice),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: _getStatusColor(invoice.status).withOpacity(0.1),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Icon(
                Icons.receipt,
                size: 16,
                color: _getStatusColor(invoice.status),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    invoice.invoiceNumber ?? 'Invoice #${invoice.id}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (invoice.loadTitle != null) ...[
                    const SizedBox(height: 2),
                    Text(
                      invoice.loadTitle!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${invoice.totalAmountValue.toStringAsFixed(2)}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 2),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: _getStatusColor(invoice.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    invoice.status.displayName,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _getStatusColor(invoice.status),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(InvoiceStatus status) {
    switch (status) {
      case InvoiceStatus.draft:
        return Colors.grey;
      case InvoiceStatus.sent:
        return Colors.blue;
      case InvoiceStatus.viewed:
        return Colors.orange;
      case InvoiceStatus.paid:
        return Colors.green;
      case InvoiceStatus.overdue:
        return Colors.red;
      case InvoiceStatus.cancelled:
        return Colors.grey;
      case InvoiceStatus.refunded:
        return Colors.purple;
    }
  }
}
