import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../shared/widgets/unified_header.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../core/constants/app_constants.dart';
import '../bloc/payment_bloc.dart';
import '../widgets/financial_overview_card.dart';
import '../widgets/recent_transactions_card.dart';
import '../widgets/invoice_management_card.dart';
import '../widgets/client_management_card.dart';
import '../widgets/financial_reports_card.dart';

/// Comprehensive financial management screen for LogiPool
/// Includes invoicing, payments tracking, client management, and financial reports
class FinancialManagementScreen extends StatefulWidget {
  const FinancialManagementScreen({super.key});

  @override
  State<FinancialManagementScreen> createState() =>
      _FinancialManagementScreenState();
}

class _FinancialManagementScreenState extends State<FinancialManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadFinancialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadFinancialData() {
    context.read<PaymentBloc>().add(const LoadPaymentSummary());
    context.read<PaymentBloc>().add(const LoadInvoiceSummary());
    context.read<PaymentBloc>().add(const LoadPayments());
    context.read<PaymentBloc>().add(const LoadInvoices());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedHeader(
        title: 'Financial Management',
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            onPressed: _loadFinancialData,
            tooltip: 'Refresh Financial Data',
          ),
          IconButton(
            icon: Icon(
              Icons.download,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            onPressed: _exportFinancialReport,
            tooltip: 'Export Financial Report',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildInvoicingTab(),
                _buildPaymentsTab(),
                _buildReportsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(
            text: 'Overview',
            icon: Icon(Icons.dashboard),
          ),
          Tab(
            text: 'Invoicing',
            icon: Icon(Icons.receipt_long),
          ),
          Tab(
            text: 'Payments',
            icon: Icon(Icons.payment),
          ),
          Tab(
            text: 'Reports',
            icon: Icon(Icons.analytics),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewTab() {
    return RefreshIndicator(
      onRefresh: () async => _loadFinancialData(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Financial Overview Cards
            _buildFinancialOverview(),
            const SizedBox(height: 24),

            // Quick Actions
            _buildQuickActions(),
            const SizedBox(height: 24),

            // Recent Transactions
            _buildRecentTransactions(),
            const SizedBox(height: 24),

            // Pending Invoices
            _buildPendingInvoices(),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoicingTab() {
    return RefreshIndicator(
      onRefresh: () async => _loadFinancialData(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Invoice Management
            _buildInvoiceManagement(),
            const SizedBox(height: 24),

            // Client Management
            _buildClientManagement(),
            const SizedBox(height: 24),

            // Invoice Templates
            _buildInvoiceTemplates(),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentsTab() {
    return RefreshIndicator(
      onRefresh: () async => _loadFinancialData(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Payment Overview
            _buildPaymentOverview(),
            const SizedBox(height: 24),

            // Payment Methods
            _buildPaymentMethods(),
            const SizedBox(height: 24),

            // Transaction History
            _buildTransactionHistory(),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsTab() {
    return RefreshIndicator(
      onRefresh: () async => _loadFinancialData(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Financial Reports
            _buildFinancialReports(),
            const SizedBox(height: 24),

            // Analytics
            _buildFinancialAnalytics(),
            const SizedBox(height: 24),

            // Export Options
            _buildExportOptions(),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialOverview() {
    return BlocBuilder<PaymentBloc, PaymentState>(
      builder: (context, state) {
        if (state is PaymentLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: LoadingWidget(),
            ),
          );
        }

        if (state is PaymentError) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: CustomErrorWidget(
                message: state.message,
                onRetry: _loadFinancialData,
              ),
            ),
          );
        }

        // For now, show placeholder cards
        return Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: _buildOverviewCard(
                    'Total Revenue',
                    '\$125,430',
                    Icons.trending_up,
                    Colors.green,
                    '+12.5%',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildOverviewCard(
                    'Outstanding',
                    '\$8,750',
                    Icons.schedule,
                    Colors.orange,
                    '5 invoices',
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildOverviewCard(
                    'This Month',
                    '\$23,890',
                    Icons.calendar_month,
                    Colors.blue,
                    '+8.2%',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildOverviewCard(
                    'Overdue',
                    '\$2,340',
                    Icons.warning,
                    Colors.red,
                    '2 invoices',
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  Widget _buildOverviewCard(
    String title,
    String amount,
    IconData icon,
    Color color,
    String subtitle,
  ) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              amount,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: theme.textTheme.bodySmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    'Create Invoice',
                    Icons.add_circle,
                    Colors.blue,
                    () => _createInvoice(),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    'Add Client',
                    Icons.person_add,
                    Colors.green,
                    () => _addClient(),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    'Record Payment',
                    Icons.payment,
                    Colors.purple,
                    () => _recordPayment(),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    'Generate Report',
                    Icons.analytics,
                    Colors.orange,
                    () => _generateReport(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.1),
        foregroundColor: color,
        elevation: 0,
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      ),
    );
  }

  // Placeholder methods for the remaining UI components
  Widget _buildRecentTransactions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Transactions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                TextButton(
                  onPressed: () => context.push('/payments/transactions'),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text('Recent transactions will be displayed here...'),
          ],
        ),
      ),
    );
  }

  Widget _buildPendingInvoices() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Pending Invoices',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                TextButton(
                  onPressed: () => context.push('/payments/invoices'),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text('Pending invoices will be displayed here...'),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceManagement() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invoice Management',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            const Text('Invoice management features coming soon...'),
          ],
        ),
      ),
    );
  }

  Widget _buildClientManagement() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Client Management',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            const Text('Client management features coming soon...'),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceTemplates() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invoice Templates',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            const Text('Invoice templates coming soon...'),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentOverview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Overview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            const Text('Payment overview coming soon...'),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethods() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Methods',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            const Text('Payment methods configuration coming soon...'),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionHistory() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Transaction History',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            const Text('Transaction history coming soon...'),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialReports() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Financial Reports',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            const Text('Financial reports coming soon...'),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialAnalytics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Financial Analytics',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            const Text('Financial analytics coming soon...'),
          ],
        ),
      ),
    );
  }

  Widget _buildExportOptions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Export Options',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            const Text('Export options coming soon...'),
          ],
        ),
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    return FutureBuilder<UserModel?>(
      future: context.read<AuthService>().getCurrentUser(),
      builder: (context, snapshot) {
        final user = snapshot.data;
        if (user?.role == AppConstants.roleTransporter ||
            user?.role == AppConstants.roleAdmin) {
          return FloatingActionButton.extended(
            onPressed: _createInvoice,
            icon: const Icon(Icons.add),
            label: const Text('Create Invoice'),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }

  // Action methods
  void _createInvoice() {
    // TODO: Implement invoice creation screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Invoice creation feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _addClient() {
    context.push('/payments/clients/add');
  }

  void _recordPayment() {
    context.push('/payments/record');
  }

  void _generateReport() {
    context.push('/payments/reports/generate');
  }

  void _exportFinancialReport() {
    // Show export options dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Financial Report'),
        content: const Text('Choose export format:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Export as PDF
            },
            child: const Text('PDF'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // Export as Excel
            },
            child: const Text('Excel'),
          ),
        ],
      ),
    );
  }
}
