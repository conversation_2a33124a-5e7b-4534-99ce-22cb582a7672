package zw.co.kanjan.logipool.dto.guest;

import jakarta.validation.constraints.*;
import lombok.Data;
import zw.co.kanjan.logipool.entity.Load;

import java.time.LocalDate;

@Data
public class GuestLoadInquiryRequest {
    
    // Contact Information
    @NotBlank
    @Size(max = 100)
    private String name;
    
    @NotBlank
    @Email
    @Size(max = 100)
    private String email;
    
    @NotBlank
    @Size(max = 20)
    private String phoneNumber;
    
    @Size(max = 100)
    private String company;
    
    // Load Information
    @NotBlank
    @Size(max = 100)
    private String title;
    
    @NotBlank
    @Size(max = 1000)
    private String description;
    
    @NotNull
    private Load.LoadType loadType;
    
    @NotNull
    @DecimalMin("0.1")
    private Double weight; // in kg
    
    @Size(max = 50)
    private String dimensions; // e.g., "2m×1m×1m"
    
    // Location and Timing
    @NotBlank
    @Size(max = 200)
    private String pickupLocation;
    
    @NotBlank
    @Size(max = 200)
    private String deliveryLocation;
    
    @NotNull
    @Future
    private LocalDate pickupDate;
    
    @Future
    private LocalDate deliveryDate;
    
    // Additional Options
    private Boolean requiresSpecialHandling = false;
    
    private Boolean isUrgent = false;
    
    @Size(max = 500)
    private String specialInstructions;
    
    // System fields
    private String ipAddress;
    private String userAgent;
}
