import 'package:flutter/material.dart';
import '../../../shared/models/company_member_model.dart';

class InviteUserDialog extends StatefulWidget {
  final int companyId;
  final Function(InviteUserRequest) onInvite;

  const InviteUserDialog({
    super.key,
    required this.companyId,
    required this.onInvite,
  });

  @override
  State<InviteUserDialog> createState() => _InviteUserDialogState();
}

class _InviteUserDialogState extends State<InviteUserDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _messageController = TextEditingController();

  CompanyRole _selectedRole = CompanyRole.driver;
  Map<String, bool> _permissions = {};
  bool _useCustomPermissions = false;

  @override
  void initState() {
    super.initState();
    _updatePermissionsForRole(_selectedRole);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.person_add, color: Colors.white),
                  const SizedBox(width: 12),
                  const Text(
                    'Invite Team Member',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Email field
                      TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          labelText: 'Email Address',
                          hintText: 'Enter user email',
                          prefixIcon: Icon(Icons.email),
                          border: OutlineInputBorder(),
                        ),
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter an email address';
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                              .hasMatch(value)) {
                            return 'Please enter a valid email address';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Role selection
                      Text(
                        'Role',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<CompanyRole>(
                        value: _selectedRole,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.work),
                        ),
                        items: CompanyRole.values.map((role) {
                          return DropdownMenuItem(
                            value: role,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(_getRoleDisplayName(role)),
                                Text(
                                  _getRoleDescription(role),
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        color: Colors.grey[600],
                                      ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (role) {
                          if (role != null) {
                            setState(() {
                              _selectedRole = role;
                              _updatePermissionsForRole(role);
                            });
                          }
                        },
                      ),
                      const SizedBox(height: 16),

                      // Custom permissions toggle
                      CheckboxListTile(
                        title: const Text('Customize Permissions'),
                        subtitle:
                            const Text('Override default role permissions'),
                        value: _useCustomPermissions,
                        onChanged: (value) {
                          setState(() {
                            _useCustomPermissions = value ?? false;
                          });
                        },
                      ),

                      // Permissions section
                      if (_useCustomPermissions) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Permissions',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        ..._buildPermissionCheckboxes(),
                      ],

                      const SizedBox(height: 16),

                      // Invitation message
                      TextFormField(
                        controller: _messageController,
                        decoration: const InputDecoration(
                          labelText: 'Invitation Message (Optional)',
                          hintText: 'Add a personal message to the invitation',
                          border: OutlineInputBorder(),
                          alignLabelWithHint: true,
                        ),
                        maxLines: 3,
                        maxLength: 500,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _sendInvitation,
                    child: const Text('Send Invitation'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildPermissionCheckboxes() {
    final permissions = [
      ('canManageMembers', 'Manage Team Members', Icons.people),
      ('canManageLoads', 'Manage Loads', Icons.local_shipping),
      ('canUpdateLoadStatus', 'Update Load Status', Icons.update),
      ('canUploadDocuments', 'Upload Documents', Icons.upload_file),
      ('canGenerateInvoices', 'Generate Invoices', Icons.receipt),
      ('canViewFinancials', 'View Financial Data', Icons.attach_money),
      ('canTrackLocation', 'Share Location', Icons.location_on),
    ];

    return permissions.map((permission) {
      final key = permission.$1;
      final label = permission.$2;
      final icon = permission.$3;

      return CheckboxListTile(
        title: Row(
          children: [
            Icon(icon, size: 20),
            const SizedBox(width: 8),
            Text(label),
          ],
        ),
        value: _permissions[key] ?? false,
        onChanged: (value) {
          setState(() {
            _permissions[key] = value ?? false;
          });
        },
      );
    }).toList();
  }

  void _updatePermissionsForRole(CompanyRole role) {
    _permissions = _getDefaultPermissions(role);
  }

  Map<String, bool> _getDefaultPermissions(CompanyRole role) {
    switch (role) {
      case CompanyRole.owner:
        return {
          'canManageMembers': true,
          'canManageLoads': true,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': true,
          'canViewFinancials': true,
          'canTrackLocation': false,
        };
      case CompanyRole.manager:
        return {
          'canManageMembers': true,
          'canManageLoads': true,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': true,
          'canViewFinancials': true,
          'canTrackLocation': false,
        };
      case CompanyRole.dispatcher:
        return {
          'canManageMembers': false,
          'canManageLoads': true,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': false,
          'canViewFinancials': false,
          'canTrackLocation': false,
        };
      case CompanyRole.driver:
        return {
          'canManageMembers': false,
          'canManageLoads': false,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': false,
          'canViewFinancials': false,
          'canTrackLocation': true,
        };
      case CompanyRole.accountant:
        return {
          'canManageMembers': false,
          'canManageLoads': false,
          'canUpdateLoadStatus': false,
          'canUploadDocuments': true,
          'canGenerateInvoices': true,
          'canViewFinancials': true,
          'canTrackLocation': false,
        };
      case CompanyRole.viewer:
        return {
          'canManageMembers': false,
          'canManageLoads': false,
          'canUpdateLoadStatus': false,
          'canUploadDocuments': false,
          'canGenerateInvoices': false,
          'canViewFinancials': false,
          'canTrackLocation': false,
        };
    }
  }

  String _getRoleDisplayName(CompanyRole role) {
    switch (role) {
      case CompanyRole.owner:
        return 'Owner';
      case CompanyRole.manager:
        return 'Manager';
      case CompanyRole.driver:
        return 'Driver';
      case CompanyRole.dispatcher:
        return 'Dispatcher';
      case CompanyRole.accountant:
        return 'Accountant';
      case CompanyRole.viewer:
        return 'Viewer';
    }
  }

  String _getRoleDescription(CompanyRole role) {
    switch (role) {
      case CompanyRole.owner:
        return 'Full access to all company features and settings';
      case CompanyRole.manager:
        return 'Can manage loads, members, and company operations';
      case CompanyRole.dispatcher:
        return 'Can manage loads and coordinate transportation';
      case CompanyRole.driver:
        return 'Can update load status and share location while driving';
      case CompanyRole.accountant:
        return 'Can manage invoices, payments, and financial data';
      case CompanyRole.viewer:
        return 'Read-only access to company information';
    }
  }

  void _sendInvitation() {
    if (_formKey.currentState?.validate() ?? false) {
      // Get default permissions for the role if not using custom permissions
      final defaultPermissions = _useCustomPermissions
          ? <String, bool>{}
          : _getDefaultPermissions(_selectedRole);

      final request = InviteUserRequest(
        email: _emailController.text.trim(),
        role: _selectedRole,
        canManageMembers: _useCustomPermissions
            ? (_permissions['canManageMembers'] ?? false)
            : (defaultPermissions['canManageMembers'] ?? false),
        canManageLoads: _useCustomPermissions
            ? (_permissions['canManageLoads'] ?? false)
            : (defaultPermissions['canManageLoads'] ?? false),
        canUpdateLoadStatus: _useCustomPermissions
            ? (_permissions['canUpdateLoadStatus'] ?? false)
            : (defaultPermissions['canUpdateLoadStatus'] ?? false),
        canUploadDocuments: _useCustomPermissions
            ? (_permissions['canUploadDocuments'] ?? false)
            : (defaultPermissions['canUploadDocuments'] ?? false),
        canGenerateInvoices: _useCustomPermissions
            ? (_permissions['canGenerateInvoices'] ?? false)
            : (defaultPermissions['canGenerateInvoices'] ?? false),
        canViewFinancials: _useCustomPermissions
            ? (_permissions['canViewFinancials'] ?? false)
            : (defaultPermissions['canViewFinancials'] ?? false),
        canTrackLocation: _useCustomPermissions
            ? (_permissions['canTrackLocation'] ?? false)
            : (defaultPermissions['canTrackLocation'] ?? false),
        invitationMessage: _messageController.text.trim().isNotEmpty
            ? _messageController.text.trim()
            : null,
      );

      widget.onInvite(request);
      Navigator.of(context).pop();
    }
  }
}
