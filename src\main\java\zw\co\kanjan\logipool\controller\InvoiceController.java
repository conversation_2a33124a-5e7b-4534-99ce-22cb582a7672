package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.invoice.InvoiceDto;
import zw.co.kanjan.logipool.service.InvoiceService;

import java.io.IOException;

@RestController
@RequestMapping("/api/invoices")
@RequiredArgsConstructor
@Tag(name = "Invoices", description = "Invoice management APIs")
public class InvoiceController {
    
    private final InvoiceService invoiceService;
    
    @PostMapping
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Create invoice", description = "Create a new invoice for a load")
    public ResponseEntity<InvoiceDto.InvoiceResponse> createInvoice(
            @Valid @RequestBody InvoiceDto.InvoiceCreateRequest request,
            Authentication authentication) {
        
        InvoiceDto.InvoiceResponse response = invoiceService.createInvoice(request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/loads/{loadId}/generate")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Generate automatic invoice",
               description = "Generate an automatic invoice for an assigned, in-transit, or delivered load based on the accepted bid")
    public ResponseEntity<InvoiceDto.InvoiceResponse> generateAutomaticInvoice(
            @PathVariable Long loadId,
            Authentication authentication) {

        InvoiceDto.InvoiceResponse response = invoiceService.generateAutomaticInvoice(loadId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/loads/{loadId}/preview")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Preview automatic invoice",
               description = "Preview what an automatic invoice would look like for an assigned, in-transit, or delivered load without saving it")
    public ResponseEntity<InvoiceDto.InvoicePreviewResponse> previewAutomaticInvoice(
            @PathVariable Long loadId,
            Authentication authentication) {

        InvoiceDto.InvoicePreviewResponse response = invoiceService.previewAutomaticInvoice(loadId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @PostMapping("/loads/{loadId}/generate-with-modifications")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Generate invoice with modifications",
               description = "Generate an invoice for an assigned, in-transit, or delivered load with custom modifications")
    public ResponseEntity<InvoiceDto.InvoiceResponse> generateInvoiceWithModifications(
            @PathVariable Long loadId,
            @Valid @RequestBody InvoiceDto.InvoiceModificationRequest request,
            Authentication authentication) {

        InvoiceDto.InvoiceResponse response = invoiceService.generateInvoiceWithModifications(loadId, request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/{invoiceId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get invoice", description = "Get invoice details by ID")
    public ResponseEntity<InvoiceDto.InvoiceResponse> getInvoice(
            @PathVariable Long invoiceId,
            Authentication authentication) {
        
        InvoiceDto.InvoiceResponse response = invoiceService.getInvoice(invoiceId, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/summary")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get invoice summary", description = "Get invoice statistics and summary for the current user")
    public ResponseEntity<InvoiceDto.InvoiceSummary> getInvoiceSummary(
            Authentication authentication) {

        InvoiceDto.InvoiceSummary summary = invoiceService.getInvoiceSummary(authentication.getName());
        return ResponseEntity.ok(summary);
    }

    @GetMapping("/user")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get user invoices", description = "Get paginated list of invoices for the current user")
    public ResponseEntity<Page<InvoiceDto.InvoiceResponse>> getUserInvoices(
            Pageable pageable,
            Authentication authentication) {

        Page<InvoiceDto.InvoiceResponse> invoices = invoiceService.getInvoices(pageable, authentication.getName());
        return ResponseEntity.ok(invoices);
    }

    @GetMapping
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get invoices", description = "Get paginated list of invoices for the current user")
    public ResponseEntity<Page<InvoiceDto.InvoiceResponse>> getInvoices(
            Pageable pageable,
            Authentication authentication) {

        Page<InvoiceDto.InvoiceResponse> invoices = invoiceService.getInvoices(pageable, authentication.getName());
        return ResponseEntity.ok(invoices);
    }
    
    @GetMapping("/loads/{loadId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get load invoices", description = "Get all invoices for a specific load")
    public ResponseEntity<Page<InvoiceDto.InvoiceResponse>> getLoadInvoices(
            @PathVariable Long loadId,
            Pageable pageable,
            Authentication authentication) {
        
        Page<InvoiceDto.InvoiceResponse> invoices = invoiceService.getLoadInvoices(loadId, pageable, authentication.getName());
        return ResponseEntity.ok(invoices);
    }
    
    @PutMapping("/{invoiceId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Update invoice", description = "Update invoice details (only draft invoices can be updated)")
    public ResponseEntity<InvoiceDto.InvoiceResponse> updateInvoice(
            @PathVariable Long invoiceId,
            @Valid @RequestBody InvoiceDto.InvoiceUpdateRequest request,
            Authentication authentication) {
        
        InvoiceDto.InvoiceResponse response = invoiceService.updateInvoice(invoiceId, request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/{invoiceId}/send")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Send invoice", description = "Send invoice to client (changes status from draft to sent)")
    public ResponseEntity<InvoiceDto.InvoiceResponse> sendInvoice(
            @PathVariable Long invoiceId,
            Authentication authentication) {
        
        InvoiceDto.InvoiceResponse response = invoiceService.sendInvoice(invoiceId, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @DeleteMapping("/{invoiceId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Delete invoice", description = "Delete invoice (only draft invoices can be deleted)")
    public ResponseEntity<Void> deleteInvoice(
            @PathVariable Long invoiceId,
            Authentication authentication) {
        
        invoiceService.deleteInvoice(invoiceId, authentication.getName());
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/{invoiceId}/pdf")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Download invoice PDF", description = "Generate and download invoice as PDF")
    public ResponseEntity<Resource> downloadInvoicePdf(
            @PathVariable Long invoiceId,
            @RequestParam(required = false) String token,
            Authentication authentication,
            HttpServletRequest request) {

        InvoiceService.InvoiceFileResponse fileResponse = invoiceService.generateInvoiceFile(invoiceId, authentication.getName());

        // Try to determine file's content type
        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(fileResponse.getResource().getFile().getAbsolutePath());
        } catch (IOException ex) {
            // Fallback to default content type
        }

        // Use the content type from the response, with fallback
        if (contentType == null) {
            contentType = fileResponse.getContentType() != null ? fileResponse.getContentType() : "application/octet-stream";
        }

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + fileResponse.getFileName() + "\"")
                .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                .header(HttpHeaders.PRAGMA, "no-cache")
                .header(HttpHeaders.EXPIRES, "0")
                .body(fileResponse.getResource());
    }

    @GetMapping("/loads/{loadId}/uploaded-documents")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get uploaded invoice documents",
               description = "Get information about uploaded invoice documents for a load")
    public ResponseEntity<InvoiceDto.UploadedInvoiceDocumentsResponse> getUploadedInvoiceDocuments(
            @PathVariable Long loadId,
            Authentication authentication) {

        InvoiceDto.UploadedInvoiceDocumentsResponse response = invoiceService.getUploadedInvoiceDocuments(loadId, authentication.getName());
        return ResponseEntity.ok(response);
    }
}
