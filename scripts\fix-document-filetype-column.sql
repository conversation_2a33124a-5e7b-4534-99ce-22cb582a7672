-- Fix for Document fileType column size constraint
-- This script increases the fileType column size from 20 to 100 characters
-- to accommodate longer MIME types like 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'

-- Check current column definition
SELECT 
    column_name, 
    data_type, 
    character_maximum_length,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'documents' 
  AND column_name = 'file_type';

-- Update the column size if it's currently limited to 20 characters
-- This is only needed if Hibernate doesn't automatically update the schema
DO $$
BEGIN
    -- Check if the column exists and has a length constraint
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'documents' 
          AND column_name = 'file_type'
          AND character_maximum_length = 20
    ) THEN
        -- Alter the column to increase the size
        ALTER TABLE documents ALTER COLUMN file_type TYPE VARCHAR(100);
        RAISE NOTICE 'Successfully updated file_type column to VARCHAR(100)';
    ELSE
        RAISE NOTICE 'file_type column is already properly sized or does not exist';
    END IF;
END $$;

-- Verify the change
SELECT 
    column_name, 
    data_type, 
    character_maximum_length,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'documents' 
  AND column_name = 'file_type';

-- Optional: Check for any existing records that might have been truncated
SELECT 
    id,
    name,
    file_type,
    LENGTH(file_type) as file_type_length
FROM documents 
WHERE LENGTH(file_type) >= 18  -- Close to the old 20-character limit
ORDER BY LENGTH(file_type) DESC;
