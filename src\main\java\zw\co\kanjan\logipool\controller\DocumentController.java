package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import zw.co.kanjan.logipool.dto.DocumentDto;
import zw.co.kanjan.logipool.entity.Document;
import zw.co.kanjan.logipool.service.DocumentService;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;

@RestController
@RequestMapping("/api/documents")
@RequiredArgsConstructor
@Tag(name = "Documents", description = "Document management APIs")
public class DocumentController {
    
    private final DocumentService documentService;
    
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Upload document", description = "Upload a new document with metadata")
    public ResponseEntity<DocumentDto.FileUploadResponse> uploadDocument(
            @RequestParam("file") MultipartFile file,
            @RequestParam("name") String name,
            @RequestParam("type") String type,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "expiryDate", required = false) String expiryDate,
            @RequestParam(value = "companyId", required = false) Long companyId,
            @RequestParam(value = "vehicleId", required = false) Long vehicleId,
            @RequestParam(value = "loadId", required = false) Long loadId,
            Authentication authentication) {
        
        DocumentDto.DocumentUploadRequest request = DocumentDto.DocumentUploadRequest.builder()
            .name(name)
            .type(parseDocumentType(type))
            .description(description)
            .companyId(companyId)
            .vehicleId(vehicleId)
            .loadId(loadId)
            .build();
        
        DocumentDto.FileUploadResponse response = documentService.uploadDocument(file, request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get document", description = "Get document details by ID")
    public ResponseEntity<DocumentDto.DocumentResponse> getDocument(
            @PathVariable Long id,
            Authentication authentication) {
        
        DocumentDto.DocumentResponse response = documentService.getDocument(id, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get documents", description = "Get paginated list of documents")
    public ResponseEntity<Page<DocumentDto.DocumentResponse>> getDocuments(
            Pageable pageable,
            Authentication authentication) {
        
        Page<DocumentDto.DocumentResponse> response = documentService.getDocuments(pageable, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/company/{companyId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get company documents", description = "Get documents for a specific company")
    public ResponseEntity<Page<DocumentDto.DocumentResponse>> getCompanyDocuments(
            @PathVariable Long companyId,
            Pageable pageable,
            Authentication authentication) {

        Page<DocumentDto.DocumentResponse> response = documentService.getCompanyDocuments(companyId, pageable, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/vehicle/{vehicleId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get vehicle documents", description = "Get documents for a specific vehicle")
    public ResponseEntity<Page<DocumentDto.DocumentResponse>> getVehicleDocuments(
            @PathVariable Long vehicleId,
            Pageable pageable,
            Authentication authentication) {

        Page<DocumentDto.DocumentResponse> response = documentService.getVehicleDocuments(vehicleId, pageable, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/{id}/download")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Download document", description = "Download document file")
    public ResponseEntity<Resource> downloadDocument(
            @PathVariable Long id,
            @RequestParam(required = false) String token,
            Authentication authentication,
            HttpServletRequest request) {

        DocumentService.DocumentDownloadResponse downloadResponse = documentService.downloadDocument(id, authentication.getName());

        // Use the stored content type from the document, with fallback logic
        String contentType = downloadResponse.getContentType();

        // If no content type is stored, try to determine it from the file
        if (contentType == null || contentType.isEmpty()) {
            try {
                contentType = request.getServletContext().getMimeType(downloadResponse.getResource().getFile().getAbsolutePath());
            } catch (IOException ex) {
                // Fallback to default content type
            }
        }

        // Final fallback to the default content type if type could not be determined
        if (contentType == null || contentType.isEmpty()) {
            contentType = "application/octet-stream";
        }

        // Use the original filename with extension from the document entity
        String filename = downloadResponse.getOriginalFileName();
        if (filename == null || filename.isEmpty()) {
            filename = downloadResponse.getResource().getFilename();
        }

        // Ensure filename has proper extension if missing
        if (filename != null && !filename.contains(".")) {
            // Try to add extension based on content type
            if ("application/pdf".equals(contentType)) {
                filename += ".pdf";
            } else if ("image/png".equals(contentType)) {
                filename += ".png";
            } else if ("image/jpeg".equals(contentType)) {
                filename += ".jpg";
            } else if ("text/plain".equals(contentType)) {
                filename += ".txt";
            }
        }

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .header(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate")
                .header(HttpHeaders.PRAGMA, "no-cache")
                .header(HttpHeaders.EXPIRES, "0")
                .body(downloadResponse.getResource());
    }
    
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Update document", description = "Update document metadata")
    public ResponseEntity<DocumentDto.DocumentResponse> updateDocument(
            @PathVariable Long id,
            @Valid @RequestBody DocumentDto.DocumentUpdateRequest request,
            Authentication authentication) {
        
        DocumentDto.DocumentResponse response = documentService.updateDocument(id, request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Delete document", description = "Delete document and its file")
    public ResponseEntity<Void> deleteDocument(
            @PathVariable Long id,
            Authentication authentication) {
        
        documentService.deleteDocument(id, authentication.getName());
        return ResponseEntity.noContent().build();
    }
    
    @PostMapping("/{id}/verify")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Verify document", description = "Verify or reject document")
    public ResponseEntity<DocumentDto.DocumentResponse> verifyDocument(
            @PathVariable Long id,
            @Valid @RequestBody DocumentDto.DocumentVerificationRequest request,
            Authentication authentication) {

        DocumentDto.DocumentResponse response = documentService.verifyDocument(id, request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    // Load Document Management Endpoints

    @GetMapping("/loads/{loadId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get load documents", description = "Get all documents for a specific load")
    public ResponseEntity<Page<DocumentDto.DocumentResponse>> getLoadDocuments(
            @PathVariable Long loadId,
            Pageable pageable,
            Authentication authentication) {

        Page<DocumentDto.DocumentResponse> documents = documentService.getLoadDocuments(loadId, pageable, authentication.getName());
        return ResponseEntity.ok(documents);
    }

    @PostMapping(value = "/loads/{loadId}/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Upload load document", description = "Upload a document for a specific load")
    public ResponseEntity<DocumentDto.FileUploadResponse> uploadLoadDocument(
            @PathVariable Long loadId,
            @RequestParam("file") MultipartFile file,
            @RequestParam("name") String name,
            @RequestParam("type") String type,
            @RequestParam(value = "description", required = false) String description,
            Authentication authentication) {

        DocumentDto.DocumentUploadRequest request = DocumentDto.DocumentUploadRequest.builder()
            .name(name)
            .type(parseDocumentType(type))
            .description(description)
            .build();

        DocumentDto.FileUploadResponse response = documentService.uploadLoadDocument(loadId, file, request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @PostMapping("/loads/{loadId}/proof-of-delivery")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Generate proof of delivery", description = "Generate proof of delivery document for a delivered load")
    public ResponseEntity<DocumentDto.DocumentResponse> generateProofOfDelivery(
            @PathVariable Long loadId,
            @RequestParam(value = "notes", required = false) String notes,
            Authentication authentication) {

        DocumentDto.DocumentResponse response = documentService.generateProofOfDelivery(loadId, notes, authentication.getName());
        return ResponseEntity.ok(response);
    }

    /**
     * Parse document type string to enum, handling legacy format compatibility
     */
    private Document.DocumentType parseDocumentType(String type) {
        // Handle legacy format where underscores might be missing
        switch (type.toUpperCase()) {
            case "ROADPERMIT":
                return Document.DocumentType.ROAD_PERMIT;
            case "ZINARAPERMIT":
                return Document.DocumentType.ZINARA_PERMIT;
            case "COMPANYREGISTRATION":
                return Document.DocumentType.COMPANY_REGISTRATION;
            case "TAXCLEARANCE":
                return Document.DocumentType.TAX_CLEARANCE;
            case "BUSINESSLICENSE":
                return Document.DocumentType.BUSINESS_LICENSE;
            case "INSURANCECERTIFICATE":
                return Document.DocumentType.INSURANCE_CERTIFICATE;
            case "VEHICLEREGISTRATION":
                return Document.DocumentType.VEHICLE_REGISTRATION;
            case "FITNESSCERTIFICATE":
                return Document.DocumentType.FITNESS_CERTIFICATE;
            case "VEHICLEINSURANCE":
                return Document.DocumentType.VEHICLE_INSURANCE;
            case "VEHICLEPHOTOS":
                return Document.DocumentType.VEHICLE_PHOTOS;
            case "PROOFOFDELIVERY":
                return Document.DocumentType.PROOF_OF_DELIVERY;
            case "CUSTOMSDECLARATION":
                return Document.DocumentType.CUSTOMS_DECLARATION;
            case "PROFILEPHOTO":
                return Document.DocumentType.PROFILE_PHOTO;
            default:
                // Try standard enum parsing for correct format
                return Document.DocumentType.valueOf(type.toUpperCase());
        }
    }
}
