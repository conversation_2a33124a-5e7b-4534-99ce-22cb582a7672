#!/bin/bash

# SSL Certificate Setup Script for LogiPool with <PERSON><PERSON><PERSON><PERSON> and Let's Encrypt
# This script helps set up and manage SSL certificates

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ACME_FILE="$PROJECT_ROOT/traefik_letsencrypt/acme.json"
ENV_FILE="$PROJECT_ROOT/.env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if .env file exists
    if [[ ! -f "$ENV_FILE" ]]; then
        log_error "Environment file .env not found. Please copy .env.production to .env and configure it."
        exit 1
    fi
    
    # Check if domain name is set
    if ! grep -q "DOMAIN_NAME=" "$ENV_FILE"; then
        log_error "DOMAIN_NAME not set in .env file"
        exit 1
    fi
    
    # Check if ACME email is set
    if ! grep -q "ACME_EMAIL=" "$ENV_FILE"; then
        log_error "ACME_EMAIL not set in .env file"
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Create ACME storage directory and file
setup_acme_storage() {
    log_info "Setting up ACME storage..."
    
    # Create directory if it doesn't exist
    mkdir -p "$(dirname "$ACME_FILE")"
    
    # Create acme.json file if it doesn't exist
    if [[ ! -f "$ACME_FILE" ]]; then
        echo '{}' > "$ACME_FILE"
        log_success "Created ACME storage file: $ACME_FILE"
    else
        log_warning "ACME storage file already exists: $ACME_FILE"
    fi
    
    # Set correct permissions (Let's Encrypt requires 600)
    chmod 600 "$ACME_FILE"
    log_success "Set correct permissions on ACME file"
}

# Verify domain DNS configuration
verify_dns() {
    local domain_name
    domain_name=$(grep "DOMAIN_NAME=" "$ENV_FILE" | cut -d'=' -f2)
    
    log_info "Verifying DNS configuration for $domain_name..."
    
    # Get server's public IP
    local server_ip
    server_ip=$(curl -s ifconfig.me || curl -s ipinfo.io/ip || echo "Unable to determine")
    
    log_info "Server IP: $server_ip"
    
    # Check DNS resolution
    local resolved_ip
    resolved_ip=$(nslookup "$domain_name" | grep -A1 "Name:" | tail -1 | awk '{print $2}' || echo "Unable to resolve")
    
    log_info "Domain $domain_name resolves to: $resolved_ip"
    
    if [[ "$server_ip" == "$resolved_ip" ]]; then
        log_success "DNS configuration is correct"
    else
        log_warning "DNS configuration may not be correct. Please ensure $domain_name points to $server_ip"
        log_warning "Let's Encrypt certificate generation may fail if DNS is not properly configured"
    fi
}

# Test certificate generation (staging)
test_certificate() {
    log_info "Testing certificate generation with Let's Encrypt staging..."
    
    # Create a temporary docker-compose override for staging
    cat > "$PROJECT_ROOT/docker-compose.staging.yml" << EOF
version: '3.8'

services:
  traefik:
    environment:
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_CASERVER=https://acme-staging-v02.api.letsencrypt.org/directory
    labels:
      - "traefik.http.routers.traefik-dashboard.tls.certresolver=letsencrypt"
EOF
    
    log_info "Created staging configuration. You can test with:"
    log_info "docker-compose -f docker-compose.traefik.yml -f docker-compose.staging.yml up -d"
    log_warning "Remember to remove docker-compose.staging.yml when moving to production"
}

# Check certificate status
check_certificates() {
    log_info "Checking certificate status..."
    
    if [[ ! -f "$ACME_FILE" ]]; then
        log_warning "ACME file not found. No certificates generated yet."
        return
    fi
    
    # Check if acme.json has certificates
    if [[ $(cat "$ACME_FILE") == "{}" ]]; then
        log_warning "ACME file is empty. No certificates generated yet."
        return
    fi
    
    log_info "ACME file contains certificate data:"
    jq -r '.letsencrypt.Certificates[]? | "Domain: \(.domain.main) | Expires: \(.certificate | @base64d | split("\n")[1] | split("=")[1])"' "$ACME_FILE" 2>/dev/null || log_warning "Unable to parse certificate data (jq not installed)"
}

# Backup certificates
backup_certificates() {
    log_info "Backing up certificates..."
    
    if [[ ! -f "$ACME_FILE" ]]; then
        log_warning "No ACME file to backup"
        return
    fi
    
    local backup_dir="$PROJECT_ROOT/backups/ssl/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    cp "$ACME_FILE" "$backup_dir/"
    log_success "Certificates backed up to: $backup_dir"
}

# Restore certificates
restore_certificates() {
    local backup_file="$1"
    
    if [[ -z "$backup_file" ]]; then
        log_error "Please specify backup file path"
        exit 1
    fi
    
    if [[ ! -f "$backup_file" ]]; then
        log_error "Backup file not found: $backup_file"
        exit 1
    fi
    
    log_info "Restoring certificates from: $backup_file"
    
    # Backup current file
    if [[ -f "$ACME_FILE" ]]; then
        cp "$ACME_FILE" "${ACME_FILE}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    cp "$backup_file" "$ACME_FILE"
    chmod 600 "$ACME_FILE"
    
    log_success "Certificates restored successfully"
}

# Clean up expired certificates
cleanup_certificates() {
    log_info "Cleaning up expired certificates..."
    
    # This is handled automatically by Traefik, but we can restart it to force cleanup
    if docker ps | grep -q "logipool-traefik"; then
        log_info "Restarting Traefik to clean up expired certificates..."
        docker restart logipool-traefik
        log_success "Traefik restarted"
    else
        log_warning "Traefik container not running"
    fi
}

# Main function
main() {
    log_info "Setting up SSL certificates with Let's Encrypt and Traefik..."
    
    check_prerequisites
    setup_acme_storage
    verify_dns
    
    log_success "SSL setup completed successfully!"
    echo
    log_info "Next steps:"
    log_info "1. Ensure your domain points to this server's IP address"
    log_info "2. Start the application: docker-compose -f docker-compose.traefik.yml up -d"
    log_info "3. Check certificate status: $0 status"
    echo
    log_warning "Note: Let's Encrypt has rate limits. Test with staging first if needed."
}

# Handle script arguments
case "${1:-}" in
    "setup")
        main
        ;;
    "test")
        test_certificate
        ;;
    "status")
        check_certificates
        ;;
    "backup")
        backup_certificates
        ;;
    "restore")
        restore_certificates "$2"
        ;;
    "cleanup")
        cleanup_certificates
        ;;
    "verify-dns")
        verify_dns
        ;;
    *)
        main
        ;;
esac
