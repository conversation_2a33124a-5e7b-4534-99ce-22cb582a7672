package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.tracking.LocationDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.LocationMapper;
import zw.co.kanjan.logipool.repository.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class LocationTrackingService {
    
    private final DriverLocationRepository driverLocationRepository;
    private final LocationPermissionRepository locationPermissionRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final CompanyMemberRepository companyMemberRepository;
    private final LoadRepository loadRepository;
    private final LocationMapper locationMapper;
    private final NotificationService notificationService;
    
    // Location update methods
    
    public LocationDto.LocationResponse updateDriverLocation(LocationDto.LocationUpdateRequest request, String username) {
        log.info("Updating location for driver: {}", username);
        
        User driver = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("Driver not found"));
        
        // Validate driver has permission to track location
        validateLocationTrackingPermission(driver, request.getLoadId());
        
        // Mark previous active locations as historical
        markPreviousLocationsAsHistorical(driver);
        
        // Create new location record
        DriverLocation location = locationMapper.toEntity(request);
        location.setDriver(driver);
        location.setStatus(DriverLocation.LocationStatus.ACTIVE);
        location.setTimestamp(LocalDateTime.now());
        
        // Set load if provided
        if (request.getLoadId() != null) {
            Load load = loadRepository.findById(request.getLoadId())
                    .orElseThrow(() -> new ResourceNotFoundException("Load not found"));
            location.setLoad(load);
            location.setCompany(load.getAssignedCompany());
        } else {
            // Try to find driver's company through company membership
            CompanyMember membership = companyMemberRepository.findByUserAndStatus(driver, CompanyMember.MemberStatus.ACTIVE)
                    .stream().findFirst().orElse(null);
            if (membership != null) {
                location.setCompany(membership.getCompany());
            }
        }
        
        // Set expiration time (4 hours from now)
        location.setExpiresAt(LocalDateTime.now().plusHours(4));
        
        DriverLocation savedLocation = driverLocationRepository.save(location);
        
        // Send real-time notifications if enabled
        sendLocationUpdateNotifications(savedLocation);
        
        log.info("Location updated successfully for driver: {}", username);
        return locationMapper.toResponse(savedLocation);
    }
    
    @Transactional(readOnly = true)
    public LocationDto.LocationResponse getDriverCurrentLocation(Long driverId, String requesterUsername) {
        User driver = userRepository.findById(driverId)
                .orElseThrow(() -> new ResourceNotFoundException("Driver not found"));
        
        User requester = userRepository.findByUsername(requesterUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Requester not found"));
        
        // Validate requester has permission to view driver's location
        validateLocationViewPermission(driver, requester);
        
        DriverLocation location = driverLocationRepository.findLatestLocationByDriver(driver)
                .orElseThrow(() -> new ResourceNotFoundException("No location found for driver"));
        
        return locationMapper.toResponse(location);
    }
    
    @Transactional(readOnly = true)
    public Page<LocationDto.LocationResponse> getDriverLocationHistory(Long driverId, Pageable pageable, String requesterUsername) {
        User driver = userRepository.findById(driverId)
                .orElseThrow(() -> new ResourceNotFoundException("Driver not found"));
        
        User requester = userRepository.findByUsername(requesterUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Requester not found"));
        
        // Validate requester has permission to view historical data
        validateHistoricalDataPermission(driver, requester);
        
        Page<DriverLocation> locations = driverLocationRepository.findByDriverOrderByTimestampDesc(driver, pageable);
        return locations.map(locationMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<LocationDto.LocationResponse> getLoadLocationHistory(Long loadId, Pageable pageable, String requesterUsername) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found"));
        
        User requester = userRepository.findByUsername(requesterUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Requester not found"));
        
        // Validate requester has permission to view load tracking data
        validateLoadTrackingPermission(load, requester);
        
        Page<DriverLocation> locations = driverLocationRepository.findByLoadOrderByTimestampDesc(load, pageable);
        return locations.map(locationMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public LocationDto.TrackingSummary getCompanyTrackingSummary(Long companyId, String requesterUsername) {
        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found"));
        
        User requester = userRepository.findByUsername(requesterUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Requester not found"));
        
        // Validate requester has permission to view company tracking data
        validateCompanyTrackingPermission(company, requester);
        
        LocalDateTime since = LocalDateTime.now().minusHours(24);
        
        List<DriverLocation> activeLocations = driverLocationRepository.findSharedLocationsByCompany(company, since);
        List<DriverLocation> onDutyDrivers = driverLocationRepository.findOnDutyDriversByCompany(company, since);
        
        return LocationDto.TrackingSummary.builder()
                .totalActiveDrivers((long) activeLocations.size())
                .driversOnDuty((long) onDutyDrivers.size())
                .driversWithSharedLocation((long) activeLocations.stream()
                        .filter(DriverLocation::getIsShared)
                        .collect(Collectors.toList()).size())
                .activeTrackedLoads((long) activeLocations.stream()
                        .filter(loc -> loc.getLoad() != null)
                        .map(DriverLocation::getLoad)
                        .distinct()
                        .count())
                .recentLocationUpdates((long) activeLocations.size())
                .activeDrivers(activeLocations.stream()
                        .map(locationMapper::toResponse)
                        .collect(Collectors.toList()))
                .lastUpdated(LocalDateTime.now())
                .build();
    }
    
    // Permission management methods
    
    public LocationDto.PermissionResponse requestLocationPermission(LocationDto.PermissionRequest request, String requesterUsername) {
        log.info("Requesting location permission for driver {} from company {}", request.getDriverId(), request.getCompanyId());
        
        User driver = userRepository.findById(request.getDriverId())
                .orElseThrow(() -> new ResourceNotFoundException("Driver not found"));
        
        Company company = companyRepository.findById(request.getCompanyId())
                .orElseThrow(() -> new ResourceNotFoundException("Company not found"));
        
        User requester = userRepository.findByUsername(requesterUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Requester not found"));
        
        // Validate requester has permission to request location access
        validatePermissionRequestPermission(company, requester);
        
        // Check if permission already exists
        if (locationPermissionRepository.findByDriverAndCompany(driver, company).isPresent()) {
            throw new BusinessException("Location permission already exists for this driver and company");
        }
        
        LocationPermission permission = locationMapper.toPermissionEntity(request);
        permission.setDriver(driver);
        permission.setCompany(company);
        permission.setStatus(LocationPermission.PermissionStatus.PENDING);
        permission.setIsActive(true);
        
        if (request.getLoadId() != null) {
            Load load = loadRepository.findById(request.getLoadId())
                    .orElseThrow(() -> new ResourceNotFoundException("Load not found"));
            permission.setLoad(load);
        }
        
        LocationPermission savedPermission = locationPermissionRepository.save(permission);
        
        // Send notification to driver
        notificationService.sendLocationPermissionRequestNotification(savedPermission);
        
        log.info("Location permission requested successfully");
        return locationMapper.toPermissionResponse(savedPermission);
    }
    
    public LocationDto.PermissionResponse grantLocationPermission(Long permissionId, String driverUsername) {
        LocationPermission permission = locationPermissionRepository.findById(permissionId)
                .orElseThrow(() -> new ResourceNotFoundException("Permission not found"));
        
        User driver = userRepository.findByUsername(driverUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Driver not found"));
        
        // Validate driver is the one being asked for permission
        if (!permission.getDriver().equals(driver)) {
            throw new BusinessException("You can only grant your own location permissions");
        }
        
        // Validate permission is pending
        if (permission.getStatus() != LocationPermission.PermissionStatus.PENDING) {
            throw new BusinessException("Permission is not pending");
        }
        
        permission.setStatus(LocationPermission.PermissionStatus.GRANTED);
        permission.setGrantedBy(driver);
        permission.setGrantedAt(LocalDateTime.now());
        
        LocationPermission savedPermission = locationPermissionRepository.save(permission);
        
        // Send notification to company
        notificationService.sendLocationPermissionGrantedNotification(savedPermission);
        
        log.info("Location permission granted by driver: {}", driverUsername);
        return locationMapper.toPermissionResponse(savedPermission);
    }
    
    public void revokeLocationPermission(Long permissionId, String revokerUsername, String reason) {
        LocationPermission permission = locationPermissionRepository.findById(permissionId)
                .orElseThrow(() -> new ResourceNotFoundException("Permission not found"));
        
        User revoker = userRepository.findByUsername(revokerUsername)
                .orElseThrow(() -> new ResourceNotFoundException("Revoker not found"));
        
        // Validate revoker has permission to revoke
        validatePermissionRevokePermission(permission, revoker);
        
        permission.setStatus(LocationPermission.PermissionStatus.REVOKED);
        permission.setRevokedBy(revoker);
        permission.setRevokedAt(LocalDateTime.now());
        permission.setRevokeReason(reason);
        permission.setIsActive(false);
        
        locationPermissionRepository.save(permission);
        
        // Send notification
        notificationService.sendLocationPermissionRevokedNotification(permission);
        
        log.info("Location permission revoked by: {}", revokerUsername);
    }
    
    // Scheduled tasks and cleanup methods

    @Scheduled(fixedRate = 300000) // Every 5 minutes
    public void cleanupExpiredLocations() {
        log.debug("Cleaning up expired locations");

        LocalDateTime now = LocalDateTime.now();
        List<DriverLocation> expiredLocations = driverLocationRepository.findExpiredLocations(now);

        for (DriverLocation location : expiredLocations) {
            location.setStatus(DriverLocation.LocationStatus.OFFLINE);
            driverLocationRepository.save(location);
        }

        if (!expiredLocations.isEmpty()) {
            log.info("Marked {} locations as offline due to expiration", expiredLocations.size());
        }
    }

    @Scheduled(fixedRate = 3600000) // Every hour
    public void cleanupOldLocations() {
        log.debug("Cleaning up old location data");

        LocalDateTime cutoffTime = LocalDateTime.now().minusDays(30);
        int deletedCount = driverLocationRepository.deleteOldLocations(cutoffTime);

        if (deletedCount > 0) {
            log.info("Deleted {} old location records", deletedCount);
        }
    }

    @Scheduled(fixedRate = 3600000) // Every hour
    public void updateExpiredPermissions() {
        log.debug("Updating expired permissions");

        LocalDateTime now = LocalDateTime.now();
        int updatedCount = locationPermissionRepository.markExpiredPermissions(now);

        if (updatedCount > 0) {
            log.info("Marked {} permissions as expired", updatedCount);
        }
    }

    // Private helper methods

    private void validateLocationTrackingPermission(User driver, Long loadId) {
        // Drivers can always track their own location
        // Additional validation can be added here if needed
        log.debug("Validating location tracking permission for driver: {}", driver.getUsername());
    }

    private void validateLocationViewPermission(User driver, User requester) {
        LocalDateTime now = LocalDateTime.now();

        // Admin can view any location
        if (requester.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Driver can view their own location
        if (driver.equals(requester)) {
            return;
        }

        // Check if requester has permission through company membership
        List<CompanyMember> requesterMemberships = companyMemberRepository.findByUserAndStatus(requester, CompanyMember.MemberStatus.ACTIVE);
        List<CompanyMember> driverMemberships = companyMemberRepository.findByUserAndStatus(driver, CompanyMember.MemberStatus.ACTIVE);

        for (CompanyMember requesterMembership : requesterMemberships) {
            for (CompanyMember driverMembership : driverMemberships) {
                if (requesterMembership.getCompany().equals(driverMembership.getCompany())) {
                    // Check if there's an active permission
                    if (locationPermissionRepository.hasActivePermission(driver, requesterMembership.getCompany(), now)) {
                        return;
                    }
                }
            }
        }

        throw new BusinessException("You don't have permission to view this driver's location");
    }

    private void validateHistoricalDataPermission(User driver, User requester) {
        LocalDateTime now = LocalDateTime.now();

        // Admin can view any historical data
        if (requester.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Driver can view their own historical data
        if (driver.equals(requester)) {
            return;
        }

        // Check if requester has permission for historical data through company
        List<CompanyMember> requesterMemberships = companyMemberRepository.findByUserAndStatus(requester, CompanyMember.MemberStatus.ACTIVE);

        for (CompanyMember membership : requesterMemberships) {
            LocationPermission permission = locationPermissionRepository.findActivePermissionByDriverAndCompany(driver, membership.getCompany(), now)
                    .orElse(null);

            if (permission != null && permission.getAllowHistoricalData()) {
                return;
            }
        }

        throw new BusinessException("You don't have permission to view this driver's historical location data");
    }

    private void validateLoadTrackingPermission(Load load, User requester) {
        // Admin can view any load tracking
        if (requester.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Load client can view their own load tracking
        if (load.getClient().equals(requester)) {
            return;
        }

        // Company members can view their company's load tracking
        if (load.getAssignedCompany() != null) {
            CompanyMember membership = companyMemberRepository.findByCompanyAndUser(load.getAssignedCompany(), requester)
                    .orElse(null);

            if (membership != null && membership.getStatus() == CompanyMember.MemberStatus.ACTIVE) {
                return;
            }
        }

        throw new BusinessException("You don't have permission to view this load's tracking data");
    }

    private void validateCompanyTrackingPermission(Company company, User requester) {
        // Admin can view any company tracking
        if (requester.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Company owner can view their company tracking
        if (company.getUser().equals(requester)) {
            return;
        }

        // Company members with appropriate permissions can view tracking
        CompanyMember membership = companyMemberRepository.findByCompanyAndUser(company, requester)
                .orElse(null);

        if (membership != null &&
            membership.getStatus() == CompanyMember.MemberStatus.ACTIVE &&
            (membership.getRole() == CompanyMember.CompanyRole.MANAGER ||
             membership.getRole() == CompanyMember.CompanyRole.DISPATCHER ||
             membership.getRole() == CompanyMember.CompanyRole.OWNER)) {
            return;
        }

        throw new BusinessException("You don't have permission to view this company's tracking data");
    }

    private void validatePermissionRequestPermission(Company company, User requester) {
        // Company owner can request permissions
        if (company.getUser().equals(requester)) {
            return;
        }

        // Company members with management permissions can request
        CompanyMember membership = companyMemberRepository.findByCompanyAndUser(company, requester)
                .orElse(null);

        if (membership != null &&
            membership.getStatus() == CompanyMember.MemberStatus.ACTIVE &&
            (membership.getCanManageMembers() ||
             membership.getRole() == CompanyMember.CompanyRole.MANAGER ||
             membership.getRole() == CompanyMember.CompanyRole.OWNER)) {
            return;
        }

        throw new BusinessException("You don't have permission to request location permissions for this company");
    }

    private void validatePermissionRevokePermission(LocationPermission permission, User revoker) {
        // Driver can revoke their own permissions
        if (permission.getDriver().equals(revoker)) {
            return;
        }

        // Company owner can revoke permissions
        if (permission.getCompany().getUser().equals(revoker)) {
            return;
        }

        // Company members with management permissions can revoke
        CompanyMember membership = companyMemberRepository.findByCompanyAndUser(permission.getCompany(), revoker)
                .orElse(null);

        if (membership != null &&
            membership.getStatus() == CompanyMember.MemberStatus.ACTIVE &&
            (membership.getCanManageMembers() ||
             membership.getRole() == CompanyMember.CompanyRole.MANAGER ||
             membership.getRole() == CompanyMember.CompanyRole.OWNER)) {
            return;
        }

        throw new BusinessException("You don't have permission to revoke this location permission");
    }

    private void markPreviousLocationsAsHistorical(User driver) {
        LocalDateTime cutoffTime = LocalDateTime.now().minusMinutes(5);
        driverLocationRepository.markLocationsAsHistorical(driver, cutoffTime);
    }

    @Async
    private void sendLocationUpdateNotifications(DriverLocation location) {
        // Send real-time notifications to interested parties
        // This would integrate with WebSocket or push notification services
        log.debug("Sending location update notifications for driver: {}", location.getDriver().getUsername());
    }
}
