import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';

class LocationData {
  final String address;
  final double? latitude;
  final double? longitude;

  const LocationData({
    required this.address,
    this.latitude,
    this.longitude,
  });

  bool get hasCoordinates => latitude != null && longitude != null;

  @override
  String toString() {
    if (hasCoordinates) {
      return '$address (${latitude!.toStringAsFixed(6)}, ${longitude!.toStringAsFixed(6)})';
    }
    return address;
  }
}

class LocationPicker extends StatefulWidget {
  final String label;
  final String? hintText;
  final LocationData? initialLocation;
  final ValueChanged<LocationData> onLocationChanged;
  final bool isRequired;

  const LocationPicker({
    super.key,
    required this.label,
    this.hintText,
    this.initialLocation,
    required this.onLocationChanged,
    this.isRequired = false,
  });

  @override
  State<LocationPicker> createState() => _LocationPickerState();
}

class _LocationPickerState extends State<LocationPicker> {
  final _addressController = TextEditingController();
  LocationData? _currentLocation;
  Position? _userPosition;

  @override
  void initState() {
    super.initState();
    if (widget.initialLocation != null) {
      _currentLocation = widget.initialLocation;
      _addressController.text = widget.initialLocation!.address;
    }
    _getCurrentLocation();
  }

  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) return;

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) return;
      }

      if (permission == LocationPermission.deniedForever) return;

      _userPosition = await Geolocator.getCurrentPosition();
    } catch (e) {
      // Handle location errors silently
    }
  }

  void _showMapPicker() {
    if (!kIsWeb && (Platform.isWindows || Platform.isLinux)) {
      _showFallbackLocationDialog();
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _MapPickerDialog(
        initialLocation: _currentLocation,
        userPosition: _userPosition,
        onLocationSelected: (location) {
          setState(() {
            _currentLocation = location;
            _addressController.text = location.address;
          });
          widget.onLocationChanged(location);
        },
      ),
    );
  }

  void _showFallbackLocationDialog() {
    final latController = TextEditingController();
    final lngController = TextEditingController();
    final addressController =
        TextEditingController(text: _addressController.text);

    if (_currentLocation?.hasCoordinates == true) {
      latController.text = _currentLocation!.latitude!.toString();
      lngController.text = _currentLocation!.longitude!.toString();
    }

    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Set ${widget.label}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: addressController,
              decoration: const InputDecoration(
                labelText: 'Address',
                hintText: 'Enter the address',
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: latController,
                    decoration: const InputDecoration(
                      labelText: 'Latitude',
                      hintText: 'e.g., -17.8252',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: lngController,
                    decoration: const InputDecoration(
                      labelText: 'Longitude',
                      hintText: 'e.g., 31.0335',
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              final address = addressController.text.trim();
              if (address.isEmpty) return;

              double? lat, lng;
              if (latController.text.isNotEmpty &&
                  lngController.text.isNotEmpty) {
                lat = double.tryParse(latController.text);
                lng = double.tryParse(lngController.text);
              }

              final location = LocationData(
                address: address,
                latitude: lat,
                longitude: lng,
              );

              setState(() {
                _currentLocation = location;
                _addressController.text = address;
              });
              widget.onLocationChanged(location);
              Navigator.of(context).pop();
            },
            child: const Text('Set'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _addressController,
          decoration: InputDecoration(
            labelText: widget.isRequired ? '${widget.label} *' : widget.label,
            hintText: widget.hintText ?? 'Enter ${widget.label.toLowerCase()}',
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_currentLocation?.hasCoordinates == true)
                  Icon(
                    Icons.location_on,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                IconButton(
                  icon: const Icon(Icons.map),
                  onPressed: _showMapPicker,
                  tooltip: 'Pick location on map',
                ),
              ],
            ),
          ),
          validator: widget.isRequired
              ? (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '${widget.label} is required';
                  }
                  return null;
                }
              : null,
          onChanged: (value) {
            final location = LocationData(
              address: value,
              latitude: _currentLocation?.latitude,
              longitude: _currentLocation?.longitude,
            );
            setState(() {
              _currentLocation = location;
            });
            widget.onLocationChanged(location);
          },
        ),
        if (_currentLocation?.hasCoordinates == true)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              'Coordinates: ${_currentLocation!.latitude!.toStringAsFixed(6)}, ${_currentLocation!.longitude!.toStringAsFixed(6)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontFamily: 'monospace',
                  ),
            ),
          ),
        if (_currentLocation?.hasCoordinates != true)
          Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    'Tap the map icon to set coordinates for map display',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.secondary,
                          fontStyle: FontStyle.italic,
                        ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}

class _MapPickerDialog extends StatefulWidget {
  final LocationData? initialLocation;
  final Position? userPosition;
  final ValueChanged<LocationData> onLocationSelected;

  const _MapPickerDialog({
    this.initialLocation,
    this.userPosition,
    required this.onLocationSelected,
  });

  @override
  State<_MapPickerDialog> createState() => _MapPickerDialogState();
}

class _MapPickerDialogState extends State<_MapPickerDialog> {
  GoogleMapController? _mapController;
  LatLng? _selectedLocation;
  final _addressController = TextEditingController();

  static const CameraPosition _defaultPosition = CameraPosition(
    target: LatLng(-17.8252, 31.0335), // Harare, Zimbabwe
    zoom: 10,
  );

  @override
  void initState() {
    super.initState();
    if (widget.initialLocation?.hasCoordinates == true) {
      _selectedLocation = LatLng(
        widget.initialLocation!.latitude!,
        widget.initialLocation!.longitude!,
      );
      _addressController.text = widget.initialLocation!.address;
    }
  }

  @override
  void dispose() {
    _addressController.dispose();
    super.dispose();
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;

    LatLng initialPosition;
    if (_selectedLocation != null) {
      initialPosition = _selectedLocation!;
    } else if (widget.userPosition != null) {
      initialPosition =
          LatLng(widget.userPosition!.latitude, widget.userPosition!.longitude);
    } else {
      initialPosition = _defaultPosition.target;
    }

    controller.animateCamera(CameraUpdate.newLatLng(initialPosition));
  }

  void _onMapTapped(LatLng position) {
    setState(() {
      _selectedLocation = position;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                const Text(
                  'Pick Location',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _addressController,
              decoration: const InputDecoration(
                labelText: 'Address',
                hintText: 'Enter address description',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: GoogleMap(
                onMapCreated: _onMapCreated,
                initialCameraPosition: _defaultPosition,
                onTap: _onMapTapped,
                markers: _selectedLocation != null
                    ? {
                        Marker(
                          markerId: const MarkerId('selected'),
                          position: _selectedLocation!,
                        ),
                      }
                    : {},
                myLocationEnabled: true,
                myLocationButtonEnabled: true,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _selectedLocation != null &&
                            _addressController.text.isNotEmpty
                        ? () {
                            final location = LocationData(
                              address: _addressController.text.trim(),
                              latitude: _selectedLocation!.latitude,
                              longitude: _selectedLocation!.longitude,
                            );
                            widget.onLocationSelected(location);
                            Navigator.of(context).pop();
                          }
                        : null,
                    child: const Text('Select'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
