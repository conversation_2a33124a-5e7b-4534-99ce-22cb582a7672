import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/models/document_model.dart';
import '../../../shared/models/paginated_response.dart';
import '../../../shared/services/document_service.dart';
import '../../../shared/services/cross_platform_file_service.dart';

// Events
abstract class DocumentEvent extends Equatable {
  const DocumentEvent();

  @override
  List<Object?> get props => [];
}

class DocumentsRequested extends DocumentEvent {
  final int page;
  final int size;
  final String? type;
  final String? status;
  final bool? isRequired;
  final bool? expiringSoon;
  final bool refresh;

  const DocumentsRequested({
    this.page = 0,
    this.size = 20,
    this.type,
    this.status,
    this.isRequired,
    this.expiringSoon,
    this.refresh = false,
  });

  @override
  List<Object?> get props =>
      [page, size, type, status, isRequired, expiringSoon, refresh];
}

class CompanyDocumentsRequested extends DocumentEvent {
  final int companyId;
  final int page;
  final int size;
  final String? type;
  final String? status;
  final bool refresh;

  const CompanyDocumentsRequested({
    required this.companyId,
    this.page = 0,
    this.size = 20,
    this.type,
    this.status,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [companyId, page, size, type, status, refresh];
}

class VehicleDocumentsRequested extends DocumentEvent {
  final int vehicleId;
  final int page;
  final int size;
  final String? type;
  final String? status;
  final bool refresh;

  const VehicleDocumentsRequested({
    required this.vehicleId,
    this.page = 0,
    this.size = 20,
    this.type,
    this.status,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [vehicleId, page, size, type, status, refresh];
}

class LoadDocumentsRequested extends DocumentEvent {
  final int loadId;
  final int page;
  final int size;
  final String? type;
  final String? status;
  final bool refresh;

  const LoadDocumentsRequested({
    required this.loadId,
    this.page = 0,
    this.size = 20,
    this.type,
    this.status,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [loadId, page, size, type, status, refresh];
}

class DocumentUploadRequested extends DocumentEvent {
  final CrossPlatformFile file;
  final DocumentUploadRequest request;

  const DocumentUploadRequested({
    required this.file,
    required this.request,
  });

  @override
  List<Object?> get props => [file, request];
}

class DocumentUpdateRequested extends DocumentEvent {
  final int id;
  final DocumentUpdateRequest request;

  const DocumentUpdateRequested({
    required this.id,
    required this.request,
  });

  @override
  List<Object?> get props => [id, request];
}

class DocumentDeleteRequested extends DocumentEvent {
  final int id;

  const DocumentDeleteRequested(this.id);

  @override
  List<Object?> get props => [id];
}

class DocumentVerificationRequested extends DocumentEvent {
  final int id;
  final DocumentVerificationRequest request;

  const DocumentVerificationRequested({
    required this.id,
    required this.request,
  });

  @override
  List<Object?> get props => [id, request];
}

class DocumentDownloadRequested extends DocumentEvent {
  final int id;

  const DocumentDownloadRequested(this.id);

  @override
  List<Object?> get props => [id];
}

class DocumentFileDownloadRequested extends DocumentEvent {
  final int id;
  final String fileName;

  const DocumentFileDownloadRequested(this.id, this.fileName);

  @override
  List<Object?> get props => [id, fileName];
}

class DocumentByIdRequested extends DocumentEvent {
  final int id;

  const DocumentByIdRequested(this.id);

  @override
  List<Object?> get props => [id];
}

class DocumentsForVerificationRequested extends DocumentEvent {
  final int page;
  final int size;
  final bool refresh;

  const DocumentsForVerificationRequested({
    this.page = 0,
    this.size = 20,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [page, size, refresh];
}

class ExpiringDocumentsRequested extends DocumentEvent {
  final int page;
  final int size;
  final int? days;
  final bool refresh;

  const ExpiringDocumentsRequested({
    this.page = 0,
    this.size = 20,
    this.days = 30,
    this.refresh = false,
  });

  @override
  List<Object?> get props => [page, size, days, refresh];
}

class DocumentLoadMoreRequested extends DocumentEvent {
  const DocumentLoadMoreRequested();
}

// States
abstract class DocumentState extends Equatable {
  const DocumentState();

  @override
  List<Object?> get props => [];
}

class DocumentInitial extends DocumentState {}

class DocumentLoading extends DocumentState {}

class DocumentsLoaded extends DocumentState {
  final List<DocumentModel> documents;
  final bool hasReachedMax;
  final int currentPage;
  final bool isLoadingMore;

  const DocumentsLoaded({
    required this.documents,
    required this.hasReachedMax,
    required this.currentPage,
    this.isLoadingMore = false,
  });

  DocumentsLoaded copyWith({
    List<DocumentModel>? documents,
    bool? hasReachedMax,
    int? currentPage,
    bool? isLoadingMore,
  }) {
    return DocumentsLoaded(
      documents: documents ?? this.documents,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }

  @override
  List<Object?> get props =>
      [documents, hasReachedMax, currentPage, isLoadingMore];
}

class DocumentLoaded extends DocumentState {
  final DocumentModel document;

  const DocumentLoaded(this.document);

  @override
  List<Object?> get props => [document];
}

class DocumentUploading extends DocumentState {}

class DocumentUploaded extends DocumentState {
  final FileUploadResponse response;

  const DocumentUploaded(this.response);

  @override
  List<Object?> get props => [response];
}

class DocumentUpdated extends DocumentState {
  final DocumentModel document;

  const DocumentUpdated(this.document);

  @override
  List<Object?> get props => [document];
}

class DocumentDeleted extends DocumentState {
  final int documentId;

  const DocumentDeleted(this.documentId);

  @override
  List<Object?> get props => [documentId];
}

class DocumentVerified extends DocumentState {
  final DocumentModel document;

  const DocumentVerified(this.document);

  @override
  List<Object?> get props => [document];
}

class DocumentDownloadReady extends DocumentState {
  final String downloadUrl;

  const DocumentDownloadReady(this.downloadUrl);

  @override
  List<Object?> get props => [downloadUrl];
}

class DocumentFileDownloaded extends DocumentState {
  final String fileName;
  final String message;

  const DocumentFileDownloaded(this.fileName, this.message);

  @override
  List<Object?> get props => [fileName, message];
}

class DocumentError extends DocumentState {
  final String message;

  const DocumentError(this.message);

  @override
  List<Object?> get props => [message];
}

// BLoC
class DocumentBloc extends Bloc<DocumentEvent, DocumentState> {
  final DocumentService _documentService;

  // Current filter state
  String? _currentType;
  String? _currentStatus;
  bool? _currentIsRequired;
  bool? _currentExpiringSoon;
  int? _currentCompanyId;
  int? _currentVehicleId;
  int? _currentLoadId;
  String _currentMode =
      'all'; // all, company, vehicle, load, verification, expiring

  DocumentBloc({required DocumentService documentService})
      : _documentService = documentService,
        super(DocumentInitial()) {
    on<DocumentsRequested>(_onDocumentsRequested);
    on<CompanyDocumentsRequested>(_onCompanyDocumentsRequested);
    on<VehicleDocumentsRequested>(_onVehicleDocumentsRequested);
    on<LoadDocumentsRequested>(_onLoadDocumentsRequested);
    on<DocumentUploadRequested>(_onDocumentUploadRequested);
    on<DocumentUpdateRequested>(_onDocumentUpdateRequested);
    on<DocumentDeleteRequested>(_onDocumentDeleteRequested);
    on<DocumentVerificationRequested>(_onDocumentVerificationRequested);
    on<DocumentDownloadRequested>(_onDocumentDownloadRequested);
    on<DocumentFileDownloadRequested>(_onDocumentFileDownloadRequested);
    on<DocumentByIdRequested>(_onDocumentByIdRequested);
    on<DocumentsForVerificationRequested>(_onDocumentsForVerificationRequested);
    on<ExpiringDocumentsRequested>(_onExpiringDocumentsRequested);
    on<DocumentLoadMoreRequested>(_onDocumentLoadMoreRequested);
  }

  Future<void> _onDocumentsRequested(
    DocumentsRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      if (event.refresh || state is! DocumentsLoaded) {
        emit(DocumentLoading());
      }

      _currentMode = 'all';
      _currentType = event.type;
      _currentStatus = event.status;
      _currentIsRequired = event.isRequired;
      _currentExpiringSoon = event.expiringSoon;

      final response = await _documentService.getAllDocuments(
        page: event.page,
        size: event.size,
        type: event.type,
        status: event.status,
        isRequired: event.isRequired,
        expiringSoon: event.expiringSoon,
      );

      final documents = event.refresh || state is! DocumentsLoaded
          ? response.content
          : [...(state as DocumentsLoaded).documents, ...response.content];

      emit(DocumentsLoaded(
        documents: documents,
        hasReachedMax: response.last,
        currentPage: response.number,
      ));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onCompanyDocumentsRequested(
    CompanyDocumentsRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      if (event.refresh || state is! DocumentsLoaded) {
        emit(DocumentLoading());
      }

      _currentMode = 'company';
      _currentCompanyId = event.companyId;
      _currentType = event.type;
      _currentStatus = event.status;

      final response = await _documentService.getCompanyDocuments(
        companyId: event.companyId,
        page: event.page,
        size: event.size,
        type: event.type,
        status: event.status,
      );

      final documents = event.refresh || state is! DocumentsLoaded
          ? response.content
          : [...(state as DocumentsLoaded).documents, ...response.content];

      emit(DocumentsLoaded(
        documents: documents,
        hasReachedMax: response.last,
        currentPage: response.number,
      ));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onVehicleDocumentsRequested(
    VehicleDocumentsRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      if (event.refresh || state is! DocumentsLoaded) {
        emit(DocumentLoading());
      }

      _currentMode = 'vehicle';
      _currentVehicleId = event.vehicleId;
      _currentType = event.type;
      _currentStatus = event.status;

      final response = await _documentService.getVehicleDocuments(
        vehicleId: event.vehicleId,
        page: event.page,
        size: event.size,
        type: event.type,
        status: event.status,
      );

      final documents = event.refresh || state is! DocumentsLoaded
          ? response.content
          : [...(state as DocumentsLoaded).documents, ...response.content];

      emit(DocumentsLoaded(
        documents: documents,
        hasReachedMax: response.last,
        currentPage: response.number,
      ));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onLoadDocumentsRequested(
    LoadDocumentsRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      if (event.refresh || state is! DocumentsLoaded) {
        emit(DocumentLoading());
      }

      _currentMode = 'load';
      _currentLoadId = event.loadId;
      _currentType = event.type;
      _currentStatus = event.status;

      final response = await _documentService.getLoadDocuments(
        loadId: event.loadId,
        page: event.page,
        size: event.size,
        type: event.type,
        status: event.status,
      );

      final documents = event.refresh || state is! DocumentsLoaded
          ? response.content
          : [...(state as DocumentsLoaded).documents, ...response.content];

      emit(DocumentsLoaded(
        documents: documents,
        hasReachedMax: response.last,
        currentPage: response.number,
      ));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onDocumentUploadRequested(
    DocumentUploadRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      emit(DocumentUploading());

      final response = event.request.loadId != null
          ? await _documentService.uploadLoadDocument(
              loadId: event.request.loadId!,
              file: event.file,
              request: event.request,
            )
          : await _documentService.uploadDocument(
              file: event.file,
              request: event.request,
            );

      emit(DocumentUploaded(response));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onDocumentUpdateRequested(
    DocumentUpdateRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      final document = await _documentService.updateDocument(
        id: event.id,
        request: event.request,
      );

      emit(DocumentUpdated(document));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onDocumentDeleteRequested(
    DocumentDeleteRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      await _documentService.deleteDocument(event.id);
      emit(DocumentDeleted(event.id));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onDocumentVerificationRequested(
    DocumentVerificationRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      final document = await _documentService.verifyDocument(
        id: event.id,
        request: event.request,
      );

      emit(DocumentVerified(document));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onDocumentDownloadRequested(
    DocumentDownloadRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      final downloadUrl = await _documentService.downloadDocument(event.id);
      emit(DocumentDownloadReady(downloadUrl));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onDocumentFileDownloadRequested(
    DocumentFileDownloadRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      final success =
          await _documentService.downloadDocumentFile(event.id, event.fileName);
      if (success) {
        // Don't emit a state that interferes with the current document list
        // Instead, emit a temporary success state and then restore the previous state
        final currentState = state;
        emit(DocumentFileDownloaded(
            event.fileName, 'Document downloaded successfully'));

        // Restore the previous state after a brief delay to show the success message
        await Future.delayed(const Duration(milliseconds: 100));
        if (currentState is DocumentsLoaded) {
          emit(currentState);
        }
      } else {
        emit(DocumentError('Failed to download document'));
      }
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onDocumentByIdRequested(
    DocumentByIdRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      emit(DocumentLoading());
      final document = await _documentService.getDocumentById(event.id);
      emit(DocumentLoaded(document));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onDocumentsForVerificationRequested(
    DocumentsForVerificationRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      if (event.refresh || state is! DocumentsLoaded) {
        emit(DocumentLoading());
      }

      _currentMode = 'verification';

      final response = await _documentService.getDocumentsForVerification(
        page: event.page,
        size: event.size,
      );

      final documents = event.refresh || state is! DocumentsLoaded
          ? response.content
          : [...(state as DocumentsLoaded).documents, ...response.content];

      emit(DocumentsLoaded(
        documents: documents,
        hasReachedMax: response.last,
        currentPage: response.number,
      ));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onExpiringDocumentsRequested(
    ExpiringDocumentsRequested event,
    Emitter<DocumentState> emit,
  ) async {
    try {
      if (event.refresh || state is! DocumentsLoaded) {
        emit(DocumentLoading());
      }

      _currentMode = 'expiring';

      final response = await _documentService.getExpiringDocuments(
        page: event.page,
        size: event.size,
        days: event.days,
      );

      final documents = event.refresh || state is! DocumentsLoaded
          ? response.content
          : [...(state as DocumentsLoaded).documents, ...response.content];

      emit(DocumentsLoaded(
        documents: documents,
        hasReachedMax: response.last,
        currentPage: response.number,
      ));
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }

  Future<void> _onDocumentLoadMoreRequested(
    DocumentLoadMoreRequested event,
    Emitter<DocumentState> emit,
  ) async {
    final currentState = state;
    if (currentState is! DocumentsLoaded ||
        currentState.hasReachedMax ||
        currentState.isLoadingMore) {
      return;
    }

    emit(currentState.copyWith(isLoadingMore: true));

    try {
      final nextPage = currentState.currentPage + 1;

      // Call appropriate method based on current mode
      switch (_currentMode) {
        case 'company':
          if (_currentCompanyId != null) {
            add(CompanyDocumentsRequested(
              companyId: _currentCompanyId!,
              page: nextPage,
              type: _currentType,
              status: _currentStatus,
            ));
          }
          break;
        case 'vehicle':
          if (_currentVehicleId != null) {
            add(VehicleDocumentsRequested(
              vehicleId: _currentVehicleId!,
              page: nextPage,
              type: _currentType,
              status: _currentStatus,
            ));
          }
          break;
        case 'load':
          if (_currentLoadId != null) {
            add(LoadDocumentsRequested(
              loadId: _currentLoadId!,
              page: nextPage,
              type: _currentType,
              status: _currentStatus,
            ));
          }
          break;
        case 'verification':
          add(DocumentsForVerificationRequested(page: nextPage));
          break;
        case 'expiring':
          add(ExpiringDocumentsRequested(page: nextPage));
          break;
        default:
          add(DocumentsRequested(
            page: nextPage,
            type: _currentType,
            status: _currentStatus,
            isRequired: _currentIsRequired,
            expiringSoon: _currentExpiringSoon,
          ));
      }
    } catch (e) {
      emit(DocumentError(e.toString()));
    }
  }
}
