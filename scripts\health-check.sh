#!/bin/bash

# Health Check Script for LogiPool with <PERSON><PERSON><PERSON><PERSON>
# This script monitors the health of all services

set -e

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
ENV_FILE="$PROJECT_ROOT/.env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get domain name from env file
get_domain_name() {
    if [[ -f "$ENV_FILE" ]]; then
        grep "DOMAIN_NAME=" "$ENV_FILE" | cut -d'=' -f2 | tr -d '"' || echo "api-logistics.kanjan.co.zw"
    else
        echo "api-logistics.kanjan.co.zw"
    fi
}

# Check Docker service status
check_docker_services() {
    log_info "Checking Docker services..."
    
    local services=("logipool-traefik" "logipool-postgres-prod" "logipool-redis-prod" "logipool-backend-prod")
    local all_healthy=true
    
    for service in "${services[@]}"; do
        if docker ps --filter "name=$service" --filter "status=running" | grep -q "$service"; then
            local health_status=$(docker inspect --format='{{.State.Health.Status}}' "$service" 2>/dev/null || echo "no-healthcheck")
            
            case $health_status in
                "healthy")
                    log_success "$service: Running and healthy"
                    ;;
                "unhealthy")
                    log_error "$service: Running but unhealthy"
                    all_healthy=false
                    ;;
                "starting")
                    log_warning "$service: Starting up..."
                    ;;
                "no-healthcheck")
                    log_info "$service: Running (no health check configured)"
                    ;;
                *)
                    log_warning "$service: Unknown health status: $health_status"
                    ;;
            esac
        else
            log_error "$service: Not running"
            all_healthy=false
        fi
    done
    
    return $([[ "$all_healthy" == "true" ]] && echo 0 || echo 1)
}

# Check HTTP/HTTPS endpoints
check_endpoints() {
    log_info "Checking HTTP/HTTPS endpoints..."
    
    local domain_name=$(get_domain_name)
    local endpoints=(
        "https://$domain_name/actuator/health"
        "https://$domain_name/api/health"
        "http://localhost:8080/api/internal"  # Traefik dashboard
    )
    
    local all_healthy=true
    
    for endpoint in "${endpoints[@]}"; do
        if curl -f -s --max-time 10 "$endpoint" >/dev/null 2>&1; then
            log_success "Endpoint accessible: $endpoint"
        else
            log_error "Endpoint not accessible: $endpoint"
            all_healthy=false
        fi
    done
    
    return $([[ "$all_healthy" == "true" ]] && echo 0 || echo 1)
}

# Check SSL certificate status
check_ssl_certificates() {
    log_info "Checking SSL certificates..."
    
    local domain_name=$(get_domain_name)
    
    # Check certificate expiration
    local cert_info=$(echo | openssl s_client -servername "$domain_name" -connect "$domain_name:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null || echo "")
    
    if [[ -n "$cert_info" ]]; then
        local expiry_date=$(echo "$cert_info" | grep "notAfter" | cut -d'=' -f2)
        local expiry_timestamp=$(date -d "$expiry_date" +%s 2>/dev/null || echo "0")
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        if [[ $days_until_expiry -gt 30 ]]; then
            log_success "SSL certificate valid for $days_until_expiry days"
        elif [[ $days_until_expiry -gt 7 ]]; then
            log_warning "SSL certificate expires in $days_until_expiry days"
        else
            log_error "SSL certificate expires in $days_until_expiry days - renewal needed!"
            return 1
        fi
    else
        log_error "Unable to check SSL certificate"
        return 1
    fi
    
    return 0
}

# Check database connectivity
check_database() {
    log_info "Checking database connectivity..."
    
    if docker exec logipool-postgres-prod pg_isready -U logipool -d logipool_prod >/dev/null 2>&1; then
        log_success "Database is accessible"
        
        # Check database size and connections
        local db_stats=$(docker exec logipool-postgres-prod psql -U logipool -d logipool_prod -t -c "
            SELECT 
                pg_size_pretty(pg_database_size('logipool_prod')) as size,
                (SELECT count(*) FROM pg_stat_activity WHERE datname='logipool_prod') as connections
        " 2>/dev/null || echo "")
        
        if [[ -n "$db_stats" ]]; then
            log_info "Database stats: $db_stats"
        fi
        
        return 0
    else
        log_error "Database is not accessible"
        return 1
    fi
}

# Check Redis connectivity
check_redis() {
    log_info "Checking Redis connectivity..."
    
    if docker exec logipool-redis-prod redis-cli ping >/dev/null 2>&1; then
        log_success "Redis is accessible"
        
        # Check Redis memory usage
        local redis_info=$(docker exec logipool-redis-prod redis-cli info memory | grep "used_memory_human" | cut -d':' -f2 | tr -d '\r' || echo "")
        if [[ -n "$redis_info" ]]; then
            log_info "Redis memory usage: $redis_info"
        fi
        
        return 0
    else
        log_error "Redis is not accessible"
        return 1
    fi
}

# Check disk space
check_disk_space() {
    log_info "Checking disk space..."
    
    local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $disk_usage -lt 80 ]]; then
        log_success "Disk usage: ${disk_usage}%"
    elif [[ $disk_usage -lt 90 ]]; then
        log_warning "Disk usage: ${disk_usage}% - consider cleanup"
    else
        log_error "Disk usage: ${disk_usage}% - critical!"
        return 1
    fi
    
    return 0
}

# Check memory usage
check_memory() {
    log_info "Checking memory usage..."
    
    local memory_info=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    
    if (( $(echo "$memory_info < 80" | bc -l) )); then
        log_success "Memory usage: ${memory_info}%"
    elif (( $(echo "$memory_info < 90" | bc -l) )); then
        log_warning "Memory usage: ${memory_info}% - monitor closely"
    else
        log_error "Memory usage: ${memory_info}% - critical!"
        return 1
    fi
    
    return 0
}

# Generate health report
generate_report() {
    log_info "Generating health report..."
    
    local report_file="$PROJECT_ROOT/logs/health-report-$(date +%Y%m%d_%H%M%S).txt"
    mkdir -p "$(dirname "$report_file")"
    
    {
        echo "LogiPool Health Report - $(date)"
        echo "=================================="
        echo
        
        echo "Docker Services:"
        docker ps --filter "name=logipool" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo
        
        echo "System Resources:"
        echo "Disk Usage: $(df -h / | awk 'NR==2 {print $5}')"
        echo "Memory Usage: $(free -h | grep Mem | awk '{print $3 "/" $2}')"
        echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
        echo
        
        echo "Application Endpoints:"
        local domain_name=$(get_domain_name)
        echo "Main API: https://$domain_name"
        echo "Health Check: https://$domain_name/actuator/health"
        echo "pgAdmin: https://pgadmin.$domain_name"
        echo
        
        echo "SSL Certificate:"
        echo | openssl s_client -servername "$domain_name" -connect "$domain_name:443" 2>/dev/null | openssl x509 -noout -subject -dates 2>/dev/null || echo "Unable to retrieve certificate info"
        
    } > "$report_file"
    
    log_success "Health report saved to: $report_file"
}

# Send alert (placeholder for notification system)
send_alert() {
    local message="$1"
    local severity="$2"
    
    log_warning "ALERT [$severity]: $message"
    
    # Here you could integrate with notification systems like:
    # - Email notifications
    # - Slack webhooks
    # - Discord webhooks
    # - SMS alerts
    # - PagerDuty
    
    # Example email notification (requires mail command):
    # echo "$message" | mail -s "LogiPool Alert [$severity]" <EMAIL>
}

# Main health check function
main() {
    log_info "Starting comprehensive health check..."
    echo
    
    local overall_health=true
    
    # Run all checks
    check_docker_services || overall_health=false
    echo
    
    check_endpoints || overall_health=false
    echo
    
    check_ssl_certificates || overall_health=false
    echo
    
    check_database || overall_health=false
    echo
    
    check_redis || overall_health=false
    echo
    
    check_disk_space || overall_health=false
    echo
    
    check_memory || overall_health=false
    echo
    
    # Generate report
    generate_report
    
    # Overall status
    if [[ "$overall_health" == "true" ]]; then
        log_success "Overall system health: HEALTHY"
        exit 0
    else
        log_error "Overall system health: UNHEALTHY"
        send_alert "LogiPool system health check failed" "CRITICAL"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "docker")
        check_docker_services
        ;;
    "endpoints")
        check_endpoints
        ;;
    "ssl")
        check_ssl_certificates
        ;;
    "database")
        check_database
        ;;
    "redis")
        check_redis
        ;;
    "disk")
        check_disk_space
        ;;
    "memory")
        check_memory
        ;;
    "report")
        generate_report
        ;;
    *)
        main
        ;;
esac
