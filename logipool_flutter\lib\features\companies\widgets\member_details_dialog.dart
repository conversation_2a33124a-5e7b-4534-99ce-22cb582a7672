import 'package:flutter/material.dart';
import '../../../shared/models/company_member_model.dart';

class MemberDetailsDialog extends StatelessWidget {
  final CompanyMemberModel member;

  const MemberDetailsDialog({
    super.key,
    required this.member,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: _getRoleColor(member.role),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Colors.white,
                    child: Text(
                      _getInitials(member.displayName),
                      style: TextStyle(
                        color: _getRoleColor(member.role),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          member.displayName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          member.roleDisplayName,
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Basic Information
                    _buildSection(
                      'Basic Information',
                      [
                        _buildInfoRow('Email', member.email, Icons.email),
                        if (member.phoneNumber != null)
                          _buildInfoRow('Phone', member.phoneNumber!, Icons.phone),
                        _buildInfoRow('Username', member.username, Icons.person),
                        _buildInfoRow('Status', member.statusDisplayName, Icons.info,
                            valueColor: _getStatusColor(member.status)),
                      ],
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Permissions
                    _buildSection(
                      'Permissions',
                      [
                        _buildPermissionRow('Manage Team Members', member.canManageMembers),
                        _buildPermissionRow('Manage Loads', member.canManageLoads),
                        _buildPermissionRow('Update Load Status', member.canUpdateLoadStatus),
                        _buildPermissionRow('Upload Documents', member.canUploadDocuments),
                        _buildPermissionRow('Generate Invoices', member.canGenerateInvoices),
                        _buildPermissionRow('View Financial Data', member.canViewFinancials),
                        _buildPermissionRow('Share Location', member.canTrackLocation),
                      ],
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // Timeline
                    _buildSection(
                      'Timeline',
                      [
                        if (member.invitedAt != null)
                          _buildInfoRow(
                            'Invited',
                            _formatDateTime(member.invitedAt!),
                            Icons.send,
                          ),
                        if (member.invitedBy != null)
                          _buildInfoRow('Invited By', member.invitedBy!, Icons.person),
                        if (member.joinedAt != null)
                          _buildInfoRow(
                            'Joined',
                            _formatDateTime(member.joinedAt!),
                            Icons.login,
                          ),
                        _buildInfoRow(
                          'Member Since',
                          _formatDateTime(member.createdAt),
                          Icons.calendar_today,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Close'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: valueColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionRow(String permission, bool hasPermission) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            hasPermission ? Icons.check_circle : Icons.cancel,
            size: 20,
            color: hasPermission ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              permission,
              style: TextStyle(
                fontSize: 14,
                color: hasPermission ? Colors.black87 : Colors.grey[600],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getRoleColor(CompanyRole role) {
    switch (role) {
      case CompanyRole.owner:
        return Colors.purple;
      case CompanyRole.manager:
        return Colors.blue;
      case CompanyRole.driver:
        return Colors.green;
      case CompanyRole.dispatcher:
        return Colors.orange;
      case CompanyRole.accountant:
        return Colors.teal;
      case CompanyRole.viewer:
        return Colors.grey;
    }
  }

  Color _getStatusColor(MemberStatus status) {
    switch (status) {
      case MemberStatus.active:
        return Colors.green;
      case MemberStatus.pending:
        return Colors.orange;
      case MemberStatus.inactive:
        return Colors.grey;
      case MemberStatus.suspended:
        return Colors.red;
    }
  }

  String _getInitials(String name) {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else if (parts.isNotEmpty) {
      return parts[0][0].toUpperCase();
    }
    return '?';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} at ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
