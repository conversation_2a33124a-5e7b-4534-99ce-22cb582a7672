package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.company.CompanyCreateRequest;
import zw.co.kanjan.logipool.dto.company.CompanyResponse;
import zw.co.kanjan.logipool.dto.company.CompanyUpdateRequest;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.CompanyMember;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.entity.Role;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.mapper.CompanyMapper;
import zw.co.kanjan.logipool.repository.CompanyMemberRepository;
import zw.co.kanjan.logipool.repository.CompanyRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class CompanyService {
    
    private final CompanyRepository companyRepository;
    private final UserRepository userRepository;
    private final CompanyMemberRepository companyMemberRepository;
    private final CompanyMapper companyMapper;
    
    public CompanyResponse createCompany(CompanyCreateRequest request, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Check if user already has a company
        if (companyRepository.findByUser(user).isPresent()) {
            throw new BusinessException("User already has a company profile");
        }
        
        // Validate registration number is unique
        if (companyRepository.existsByRegistrationNumber(request.getRegistrationNumber())) {
            throw new BusinessException("Company with this registration number already exists");
        }
        
        // Validate tax number is unique
        if (request.getTaxNumber() != null && companyRepository.existsByTaxNumber(request.getTaxNumber())) {
            throw new BusinessException("Company with this tax number already exists");
        }
        
        Company company = companyMapper.toEntity(request);
        company.setUser(user);
        company.setVerificationStatus(Company.VerificationStatus.PENDING);
        company.setRating(BigDecimal.ZERO);
        company.setTotalJobs(0);
        company.setCompletedJobs(0);
        
        Company savedCompany = companyRepository.save(company);

        // Create owner membership for the company creator
        CompanyMember ownerMembership = CompanyMember.builder()
                .company(savedCompany)
                .user(user)
                .role(CompanyMember.CompanyRole.OWNER)
                .status(CompanyMember.MemberStatus.ACTIVE)
                .canManageMembers(true)
                .canManageLoads(true)
                .canUpdateLoadStatus(true)
                .canUploadDocuments(true)
                .canGenerateInvoices(true)
                .canViewFinancials(true)
                .canTrackLocation(false)
                .joinedAt(LocalDateTime.now())
                .build();

        companyMemberRepository.save(ownerMembership);

        log.info("Company created successfully: {} by user: {}", savedCompany.getName(), username);
        return companyMapper.toResponse(savedCompany);
    }
    
    public CompanyResponse updateCompany(Long companyId, CompanyUpdateRequest request, String username) {
        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));
        
        // Verify ownership
        if (!company.getUser().getUsername().equals(username)) {
            throw new BusinessException("You can only update your own company");
        }
        
        // Validate registration number is unique (if changed)
        if (request.getRegistrationNumber() != null && 
            !request.getRegistrationNumber().equals(company.getRegistrationNumber()) &&
            companyRepository.existsByRegistrationNumber(request.getRegistrationNumber())) {
            throw new BusinessException("Company with this registration number already exists");
        }
        
        // Validate tax number is unique (if changed)
        if (request.getTaxNumber() != null && 
            !request.getTaxNumber().equals(company.getTaxNumber()) &&
            companyRepository.existsByTaxNumber(request.getTaxNumber())) {
            throw new BusinessException("Company with this tax number already exists");
        }
        
        // Update company fields
        updateCompanyFromRequest(company, request);
        
        Company savedCompany = companyRepository.save(company);
        log.info("Company updated successfully: {} by user: {}", savedCompany.getName(), username);
        return companyMapper.toResponse(savedCompany);
    }
    
    @Transactional(readOnly = true)
    public CompanyResponse getCompanyById(Long companyId) {
        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));
        return companyMapper.toResponse(company);
    }
    
    @Transactional(readOnly = true)
    public CompanyResponse getMyCompany(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        Company company = companyRepository.findByUser(user)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found for user"));

        return companyMapper.toResponse(company);
    }
    
    @Transactional(readOnly = true)
    public Page<CompanyResponse> getAllCompanies(Pageable pageable) {
        Page<Company> companies = companyRepository.findAll(pageable);
        return companies.map(companyMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<CompanyResponse> getVerifiedLogisticsProviders(Pageable pageable) {
        Page<Company> companies = companyRepository.findVerifiedLogisticsProviders(pageable);
        return companies.map(companyMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<CompanyResponse> getCompaniesByVerificationStatus(Company.VerificationStatus status, Pageable pageable) {
        Page<Company> companies = companyRepository.findByVerificationStatus(status, pageable);
        return companies.map(companyMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<CompanyResponse> searchCompaniesByName(String name, Pageable pageable) {
        Page<Company> companies = companyRepository.findByNameContaining(name, pageable);
        return companies.map(companyMapper::toResponse);
    }
    
    public CompanyResponse verifyCompany(Long companyId, Company.VerificationStatus status) {
        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));
        
        company.setVerificationStatus(status);
        Company savedCompany = companyRepository.save(company);
        
        log.info("Company verification status updated: {} - Status: {}", company.getName(), status);
        return companyMapper.toResponse(savedCompany);
    }
    
    public void updateCompanyRating(Long companyId, BigDecimal rating) {
        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));
        
        company.setRating(rating);
        companyRepository.save(company);
        
        log.info("Company rating updated: {} - Rating: {}", company.getName(), rating);
    }
    
    public void incrementCompletedJobs(Long companyId) {
        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));
        
        company.setCompletedJobs(company.getCompletedJobs() + 1);
        company.setTotalJobs(company.getTotalJobs() + 1);
        companyRepository.save(company);
        
        log.info("Company job count updated: {} - Total: {}, Completed: {}", 
                company.getName(), company.getTotalJobs(), company.getCompletedJobs());
    }
    
    // Helper methods
    private void updateCompanyFromRequest(Company company, CompanyUpdateRequest request) {
        if (request.getName() != null) company.setName(request.getName());
        if (request.getDescription() != null) company.setDescription(request.getDescription());
        if (request.getRegistrationNumber() != null) company.setRegistrationNumber(request.getRegistrationNumber());
        if (request.getTaxNumber() != null) company.setTaxNumber(request.getTaxNumber());
        if (request.getType() != null) company.setType(request.getType());
        if (request.getAddress() != null) company.setAddress(request.getAddress());
        if (request.getCity() != null) company.setCity(request.getCity());
        if (request.getCountry() != null) company.setCountry(request.getCountry());
        if (request.getPhoneNumber() != null) company.setPhoneNumber(request.getPhoneNumber());
        if (request.getEmail() != null) company.setEmail(request.getEmail());
        if (request.getWebsite() != null) company.setWebsite(request.getWebsite());
    }
}
