package zw.co.kanjan.logipool.dto.bid;

import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class BidUpdateRequest {
    
    @Positive
    private BigDecimal amount;
    
    @Size(max = 1000)
    private String proposal;
    
    private LocalDateTime estimatedPickupTime;
    
    private LocalDateTime estimatedDeliveryTime;
    
    @Size(max = 500)
    private String notes;
}
