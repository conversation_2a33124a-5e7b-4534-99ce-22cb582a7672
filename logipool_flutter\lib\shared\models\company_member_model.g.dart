// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'company_member_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CompanyMemberModel _$CompanyMemberModelFromJson(Map<String, dynamic> json) =>
    CompanyMemberModel(
      id: (json['id'] as num).toInt(),
      userId: (json['userId'] as num).toInt(),
      username: json['username'] as String,
      email: json['email'] as String,
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      role: $enumDecode(_$CompanyRoleEnumMap, json['role']),
      status: $enumDecode(_$MemberStatusEnumMap, json['status']),
      canManageMembers: json['canManageMembers'] as bool,
      canManageLoads: json['canManageLoads'] as bool,
      canUpdateLoadStatus: json['canUpdateLoadStatus'] as bool,
      canUploadDocuments: json['canUploadDocuments'] as bool,
      canGenerateInvoices: json['canGenerateInvoices'] as bool,
      canViewFinancials: json['canViewFinancials'] as bool,
      canTrackLocation: json['canTrackLocation'] as bool,
      invitedBy: json['invitedBy'] as String?,
      invitedAt: json['invitedAt'] == null
          ? null
          : DateTime.parse(json['invitedAt'] as String),
      joinedAt: json['joinedAt'] == null
          ? null
          : DateTime.parse(json['joinedAt'] as String),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$CompanyMemberModelToJson(CompanyMemberModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'username': instance.username,
      'email': instance.email,
      'firstName': instance.firstName,
      'lastName': instance.lastName,
      'phoneNumber': instance.phoneNumber,
      'role': _$CompanyRoleEnumMap[instance.role]!,
      'status': _$MemberStatusEnumMap[instance.status]!,
      'canManageMembers': instance.canManageMembers,
      'canManageLoads': instance.canManageLoads,
      'canUpdateLoadStatus': instance.canUpdateLoadStatus,
      'canUploadDocuments': instance.canUploadDocuments,
      'canGenerateInvoices': instance.canGenerateInvoices,
      'canViewFinancials': instance.canViewFinancials,
      'canTrackLocation': instance.canTrackLocation,
      'invitedBy': instance.invitedBy,
      'invitedAt': instance.invitedAt?.toIso8601String(),
      'joinedAt': instance.joinedAt?.toIso8601String(),
      'createdAt': instance.createdAt.toIso8601String(),
    };

const _$CompanyRoleEnumMap = {
  CompanyRole.owner: 'OWNER',
  CompanyRole.manager: 'MANAGER',
  CompanyRole.driver: 'DRIVER',
  CompanyRole.dispatcher: 'DISPATCHER',
  CompanyRole.accountant: 'ACCOUNTANT',
  CompanyRole.viewer: 'VIEWER',
};

const _$MemberStatusEnumMap = {
  MemberStatus.pending: 'PENDING',
  MemberStatus.active: 'ACTIVE',
  MemberStatus.inactive: 'INACTIVE',
  MemberStatus.suspended: 'SUSPENDED',
};

CompanyMemberListResponse _$CompanyMemberListResponseFromJson(
        Map<String, dynamic> json) =>
    CompanyMemberListResponse(
      members: (json['members'] as List<dynamic>)
          .map((e) => CompanyMemberModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalMembers: (json['totalMembers'] as num).toInt(),
      activeMembers: (json['activeMembers'] as num).toInt(),
      pendingInvitations: (json['pendingInvitations'] as num).toInt(),
    );

Map<String, dynamic> _$CompanyMemberListResponseToJson(
        CompanyMemberListResponse instance) =>
    <String, dynamic>{
      'members': instance.members,
      'totalMembers': instance.totalMembers,
      'activeMembers': instance.activeMembers,
      'pendingInvitations': instance.pendingInvitations,
    };

InviteUserRequest _$InviteUserRequestFromJson(Map<String, dynamic> json) =>
    InviteUserRequest(
      email: json['email'] as String,
      role: $enumDecode(_$CompanyRoleEnumMap, json['role']),
      canManageMembers: json['canManageMembers'] as bool? ?? false,
      canManageLoads: json['canManageLoads'] as bool? ?? false,
      canUpdateLoadStatus: json['canUpdateLoadStatus'] as bool? ?? false,
      canUploadDocuments: json['canUploadDocuments'] as bool? ?? false,
      canGenerateInvoices: json['canGenerateInvoices'] as bool? ?? false,
      canViewFinancials: json['canViewFinancials'] as bool? ?? false,
      canTrackLocation: json['canTrackLocation'] as bool? ?? false,
      invitationMessage: json['invitationMessage'] as String?,
    );

Map<String, dynamic> _$InviteUserRequestToJson(InviteUserRequest instance) =>
    <String, dynamic>{
      'email': instance.email,
      'role': _$CompanyRoleEnumMap[instance.role]!,
      'canManageMembers': instance.canManageMembers,
      'canManageLoads': instance.canManageLoads,
      'canUpdateLoadStatus': instance.canUpdateLoadStatus,
      'canUploadDocuments': instance.canUploadDocuments,
      'canGenerateInvoices': instance.canGenerateInvoices,
      'canViewFinancials': instance.canViewFinancials,
      'canTrackLocation': instance.canTrackLocation,
      'invitationMessage': instance.invitationMessage,
    };

UpdateMemberRequest _$UpdateMemberRequestFromJson(Map<String, dynamic> json) =>
    UpdateMemberRequest(
      memberId: (json['memberId'] as num).toInt(),
      role: $enumDecodeNullable(_$CompanyRoleEnumMap, json['role']),
      status: $enumDecodeNullable(_$MemberStatusEnumMap, json['status']),
      canManageMembers: json['canManageMembers'] as bool?,
      canManageLoads: json['canManageLoads'] as bool?,
      canUpdateLoadStatus: json['canUpdateLoadStatus'] as bool?,
      canUploadDocuments: json['canUploadDocuments'] as bool?,
      canGenerateInvoices: json['canGenerateInvoices'] as bool?,
      canViewFinancials: json['canViewFinancials'] as bool?,
      canTrackLocation: json['canTrackLocation'] as bool?,
    );

Map<String, dynamic> _$UpdateMemberRequestToJson(
        UpdateMemberRequest instance) =>
    <String, dynamic>{
      'memberId': instance.memberId,
      'role': _$CompanyRoleEnumMap[instance.role],
      'status': _$MemberStatusEnumMap[instance.status],
      'canManageMembers': instance.canManageMembers,
      'canManageLoads': instance.canManageLoads,
      'canUpdateLoadStatus': instance.canUpdateLoadStatus,
      'canUploadDocuments': instance.canUploadDocuments,
      'canGenerateInvoices': instance.canGenerateInvoices,
      'canViewFinancials': instance.canViewFinancials,
      'canTrackLocation': instance.canTrackLocation,
    };
