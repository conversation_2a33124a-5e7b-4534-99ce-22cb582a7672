package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.entity.Payment;
import zw.co.kanjan.logipool.service.LoadLifecycleService;

@RestController
@RequestMapping("/api/loads")
@RequiredArgsConstructor
@Tag(name = "Load Lifecycle", description = "Complete load lifecycle management APIs")
public class LoadLifecycleController {
    
    private final LoadLifecycleService loadLifecycleService;
    
    @PostMapping("/{loadId}/complete-lifecycle")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Complete load lifecycle", 
               description = "Execute the complete load lifecycle from assignment to final closure")
    public ResponseEntity<LoadResponse> completeLoadLifecycle(
            @PathVariable Long loadId,
            Authentication authentication) {
        
        LoadResponse response = loadLifecycleService.completeLoadLifecycle(loadId, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/{loadId}/mark-delivered")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Mark load as delivered", 
               description = "Mark load as delivered and trigger completion workflow")
    public ResponseEntity<LoadResponse> markLoadAsDelivered(
            @PathVariable Long loadId,
            @RequestParam(value = "notes", required = false) String deliveryNotes,
            Authentication authentication) {
        
        LoadResponse response = loadLifecycleService.markLoadAsDelivered(loadId, deliveryNotes, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/{loadId}/verify-documents")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Verify load documents", 
               description = "Verify all required documents are uploaded and verified")
    public ResponseEntity<Boolean> verifyLoadDocuments(
            @PathVariable Long loadId,
            Authentication authentication) {
        
        boolean verified = loadLifecycleService.verifyLoadDocuments(loadId, authentication.getName());
        return ResponseEntity.ok(verified);
    }
    
    @PostMapping("/{loadId}/process-payment")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Process load payment", 
               description = "Process payment for completed load")
    public ResponseEntity<Payment> processLoadPayment(
            @PathVariable Long loadId,
            Authentication authentication) {
        
        Payment payment = loadLifecycleService.processLoadPayment(loadId, authentication.getName());
        return ResponseEntity.ok(payment);
    }
    
    @PostMapping("/{loadId}/finalize")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Finalize load", 
               description = "Finalize and close the load after all processes are complete")
    public ResponseEntity<LoadResponse> finalizeLoad(
            @PathVariable Long loadId,
            Authentication authentication) {
        
        LoadResponse response = loadLifecycleService.finalizeLoad(loadId, authentication.getName());
        return ResponseEntity.ok(response);
    }
}
