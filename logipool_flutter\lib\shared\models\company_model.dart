import 'package:json_annotation/json_annotation.dart';

part 'company_model.g.dart';

@JsonSerializable()
class CompanyModel {
  final int? id;
  final String name;
  final String? description;
  final String registrationNumber;
  final String? taxNumber;
  final CompanyType type;
  final String address;
  final String city;
  final String country;
  final String? phoneNumber;
  final String? email;
  final String? website;
  final VerificationStatus verificationStatus;
  final double rating;
  final int totalJobs;
  final int completedJobs;
  final int? userId;
  final String? userName;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  CompanyModel({
    this.id,
    required this.name,
    this.description,
    required this.registrationNumber,
    this.taxNumber,
    required this.type,
    required this.address,
    required this.city,
    required this.country,
    this.phoneNumber,
    this.email,
    this.website,
    this.verificationStatus = VerificationStatus.pending,
    this.rating = 0.0,
    this.totalJobs = 0,
    this.completedJobs = 0,
    this.userId,
    this.userName,
    this.createdAt,
    this.updatedAt,
  });

  factory CompanyModel.fromJson(Map<String, dynamic> json) =>
      _$CompanyModelFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyModelToJson(this);

  CompanyModel copyWith({
    int? id,
    String? name,
    String? description,
    String? registrationNumber,
    String? taxNumber,
    CompanyType? type,
    String? address,
    String? city,
    String? country,
    String? phoneNumber,
    String? email,
    String? website,
    VerificationStatus? verificationStatus,
    double? rating,
    int? totalJobs,
    int? completedJobs,
    int? userId,
    String? userName,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CompanyModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      registrationNumber: registrationNumber ?? this.registrationNumber,
      taxNumber: taxNumber ?? this.taxNumber,
      type: type ?? this.type,
      address: address ?? this.address,
      city: city ?? this.city,
      country: country ?? this.country,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      website: website ?? this.website,
      verificationStatus: verificationStatus ?? this.verificationStatus,
      rating: rating ?? this.rating,
      totalJobs: totalJobs ?? this.totalJobs,
      completedJobs: completedJobs ?? this.completedJobs,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  double get completionRate {
    if (totalJobs == 0) return 0.0;
    return (completedJobs / totalJobs) * 100;
  }

  bool get isVerified => verificationStatus == VerificationStatus.verified;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CompanyModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CompanyModel(id: $id, name: $name, type: $type, verificationStatus: $verificationStatus)';
  }
}

@JsonEnum()
enum CompanyType {
  @JsonValue('LOGISTICS_PROVIDER')
  logisticsProvider,
  @JsonValue('CLIENT')
  client,
  @JsonValue('BOTH')
  both;

  String get displayName {
    switch (this) {
      case CompanyType.logisticsProvider:
        return 'Logistics Provider';
      case CompanyType.client:
        return 'Client';
      case CompanyType.both:
        return 'Both';
    }
  }

  String get description {
    switch (this) {
      case CompanyType.logisticsProvider:
        return 'Provides transportation and logistics services';
      case CompanyType.client:
        return 'Posts loads and requires transportation services';
      case CompanyType.both:
        return 'Both provides services and posts loads';
    }
  }
}

@JsonEnum()
enum VerificationStatus {
  @JsonValue('PENDING')
  pending,
  @JsonValue('VERIFIED')
  verified,
  @JsonValue('REJECTED')
  rejected,
  @JsonValue('SUSPENDED')
  suspended;

  String get displayName {
    switch (this) {
      case VerificationStatus.pending:
        return 'Pending';
      case VerificationStatus.verified:
        return 'Verified';
      case VerificationStatus.rejected:
        return 'Rejected';
      case VerificationStatus.suspended:
        return 'Suspended';
    }
  }

  String get description {
    switch (this) {
      case VerificationStatus.pending:
        return 'Verification is pending review';
      case VerificationStatus.verified:
        return 'Company is verified and trusted';
      case VerificationStatus.rejected:
        return 'Verification was rejected';
      case VerificationStatus.suspended:
        return 'Company is temporarily suspended';
    }
  }

  bool get isActive => this == VerificationStatus.verified;
  bool get isPending => this == VerificationStatus.pending;
  bool get isRejected => this == VerificationStatus.rejected;
  bool get isSuspended => this == VerificationStatus.suspended;
}

@JsonSerializable()
class CompanyCreateRequest {
  final String name;
  final String? description;
  final String registrationNumber;
  final String? taxNumber;
  final CompanyType type;
  final String address;
  final String city;
  final String country;
  final String? phoneNumber;
  final String? email;
  final String? website;

  CompanyCreateRequest({
    required this.name,
    this.description,
    required this.registrationNumber,
    this.taxNumber,
    required this.type,
    required this.address,
    required this.city,
    required this.country,
    this.phoneNumber,
    this.email,
    this.website,
  });

  factory CompanyCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$CompanyCreateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyCreateRequestToJson(this);
}

@JsonSerializable()
class CompanyUpdateRequest {
  final String? name;
  final String? description;
  final String? registrationNumber;
  final String? taxNumber;
  final CompanyType? type;
  final String? address;
  final String? city;
  final String? country;
  final String? phoneNumber;
  final String? email;
  final String? website;

  CompanyUpdateRequest({
    this.name,
    this.description,
    this.registrationNumber,
    this.taxNumber,
    this.type,
    this.address,
    this.city,
    this.country,
    this.phoneNumber,
    this.email,
    this.website,
  });

  factory CompanyUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$CompanyUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyUpdateRequestToJson(this);
}
