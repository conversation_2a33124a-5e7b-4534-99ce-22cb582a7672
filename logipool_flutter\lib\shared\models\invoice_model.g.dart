// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InvoiceModel _$InvoiceModelFromJson(Map<String, dynamic> json) => InvoiceModel(
      id: (json['id'] as num?)?.toInt(),
      invoiceNumber: json['invoiceNumber'] as String?,
      subtotal: (json['subtotal'] as num?)?.toDouble(),
      taxAmount: (json['taxAmount'] as num?)?.toDouble(),
      discountAmount: (json['discountAmount'] as num?)?.toDouble(),
      totalAmount: (json['totalAmount'] as num?)?.toDouble(),
      commissionAmount: (json['commissionAmount'] as num?)?.toDouble(),
      netAmount: (json['netAmount'] as num?)?.toDouble(),
      status: $enumDecode(_$InvoiceStatusEnumMap, json['status']),
      type: $enumDecode(_$InvoiceTypeEnumMap, json['type']),
      description: json['description'] as String?,
      notes: json['notes'] as String?,
      dueDate: DateTime.parse(json['dueDate'] as String),
      loadId: (json['loadId'] as num?)?.toInt(),
      loadTitle: json['loadTitle'] as String?,
      bidId: (json['bidId'] as num?)?.toInt(),
      clientId: (json['clientId'] as num?)?.toInt(),
      clientName: json['clientName'] as String?,
      transporterId: (json['transporterId'] as num?)?.toInt(),
      transporterName: json['transporterName'] as String?,
      companyId: (json['companyId'] as num?)?.toInt(),
      companyName: json['companyName'] as String?,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => InvoiceItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      sentAt: json['sentAt'] == null
          ? null
          : DateTime.parse(json['sentAt'] as String),
      paidAt: json['paidAt'] == null
          ? null
          : DateTime.parse(json['paidAt'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$InvoiceModelToJson(InvoiceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'invoiceNumber': instance.invoiceNumber,
      'subtotal': instance.subtotal,
      'taxAmount': instance.taxAmount,
      'discountAmount': instance.discountAmount,
      'totalAmount': instance.totalAmount,
      'commissionAmount': instance.commissionAmount,
      'netAmount': instance.netAmount,
      'status': _$InvoiceStatusEnumMap[instance.status]!,
      'type': _$InvoiceTypeEnumMap[instance.type]!,
      'description': instance.description,
      'notes': instance.notes,
      'dueDate': instance.dueDate.toIso8601String(),
      'loadId': instance.loadId,
      'loadTitle': instance.loadTitle,
      'bidId': instance.bidId,
      'clientId': instance.clientId,
      'clientName': instance.clientName,
      'transporterId': instance.transporterId,
      'transporterName': instance.transporterName,
      'companyId': instance.companyId,
      'companyName': instance.companyName,
      'items': instance.items,
      'sentAt': instance.sentAt?.toIso8601String(),
      'paidAt': instance.paidAt?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$InvoiceStatusEnumMap = {
  InvoiceStatus.draft: 'DRAFT',
  InvoiceStatus.sent: 'SENT',
  InvoiceStatus.viewed: 'VIEWED',
  InvoiceStatus.paid: 'PAID',
  InvoiceStatus.overdue: 'OVERDUE',
  InvoiceStatus.cancelled: 'CANCELLED',
  InvoiceStatus.refunded: 'REFUNDED',
};

const _$InvoiceTypeEnumMap = {
  InvoiceType.loadInvoice: 'LOAD_INVOICE',
  InvoiceType.commissionInvoice: 'COMMISSION_INVOICE',
  InvoiceType.serviceInvoice: 'SERVICE_INVOICE',
  InvoiceType.penaltyInvoice: 'PENALTY_INVOICE',
};

InvoicePreviewResponse _$InvoicePreviewResponseFromJson(
        Map<String, dynamic> json) =>
    InvoicePreviewResponse(
      invoiceNumber: json['invoiceNumber'] as String?,
      subtotal: (json['subtotal'] as num?)?.toDouble(),
      taxAmount: (json['taxAmount'] as num?)?.toDouble(),
      taxRate: (json['taxRate'] as num?)?.toDouble(),
      discountAmount: (json['discountAmount'] as num?)?.toDouble(),
      totalAmount: (json['totalAmount'] as num?)?.toDouble(),
      commissionAmount: (json['commissionAmount'] as num?)?.toDouble(),
      commissionRate: (json['commissionRate'] as num?)?.toDouble(),
      netAmount: (json['netAmount'] as num?)?.toDouble(),
      type: $enumDecodeNullable(_$InvoiceTypeEnumMap, json['type']),
      description: json['description'] as String?,
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
      loadId: (json['loadId'] as num?)?.toInt(),
      loadTitle: json['loadTitle'] as String?,
      bidId: (json['bidId'] as num?)?.toInt(),
      bidAmount: (json['bidAmount'] as num?)?.toDouble(),
      clientId: (json['clientId'] as num?)?.toInt(),
      clientName: json['clientName'] as String?,
      transporterId: (json['transporterId'] as num?)?.toInt(),
      transporterName: json['transporterName'] as String?,
      companyId: (json['companyId'] as num?)?.toInt(),
      companyName: json['companyName'] as String?,
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => InvoiceItemModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      hasExistingInvoiceDocument: json['hasExistingInvoiceDocument'] as bool?,
      commissionNote: json['commissionNote'] as String?,
    );

Map<String, dynamic> _$InvoicePreviewResponseToJson(
        InvoicePreviewResponse instance) =>
    <String, dynamic>{
      'invoiceNumber': instance.invoiceNumber,
      'subtotal': instance.subtotal,
      'taxAmount': instance.taxAmount,
      'taxRate': instance.taxRate,
      'discountAmount': instance.discountAmount,
      'totalAmount': instance.totalAmount,
      'commissionAmount': instance.commissionAmount,
      'commissionRate': instance.commissionRate,
      'netAmount': instance.netAmount,
      'type': _$InvoiceTypeEnumMap[instance.type],
      'description': instance.description,
      'dueDate': instance.dueDate?.toIso8601String(),
      'loadId': instance.loadId,
      'loadTitle': instance.loadTitle,
      'bidId': instance.bidId,
      'bidAmount': instance.bidAmount,
      'clientId': instance.clientId,
      'clientName': instance.clientName,
      'transporterId': instance.transporterId,
      'transporterName': instance.transporterName,
      'companyId': instance.companyId,
      'companyName': instance.companyName,
      'items': instance.items,
      'hasExistingInvoiceDocument': instance.hasExistingInvoiceDocument,
      'commissionNote': instance.commissionNote,
    };

InvoiceItemModel _$InvoiceItemModelFromJson(Map<String, dynamic> json) =>
    InvoiceItemModel(
      id: (json['id'] as num?)?.toInt(),
      description: json['description'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      unitPrice: (json['unitPrice'] as num).toDouble(),
      totalPrice: (json['totalPrice'] as num).toDouble(),
      unit: json['unit'] as String?,
      type: $enumDecode(_$InvoiceItemTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$InvoiceItemModelToJson(InvoiceItemModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'description': instance.description,
      'quantity': instance.quantity,
      'unitPrice': instance.unitPrice,
      'totalPrice': instance.totalPrice,
      'unit': instance.unit,
      'type': _$InvoiceItemTypeEnumMap[instance.type]!,
    };

const _$InvoiceItemTypeEnumMap = {
  InvoiceItemType.transportationFee: 'TRANSPORTATION_FEE',
  InvoiceItemType.commissionFee: 'COMMISSION_FEE',
  InvoiceItemType.fuelSurcharge: 'FUEL_SURCHARGE',
  InvoiceItemType.insuranceFee: 'INSURANCE_FEE',
  InvoiceItemType.handlingFee: 'HANDLING_FEE',
  InvoiceItemType.storageFee: 'STORAGE_FEE',
  InvoiceItemType.penaltyFee: 'PENALTY_FEE',
  InvoiceItemType.discount: 'DISCOUNT',
  InvoiceItemType.other: 'OTHER',
};

InvoiceCreateRequest _$InvoiceCreateRequestFromJson(
        Map<String, dynamic> json) =>
    InvoiceCreateRequest(
      type: $enumDecode(_$InvoiceTypeEnumMap, json['type']),
      description: json['description'] as String?,
      notes: json['notes'] as String?,
      dueDate: DateTime.parse(json['dueDate'] as String),
      loadId: (json['loadId'] as num).toInt(),
      bidId: (json['bidId'] as num?)?.toInt(),
      clientId: (json['clientId'] as num).toInt(),
      transporterId: (json['transporterId'] as num?)?.toInt(),
      companyId: (json['companyId'] as num?)?.toInt(),
      items: (json['items'] as List<dynamic>)
          .map((e) => InvoiceItemRequest.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$InvoiceCreateRequestToJson(
        InvoiceCreateRequest instance) =>
    <String, dynamic>{
      'type': _$InvoiceTypeEnumMap[instance.type]!,
      'description': instance.description,
      'notes': instance.notes,
      'dueDate': instance.dueDate.toIso8601String(),
      'loadId': instance.loadId,
      'bidId': instance.bidId,
      'clientId': instance.clientId,
      'transporterId': instance.transporterId,
      'companyId': instance.companyId,
      'items': instance.items,
    };

InvoiceUpdateRequest _$InvoiceUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    InvoiceUpdateRequest(
      status: $enumDecodeNullable(_$InvoiceStatusEnumMap, json['status']),
      description: json['description'] as String?,
      notes: json['notes'] as String?,
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => InvoiceItemRequest.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$InvoiceUpdateRequestToJson(
        InvoiceUpdateRequest instance) =>
    <String, dynamic>{
      'status': _$InvoiceStatusEnumMap[instance.status],
      'description': instance.description,
      'notes': instance.notes,
      'dueDate': instance.dueDate?.toIso8601String(),
      'items': instance.items,
    };

InvoiceItemRequest _$InvoiceItemRequestFromJson(Map<String, dynamic> json) =>
    InvoiceItemRequest(
      description: json['description'] as String,
      quantity: (json['quantity'] as num).toDouble(),
      unitPrice: (json['unitPrice'] as num).toDouble(),
      unit: json['unit'] as String?,
      type: $enumDecode(_$InvoiceItemTypeEnumMap, json['type']),
    );

Map<String, dynamic> _$InvoiceItemRequestToJson(InvoiceItemRequest instance) =>
    <String, dynamic>{
      'description': instance.description,
      'quantity': instance.quantity,
      'unitPrice': instance.unitPrice,
      'unit': instance.unit,
      'type': _$InvoiceItemTypeEnumMap[instance.type]!,
    };

InvoiceModificationRequest _$InvoiceModificationRequestFromJson(
        Map<String, dynamic> json) =>
    InvoiceModificationRequest(
      description: json['description'] as String?,
      notes: json['notes'] as String?,
      dueDate: json['dueDate'] == null
          ? null
          : DateTime.parse(json['dueDate'] as String),
      discountAmount: (json['discountAmount'] as num?)?.toDouble(),
      items: (json['items'] as List<dynamic>?)
          ?.map((e) => InvoiceItemRequest.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$InvoiceModificationRequestToJson(
        InvoiceModificationRequest instance) =>
    <String, dynamic>{
      'description': instance.description,
      'notes': instance.notes,
      'dueDate': instance.dueDate?.toIso8601String(),
      'discountAmount': instance.discountAmount,
      'items': instance.items,
    };

InvoiceSummary _$InvoiceSummaryFromJson(Map<String, dynamic> json) =>
    InvoiceSummary(
      totalAmount: (json['totalAmount'] as num).toDouble(),
      totalCommission: (json['totalCommission'] as num).toDouble(),
      totalTax: (json['totalTax'] as num).toDouble(),
      totalCount: (json['totalCount'] as num).toInt(),
      draftCount: (json['draftCount'] as num).toInt(),
      sentCount: (json['sentCount'] as num).toInt(),
      paidCount: (json['paidCount'] as num).toInt(),
      overdueCount: (json['overdueCount'] as num).toInt(),
    );

Map<String, dynamic> _$InvoiceSummaryToJson(InvoiceSummary instance) =>
    <String, dynamic>{
      'totalAmount': instance.totalAmount,
      'totalCommission': instance.totalCommission,
      'totalTax': instance.totalTax,
      'totalCount': instance.totalCount,
      'draftCount': instance.draftCount,
      'sentCount': instance.sentCount,
      'paidCount': instance.paidCount,
      'overdueCount': instance.overdueCount,
    };
