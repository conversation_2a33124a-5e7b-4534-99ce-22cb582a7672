package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Commission;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.Bid;
import zw.co.kanjan.logipool.entity.Payment;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface CommissionRepository extends JpaRepository<Commission, Long> {
    
    // Find commissions by status
    Page<Commission> findByStatus(Commission.CommissionStatus status, Pageable pageable);
    
    // Find commissions by load
    List<Commission> findByLoad(Load load);
    
    // Find commissions by bid
    List<Commission> findByBid(Bid bid);
    
    // Find commissions by payment
    List<Commission> findByPayment(Payment payment);
    
    // Find commissions by type
    List<Commission> findByType(Commission.CommissionType type);
    
    // Find commissions by date range
    @Query("SELECT c FROM Commission c WHERE c.createdAt BETWEEN :startDate AND :endDate")
    List<Commission> findByDateRange(@Param("startDate") LocalDateTime startDate, 
                                   @Param("endDate") LocalDateTime endDate);
    
    // Calculate total commission by status
    @Query("SELECT SUM(c.commissionAmount) FROM Commission c WHERE c.status = :status")
    BigDecimal getTotalCommissionByStatus(@Param("status") Commission.CommissionStatus status);
    
    // Calculate total commission collected
    @Query("SELECT SUM(c.commissionAmount) FROM Commission c WHERE c.status = 'COLLECTED'")
    BigDecimal getTotalCommissionCollected();
    
    // Calculate total commission pending
    @Query("SELECT SUM(c.commissionAmount) FROM Commission c WHERE c.status = 'PENDING'")
    BigDecimal getTotalCommissionPending();
    
    // Find pending commissions
    List<Commission> findByStatusOrderByCreatedAtAsc(Commission.CommissionStatus status);
    
    // Count commissions by status
    long countByStatus(Commission.CommissionStatus status);
    
    // Find commissions for collection
    @Query("SELECT c FROM Commission c WHERE c.status = 'CALCULATED' AND c.calculatedAt <= :cutoffDate")
    List<Commission> findCommissionsForCollection(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    // Calculate commission statistics by date range
    @Query("SELECT c.type, COUNT(c), SUM(c.commissionAmount) FROM Commission c WHERE c.createdAt BETWEEN :startDate AND :endDate GROUP BY c.type")
    List<Object[]> getCommissionStatsByDateRange(@Param("startDate") LocalDateTime startDate, 
                                               @Param("endDate") LocalDateTime endDate);
    
    // Find recent commissions
    @Query("SELECT c FROM Commission c ORDER BY c.createdAt DESC")
    Page<Commission> findRecentCommissions(Pageable pageable);

    // Find commissions created after a specific date
    @Query("SELECT c FROM Commission c WHERE c.createdAt >= :date")
    List<Commission> findByCreatedAtAfter(@Param("date") LocalDateTime date);
}
