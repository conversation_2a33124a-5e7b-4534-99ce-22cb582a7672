package zw.co.kanjan.logipool.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.stereotype.Controller;
import zw.co.kanjan.logipool.dto.NotificationDto;

@Slf4j
@Controller
@RequiredArgsConstructor
public class WebSocketController {
    
    @MessageMapping("/notification.subscribe")
    @SendTo("/topic/notifications/all")
    public NotificationDto subscribeToNotifications(@Payload NotificationDto notification,
                                                   SimpMessageHeaderAccessor headerAccessor) {
        // Add username in web socket session
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        log.info("User {} subscribed to notifications", username);
        
        return notification;
    }
    
    @MessageMapping("/notification.send")
    @SendTo("/topic/notifications/all")
    public NotificationDto sendNotification(@Payload NotificationDto notification,
                                          SimpMessageHeaderAccessor headerAccessor) {
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        log.info("User {} sent notification: {}", username, notification.getTitle());
        
        return notification;
    }
}
