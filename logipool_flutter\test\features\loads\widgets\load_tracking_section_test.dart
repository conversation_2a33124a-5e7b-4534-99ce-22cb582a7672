import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';

import 'package:logipool_flutter/features/loads/widgets/load_tracking_section.dart';
import 'package:logipool_flutter/shared/models/load_model.dart';
import 'package:logipool_flutter/shared/models/user_model.dart';
import 'package:logipool_flutter/shared/services/auth_service.dart';
import 'package:logipool_flutter/shared/services/location_tracking_service.dart';
import 'package:logipool_flutter/core/di/service_locator.dart';

import 'load_tracking_section_test.mocks.dart';

@GenerateMocks([AuthService, LocationTrackingService])
void main() {
  late MockAuthService mockAuthService;
  late MockLocationTrackingService mockLocationTrackingService;
  late LoadModel testLoad;
  late UserModel testUser;

  setUp(() {
    mockAuthService = MockAuthService();
    mockLocationTrackingService = MockLocationTrackingService();
    
    // Setup test data
    testUser = UserModel(
      id: '1',
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      role: 'TRANSPORTER',
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      company: CompanyInfo(
        id: 1,
        name: 'Test Company',
        registrationNumber: 'TC001',
        contactEmail: '<EMAIL>',
        contactPhone: '+1234567890',
        address: '123 Test Street',
        isVerified: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    );

    testLoad = LoadModel(
      id: 1,
      title: 'Test Load',
      cargoType: 'Electronics',
      weight: 100.0,
      pickupLocation: 'Harare',
      deliveryLocation: 'Bulawayo',
      pickupDate: DateTime.now().add(Duration(days: 1)),
      deliveryDate: DateTime.now().add(Duration(days: 3)),
      status: LoadStatus.assigned,
      assignedCompanyId: 1,
      assignedCompanyName: 'Test Company',
    );

    // Setup mocks
    when(mockAuthService.currentUser).thenReturn(testUser);
    when(mockLocationTrackingService.isTracking).thenReturn(false);
    when(mockLocationTrackingService.currentLoadId).thenReturn(null);
    when(mockLocationTrackingService.hasLocationPermission).thenReturn(true);
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: Scaffold(
        body: MultiProvider(
          providers: [
            ChangeNotifierProvider<AuthService>.value(value: mockAuthService),
          ],
          child: LoadTrackingSection(load: testLoad),
        ),
      ),
    );
  }

  group('LoadTrackingSection', () {
    testWidgets('should display live tracking controls for assigned loads', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show live tracking status card
      expect(find.text('Live Tracking Status'), findsOneWidget);
      
      // Should show live tracking control card for assigned loads
      expect(find.text('Live Location Tracking'), findsOneWidget);
      expect(find.text('Start Live Tracking'), findsOneWidget);
    });

    testWidgets('should not show tracking controls for non-assigned loads', (WidgetTester tester) async {
      // Create load not assigned to user's company
      final unassignedLoad = testLoad.copyWith(
        assignedCompanyId: 999,
        assignedCompanyName: 'Other Company',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MultiProvider(
              providers: [
                ChangeNotifierProvider<AuthService>.value(value: mockAuthService),
              ],
              child: LoadTrackingSection(load: unassignedLoad),
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Should not show live tracking control card
      expect(find.text('Live Location Tracking'), findsNothing);
      expect(find.text('Start Live Tracking'), findsNothing);
    });

    testWidgets('should show warning when tracking another load', (WidgetTester tester) async {
      // Setup mock to simulate tracking another load
      when(mockLocationTrackingService.isTracking).thenReturn(true);
      when(mockLocationTrackingService.currentLoadId).thenReturn(2);

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show warning message
      expect(find.text('You are currently tracking another load. Stop that tracking first.'), findsOneWidget);
      
      // Start button should be disabled
      final startButton = find.text('Start Live Tracking');
      expect(startButton, findsOneWidget);
      
      final button = tester.widget<ElevatedButton>(
        find.ancestor(of: startButton, matching: find.byType(ElevatedButton)),
      );
      expect(button.onPressed, isNull);
    });

    testWidgets('should show stop button when tracking current load', (WidgetTester tester) async {
      // Setup mock to simulate tracking current load
      when(mockLocationTrackingService.isTracking).thenReturn(true);
      when(mockLocationTrackingService.currentLoadId).thenReturn(1);

      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Should show stop button
      expect(find.text('Stop Live Tracking'), findsOneWidget);
      expect(find.text('Start Live Tracking'), findsNothing);
    });
  });
}
