#!/bin/bash

# LogiPool Backup Script
# This script creates backups of the database and uploaded files

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_DIR="${PROJECT_ROOT}/backups"
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Backup retention (days)
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}

# S3 Configuration (optional)
S3_BACKUP_ENABLED=${S3_BACKUP_ENABLED:-false}
S3_BUCKET=${BACKUP_S3_BUCKET:-logipool-backups-prod}
AWS_REGION=${AWS_REGION:-us-east-1}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create backup directory
create_backup_dir() {
    local backup_path="${BACKUP_DIR}/${TIMESTAMP}"
    mkdir -p "$backup_path"
    echo "$backup_path"
}

# Backup database
backup_database() {
    local backup_path="$1"
    local db_backup_file="${backup_path}/database_backup.sql"
    
    log_info "Creating database backup..."
    
    # Check if PostgreSQL container is running
    if ! docker-compose -f "${PROJECT_ROOT}/${DOCKER_COMPOSE_FILE}" ps postgres | grep -q "Up"; then
        log_error "PostgreSQL container is not running"
        return 1
    fi
    
    # Create database backup
    docker-compose -f "${PROJECT_ROOT}/${DOCKER_COMPOSE_FILE}" exec -T postgres \
        pg_dump -U "${DATABASE_USERNAME:-logipool}" "${DATABASE_NAME:-logipool_prod}" \
        > "$db_backup_file"
    
    if [[ $? -eq 0 ]]; then
        # Compress the backup
        gzip "$db_backup_file"
        log_success "Database backup created: ${db_backup_file}.gz"
        return 0
    else
        log_error "Database backup failed"
        return 1
    fi
}

# Backup uploaded files
backup_files() {
    local backup_path="$1"
    local files_backup_file="${backup_path}/uploads_backup.tar.gz"
    
    log_info "Creating files backup..."
    
    if [[ -d "${PROJECT_ROOT}/uploads" ]]; then
        tar -czf "$files_backup_file" -C "$PROJECT_ROOT" uploads/
        
        if [[ $? -eq 0 ]]; then
            log_success "Files backup created: $files_backup_file"
            return 0
        else
            log_error "Files backup failed"
            return 1
        fi
    else
        log_warning "Uploads directory not found, skipping files backup"
        return 0
    fi
}

# Backup configuration files
backup_config() {
    local backup_path="$1"
    local config_backup_file="${backup_path}/config_backup.tar.gz"
    
    log_info "Creating configuration backup..."
    
    # Backup important configuration files
    tar -czf "$config_backup_file" -C "$PROJECT_ROOT" \
        .env \
        docker-compose.prod.yml \
        nginx/ \
        monitoring/ \
        scripts/ \
        2>/dev/null || true
    
    if [[ $? -eq 0 ]]; then
        log_success "Configuration backup created: $config_backup_file"
        return 0
    else
        log_warning "Configuration backup had some issues but continued"
        return 0
    fi
}

# Create backup manifest
create_manifest() {
    local backup_path="$1"
    local manifest_file="${backup_path}/backup_manifest.txt"
    
    log_info "Creating backup manifest..."
    
    cat > "$manifest_file" << EOF
LogiPool Backup Manifest
========================
Backup Date: $(date)
Backup Path: $backup_path
Timestamp: $TIMESTAMP

Files in this backup:
EOF
    
    # List all files in the backup
    find "$backup_path" -type f -exec basename {} \; | sort >> "$manifest_file"
    
    # Add file sizes
    echo "" >> "$manifest_file"
    echo "File Sizes:" >> "$manifest_file"
    find "$backup_path" -type f -exec ls -lh {} \; | awk '{print $9 " - " $5}' >> "$manifest_file"
    
    # Add checksums
    echo "" >> "$manifest_file"
    echo "Checksums (SHA256):" >> "$manifest_file"
    find "$backup_path" -type f -name "*.gz" -exec sha256sum {} \; >> "$manifest_file"
    
    log_success "Backup manifest created: $manifest_file"
}

# Upload to S3 (if enabled)
upload_to_s3() {
    local backup_path="$1"
    
    if [[ "$S3_BACKUP_ENABLED" != "true" ]]; then
        log_info "S3 backup is disabled, skipping upload"
        return 0
    fi
    
    log_info "Uploading backup to S3..."
    
    # Check if AWS CLI is available
    if ! command -v aws &> /dev/null; then
        log_warning "AWS CLI not found, skipping S3 upload"
        return 0
    fi
    
    # Create S3 path
    local s3_path="s3://${S3_BUCKET}/logipool/${TIMESTAMP}/"
    
    # Upload files
    aws s3 cp "$backup_path" "$s3_path" --recursive --region "$AWS_REGION"
    
    if [[ $? -eq 0 ]]; then
        log_success "Backup uploaded to S3: $s3_path"
        return 0
    else
        log_error "S3 upload failed"
        return 1
    fi
}

# Clean old backups
cleanup_old_backups() {
    log_info "Cleaning up old backups (older than $RETENTION_DAYS days)..."
    
    # Local cleanup
    find "$BACKUP_DIR" -type d -name "20*" -mtime +$RETENTION_DAYS -exec rm -rf {} \; 2>/dev/null || true
    
    # S3 cleanup (if enabled)
    if [[ "$S3_BACKUP_ENABLED" == "true" ]] && command -v aws &> /dev/null; then
        local cutoff_date=$(date -d "$RETENTION_DAYS days ago" +%Y%m%d)
        aws s3 ls "s3://${S3_BUCKET}/logipool/" | \
        awk '{print $2}' | \
        sed 's/\///g' | \
        while read -r backup_date; do
            if [[ "$backup_date" < "$cutoff_date" ]]; then
                aws s3 rm "s3://${S3_BUCKET}/logipool/${backup_date}/" --recursive --region "$AWS_REGION"
                log_info "Removed old S3 backup: $backup_date"
            fi
        done
    fi
    
    log_success "Old backups cleaned up"
}

# Send notification (if configured)
send_notification() {
    local status="$1"
    local backup_path="$2"
    
    if [[ -n "$BACKUP_NOTIFICATION_EMAIL" ]]; then
        local subject="LogiPool Backup $status - $TIMESTAMP"
        local body="Backup $status at $(date)\nBackup location: $backup_path"
        
        # Send email notification (requires mail command)
        if command -v mail &> /dev/null; then
            echo -e "$body" | mail -s "$subject" "$BACKUP_NOTIFICATION_EMAIL"
            log_info "Notification sent to $BACKUP_NOTIFICATION_EMAIL"
        fi
    fi
}

# Main backup function
main() {
    log_info "Starting LogiPool backup process..."
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Create backup directory
    local backup_path
    backup_path=$(create_backup_dir)
    
    # Perform backups
    local backup_success=true
    
    if ! backup_database "$backup_path"; then
        backup_success=false
    fi
    
    if ! backup_files "$backup_path"; then
        backup_success=false
    fi
    
    backup_config "$backup_path"
    create_manifest "$backup_path"
    
    # Upload to S3 if successful
    if [[ "$backup_success" == "true" ]]; then
        upload_to_s3 "$backup_path"
        cleanup_old_backups
        send_notification "SUCCESS" "$backup_path"
        log_success "Backup completed successfully: $backup_path"
    else
        send_notification "FAILED" "$backup_path"
        log_error "Backup completed with errors: $backup_path"
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "database")
        backup_path=$(create_backup_dir)
        backup_database "$backup_path"
        ;;
    "files")
        backup_path=$(create_backup_dir)
        backup_files "$backup_path"
        ;;
    "config")
        backup_path=$(create_backup_dir)
        backup_config "$backup_path"
        ;;
    "cleanup")
        cleanup_old_backups
        ;;
    *)
        main
        ;;
esac
