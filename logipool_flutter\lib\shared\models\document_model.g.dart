// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'document_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DocumentModel _$DocumentModelFromJson(Map<String, dynamic> json) =>
    DocumentModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      type: $enumDecode(_$DocumentTypeEnumMap, json['type']),
      fileName: json['fileName'] as String,
      fileType: json['fileType'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      status: $enumDecode(_$DocumentStatusEnumMap, json['status']),
      description: json['description'] as String?,
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
      isRequired: json['isRequired'] as bool,
      companyId: (json['companyId'] as num?)?.toInt(),
      vehicleId: (json['vehicleId'] as num?)?.toInt(),
      loadId: (json['loadId'] as num?)?.toInt(),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      verifiedAt: json['verifiedAt'] == null
          ? null
          : DateTime.parse(json['verifiedAt'] as String),
      verifiedById: (json['verifiedById'] as num?)?.toInt(),
      downloadUrl: json['downloadUrl'] as String?,
    );

Map<String, dynamic> _$DocumentModelToJson(DocumentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': _$DocumentTypeEnumMap[instance.type]!,
      'fileName': instance.fileName,
      'fileType': instance.fileType,
      'fileSize': instance.fileSize,
      'status': _$DocumentStatusEnumMap[instance.status]!,
      'description': instance.description,
      'expiryDate': instance.expiryDate?.toIso8601String(),
      'isRequired': instance.isRequired,
      'companyId': instance.companyId,
      'vehicleId': instance.vehicleId,
      'loadId': instance.loadId,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'verifiedAt': instance.verifiedAt?.toIso8601String(),
      'verifiedById': instance.verifiedById,
      'downloadUrl': instance.downloadUrl,
    };

const _$DocumentTypeEnumMap = {
  DocumentType.companyRegistration: 'COMPANY_REGISTRATION',
  DocumentType.taxClearance: 'TAX_CLEARANCE',
  DocumentType.businessLicense: 'BUSINESS_LICENSE',
  DocumentType.insuranceCertificate: 'INSURANCE_CERTIFICATE',
  DocumentType.vehicleRegistration: 'VEHICLE_REGISTRATION',
  DocumentType.fitnessCertificate: 'FITNESS_CERTIFICATE',
  DocumentType.roadPermit: 'ROAD_PERMIT',
  DocumentType.zinaraPermit: 'ZINARA_PERMIT',
  DocumentType.vehicleInsurance: 'VEHICLE_INSURANCE',
  DocumentType.vehiclePhotos: 'VEHICLE_PHOTOS',
  DocumentType.proofOfDelivery: 'PROOF_OF_DELIVERY',
  DocumentType.invoice: 'INVOICE',
  DocumentType.contract: 'CONTRACT',
  DocumentType.waybill: 'WAYBILL',
  DocumentType.customsDeclaration: 'CUSTOMS_DECLARATION',
  DocumentType.profilePhoto: 'PROFILE_PHOTO',
  DocumentType.other: 'OTHER',
};

const _$DocumentStatusEnumMap = {
  DocumentStatus.pending: 'PENDING',
  DocumentStatus.verified: 'VERIFIED',
  DocumentStatus.rejected: 'REJECTED',
  DocumentStatus.expired: 'EXPIRED',
};

DocumentUploadRequest _$DocumentUploadRequestFromJson(
        Map<String, dynamic> json) =>
    DocumentUploadRequest(
      name: json['name'] as String,
      type: $enumDecode(_$DocumentTypeEnumMap, json['type']),
      description: json['description'] as String?,
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
      companyId: (json['companyId'] as num?)?.toInt(),
      vehicleId: (json['vehicleId'] as num?)?.toInt(),
      loadId: (json['loadId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$DocumentUploadRequestToJson(
        DocumentUploadRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': _$DocumentTypeEnumMap[instance.type]!,
      'description': instance.description,
      'expiryDate': instance.expiryDate?.toIso8601String(),
      'companyId': instance.companyId,
      'vehicleId': instance.vehicleId,
      'loadId': instance.loadId,
    };

DocumentUpdateRequest _$DocumentUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    DocumentUpdateRequest(
      name: json['name'] as String?,
      description: json['description'] as String?,
      expiryDate: json['expiryDate'] == null
          ? null
          : DateTime.parse(json['expiryDate'] as String),
    );

Map<String, dynamic> _$DocumentUpdateRequestToJson(
        DocumentUpdateRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'expiryDate': instance.expiryDate?.toIso8601String(),
    };

DocumentVerificationRequest _$DocumentVerificationRequestFromJson(
        Map<String, dynamic> json) =>
    DocumentVerificationRequest(
      status: $enumDecode(_$DocumentStatusEnumMap, json['status']),
      comments: json['comments'] as String?,
    );

Map<String, dynamic> _$DocumentVerificationRequestToJson(
        DocumentVerificationRequest instance) =>
    <String, dynamic>{
      'status': _$DocumentStatusEnumMap[instance.status]!,
      'comments': instance.comments,
    };

FileUploadResponse _$FileUploadResponseFromJson(Map<String, dynamic> json) =>
    FileUploadResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      fileName: json['fileName'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      fileType: json['fileType'] as String,
      documentId: (json['documentId'] as num).toInt(),
      downloadUrl: json['downloadUrl'] as String,
    );

Map<String, dynamic> _$FileUploadResponseToJson(FileUploadResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'fileName': instance.fileName,
      'fileSize': instance.fileSize,
      'fileType': instance.fileType,
      'documentId': instance.documentId,
      'downloadUrl': instance.downloadUrl,
    };
