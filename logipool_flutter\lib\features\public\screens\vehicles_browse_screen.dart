import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/widgets/public_footer.dart';
import '../../../shared/models/vehicle_model.dart';
import '../../../shared/models/paginated_response.dart';
import '../../../shared/services/public_api_service.dart';
import '../../../shared/utils/api_client.dart';

class VehiclesBrowseScreen extends StatefulWidget {
  const VehiclesBrowseScreen({super.key});

  @override
  State<VehiclesBrowseScreen> createState() => _VehiclesBrowseScreenState();
}

class _VehiclesBrowseScreenState extends State<VehiclesBrowseScreen> {
  String _selectedType = 'All';
  String _selectedLocation = 'All';
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // API related state
  late PublicApiService _publicApiService;
  List<VehicleModel> _vehicles = [];
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentPage = 0;
  bool _hasMoreData = true;
  final ScrollController _scrollController = ScrollController();

  final List<String> _vehicleTypes = [
    'All',
    'Truck',
    'Trailer',
    'Flatbed',
    'Refrigerated',
    'Tanker',
    'Container',
    'Van',
    'Pickup'
  ];

  final List<String> _locations = [
    'All',
    'Harare',
    'Bulawayo',
    'Mutare',
    'Gweru',
    'Kwekwe',
    'Masvingo',
    'Chinhoyi'
  ];

  @override
  void initState() {
    super.initState();
    _publicApiService = PublicApiService(ApiClient.instance);
    _loadVehicles();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
      if (!_isLoading && _hasMoreData) {
        _loadMoreVehicles();
      }
    }
  }

  Future<void> _loadVehicles() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
      _currentPage = 0;
      _vehicles.clear();
    });

    try {
      final response = await _publicApiService.browseVehicles(
        page: _currentPage,
        size: 20,
        type: _selectedType != 'All' ? _selectedType.toUpperCase() : null,
        location: _selectedLocation != 'All' ? _selectedLocation : null,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      setState(() {
        _vehicles = response.content;
        _hasMoreData = !response.last;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = e.toString();
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreVehicles() async {
    if (_isLoading || !_hasMoreData) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await _publicApiService.browseVehicles(
        page: _currentPage + 1,
        size: 20,
        type: _selectedType != 'All' ? _selectedType.toUpperCase() : null,
        location: _selectedLocation != 'All' ? _selectedLocation : null,
        search: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      setState(() {
        _vehicles.addAll(response.content);
        _currentPage++;
        _hasMoreData = !response.last;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          _buildHeader(context),
          // _buildFilters(context),
          Expanded(
            child: RefreshIndicator(
              onRefresh: _loadVehicles,
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  children: [
                    _buildVehicleGrid(context),
                    const PublicFooter(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Text(
          //   'Browse Vehicles',
          //   style: Theme.of(context).textTheme.headlineLarge?.copyWith(
          //         color: Colors.white,
          //         fontWeight: FontWeight.bold,
          //       ),
          // ),
          // const SizedBox(height: 8),
          // Text(
          //   'Find the perfect vehicle for your transportation needs',
          //   style: Theme.of(context).textTheme.bodyLarge?.copyWith(
          //         color: Colors.white70,
          //       ),
          // ),
          // const SizedBox(height: 16),
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search vehicles...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  setState(() {
                    _searchQuery = '';
                  });
                },
              ),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(30),
                borderSide: BorderSide.none,
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchQuery = value;
              });
              // Debounce search to avoid too many API calls
              Future.delayed(const Duration(milliseconds: 500), () {
                if (_searchQuery == value) {
                  _loadVehicles();
                }
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFilters(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Theme.of(context).primaryColor.withOpacity(0.05),
      child: Row(
        children: [
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Vehicle Type',
                border: OutlineInputBorder(),
              ),
              items: _vehicleTypes.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                });
                _loadVehicles();
              },
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedLocation,
              decoration: const InputDecoration(
                labelText: 'Location',
                border: OutlineInputBorder(),
              ),
              items: _locations.map((location) {
                return DropdownMenuItem(
                  value: location,
                  child: Text(location),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedLocation = value!;
                });
                _loadVehicles();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVehicleGrid(BuildContext context) {
    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load vehicles',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadVehicles,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_isLoading && _vehicles.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_vehicles.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_shipping_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No vehicles found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search criteria',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      );
    }

    final crossAxisCount = MediaQuery.of(context).size.width > 768 ? 3 : 2;
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = (screenWidth - 48) / crossAxisCount; // 48 = padding + spacing

    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: _vehicles.map((vehicle) => SizedBox(
              width: cardWidth,
              child: _buildVehicleCard(context, vehicle),
            )).toList(),
          ),
          if (_isLoading && _vehicles.isNotEmpty)
            const Padding(
              padding: EdgeInsets.all(16),
              child: CircularProgressIndicator(),
            ),
        ],
      ),
    );
  }

  Widget _buildVehicleCard(BuildContext context, VehicleModel vehicle) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(12)),
                color: Theme.of(context).primaryColor.withOpacity(0.1),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.local_shipping,
                      size: 48,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: (vehicle.isAvailableForRent == true)
                            ? Colors.green
                            : Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        (vehicle.isAvailableForRent == true) ? 'Available' : 'Busy',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${vehicle.make ?? ''} ${vehicle.model ?? ''}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Capacity: ${vehicle.maxWeight ?? 0} ${vehicle.weightUnit ?? 'kg'}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  Text(
                    'Company: ${vehicle.company?.name ?? 'Unknown'}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const Spacer(),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () =>
                              _showVehicleDetails(context, vehicle),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'View Details',
                            style: TextStyle(fontSize: 12),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () => _showInquiryDialog(context, vehicle),
                        icon: const Icon(Icons.message),
                        style: IconButton.styleFrom(
                          backgroundColor:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }




  void _showVehicleDetails(BuildContext context, VehicleModel vehicle) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${vehicle.make ?? ''} ${vehicle.model ?? ''}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Type: ${vehicle.type?.name ?? 'Unknown'}'),
            Text('Capacity: ${vehicle.maxWeight ?? 0} ${vehicle.weightUnit ?? 'kg'}'),
            Text('Registration: ${vehicle.registrationNumber ?? 'N/A'}'),
            Text('Company: ${vehicle.company?.name ?? 'Unknown'}'),
            if (vehicle.company?.rating != null)
              Text('Rating: ${vehicle.company!.rating!.toStringAsFixed(1)} ⭐'),
            Text('Status: ${(vehicle.isAvailableForRent == true) ? 'Available' : 'Busy'}'),
            if (vehicle.dailyRate != null)
              Text('Rate: \$${vehicle.dailyRate}/day'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showInquiryDialog(context, vehicle);
            },
            child: const Text('Inquire'),
          ),
        ],
      ),
    );
  }

  void _showInquiryDialog(BuildContext context, VehicleModel vehicle) {
    final nameController = TextEditingController();
    final emailController = TextEditingController();
    final messageController = TextEditingController();

    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Inquire about ${vehicle.make ?? ''} ${vehicle.model ?? ''}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Your Name',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: messageController,
              decoration: const InputDecoration(
                labelText: 'Message',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Handle inquiry submission
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Inquiry sent successfully!'),
                ),
              );
            },
            child: const Text('Send Inquiry'),
          ),
        ],
      ),
    );
  }
}
