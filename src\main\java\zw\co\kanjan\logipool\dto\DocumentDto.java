package zw.co.kanjan.logipool.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.Document;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.time.LocalDateTime;

public class DocumentDto {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Document response")
    public static class DocumentResponse {
        
        @Schema(description = "Document ID", example = "1")
        private Long id;
        
        @Schema(description = "Document name", example = "Company Registration Certificate")
        private String name;
        
        @Schema(description = "Document type")
        private Document.DocumentType type;
        
        @Schema(description = "File name", example = "company_reg_cert.pdf")
        private String fileName;
        
        @Schema(description = "File type", example = "application/pdf")
        private String fileType;
        
        @Schema(description = "File size in bytes", example = "1024000")
        private Long fileSize;
        
        @Schema(description = "Document status")
        private Document.DocumentStatus status;
        
        @Schema(description = "Document description")
        private String description;
        
        @Schema(description = "Expiry date")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime expiryDate;
        
        @Schema(description = "Is required document", example = "true")
        private Boolean isRequired;
        
        @Schema(description = "Company ID", example = "1")
        private Long companyId;
        
        @Schema(description = "Vehicle ID", example = "1")
        private Long vehicleId;
        
        @Schema(description = "Load ID", example = "1")
        private Long loadId;
        
        @Schema(description = "Creation timestamp")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;
        
        @Schema(description = "Last update timestamp")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime updatedAt;
        
        @Schema(description = "Verification timestamp")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime verifiedAt;
        
        @Schema(description = "Verified by user ID")
        private Long verifiedById;
        
        @Schema(description = "Download URL")
        private String downloadUrl;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Document upload request")
    public static class DocumentUploadRequest {
        
        @NotBlank(message = "Document name is required")
        @Size(max = 100, message = "Document name must not exceed 100 characters")
        @Schema(description = "Document name", example = "Company Registration Certificate", required = true)
        private String name;
        
        @NotNull(message = "Document type is required")
        @Schema(description = "Document type", required = true)
        private Document.DocumentType type;
        
        @Size(max = 500, message = "Description must not exceed 500 characters")
        @Schema(description = "Document description", example = "Official company registration certificate from government")
        private String description;
        
        @Schema(description = "Expiry date (if applicable)")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime expiryDate;
        
        @Schema(description = "Company ID (for company documents)", example = "1")
        private Long companyId;
        
        @Schema(description = "Vehicle ID (for vehicle documents)", example = "1")
        private Long vehicleId;
        
        @Schema(description = "Load ID (for load documents)", example = "1")
        private Long loadId;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Document update request")
    public static class DocumentUpdateRequest {
        
        @Size(max = 100, message = "Document name must not exceed 100 characters")
        @Schema(description = "Document name", example = "Updated Company Registration Certificate")
        private String name;
        
        @Size(max = 500, message = "Description must not exceed 500 characters")
        @Schema(description = "Document description")
        private String description;
        
        @Schema(description = "Expiry date")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime expiryDate;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Document verification request")
    public static class DocumentVerificationRequest {
        
        @NotNull(message = "Status is required")
        @Schema(description = "Verification status", required = true)
        private Document.DocumentStatus status;
        
        @Size(max = 500, message = "Comments must not exceed 500 characters")
        @Schema(description = "Verification comments")
        private String comments;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "File upload response")
    public static class FileUploadResponse {
        
        @Schema(description = "Upload success status", example = "true")
        private Boolean success;
        
        @Schema(description = "Response message", example = "File uploaded successfully")
        private String message;
        
        @Schema(description = "Uploaded file name", example = "document_123456789.pdf")
        private String fileName;
        
        @Schema(description = "File size in bytes", example = "1024000")
        private Long fileSize;
        
        @Schema(description = "File type", example = "application/pdf")
        private String fileType;
        
        @Schema(description = "Document ID", example = "1")
        private Long documentId;
        
        @Schema(description = "Download URL")
        private String downloadUrl;
    }
}
