package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.company.CompanyCreateRequest;
import zw.co.kanjan.logipool.dto.company.CompanyMemberDto;
import zw.co.kanjan.logipool.dto.company.CompanyResponse;
import zw.co.kanjan.logipool.dto.company.CompanyUpdateRequest;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.service.CompanyMemberService;
import zw.co.kanjan.logipool.service.CompanyService;

@RestController
@RequestMapping("/api/companies")
@RequiredArgsConstructor
@Tag(name = "Companies", description = "Company management APIs")
public class CompanyController {

    private final CompanyService companyService;
    private final CompanyMemberService companyMemberService;
    
    @PostMapping
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Create company", description = "Create a new company profile")
    public ResponseEntity<CompanyResponse> createCompany(@Valid @RequestBody CompanyCreateRequest request,
                                                       Authentication authentication) {
        CompanyResponse response = companyService.createCompany(request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/{companyId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Update company", description = "Update company profile")
    public ResponseEntity<CompanyResponse> updateCompany(@PathVariable Long companyId,
                                                       @Valid @RequestBody CompanyUpdateRequest request,
                                                       Authentication authentication) {
        CompanyResponse response = companyService.updateCompany(companyId, request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/{companyId}")
    @Operation(summary = "Get company by ID", description = "Get company details by ID")
    public ResponseEntity<CompanyResponse> getCompanyById(@PathVariable Long companyId) {
        CompanyResponse response = companyService.getCompanyById(companyId);
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/my-company")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get my company", description = "Get current user's company profile")
    public ResponseEntity<CompanyResponse> getMyCompany(Authentication authentication) {
        CompanyResponse response = companyService.getMyCompany(authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping
    @Operation(summary = "Get all companies", description = "Get paginated list of all companies")
    public ResponseEntity<Page<CompanyResponse>> getAllCompanies(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "name") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<CompanyResponse> companies = companyService.getAllCompanies(pageable);
        return ResponseEntity.ok(companies);
    }
    
    @GetMapping("/logistics-providers")
    @Operation(summary = "Get verified logistics providers", description = "Get paginated list of verified logistics providers")
    public ResponseEntity<Page<CompanyResponse>> getVerifiedLogisticsProviders(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "rating") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<CompanyResponse> companies = companyService.getVerifiedLogisticsProviders(pageable);
        return ResponseEntity.ok(companies);
    }
    
    @GetMapping("/search")
    @Operation(summary = "Search companies", description = "Search companies by name")
    public ResponseEntity<Page<CompanyResponse>> searchCompanies(
            @RequestParam String name,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "name") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<CompanyResponse> companies = companyService.searchCompaniesByName(name, pageable);
        return ResponseEntity.ok(companies);
    }
    
    @GetMapping("/verification-status/{status}")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get companies by verification status", description = "Get companies by verification status (Admin only)")
    public ResponseEntity<Page<CompanyResponse>> getCompaniesByVerificationStatus(
            @PathVariable Company.VerificationStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<CompanyResponse> companies = companyService.getCompaniesByVerificationStatus(status, pageable);
        return ResponseEntity.ok(companies);
    }
    
    @PutMapping("/{companyId}/verify")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Verify company", description = "Update company verification status (Admin only)")
    public ResponseEntity<CompanyResponse> verifyCompany(@PathVariable Long companyId,
                                                       @RequestParam Company.VerificationStatus status) {
        CompanyResponse response = companyService.verifyCompany(companyId, status);
        return ResponseEntity.ok(response);
    }

    // Company Member Management Endpoints

    @PostMapping("/{companyId}/members/invite")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Invite user to company", description = "Invite a user to join the company")
    public ResponseEntity<CompanyMemberDto.MemberResponse> inviteUser(
            @PathVariable Long companyId,
            @Valid @RequestBody CompanyMemberDto.InviteRequest request,
            Authentication authentication) {
        CompanyMemberDto.MemberResponse response = companyMemberService.inviteUser(companyId, request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{companyId}/members")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get company members", description = "Get list of company members")
    public ResponseEntity<CompanyMemberDto.MemberListResponse> getCompanyMembers(
            @PathVariable Long companyId,
            Authentication authentication) {
        CompanyMemberDto.MemberListResponse response = companyMemberService.getCompanyMembers(companyId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @PutMapping("/members")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Update company member", description = "Update company member role and permissions")
    public ResponseEntity<CompanyMemberDto.MemberResponse> updateMember(
            @Valid @RequestBody CompanyMemberDto.UpdateRequest request,
            Authentication authentication) {
        CompanyMemberDto.MemberResponse response = companyMemberService.updateMember(request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/members/{memberId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Remove company member", description = "Remove a member from the company")
    public ResponseEntity<Void> removeMember(
            @PathVariable Long memberId,
            Authentication authentication) {
        companyMemberService.removeMember(memberId, authentication.getName());
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/members/accept-invitation")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Accept company invitation", description = "Accept an invitation to join a company")
    public ResponseEntity<CompanyMemberDto.MemberResponse> acceptInvitation(
            @Valid @RequestBody CompanyMemberDto.AcceptInvitationRequest request,
            Authentication authentication) {
        CompanyMemberDto.MemberResponse response = companyMemberService.acceptInvitation(request, authentication.getName());
        return ResponseEntity.ok(response);
    }
}
