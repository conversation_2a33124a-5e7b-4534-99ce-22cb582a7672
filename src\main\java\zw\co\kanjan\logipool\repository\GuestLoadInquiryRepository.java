package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.GuestLoadInquiry;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface GuestLoadInquiryRepository extends JpaRepository<GuestLoadInquiry, Long> {
    
    // Find by status
    Page<GuestLoadInquiry> findByStatus(GuestLoadInquiry.InquiryStatus status, Pageable pageable);
    
    // Find by email (to check for duplicate inquiries)
    List<GuestLoadInquiry> findByEmailOrderByCreatedAtDesc(String email);
    
    // Find recent inquiries from same email (to prevent spam)
    @Query("SELECT g FROM GuestLoadInquiry g WHERE g.email = :email AND g.createdAt > :since")
    List<GuestLoadInquiry> findRecentInquiriesByEmail(@Param("email") String email, @Param("since") LocalDateTime since);
    
    // Find by assigned admin
    Page<GuestLoadInquiry> findByAssignedTo(Long assignedTo, Pageable pageable);
    
    // Find unassigned inquiries
    Page<GuestLoadInquiry> findByAssignedToIsNull(Pageable pageable);
    
    // Find urgent inquiries
    @Query("SELECT g FROM GuestLoadInquiry g WHERE g.isUrgent = true AND g.status IN :statuses ORDER BY g.createdAt ASC")
    List<GuestLoadInquiry> findUrgentInquiries(@Param("statuses") List<GuestLoadInquiry.InquiryStatus> statuses);
    
    // Count by status
    long countByStatus(GuestLoadInquiry.InquiryStatus status);
    
    // Find inquiries created within date range
    @Query("SELECT g FROM GuestLoadInquiry g WHERE g.createdAt BETWEEN :startDate AND :endDate")
    Page<GuestLoadInquiry> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                                  @Param("endDate") LocalDateTime endDate, 
                                                  Pageable pageable);
    
    // Search inquiries by title or description
    @Query("SELECT g FROM GuestLoadInquiry g WHERE " +
           "LOWER(g.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(g.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(g.pickupLocation) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(g.deliveryLocation) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<GuestLoadInquiry> searchInquiries(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    // Find inquiries that need follow-up (created more than X hours ago but not contacted)
    @Query("SELECT g FROM GuestLoadInquiry g WHERE g.status = 'NEW' AND g.createdAt < :cutoffTime")
    List<GuestLoadInquiry> findInquiriesNeedingFollowUp(@Param("cutoffTime") LocalDateTime cutoffTime);
}
