package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import zw.co.kanjan.logipool.dto.tracking.TrackingDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.TrackingMapper;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.LoadTrackingRepository;
import zw.co.kanjan.logipool.repository.LiveTrackingSessionRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TrackingServiceTest {

    @Mock
    private LoadTrackingRepository trackingRepository;

    @Mock
    private LoadRepository loadRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private TrackingMapper trackingMapper;

    @Mock
    private NotificationService notificationService;

    @Mock
    private RealTimeNotificationService realTimeNotificationService;

    @Mock
    private LiveTrackingSessionRepository liveTrackingSessionRepository;

    @InjectMocks
    private TrackingService trackingService;

    private Load testLoad;
    private User testUser;
    private User testClient;
    private Company testCompany;
    private LoadTracking testTracking;
    private TrackingDto.TrackingUpdateRequest trackingUpdateRequest;
    private TrackingDto.LocationUpdateRequest locationUpdateRequest;

    @BeforeEach
    void setUp() {
        // Create test client
        testClient = User.builder()
                .id(1L)
                .username("testclient")
                .firstName("Test")
                .lastName("Client")
                .email("<EMAIL>")
                .build();

        // Create test company user
        testUser = User.builder()
                .id(2L)
                .username("testuser")
                .firstName("Test")
                .lastName("User")
                .email("<EMAIL>")
                .build();

        // Create test company
        testCompany = Company.builder()
                .id(1L)
                .name("Test Transport Company")
                .user(testUser)
                .build();

        // Create test load
        testLoad = Load.builder()
                .id(1L)
                .title("Test Load")
                .client(testClient)
                .assignedCompany(testCompany)
                .status(Load.LoadStatus.IN_TRANSIT)
                .deliveryDate(LocalDateTime.now().plusDays(1))
                .build();

        // Create test tracking
        testTracking = LoadTracking.builder()
                .id(1L)
                .load(testLoad)
                .location("Test Location")
                .latitude(new BigDecimal("-17.8252"))
                .longitude(new BigDecimal("31.0335"))
                .status(LoadTracking.TrackingStatus.IN_TRANSIT_TO_DELIVERY)
                .updatedBy(testUser)
                .timestamp(LocalDateTime.now())
                .isAutomated(false)
                .build();

        // Create test requests
        trackingUpdateRequest = TrackingDto.TrackingUpdateRequest.builder()
                .loadId(1L)
                .location("Updated Location")
                .latitude(new BigDecimal("-17.8300"))
                .longitude(new BigDecimal("31.0400"))
                .status(LoadTracking.TrackingStatus.ARRIVED_AT_DELIVERY)
                .notes("Arrived at delivery location")
                .build();

        locationUpdateRequest = TrackingDto.LocationUpdateRequest.builder()
                .loadId(1L)
                .location("GPS Location")
                .latitude(new BigDecimal("-17.8350"))
                .longitude(new BigDecimal("31.0450"))
                .speed(new BigDecimal("60.5"))
                .heading(new BigDecimal("180.0"))
                .accuracy(new BigDecimal("5.0"))
                .build();
    }

    @Test
    void updateTracking_Success() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(trackingMapper.toEntity(trackingUpdateRequest)).thenReturn(testTracking);
        when(trackingRepository.save(any(LoadTracking.class))).thenReturn(testTracking);
        when(trackingMapper.toResponse(testTracking)).thenReturn(createTrackingResponse());

        // Mock role check for permission validation
        Role transporterRole = new Role();
        transporterRole.setName(Role.RoleName.TRANSPORTER);
        testUser.setRoles(Set.of(transporterRole));

        // Act
        TrackingDto.TrackingResponse result = trackingService.updateTracking(trackingUpdateRequest, "testuser");

        // Assert
        assertNotNull(result);
        verify(trackingRepository).save(any(LoadTracking.class));
        verify(notificationService).sendLoadStatusUpdateNotification(eq(testLoad), anyString());
        verify(realTimeNotificationService).sendNotificationToUser(eq(testClient.getId()), any());
        verify(realTimeNotificationService).sendNotificationToUser(eq(testUser.getId()), any());
    }

    @Test
    void updateTracking_LoadNotFound() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, 
                () -> trackingService.updateTracking(trackingUpdateRequest, "testuser"));
    }

    @Test
    void updateTracking_UserNotFound() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, 
                () -> trackingService.updateTracking(trackingUpdateRequest, "testuser"));
    }

    @Test
    void updateTracking_NoPermission() {
        // Arrange
        User unauthorizedUser = User.builder()
                .id(3L)
                .username("unauthorized")
                .roles(Set.of()) // No roles
                .build();

        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(userRepository.findByUsername("unauthorized")).thenReturn(Optional.of(unauthorizedUser));

        // Act & Assert
        assertThrows(BusinessException.class, 
                () -> trackingService.updateTracking(trackingUpdateRequest, "unauthorized"));
    }

    @Test
    void updateLocation_Success() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(trackingRepository.findLatestByLoad(testLoad)).thenReturn(Optional.of(testTracking));
        when(trackingRepository.save(any(LoadTracking.class))).thenReturn(testTracking);
        when(trackingMapper.toResponse(any(LoadTracking.class))).thenReturn(createTrackingResponse());

        // Mock role check for permission validation
        Role transporterRole = new Role();
        transporterRole.setName(Role.RoleName.TRANSPORTER);
        testUser.setRoles(Set.of(transporterRole));

        // Act
        TrackingDto.TrackingResponse result = trackingService.updateLocation(locationUpdateRequest, "testuser");

        // Assert
        assertNotNull(result);
        verify(trackingRepository).save(any(LoadTracking.class));
    }

    @Test
    void getLoadTrackingHistory_Success() {
        // Arrange
        List<LoadTracking> trackingHistory = List.of(testTracking);
        TrackingDto.LoadTrackingHistory expectedHistory = createTrackingHistory();

        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(trackingRepository.findByLoadOrderByTimestampDesc(testLoad)).thenReturn(trackingHistory);
        when(trackingMapper.toTrackingHistory(testTracking)).thenReturn(expectedHistory);
        when(trackingMapper.toResponseList(trackingHistory)).thenReturn(List.of(createTrackingResponse()));

        // Act
        TrackingDto.LoadTrackingHistory result = trackingService.getLoadTrackingHistory(1L);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalEntries());
    }

    @Test
    void getLoadTrackingHistory_NoHistory() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(trackingRepository.findByLoadOrderByTimestampDesc(testLoad)).thenReturn(List.of());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, 
                () -> trackingService.getLoadTrackingHistory(1L));
    }

    @Test
    void getTrackingHistory_WithPagination() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<LoadTracking> trackingPage = new PageImpl<>(List.of(testTracking));
        
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(trackingRepository.findByLoadOrderByTimestampDesc(testLoad, pageable)).thenReturn(trackingPage);
        when(trackingMapper.toResponse(testTracking)).thenReturn(createTrackingResponse());

        // Act
        Page<TrackingDto.TrackingResponse> result = trackingService.getTrackingHistory(1L, pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
    }

    @Test
    void getLatestTracking_Success() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(trackingRepository.findLatestByLoad(testLoad)).thenReturn(Optional.of(testTracking));
        when(trackingMapper.toResponse(testTracking)).thenReturn(createTrackingResponse());

        // Act
        Optional<TrackingDto.TrackingResponse> result = trackingService.getLatestTracking(1L);

        // Assert
        assertTrue(result.isPresent());
    }

    @Test
    void getLatestTracking_NotFound() {
        // Arrange
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(trackingRepository.findLatestByLoad(testLoad)).thenReturn(Optional.empty());

        // Act
        Optional<TrackingDto.TrackingResponse> result = trackingService.getLatestTracking(1L);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void getActiveLoadsLocation_Success() {
        // Arrange
        List<Load> activeLoads = List.of(testLoad);
        when(trackingRepository.findLoadsInTransit()).thenReturn(activeLoads);
        when(trackingRepository.findLatestByLoad(testLoad)).thenReturn(Optional.of(testTracking));

        // Act
        List<TrackingDto.RealTimeLocation> result = trackingService.getActiveLoadsLocation();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    void getTrackingStatistics_Success() {
        // Arrange
        when(trackingRepository.countLoadsByStatus(any())).thenReturn(5L);

        // Act
        TrackingDto.TrackingStatistics result = trackingService.getTrackingStatistics();

        // Assert
        assertNotNull(result);
        assertNotNull(result.getGeneratedAt());
    }

    @Test
    void confirmDelivery_Success() {
        // Arrange
        TrackingDto.DeliveryConfirmationRequest deliveryRequest = TrackingDto.DeliveryConfirmationRequest.builder()
                .loadId(1L)
                .deliveryLocation("Final Destination")
                .deliveryLatitude(new BigDecimal("-17.8400"))
                .deliveryLongitude(new BigDecimal("31.0500"))
                .deliveryNotes("Package delivered successfully")
                .recipientName("John Doe")
                .build();

        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(userRepository.findByUsername("testuser")).thenReturn(Optional.of(testUser));
        when(trackingRepository.save(any(LoadTracking.class))).thenReturn(testTracking);
        when(trackingMapper.toResponse(any(LoadTracking.class))).thenReturn(createTrackingResponse());

        // Mock role check for permission validation
        Role transporterRole = new Role();
        transporterRole.setName(Role.RoleName.TRANSPORTER);
        testUser.setRoles(Set.of(transporterRole));

        // Act
        TrackingDto.TrackingResponse result = trackingService.confirmDelivery(deliveryRequest, "testuser");

        // Assert
        assertNotNull(result);
        verify(loadRepository).save(testLoad);
        assertEquals(Load.LoadStatus.DELIVERED, testLoad.getStatus());
    }

    // Helper methods
    private TrackingDto.TrackingResponse createTrackingResponse() {
        return TrackingDto.TrackingResponse.builder()
                .id(1L)
                .loadId(1L)
                .loadTitle("Test Load")
                .location("Test Location")
                .latitude(new BigDecimal("-17.8252"))
                .longitude(new BigDecimal("31.0335"))
                .status(LoadTracking.TrackingStatus.IN_TRANSIT_TO_DELIVERY)
                .updatedById(2L)
                .updatedByName("Test User")
                .timestamp(LocalDateTime.now())
                .isAutomated(false)
                .build();
    }

    @Test
    void testGetActiveLoadsLocation_Success() {
        // Arrange
        List<Load> activeLoads = List.of(testLoad);

        when(trackingRepository.findLoadsInTransit()).thenReturn(activeLoads);
        when(trackingRepository.findLatestByLoad(testLoad))
                .thenReturn(Optional.of(testTracking));

        // Act
        var result = trackingService.getActiveLoadsLocation();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());

        var location = result.get(0);
        assertEquals(testLoad.getId(), location.getLoadId());
        assertEquals(testTracking.getLatitude(), location.getLatitude());
        assertEquals(testTracking.getLongitude(), location.getLongitude());
        assertEquals(testTracking.getStatus(), location.getStatus());

        verify(trackingRepository).findLoadsInTransit();
        verify(trackingRepository).findLatestByLoad(testLoad);
    }

    @Test
    void testGetActiveLoadsLocation_WithFallback() {
        // Arrange
        List<Load> activeLoads = List.of(testLoad);

        when(trackingRepository.findLoadsInTransit()).thenThrow(new RuntimeException("JPQL query failed"));
        when(trackingRepository.findLoadsInTransitNative()).thenReturn(activeLoads);
        when(trackingRepository.findLatestByLoad(testLoad))
                .thenReturn(Optional.of(testTracking));

        // Act
        var result = trackingService.getActiveLoadsLocation();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());

        verify(trackingRepository).findLoadsInTransit();
        verify(trackingRepository).findLoadsInTransitNative();
        verify(trackingRepository).findLatestByLoad(testLoad);
    }

    @Test
    void testGetActiveLoadsLocation_EmptyResult() {
        // Arrange
        when(trackingRepository.findLoadsInTransit()).thenReturn(List.of());

        // Act
        var result = trackingService.getActiveLoadsLocation();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(trackingRepository).findLoadsInTransit();
        verify(trackingRepository, never()).findLatestByLoad(any());
    }

    private TrackingDto.LoadTrackingHistory createTrackingHistory() {
        return TrackingDto.LoadTrackingHistory.builder()
                .loadId(1L)
                .loadTitle("Test Load")
                .currentStatus(LoadTracking.TrackingStatus.IN_TRANSIT_TO_DELIVERY)
                .currentLocation("Test Location")
                .currentLatitude(new BigDecimal("-17.8252"))
                .currentLongitude(new BigDecimal("31.0335"))
                .lastUpdated(LocalDateTime.now())
                .totalEntries(1)
                .build();
    }
}
