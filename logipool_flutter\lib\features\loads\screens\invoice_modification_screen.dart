import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/invoice_model.dart';
import '../../../shared/models/load_model.dart';
import '../../invoices/bloc/invoice_bloc.dart';

class InvoiceModificationScreen extends StatefulWidget {
  final LoadModel load;
  final InvoicePreviewResponse preview;

  const InvoiceModificationScreen({
    super.key,
    required this.load,
    required this.preview,
  });

  @override
  State<InvoiceModificationScreen> createState() =>
      _InvoiceModificationScreenState();
}

class _InvoiceModificationScreenState extends State<InvoiceModificationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();
  final _discountController = TextEditingController();

  DateTime? _selectedDueDate;
  List<InvoiceItemRequest> _items = [];
  bool _isGenerating = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    _descriptionController.text = widget.preview.description ?? '';
    _selectedDueDate = widget.preview.dueDateValue;
    _discountController.text =
        widget.preview.discountAmountValue.toStringAsFixed(2);

    // Add listeners to update live preview
    _discountController.addListener(() {
      setState(() {}); // Trigger rebuild for live preview
    });

    // Initialize items from preview
    if (widget.preview.items != null) {
      _items = widget.preview.items!
          .map((item) => InvoiceItemRequest(
                description: item.description,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                unit: item.unit,
                type: item.type,
              ))
          .toList();
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _notesController.dispose();
    _discountController.dispose();
    super.dispose();
  }

  void _generateInvoice() {
    if (!_formKey.currentState!.validate()) return;

    final request = InvoiceModificationRequest(
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      notes: _notesController.text.trim().isEmpty
          ? null
          : _notesController.text.trim(),
      dueDate: _selectedDueDate,
      discountAmount: double.tryParse(_discountController.text),
      items: _items.isEmpty ? null : _items,
    );

    setState(() {
      _isGenerating = true;
    });

    context.read<InvoiceBloc>().add(
          InvoiceGenerateWithModifications(
            loadId: widget.load.id!,
            request: request,
          ),
        );
  }

  void _addItem() {
    setState(() {
      _items.add(const InvoiceItemRequest(
        description: '',
        quantity: 1.0,
        unitPrice: 0.0,
        type: InvoiceItemType.other,
      ));
    });
  }

  void _updateItem(int index, InvoiceItemRequest updatedItem) {
    setState(() {
      _items[index] = updatedItem;
    });
  }

  void _removeItem(int index) {
    setState(() {
      _items.removeAt(index);
    });
  }

  void _selectDueDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate:
          _selectedDueDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _selectedDueDate = date;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<InvoiceBloc, InvoiceState>(
      listener: (context, state) {
        if (state is InvoiceGenerated) {
          Navigator.of(context).pop();
          Navigator.of(context).pop(); // Also close the preview dialog
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content:
                  Text('Invoice generated successfully with modifications'),
              backgroundColor: Colors.green,
            ),
          );
        } else if (state is InvoiceError) {
          setState(() {
            _isGenerating = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Modify Invoice'),
          actions: [
            TextButton(
              onPressed: _isGenerating ? null : _generateInvoice,
              child: _isGenerating
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Generate'),
            ),
          ],
        ),
        body: Form(
          key: _formKey,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildBasicInfoSection(),
                const SizedBox(height: 24),
                _buildItemsSection(),
                const SizedBox(height: 24),
                _buildLiveTotalsPreview(),
                const SizedBox(height: 24),
                _buildSummarySection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Invoice Details',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
                hintText: 'Enter invoice description',
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes (Optional)',
                hintText: 'Additional notes for the invoice',
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _discountController,
                    decoration: const InputDecoration(
                      labelText: 'Discount Amount',
                      prefixText: '\$',
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final amount = double.tryParse(value);
                        if (amount == null || amount < 0) {
                          return 'Enter a valid discount amount';
                        }
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: _selectDueDate,
                    child: InputDecorator(
                      decoration: const InputDecoration(
                        labelText: 'Due Date',
                        suffixIcon: Icon(Icons.calendar_today),
                      ),
                      child: Text(
                        _selectedDueDate != null
                            ? DateFormat('MMM dd, yyyy')
                                .format(_selectedDueDate!)
                            : 'Select date',
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Invoice Items',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                TextButton.icon(
                  onPressed: _addItem,
                  icon: const Icon(Icons.add),
                  label: const Text('Add Item'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_items.isEmpty)
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Center(
                  child: Text('No custom items. Default items will be used.'),
                ),
              )
            else
              ..._items.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                return _buildItemRow(index, item);
              }),
          ],
        ),
      ),
    );
  }

  Widget _buildItemRow(int index, InvoiceItemRequest item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  'Item ${index + 1}',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
              IconButton(
                onPressed: () => _removeItem(index),
                icon: const Icon(Icons.delete, color: Colors.red),
              ),
            ],
          ),
          const SizedBox(height: 8),
          TextFormField(
            initialValue: item.description,
            decoration: const InputDecoration(
              labelText: 'Description',
              isDense: true,
            ),
            onChanged: (value) {
              _items[index] = InvoiceItemRequest(
                description: value,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
                unit: item.unit,
                type: item.type,
              );
            },
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  initialValue: item.quantity.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Quantity',
                    isDense: true,
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final quantity = double.tryParse(value) ?? item.quantity;
                    _items[index] = InvoiceItemRequest(
                      description: item.description,
                      quantity: quantity,
                      unitPrice: item.unitPrice,
                      unit: item.unit,
                      type: item.type,
                    );
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: TextFormField(
                  initialValue: item.unitPrice.toString(),
                  decoration: const InputDecoration(
                    labelText: 'Unit Price',
                    prefixText: '\$',
                    isDense: true,
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    final unitPrice = double.tryParse(value) ?? item.unitPrice;
                    _items[index] = InvoiceItemRequest(
                      description: item.description,
                      quantity: item.quantity,
                      unitPrice: unitPrice,
                      unit: item.unit,
                      type: item.type,
                    );
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLiveTotalsPreview() {
    // Calculate live totals based on current form state
    double subtotal =
        _items.fold(0.0, (sum, item) => sum + (item.quantity * item.unitPrice));
    double discount = double.tryParse(_discountController.text) ?? 0.0;
    double taxAmount = subtotal * widget.preview.taxRateValue;
    double totalAmount = subtotal + taxAmount - discount;
    double commissionAmount = totalAmount * widget.preview.commissionRateValue;
    double netAmount = totalAmount - commissionAmount;

    return Card(
      elevation: 4,
      color: Colors.green[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green[700],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.preview,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Live Preview',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.green[800],
                            ),
                      ),
                      Text(
                        'Updated totals based on your changes',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.green[600],
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[300]!),
              ),
              child: Column(
                children: [
                  _buildPreviewTotalRow('Subtotal', subtotal),
                  _buildPreviewTotalRow(
                      'Tax (${(widget.preview.taxRateValue * 100).toStringAsFixed(1)}%)',
                      taxAmount),
                  if (discount > 0)
                    _buildPreviewTotalRow('Discount', -discount),
                  const Divider(),
                  _buildPreviewTotalRow('Total Amount', totalAmount,
                      isTotal: true),
                  _buildPreviewTotalRow(
                      'Commission (${(widget.preview.commissionRateValue * 100).toStringAsFixed(1)}%)',
                      -commissionAmount,
                      isCommission: true),
                  const Divider(),
                  _buildPreviewTotalRow('Net Amount (You receive)', netAmount,
                      isNet: true),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewTotalRow(
    String label,
    double amount, {
    bool isTotal = false,
    bool isCommission = false,
    bool isNet = false,
  }) {
    Color? textColor;
    FontWeight fontWeight = FontWeight.normal;

    if (isTotal) {
      fontWeight = FontWeight.bold;
    } else if (isCommission) {
      textColor = Colors.red[700];
    } else if (isNet) {
      textColor = Colors.green[700];
      fontWeight = FontWeight.bold;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: fontWeight,
                  color: textColor,
                ),
          ),
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: fontWeight,
                  color: textColor,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue[50]!, Colors.blue[100]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.blue[100]!.withValues(alpha: 0.5),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue[700],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.account_balance,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Platform Commission',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue[800],
                            ),
                      ),
                      Text(
                        'Service fee for using LogiPool platform',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.blue[600],
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[300]!),
              ),
              child: Text(
                widget.preview.commissionNote ??
                    'A platform commission of ${(widget.preview.commissionRateValue * 100).toStringAsFixed(1)}% will be deducted from the total invoice amount. This fee covers platform services including load matching, payment processing, and customer support.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.blue[800],
                      height: 1.4,
                    ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildCommissionDetailCard(
                    'Commission Rate',
                    '${(widget.preview.commissionRateValue * 100).toStringAsFixed(1)}%',
                    Icons.percent,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCommissionDetailCard(
                    'Commission Amount',
                    '\$${widget.preview.commissionAmountValue.toStringAsFixed(2)}',
                    Icons.attach_money,
                    Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildCommissionDetailCard(
              'You Will Receive',
              '\$${widget.preview.netAmountValue.toStringAsFixed(2)}',
              Icons.account_balance_wallet,
              Colors.green,
              isFullWidth: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommissionDetailCard(
    String label,
    String value,
    IconData icon,
    Color color, {
    bool isFullWidth = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: isFullWidth
          ? Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(icon, color: color, size: 18),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        label,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      Text(
                        value,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: color,
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                    ],
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(icon, color: color, size: 16),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        label,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: color,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
    );
  }
}
