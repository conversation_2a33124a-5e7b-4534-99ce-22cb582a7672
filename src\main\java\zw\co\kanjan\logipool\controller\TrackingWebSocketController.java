package zw.co.kanjan.logipool.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;
import zw.co.kanjan.logipool.dto.tracking.TrackingDto;
import zw.co.kanjan.logipool.service.TrackingService;

import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
@Controller
@RequiredArgsConstructor
public class TrackingWebSocketController {

    private final SimpMessagingTemplate messagingTemplate;
    private final TrackingService trackingService;

    @MessageMapping("/tracking.subscribe.{loadId}")
    @SendTo("/topic/tracking/{loadId}")
    public TrackingDto.RealTimeLocation subscribeToLoadTracking(
            @DestinationVariable Long loadId,
            @Payload Map<String, Object> payload,
            SimpMessageHeaderAccessor headerAccessor) {
        
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        log.info("User {} subscribed to tracking updates for load {}", username, loadId);
        
        // Return current location if available
        return trackingService.getLatestTracking(loadId)
                .map(tracking -> TrackingDto.RealTimeLocation.builder()
                        .loadId(loadId)
                        .latitude(tracking.getLatitude())
                        .longitude(tracking.getLongitude())
                        .timestamp(tracking.getTimestamp())
                        .status(tracking.getStatus())
                        .build())
                .orElse(TrackingDto.RealTimeLocation.builder()
                        .loadId(loadId)
                        .timestamp(LocalDateTime.now())
                        .build());
    }

    @MessageMapping("/tracking.location.update")
    public void handleLocationUpdate(@Payload TrackingDto.LocationUpdateRequest locationUpdate,
                                   SimpMessageHeaderAccessor headerAccessor) {
        
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        log.info("Received location update from user {} for load {}", username, locationUpdate.getLoadId());
        
        try {
            // Update location through service
            TrackingDto.TrackingResponse response = trackingService.updateLocation(locationUpdate, username);
            
            // Broadcast to load-specific topic
            TrackingDto.RealTimeLocation realTimeLocation = TrackingDto.RealTimeLocation.builder()
                    .loadId(locationUpdate.getLoadId())
                    .latitude(locationUpdate.getLatitude())
                    .longitude(locationUpdate.getLongitude())
                    .speed(locationUpdate.getSpeed())
                    .heading(locationUpdate.getHeading())
                    .accuracy(locationUpdate.getAccuracy())
                    .timestamp(response.getTimestamp())
                    .status(response.getStatus())
                    .build();
            
            messagingTemplate.convertAndSend(
                "/topic/tracking/" + locationUpdate.getLoadId(), 
                realTimeLocation
            );
            
            // Also broadcast to general tracking topic for dashboard
            messagingTemplate.convertAndSend("/topic/tracking/all", realTimeLocation);
            
        } catch (Exception e) {
            log.error("Failed to process location update for load {}: {}", locationUpdate.getLoadId(), e.getMessage());
            
            // Send error back to user
            messagingTemplate.convertAndSendToUser(
                username,
                "/queue/errors",
                Map.of("error", "Failed to update location: " + e.getMessage(),
                       "loadId", locationUpdate.getLoadId(),
                       "timestamp", LocalDateTime.now())
            );
        }
    }

    @MessageMapping("/tracking.status.update")
    public void handleStatusUpdate(@Payload TrackingDto.TrackingUpdateRequest statusUpdate,
                                 SimpMessageHeaderAccessor headerAccessor) {
        
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        log.info("Received status update from user {} for load {}: {}", 
                username, statusUpdate.getLoadId(), statusUpdate.getStatus());
        
        try {
            // Update tracking through service
            TrackingDto.TrackingResponse response = trackingService.updateTracking(statusUpdate, username);
            
            // Broadcast status update to load-specific topic
            messagingTemplate.convertAndSend(
                "/topic/tracking/" + statusUpdate.getLoadId() + "/status", 
                response
            );
            
            // Broadcast to general status updates topic
            messagingTemplate.convertAndSend("/topic/tracking/status/all", response);
            
        } catch (Exception e) {
            log.error("Failed to process status update for load {}: {}", statusUpdate.getLoadId(), e.getMessage());
            
            // Send error back to user
            messagingTemplate.convertAndSendToUser(
                username,
                "/queue/errors",
                Map.of("error", "Failed to update status: " + e.getMessage(),
                       "loadId", statusUpdate.getLoadId(),
                       "status", statusUpdate.getStatus(),
                       "timestamp", LocalDateTime.now())
            );
        }
    }

    @MessageMapping("/tracking.delivery.confirm")
    public void handleDeliveryConfirmation(@Payload TrackingDto.DeliveryConfirmationRequest deliveryConfirmation,
                                         SimpMessageHeaderAccessor headerAccessor) {
        
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        log.info("Received delivery confirmation from user {} for load {}", 
                username, deliveryConfirmation.getLoadId());
        
        try {
            // Confirm delivery through service
            TrackingDto.TrackingResponse response = trackingService.confirmDelivery(deliveryConfirmation, username);
            
            // Broadcast delivery confirmation
            messagingTemplate.convertAndSend(
                "/topic/tracking/" + deliveryConfirmation.getLoadId() + "/delivery", 
                response
            );
            
            // Broadcast to general delivery notifications
            messagingTemplate.convertAndSend("/topic/deliveries/confirmed", response);
            
        } catch (Exception e) {
            log.error("Failed to process delivery confirmation for load {}: {}", 
                    deliveryConfirmation.getLoadId(), e.getMessage());
            
            // Send error back to user
            messagingTemplate.convertAndSendToUser(
                username,
                "/queue/errors",
                Map.of("error", "Failed to confirm delivery: " + e.getMessage(),
                       "loadId", deliveryConfirmation.getLoadId(),
                       "timestamp", LocalDateTime.now())
            );
        }
    }

    @MessageMapping("/tracking.subscribe.company.{companyId}")
    @SendTo("/topic/tracking/company/{companyId}")
    public Map<String, Object> subscribeToCompanyTracking(
            @DestinationVariable Long companyId,
            @Payload Map<String, Object> payload,
            SimpMessageHeaderAccessor headerAccessor) {
        
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        log.info("User {} subscribed to company {} tracking updates", username, companyId);
        
        // Return current company tracking summary
        return Map.of(
            "companyId", companyId,
            "subscribedAt", LocalDateTime.now(),
            "message", "Subscribed to company tracking updates"
        );
    }

    @MessageMapping("/tracking.subscribe.all")
    @SendTo("/topic/tracking/all")
    public Map<String, Object> subscribeToAllTracking(@Payload Map<String, Object> payload,
                                                     SimpMessageHeaderAccessor headerAccessor) {
        
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        log.info("User {} subscribed to all tracking updates", username);
        
        return Map.of(
            "subscribedAt", LocalDateTime.now(),
            "message", "Subscribed to all tracking updates"
        );
    }

    // Method to send tracking update to specific users
    public void sendTrackingUpdateToUser(Long userId, TrackingDto.TrackingResponse tracking) {
        try {
            messagingTemplate.convertAndSendToUser(
                userId.toString(),
                "/queue/tracking",
                tracking
            );
        } catch (Exception e) {
            log.error("Failed to send tracking update to user {}: {}", userId, e.getMessage());
        }
    }

    // Method to broadcast location update to all subscribers
    public void broadcastLocationUpdate(TrackingDto.RealTimeLocation location) {
        try {
            // Send to load-specific topic
            messagingTemplate.convertAndSend(
                "/topic/tracking/" + location.getLoadId(),
                location
            );
            
            // Send to general tracking topic
            messagingTemplate.convertAndSend("/topic/tracking/all", location);
            
        } catch (Exception e) {
            log.error("Failed to broadcast location update for load {}: {}", 
                    location.getLoadId(), e.getMessage());
        }
    }

    // Method to send delivery notification
    public void sendDeliveryNotification(Long loadId, TrackingDto.TrackingResponse delivery) {
        try {
            messagingTemplate.convertAndSend(
                "/topic/tracking/" + loadId + "/delivery",
                delivery
            );
            
            messagingTemplate.convertAndSend("/topic/deliveries/confirmed", delivery);
            
        } catch (Exception e) {
            log.error("Failed to send delivery notification for load {}: {}", loadId, e.getMessage());
        }
    }
}
