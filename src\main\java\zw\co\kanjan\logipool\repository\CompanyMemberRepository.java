package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.CompanyMember;
import zw.co.kanjan.logipool.entity.User;

import java.util.List;
import java.util.Optional;

@Repository
public interface CompanyMemberRepository extends JpaRepository<CompanyMember, Long> {
    
    Optional<CompanyMember> findByCompanyAndUser(Company company, User user);
    
    List<CompanyMember> findByCompany(Company company);
    
    Page<CompanyMember> findByCompany(Company company, Pageable pageable);
    
    List<CompanyMember> findByUser(User user);
    
    List<CompanyMember> findByCompanyAndStatus(Company company, CompanyMember.MemberStatus status);
    
    List<CompanyMember> findByCompanyAndRole(Company company, CompanyMember.CompanyRole role);
    
    @Query("SELECT cm FROM CompanyMember cm WHERE cm.company = :company AND cm.role = :role AND cm.status = :status")
    List<CompanyMember> findByCompanyAndRoleAndStatus(
            @Param("company") Company company, 
            @Param("role") CompanyMember.CompanyRole role, 
            @Param("status") CompanyMember.MemberStatus status);
    
    @Query("SELECT cm FROM CompanyMember cm WHERE cm.user = :user AND cm.status = :status")
    List<CompanyMember> findByUserAndStatus(@Param("user") User user, @Param("status") CompanyMember.MemberStatus status);
    
    @Query("SELECT cm FROM CompanyMember cm WHERE cm.company = :company AND cm.canManageMembers = true AND cm.status = 'ACTIVE'")
    List<CompanyMember> findMembersWhoCanManageMembers(@Param("company") Company company);
    
    @Query("SELECT cm FROM CompanyMember cm WHERE cm.company = :company AND cm.canUpdateLoadStatus = true AND cm.status = 'ACTIVE'")
    List<CompanyMember> findMembersWhoCanUpdateLoadStatus(@Param("company") Company company);
    
    @Query("SELECT cm FROM CompanyMember cm WHERE cm.company = :company AND cm.canTrackLocation = true AND cm.status = 'ACTIVE'")
    List<CompanyMember> findDriversWhoCanTrackLocation(@Param("company") Company company);
    
    @Query("SELECT COUNT(cm) FROM CompanyMember cm WHERE cm.company = :company AND cm.status = 'ACTIVE'")
    Long countActiveMembers(@Param("company") Company company);
    
    @Query("SELECT COUNT(cm) FROM CompanyMember cm WHERE cm.company = :company AND cm.role = 'OWNER' AND cm.status = 'ACTIVE'")
    Long countActiveOwners(@Param("company") Company company);
    
    boolean existsByCompanyAndUser(Company company, User user);
    
    boolean existsByCompanyAndUserAndStatus(Company company, User user, CompanyMember.MemberStatus status);
}
