import 'package:json_annotation/json_annotation.dart';

part 'invoice_model.g.dart';

// Type alias for API responses
typedef InvoiceResponse = InvoiceModel;

@JsonSerializable()
class InvoiceModel {
  final int? id;
  final String? invoiceNumber;
  final double? subtotal;
  final double? taxAmount;
  final double? discountAmount;
  final double? totalAmount;
  final double? commissionAmount;
  final double? netAmount;
  final InvoiceStatus status;
  final InvoiceType type;
  final String? description;
  final String? notes;
  final DateTime dueDate;
  final int? loadId;
  final String? loadTitle;
  final int? bidId;
  final int? clientId;
  final String? clientName;
  final int? transporterId;
  final String? transporterName;
  final int? companyId;
  final String? companyName;
  final List<InvoiceItemModel>? items;
  final DateTime? sentAt;
  final DateTime? paidAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const InvoiceModel({
    this.id,
    this.invoiceNumber,
    this.subtotal,
    this.taxAmount,
    this.discountAmount,
    this.totalAmount,
    this.commissionAmount,
    this.netAmount,
    required this.status,
    required this.type,
    this.description,
    this.notes,
    required this.dueDate,
    this.loadId,
    this.loadTitle,
    this.bidId,
    this.clientId,
    this.clientName,
    this.transporterId,
    this.transporterName,
    this.companyId,
    this.companyName,
    this.items,
    this.sentAt,
    this.paidAt,
    this.createdAt,
    this.updatedAt,
  });

  factory InvoiceModel.fromJson(Map<String, dynamic> json) =>
      _$InvoiceModelFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceModelToJson(this);

  // Helper methods to get numeric values with defaults
  double get subtotalValue => subtotal ?? 0.0;
  double get taxAmountValue => taxAmount ?? 0.0;
  double get discountAmountValue => discountAmount ?? 0.0;
  double get totalAmountValue => totalAmount ?? 0.0;
  double get commissionAmountValue => commissionAmount ?? 0.0;
  double get netAmountValue => netAmount ?? 0.0;

  InvoiceModel copyWith({
    int? id,
    String? invoiceNumber,
    double? subtotal,
    double? taxAmount,
    double? discountAmount,
    double? totalAmount,
    double? commissionAmount,
    double? netAmount,
    InvoiceStatus? status,
    InvoiceType? type,
    String? description,
    String? notes,
    DateTime? dueDate,
    int? loadId,
    String? loadTitle,
    int? bidId,
    int? clientId,
    String? clientName,
    int? transporterId,
    String? transporterName,
    int? companyId,
    String? companyName,
    List<InvoiceItemModel>? items,
    DateTime? sentAt,
    DateTime? paidAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return InvoiceModel(
      id: id ?? this.id,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      subtotal: subtotal ?? this.subtotal,
      taxAmount: taxAmount ?? this.taxAmount,
      discountAmount: discountAmount ?? this.discountAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      netAmount: netAmount ?? this.netAmount,
      status: status ?? this.status,
      type: type ?? this.type,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      dueDate: dueDate ?? this.dueDate,
      loadId: loadId ?? this.loadId,
      loadTitle: loadTitle ?? this.loadTitle,
      bidId: bidId ?? this.bidId,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      transporterId: transporterId ?? this.transporterId,
      transporterName: transporterName ?? this.transporterName,
      companyId: companyId ?? this.companyId,
      companyName: companyName ?? this.companyName,
      items: items ?? this.items,
      sentAt: sentAt ?? this.sentAt,
      paidAt: paidAt ?? this.paidAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class InvoicePreviewResponse {
  final String? invoiceNumber;
  final double? subtotal;
  final double? taxAmount;
  final double? taxRate;
  final double? discountAmount;
  final double? totalAmount;
  final double? commissionAmount;
  final double? commissionRate;
  final double? netAmount;
  final InvoiceType? type;
  final String? description;
  final DateTime? dueDate;
  final int? loadId;
  final String? loadTitle;
  final int? bidId;
  final double? bidAmount;
  final int? clientId;
  final String? clientName;
  final int? transporterId;
  final String? transporterName;
  final int? companyId;
  final String? companyName;
  final List<InvoiceItemModel>? items;
  final bool? hasExistingInvoiceDocument;
  final String? commissionNote;

  const InvoicePreviewResponse({
    this.invoiceNumber,
    this.subtotal,
    this.taxAmount,
    this.taxRate,
    this.discountAmount,
    this.totalAmount,
    this.commissionAmount,
    this.commissionRate,
    this.netAmount,
    this.type,
    this.description,
    this.dueDate,
    this.loadId,
    this.loadTitle,
    this.bidId,
    this.bidAmount,
    this.clientId,
    this.clientName,
    this.transporterId,
    this.transporterName,
    this.companyId,
    this.companyName,
    this.items,
    this.hasExistingInvoiceDocument,
    this.commissionNote,
  });

  factory InvoicePreviewResponse.fromJson(Map<String, dynamic> json) =>
      _$InvoicePreviewResponseFromJson(json);

  Map<String, dynamic> toJson() => _$InvoicePreviewResponseToJson(this);

  // Helper methods to get numeric values with defaults
  double get subtotalValue => subtotal ?? 0.0;
  double get taxAmountValue => taxAmount ?? 0.0;
  double get taxRateValue => taxRate ?? 0.0;
  double get discountAmountValue => discountAmount ?? 0.0;
  double get totalAmountValue => totalAmount ?? 0.0;
  double get commissionAmountValue => commissionAmount ?? 0.0;
  double get commissionRateValue => commissionRate ?? 0.0;
  double get netAmountValue => netAmount ?? 0.0;
  bool get hasExistingInvoiceDocumentValue =>
      hasExistingInvoiceDocument ?? false;
  DateTime get dueDateValue => dueDate ?? DateTime.now();
  InvoiceType get typeValue => type ?? InvoiceType.loadInvoice;
}

@JsonSerializable()
class InvoiceItemModel {
  final int? id;
  final String description;
  final double quantity;
  final double unitPrice;
  final double totalPrice;
  final String? unit;
  final InvoiceItemType type;

  const InvoiceItemModel({
    this.id,
    required this.description,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
    this.unit,
    required this.type,
  });

  factory InvoiceItemModel.fromJson(Map<String, dynamic> json) =>
      _$InvoiceItemModelFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceItemModelToJson(this);

  InvoiceItemModel copyWith({
    int? id,
    String? description,
    double? quantity,
    double? unitPrice,
    double? totalPrice,
    String? unit,
    InvoiceItemType? type,
  }) {
    return InvoiceItemModel(
      id: id ?? this.id,
      description: description ?? this.description,
      quantity: quantity ?? this.quantity,
      unitPrice: unitPrice ?? this.unitPrice,
      totalPrice: totalPrice ?? this.totalPrice,
      unit: unit ?? this.unit,
      type: type ?? this.type,
    );
  }
}

enum InvoiceStatus {
  @JsonValue('DRAFT')
  draft,
  @JsonValue('SENT')
  sent,
  @JsonValue('VIEWED')
  viewed,
  @JsonValue('PAID')
  paid,
  @JsonValue('OVERDUE')
  overdue,
  @JsonValue('CANCELLED')
  cancelled,
  @JsonValue('REFUNDED')
  refunded;

  String get displayName {
    switch (this) {
      case InvoiceStatus.draft:
        return 'Draft';
      case InvoiceStatus.sent:
        return 'Sent';
      case InvoiceStatus.viewed:
        return 'Viewed';
      case InvoiceStatus.paid:
        return 'Paid';
      case InvoiceStatus.overdue:
        return 'Overdue';
      case InvoiceStatus.cancelled:
        return 'Cancelled';
      case InvoiceStatus.refunded:
        return 'Refunded';
    }
  }

  String get description {
    switch (this) {
      case InvoiceStatus.draft:
        return 'Invoice is in draft state';
      case InvoiceStatus.sent:
        return 'Invoice has been sent';
      case InvoiceStatus.viewed:
        return 'Invoice has been viewed';
      case InvoiceStatus.paid:
        return 'Invoice has been paid';
      case InvoiceStatus.overdue:
        return 'Invoice is overdue';
      case InvoiceStatus.cancelled:
        return 'Invoice has been cancelled';
      case InvoiceStatus.refunded:
        return 'Invoice has been refunded';
    }
  }

  bool get isDraft => this == InvoiceStatus.draft;
  bool get isSent => this == InvoiceStatus.sent;
  bool get isViewed => this == InvoiceStatus.viewed;
  bool get isPaid => this == InvoiceStatus.paid;
  bool get isOverdue => this == InvoiceStatus.overdue;
  bool get isCancelled => this == InvoiceStatus.cancelled;
  bool get isRefunded => this == InvoiceStatus.refunded;
  bool get isActive => !isPaid && !isCancelled && !isRefunded;
  bool get isFinalized => isPaid || isCancelled || isRefunded;
  bool get canMarkAsPaid => isSent || isViewed || isOverdue;
}

enum InvoiceType {
  @JsonValue('LOAD_INVOICE')
  loadInvoice,
  @JsonValue('COMMISSION_INVOICE')
  commissionInvoice,
  @JsonValue('SERVICE_INVOICE')
  serviceInvoice,
  @JsonValue('PENALTY_INVOICE')
  penaltyInvoice;

  String get displayName {
    switch (this) {
      case InvoiceType.loadInvoice:
        return 'Load Invoice';
      case InvoiceType.commissionInvoice:
        return 'Commission Invoice';
      case InvoiceType.serviceInvoice:
        return 'Service Invoice';
      case InvoiceType.penaltyInvoice:
        return 'Penalty Invoice';
    }
  }

  String get description {
    switch (this) {
      case InvoiceType.loadInvoice:
        return 'Invoice for load transportation';
      case InvoiceType.commissionInvoice:
        return 'Invoice for platform commission';
      case InvoiceType.serviceInvoice:
        return 'Invoice for additional services';
      case InvoiceType.penaltyInvoice:
        return 'Invoice for penalties';
    }
  }
}

enum InvoiceItemType {
  @JsonValue('TRANSPORTATION_FEE')
  transportationFee,
  @JsonValue('COMMISSION_FEE')
  commissionFee,
  @JsonValue('FUEL_SURCHARGE')
  fuelSurcharge,
  @JsonValue('INSURANCE_FEE')
  insuranceFee,
  @JsonValue('HANDLING_FEE')
  handlingFee,
  @JsonValue('STORAGE_FEE')
  storageFee,
  @JsonValue('PENALTY_FEE')
  penaltyFee,
  @JsonValue('DISCOUNT')
  discount,
  @JsonValue('OTHER')
  other;

  String get displayName {
    switch (this) {
      case InvoiceItemType.transportationFee:
        return 'Transportation Fee';
      case InvoiceItemType.commissionFee:
        return 'Commission Fee';
      case InvoiceItemType.fuelSurcharge:
        return 'Fuel Surcharge';
      case InvoiceItemType.insuranceFee:
        return 'Insurance Fee';
      case InvoiceItemType.handlingFee:
        return 'Handling Fee';
      case InvoiceItemType.storageFee:
        return 'Storage Fee';
      case InvoiceItemType.penaltyFee:
        return 'Penalty Fee';
      case InvoiceItemType.discount:
        return 'Discount';
      case InvoiceItemType.other:
        return 'Other';
    }
  }

  String get description {
    switch (this) {
      case InvoiceItemType.transportationFee:
        return 'Fee for transportation service';
      case InvoiceItemType.commissionFee:
        return 'Platform commission fee';
      case InvoiceItemType.fuelSurcharge:
        return 'Additional fuel surcharge';
      case InvoiceItemType.insuranceFee:
        return 'Insurance coverage fee';
      case InvoiceItemType.handlingFee:
        return 'Special handling fee';
      case InvoiceItemType.storageFee:
        return 'Storage and warehousing fee';
      case InvoiceItemType.penaltyFee:
        return 'Penalty or late fee';
      case InvoiceItemType.discount:
        return 'Discount applied';
      case InvoiceItemType.other:
        return 'Other miscellaneous fee';
    }
  }

  bool get isDiscount => this == InvoiceItemType.discount;
  bool get isFee => !isDiscount;
}

// Request DTOs
@JsonSerializable()
class InvoiceCreateRequest {
  final InvoiceType type;
  final String? description;
  final String? notes;
  final DateTime dueDate;
  final int loadId;
  final int? bidId;
  final int clientId;
  final int? transporterId;
  final int? companyId;
  final List<InvoiceItemRequest> items;

  const InvoiceCreateRequest({
    required this.type,
    this.description,
    this.notes,
    required this.dueDate,
    required this.loadId,
    this.bidId,
    required this.clientId,
    this.transporterId,
    this.companyId,
    required this.items,
  });

  factory InvoiceCreateRequest.fromJson(Map<String, dynamic> json) =>
      _$InvoiceCreateRequestFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceCreateRequestToJson(this);
}

@JsonSerializable()
class InvoiceUpdateRequest {
  final InvoiceStatus? status;
  final String? description;
  final String? notes;
  final DateTime? dueDate;
  final List<InvoiceItemRequest>? items;

  const InvoiceUpdateRequest({
    this.status,
    this.description,
    this.notes,
    this.dueDate,
    this.items,
  });

  factory InvoiceUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$InvoiceUpdateRequestFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceUpdateRequestToJson(this);
}

@JsonSerializable()
class InvoiceItemRequest {
  final String description;
  final double quantity;
  final double unitPrice;
  final String? unit;
  final InvoiceItemType type;

  const InvoiceItemRequest({
    required this.description,
    required this.quantity,
    required this.unitPrice,
    this.unit,
    required this.type,
  });

  factory InvoiceItemRequest.fromJson(Map<String, dynamic> json) =>
      _$InvoiceItemRequestFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceItemRequestToJson(this);
}

@JsonSerializable()
class InvoiceModificationRequest {
  final String? description;
  final String? notes;
  final DateTime? dueDate;
  final double? discountAmount;
  final List<InvoiceItemRequest>? items;

  const InvoiceModificationRequest({
    this.description,
    this.notes,
    this.dueDate,
    this.discountAmount,
    this.items,
  });

  factory InvoiceModificationRequest.fromJson(Map<String, dynamic> json) =>
      _$InvoiceModificationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$InvoiceModificationRequestToJson(this);
}

@JsonSerializable()
class InvoiceSummary {
  final double totalAmount;
  final double totalCommission;
  final double totalTax;
  final int totalCount;
  final int draftCount;
  final int sentCount;
  final int paidCount;
  final int overdueCount;

  const InvoiceSummary({
    required this.totalAmount,
    required this.totalCommission,
    required this.totalTax,
    required this.totalCount,
    required this.draftCount,
    required this.sentCount,
    required this.paidCount,
    required this.overdueCount,
  });

  factory InvoiceSummary.fromJson(Map<String, dynamic> json) =>
      _$InvoiceSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$InvoiceSummaryToJson(this);
}
