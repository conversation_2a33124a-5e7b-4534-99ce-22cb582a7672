import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

import '../models/location_model.dart';
import '../models/paginated_response.dart';
import '../../core/network/api_client.dart';

class LocationTrackingService extends ChangeNotifier {
  final ApiClient _apiClient;

  StreamSubscription<Position>? _positionStreamSubscription;
  Timer? _trackingTimer;

  Position? _currentPosition;
  bool _isTracking = false;
  bool _hasLocationPermission = false;
  int? _currentLoadId;

  // Tracking configuration
  static const Duration _trackingInterval = Duration(minutes: 3);
  static const int _minimumDistanceFilter = 50; // meters

  LocationTrackingService(this._apiClient);

  // Getters
  Position? get currentPosition => _currentPosition;
  bool get isTracking => _isTracking;
  bool get hasLocationPermission => _hasLocationPermission;
  int? get currentLoadId => _currentLoadId;

  /// Initialize location services and check permissions
  Future<void> initialize() async {
    await _checkLocationPermission();
    if (_hasLocationPermission) {
      await _getCurrentLocation();
    }
  }

  /// Check and request location permissions
  Future<bool> _checkLocationPermission() async {
    // Check if location services are enabled
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      _hasLocationPermission = false;
      notifyListeners();
      return false;
    }

    // Check location permission
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        _hasLocationPermission = false;
        notifyListeners();
        return false;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      _hasLocationPermission = false;
      notifyListeners();
      return false;
    }

    _hasLocationPermission = true;
    notifyListeners();
    return true;
  }

  /// Request location permission from user
  Future<bool> requestLocationPermission() async {
    return await _checkLocationPermission();
  }

  /// Get current location once
  Future<Position?> _getCurrentLocation() async {
    if (!_hasLocationPermission) {
      return null;
    }

    try {
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      _currentPosition = position;
      notifyListeners();
      return position;
    } catch (e) {
      debugPrint('Error getting current location: $e');
      return null;
    }
  }

  /// Start tracking location for a specific load
  Future<bool> startTracking(int loadId) async {
    if (_isTracking) {
      await stopTracking();
    }

    if (!await _checkLocationPermission()) {
      throw Exception('Location permission not granted');
    }

    _currentLoadId = loadId;
    _isTracking = true;
    notifyListeners();

    try {
      // Start position stream for real-time updates
      _positionStreamSubscription = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: _minimumDistanceFilter,
        ),
      ).listen(
        _onLocationUpdate,
        onError: _onLocationError,
      );

      // Start periodic tracking timer
      _trackingTimer = Timer.periodic(_trackingInterval, (_) {
        _sendLocationUpdate();
      });

      // Send initial location
      await _sendLocationUpdate();

      return true;
    } catch (e) {
      _isTracking = false;
      _currentLoadId = null;
      notifyListeners();
      debugPrint('Error starting location tracking: $e');
      return false;
    }
  }

  /// Stop location tracking
  Future<void> stopTracking() async {
    _positionStreamSubscription?.cancel();
    _positionStreamSubscription = null;

    _trackingTimer?.cancel();
    _trackingTimer = null;

    if (_isTracking && _currentLoadId != null) {
      // Send final location update to mark tracking as stopped
      await _sendLocationUpdate(isTrackingEnd: true);
    }

    _isTracking = false;
    _currentLoadId = null;
    notifyListeners();
  }

  /// Handle location updates from the stream
  void _onLocationUpdate(Position position) {
    _currentPosition = position;
    notifyListeners();

    // Send location update to server
    _sendLocationUpdate();
  }

  /// Handle location stream errors
  void _onLocationError(dynamic error) {
    debugPrint('Location stream error: $error');
    // Optionally restart tracking or handle error
  }

  /// Send location update to the server
  Future<void> _sendLocationUpdate({bool isTrackingEnd = false}) async {
    if (_currentPosition == null || _currentLoadId == null) {
      return;
    }

    try {
      final locationUpdate = {
        'loadId': _currentLoadId!,
        'latitude': _currentPosition!.latitude,
        'longitude': _currentPosition!.longitude,
        'accuracy': _currentPosition!.accuracy,
        'altitude': _currentPosition!.altitude,
        'speed': _currentPosition!.speed,
        'heading': _currentPosition!.heading,
        'timestamp': DateTime.fromMillisecondsSinceEpoch(
          _currentPosition!.timestamp!.millisecondsSinceEpoch,
        ).toIso8601String(),
        'isTrackingEnd': isTrackingEnd,
      };

      await _apiClient.post<void>(
        '/loads/${_currentLoadId}/location',
        data: locationUpdate,
      );
    } catch (e) {
      debugPrint('Error sending location update: $e');
      // Don't throw error to avoid stopping tracking
    }
  }

  /// Update driver's current location
  Future<LocationResponse> updateLocation(LocationUpdateRequest request) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/location-tracking/location',
        data: request.toJson(),
      );
      return LocationResponse.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get current location of a specific driver
  Future<LocationResponse> getDriverLocation(int driverId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
          '/location-tracking/drivers/$driverId/location');
      return LocationResponse.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get location history for a driver
  Future<PaginatedResponse<LocationResponse>> getDriverLocationHistory(
    int driverId, {
    int page = 0,
    int size = 20,
  }) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/location-tracking/drivers/$driverId/history',
        queryParameters: {'page': page, 'size': size},
      );
      return PaginatedResponse<LocationResponse>.fromJson(
        response.data!,
        (json) => LocationResponse.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get tracking history for a specific load
  Future<PaginatedResponse<LocationResponse>> getLoadTrackingHistory(
    int loadId, {
    int page = 0,
    int size = 20,
  }) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/location-tracking/loads/$loadId/history',
        queryParameters: {'page': page, 'size': size},
      );
      return PaginatedResponse<LocationResponse>.fromJson(
        response.data!,
        (json) => LocationResponse.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get company tracking summary
  Future<TrackingSummary> getCompanyTrackingSummary(int companyId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
          '/location-tracking/companies/$companyId/summary');
      return TrackingSummary.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Request location permission from a driver
  Future<LocationPermissionResponse> requestDriverLocationPermission(
    LocationPermissionRequest request,
  ) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/location-tracking/permissions/request',
        data: request.toJson(),
      );
      return LocationPermissionResponse.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Grant location permission (driver only)
  Future<LocationPermissionResponse> grantLocationPermission(
      int permissionId) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/location-tracking/permissions/$permissionId/grant',
      );
      return LocationPermissionResponse.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Revoke location permission
  Future<void> revokeLocationPermission(int permissionId,
      {String? reason}) async {
    try {
      await _apiClient.post<void>(
        '/location-tracking/permissions/$permissionId/revoke',
        queryParameters: reason != null ? {'reason': reason} : null,
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Start live tracking for a load
  Future<Map<String, dynamic>> startLiveTracking(int loadId) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/tracking/live/$loadId/start',
      );
      return response.data!;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Stop live tracking for a load
  Future<Map<String, dynamic>> stopLiveTracking(int loadId) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/tracking/live/$loadId/stop',
      );
      return response.data!;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get live tracking status for a load
  Future<Map<String, dynamic>> getLiveTrackingStatus(int loadId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/tracking/live/$loadId/status',
      );
      return response.data!;
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get all active live tracking sessions
  Future<List<Map<String, dynamic>>> getActiveLiveTrackingSessions() async {
    try {
      final response = await _apiClient.get<List<dynamic>>(
        '/tracking/live/active',
      );
      return response.data!.cast<Map<String, dynamic>>();
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  Exception _handleError(DioException e) {
    if (e.response?.statusCode == 404) {
      return Exception('Driver or location not found');
    } else if (e.response?.statusCode == 403) {
      return Exception(
          'You don\'t have permission to access this location data');
    } else if (e.response?.data != null &&
        e.response?.data['message'] != null) {
      return Exception(e.response?.data['message']);
    } else {
      return Exception('An error occurred while managing location tracking');
    }
  }
}
