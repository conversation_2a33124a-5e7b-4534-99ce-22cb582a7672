package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.tracking.LocationDto;
import zw.co.kanjan.logipool.service.LocationTrackingService;

@RestController
@RequestMapping("/api/location-tracking")
@RequiredArgsConstructor
@Tag(name = "Location Tracking", description = "Driver location tracking and permission management APIs")
public class LocationTrackingController {
    
    private final LocationTrackingService locationTrackingService;
    
    // Location update endpoints
    
    @PostMapping("/location")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Update driver location", 
               description = "Update current location for the authenticated driver")
    public ResponseEntity<LocationDto.LocationResponse> updateLocation(
            @Valid @RequestBody LocationDto.LocationUpdateRequest request,
            Authentication authentication) {
        
        LocationDto.LocationResponse response = locationTrackingService.updateDriverLocation(request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/drivers/{driverId}/location")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get driver current location", 
               description = "Get current location of a specific driver (requires permission)")
    public ResponseEntity<LocationDto.LocationResponse> getDriverLocation(
            @PathVariable Long driverId,
            Authentication authentication) {
        
        LocationDto.LocationResponse response = locationTrackingService.getDriverCurrentLocation(driverId, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/drivers/{driverId}/history")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get driver location history", 
               description = "Get location history for a specific driver (requires permission)")
    public ResponseEntity<Page<LocationDto.LocationResponse>> getDriverLocationHistory(
            @PathVariable Long driverId,
            Pageable pageable,
            Authentication authentication) {
        
        Page<LocationDto.LocationResponse> response = locationTrackingService.getDriverLocationHistory(driverId, pageable, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/loads/{loadId}/history")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get load tracking history", 
               description = "Get location tracking history for a specific load")
    public ResponseEntity<Page<LocationDto.LocationResponse>> getLoadTrackingHistory(
            @PathVariable Long loadId,
            Pageable pageable,
            Authentication authentication) {
        
        Page<LocationDto.LocationResponse> response = locationTrackingService.getLoadLocationHistory(loadId, pageable, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/companies/{companyId}/summary")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get company tracking summary", 
               description = "Get tracking summary for all drivers in a company")
    public ResponseEntity<LocationDto.TrackingSummary> getCompanyTrackingSummary(
            @PathVariable Long companyId,
            Authentication authentication) {
        
        LocationDto.TrackingSummary response = locationTrackingService.getCompanyTrackingSummary(companyId, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    // Permission management endpoints
    
    @PostMapping("/permissions/request")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Request location permission", 
               description = "Request permission to track a driver's location")
    public ResponseEntity<LocationDto.PermissionResponse> requestLocationPermission(
            @Valid @RequestBody LocationDto.PermissionRequest request,
            Authentication authentication) {
        
        LocationDto.PermissionResponse response = locationTrackingService.requestLocationPermission(request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/permissions/{permissionId}/grant")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Grant location permission", 
               description = "Grant a location permission request (driver only)")
    public ResponseEntity<LocationDto.PermissionResponse> grantLocationPermission(
            @PathVariable Long permissionId,
            Authentication authentication) {
        
        LocationDto.PermissionResponse response = locationTrackingService.grantLocationPermission(permissionId, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/permissions/{permissionId}/revoke")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Revoke location permission", 
               description = "Revoke a location permission")
    public ResponseEntity<Void> revokeLocationPermission(
            @PathVariable Long permissionId,
            @RequestParam(value = "reason", required = false) String reason,
            Authentication authentication) {
        
        locationTrackingService.revokeLocationPermission(permissionId, authentication.getName(), reason);
        return ResponseEntity.noContent().build();
    }
}
