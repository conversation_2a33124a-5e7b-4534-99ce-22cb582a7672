package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import zw.co.kanjan.logipool.dto.EquipmentDto;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Equipment;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.exception.UnauthorizedException;
import zw.co.kanjan.logipool.exception.ValidationException;
import zw.co.kanjan.logipool.mapper.EquipmentMapper;
import zw.co.kanjan.logipool.repository.CompanyRepository;
import zw.co.kanjan.logipool.repository.EquipmentRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class EquipmentService {

    private final EquipmentRepository equipmentRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final EquipmentMapper equipmentMapper;
    private final FileStorageService fileStorageService;
    private final NotificationService notificationService;

    public EquipmentDto.EquipmentResponse createEquipment(EquipmentDto.EquipmentCreateRequest request, String username) {
        log.info("Creating equipment for user: {}", username);
        
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        // Check if serial number already exists (if provided)
        if (request.getSerialNumber() != null && 
            equipmentRepository.existsBySerialNumber(request.getSerialNumber())) {
            throw new ValidationException("Equipment with serial number " + request.getSerialNumber() + " already exists");
        }
        
        Equipment equipment = equipmentMapper.toEntity(request);
        equipment.setCompany(company);
        
        Equipment savedEquipment = equipmentRepository.save(equipment);
        log.info("Equipment created with ID: {} for company: {}", savedEquipment.getId(), company.getName());
        
        return equipmentMapper.toResponse(savedEquipment);
    }

    @Transactional(readOnly = true)
    public Page<EquipmentDto.EquipmentResponse> getCompanyEquipment(
            String username, 
            Pageable pageable, 
            Equipment.EquipmentStatus status, 
            Equipment.EquipmentType type) {
        
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Page<Equipment> equipment;
        
        if (status != null && type != null) {
            equipment = equipmentRepository.findByCompanyAndStatusAndType(company, status, type, pageable);
        } else if (status != null) {
            equipment = equipmentRepository.findByCompanyAndStatus(company, status, pageable);
        } else if (type != null) {
            equipment = equipmentRepository.findByCompanyAndType(company, type, pageable);
        } else {
            equipment = equipmentRepository.findByCompany(company, pageable);
        }
        
        return equipment.map(equipmentMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public EquipmentDto.EquipmentResponse getEquipment(Long equipmentId, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Equipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Equipment not found with ID: " + equipmentId));
        
        // Check if equipment belongs to user's company
        if (!equipment.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to access this equipment");
        }
        
        return equipmentMapper.toResponse(equipment);
    }

    public EquipmentDto.EquipmentResponse updateEquipment(Long equipmentId, EquipmentDto.EquipmentUpdateRequest request, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Equipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Equipment not found with ID: " + equipmentId));
        
        // Check if equipment belongs to user's company
        if (!equipment.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to update this equipment");
        }
        
        equipmentMapper.updateEntity(request, equipment);
        Equipment updatedEquipment = equipmentRepository.save(equipment);
        
        log.info("Equipment {} updated by user: {}", equipmentId, username);
        return equipmentMapper.toResponse(updatedEquipment);
    }

    public void deleteEquipment(Long equipmentId, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Equipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Equipment not found with ID: " + equipmentId));
        
        // Check if equipment belongs to user's company
        if (!equipment.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to delete this equipment");
        }
        
        // Check if equipment is currently in use
        if (equipment.getStatus() == Equipment.EquipmentStatus.IN_USE) {
            throw new ValidationException("Cannot delete equipment that is currently in use");
        }
        
        equipmentRepository.delete(equipment);
        log.info("Equipment {} deleted by user: {}", equipmentId, username);
    }

    public String uploadEquipmentImage(Long equipmentId, MultipartFile file, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Equipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Equipment not found with ID: " + equipmentId));
        
        // Check if equipment belongs to user's company
        if (!equipment.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to upload image for this equipment");
        }
        
        String imageUrl = fileStorageService.storeFile(file, "equipment");
        equipment.setImageUrl(imageUrl);
        equipmentRepository.save(equipment);
        
        log.info("Image uploaded for equipment {} by user: {}", equipmentId, username);
        return imageUrl;
    }

    public void requestPublicApproval(Long equipmentId, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Equipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Equipment not found with ID: " + equipmentId));
        
        // Check if equipment belongs to user's company
        if (!equipment.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to request approval for this equipment");
        }
        
        // Check if company is verified
        if (company.getVerificationStatus() != Company.VerificationStatus.VERIFIED) {
            throw new ValidationException("Company must be verified before requesting public approval for equipment");
        }
        
        // Check if equipment has required information
        if (equipment.getImageUrl() == null || equipment.getDescription() == null || equipment.getDescription().trim().isEmpty()) {
            throw new ValidationException("Equipment must have an image and description before requesting public approval");
        }
        
        equipment.setPublicApprovalStatus(Equipment.PublicApprovalStatus.PENDING);
        equipmentRepository.save(equipment);
        
        // Notify admins about pending approval
        notificationService.notifyAdminsAboutPendingEquipmentApproval(equipment);
        
        log.info("Public approval requested for equipment {} by user: {}", equipmentId, username);
    }

    public void togglePublicVisibility(Long equipmentId, boolean enable, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Equipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Equipment not found with ID: " + equipmentId));
        
        // Check if equipment belongs to user's company
        if (!equipment.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to modify this equipment");
        }
        
        // Check if equipment is approved for public visibility
        if (enable && equipment.getPublicApprovalStatus() != Equipment.PublicApprovalStatus.APPROVED) {
            throw new ValidationException("Equipment must be approved before enabling public visibility");
        }
        
        equipment.setIsPubliclyVisible(enable);
        equipmentRepository.save(equipment);
        
        log.info("Public visibility for equipment {} set to {} by user: {}", equipmentId, enable, username);
    }

    public void toggleRentalAvailability(Long equipmentId, boolean enable, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Equipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Equipment not found with ID: " + equipmentId));
        
        // Check if equipment belongs to user's company
        if (!equipment.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to modify this equipment");
        }
        
        // Check if daily rate is set when enabling rental
        if (enable && equipment.getDailyRate() == null && equipment.getHourlyRate() == null) {
            throw new ValidationException("Daily or hourly rate must be set before enabling rental availability");
        }
        
        equipment.setIsAvailableForRent(enable);
        equipmentRepository.save(equipment);
        
        log.info("Rental availability for equipment {} set to {} by user: {}", equipmentId, enable, username);
    }

    @Transactional(readOnly = true)
    public Equipment.PublicApprovalStatus getApprovalStatus(Long equipmentId, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Equipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Equipment not found with ID: " + equipmentId));
        
        // Check if equipment belongs to user's company
        if (!equipment.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to access this equipment");
        }
        
        return equipment.getPublicApprovalStatus();
    }

    public void scheduleMaintenance(Long equipmentId, String maintenanceDate, String notes, String username) {
        User user = getUserByUsername(username);
        Company company = getCompanyByUser(user);
        
        Equipment equipment = equipmentRepository.findById(equipmentId)
                .orElseThrow(() -> new ResourceNotFoundException("Equipment not found with ID: " + equipmentId));
        
        // Check if equipment belongs to user's company
        if (!equipment.getCompany().getId().equals(company.getId())) {
            throw new UnauthorizedException("You don't have permission to schedule maintenance for this equipment");
        }
        
        try {
            LocalDateTime maintenanceDateTime = LocalDateTime.parse(maintenanceDate, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            equipment.setNextMaintenanceDate(maintenanceDateTime);
            equipmentRepository.save(equipment);
            
            log.info("Maintenance scheduled for equipment {} on {} by user: {}", equipmentId, maintenanceDate, username);
        } catch (Exception e) {
            throw new ValidationException("Invalid maintenance date format. Use ISO format: yyyy-MM-ddTHH:mm:ss");
        }
    }

    private User getUserByUsername(String username) {
        return userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found: " + username));
    }

    private Company getCompanyByUser(User user) {
        return companyRepository.findByUser(user)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found for user: " + user.getUsername()));
    }
}
