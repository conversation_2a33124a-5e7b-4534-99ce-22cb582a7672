import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../shared/models/load_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../bloc/load_bloc.dart';
import '../../invoices/bloc/invoice_bloc.dart';

class LoadCompletionActions extends StatefulWidget {
  final LoadModel load;
  final VoidCallback onActionCompleted;

  const LoadCompletionActions({
    super.key,
    required this.load,
    required this.onActionCompleted,
  });

  @override
  State<LoadCompletionActions> createState() => _LoadCompletionActionsState();
}

class _LoadCompletionActionsState extends State<LoadCompletionActions> {
  UserModel? _currentUser;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _currentUser = context.read<AuthService>().currentUser;
  }

  bool _isLoadOwner() {
    return _currentUser != null && widget.load.clientId == _currentUser!.id;
  }

  bool _isAssignedCompany() {
    return _currentUser != null &&
        _currentUser!.company != null &&
        int.tryParse(_currentUser!.company!.id) ==
            widget.load.assignedCompanyId;
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.task_alt,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Completion Actions',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final actions = <Widget>[];

    // Actions for assigned company
    if (_isAssignedCompany()) {
      switch (widget.load.status) {
        case LoadStatus.assigned:
          actions.add(_buildActionButton(
            context,
            'Start Transit',
            'Mark load as in transit',
            Icons.local_shipping,
            Colors.blue,
            () => _updateLoadStatus(LoadStatus.inTransit),
          ));
          break;

        case LoadStatus.inTransit:
          actions.add(_buildActionButton(
            context,
            'Mark as Delivered',
            'Confirm load has been delivered',
            Icons.check_circle,
            Colors.green,
            () => _updateLoadStatus(LoadStatus.delivered),
          ));
          break;

        case LoadStatus.delivered:
          actions.add(_buildActionButton(
            context,
            'Generate Invoice',
            'Create invoice for payment',
            Icons.receipt,
            Colors.orange,
            _generateInvoice,
          ));
          break;

        case LoadStatus.posted:
        case LoadStatus.biddingClosed:
        case LoadStatus.completed:
        case LoadStatus.cancelled:
          // No actions for these statuses
          break;
      }
    }

    // Actions for load owner
    if (_isLoadOwner()) {
      switch (widget.load.status) {
        case LoadStatus.delivered:
          actions.add(_buildActionButton(
            context,
            'Confirm Delivery',
            'Confirm that load was received',
            Icons.verified,
            Colors.green,
            () => _confirmDelivery(),
          ));
          actions.add(_buildActionButton(
            context,
            'Process Payment',
            'Process payment for completed load',
            Icons.payment,
            Colors.purple,
            _processPayment,
          ));
          break;
        case LoadStatus.posted:
        case LoadStatus.biddingClosed:
        case LoadStatus.assigned:
        case LoadStatus.inTransit:
        case LoadStatus.completed:
        case LoadStatus.cancelled:
          // No actions for these statuses
          break;
      }
    }

    // Common actions
    if (widget.load.status != LoadStatus.completed &&
        widget.load.status != LoadStatus.cancelled) {
      actions.add(_buildActionButton(
        context,
        'Cancel Load',
        'Cancel this load',
        Icons.cancel,
        Colors.red,
        _cancelLoad,
      ));
    }

    if (actions.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'No actions available at this stage.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: actions
          .map((action) => Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: action,
              ))
          .toList(),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isProcessing ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.all(16),
        ),
        child: Row(
          children: [
            Icon(icon),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (_isProcessing)
              const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _updateLoadStatus(LoadStatus newStatus) {
    setState(() {
      _isProcessing = true;
    });

    context.read<LoadBloc>().add(
          LoadUpdateStatusRequested(
            id: widget.load.id!,
            status: newStatus,
          ),
        );

    // Listen for completion
    _listenForCompletion();
  }

  void _generateInvoice() {
    setState(() {
      _isProcessing = true;
    });

    context.read<InvoiceBloc>().add(
          InvoiceGenerateAutomatic(loadId: widget.load.id!),
        );

    // Listen for completion
    _listenForInvoiceCompletion();
  }

  void _confirmDelivery() {
    _showConfirmationDialog(
      'Confirm Delivery',
      'Are you sure you want to confirm that this load has been delivered?',
      () => _updateLoadStatus(LoadStatus.delivered),
    );
  }

  void _processPayment() {
    _showConfirmationDialog(
      'Process Payment',
      'Are you sure you want to process payment for this load? This action cannot be undone.',
      () => _updateLoadStatus(LoadStatus.completed),
    );
  }

  void _cancelLoad() {
    _showConfirmationDialog(
      'Cancel Load',
      'Are you sure you want to cancel this load? This action cannot be undone.',
      () => _updateLoadStatus(LoadStatus.cancelled),
    );
  }

  void _showConfirmationDialog(
    String title,
    String message,
    VoidCallback onConfirm,
  ) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              onConfirm();
            },
            child: const Text('Confirm'),
          ),
        ],
      ),
    );
  }

  void _listenForCompletion() {
    // This would typically use a BlocListener
    // For now, simulate completion
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
        widget.onActionCompleted();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Action completed successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  void _listenForInvoiceCompletion() {
    // This would typically use a BlocListener
    // For now, simulate completion
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
        widget.onActionCompleted();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Invoice generated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }
}
