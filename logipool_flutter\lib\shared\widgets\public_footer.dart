import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:provider/provider.dart';
import '../services/contact_config_service.dart';
import 'dynamic_contact_helper.dart';

class PublicFooter extends StatelessWidget {
  const PublicFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.05),
        border: Border(
          top: BorderSide(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Company info and contact section
          _buildCompanySection(context),
          const SizedBox(height: 32),
          
          // Links sections
          _buildLinksSection(context),
          const SizedBox(height: 32),
          
          // Quick contact section
          _buildQuickContactSection(context),
          const SizedBox(height: 24),
          
          // Divider
          Divider(
            color: Theme.of(context).primaryColor.withOpacity(0.2),
            thickness: 1,
          ),
          const SizedBox(height: 16),
          
          // Copyright
          _buildCopyrightSection(context),
        ],
      ),
    );
  }

  Widget _buildCompanySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Image.asset(
                'assets/images/logo.png',
                height: 32,
                errorBuilder: (context, error, stackTrace) => Icon(
                  Icons.local_shipping,
                  size: 32,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'LogiPool',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                Text(
                  'Logistics Marketplace',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).primaryColor.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        Text(
          'Your trusted partner for all logistics needs. Connecting shippers with reliable transport providers across Zimbabwe.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.grey.shade600,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildLinksSection(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 768;
    
    if (isDesktop) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(child: _buildServicesLinks(context)),
          Expanded(child: _buildCompanyLinks(context)),
          Expanded(child: _buildSupportLinks(context)),
        ],
      );
    } else {
      return Column(
        children: [
          _buildServicesLinks(context),
          const SizedBox(height: 24),
          _buildCompanyLinks(context),
          const SizedBox(height: 24),
          _buildSupportLinks(context),
        ],
      );
    }
  }

  Widget _buildServicesLinks(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Services',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 12),
        _buildFooterLink(context, 'Vehicle Rental', '/vehicles'),
        _buildFooterLink(context, 'Equipment Rental', '/equipment'),
        _buildFooterLink(context, 'Logistics Services', '/services'),
        _buildFooterLink(context, 'Track Order', '/track'),
      ],
    );
  }

  Widget _buildCompanyLinks(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Company',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 12),
        _buildFooterLink(context, 'About Us', '/about'),
        _buildFooterLink(context, 'Contact', '/contact'),
        _buildFooterLink(context, 'FAQ', '/faq'),
      ],
    );
  }

  Widget _buildSupportLinks(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Legal',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).primaryColor,
          ),
        ),
        const SizedBox(height: 12),
        _buildFooterLink(context, 'Terms & Conditions', '/terms'),
        _buildFooterLink(context, 'Privacy Policy', '/privacy'),
      ],
    );
  }

  Widget _buildQuickContactSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade200,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Get in Touch',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Consumer<ContactConfigService>(
            builder: (context, contactService, child) {
              final config = contactService.contactConfig;
              return Wrap(
                spacing: 12,
                runSpacing: 12,
                children: [
                  _buildQuickContactItem(
                    context,
                    Icons.phone,
                    config.formattedPhoneNumber,
                    () => DynamicContactHelper.launchPhone(context),
                  ),
                  _buildQuickContactItem(
                    context,
                    Icons.chat,
                    'WhatsApp',
                    () => DynamicContactHelper.launchWhatsApp(context),
                  ),
                  _buildQuickContactItem(
                    context,
                    Icons.email,
                    config.email,
                    () => DynamicContactHelper.launchEmail(context),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCopyrightSection(BuildContext context) {
    return Center(
      child: Text(
        '© '+DateTime.now().year.toString() +' LogiPool. All rights reserved.',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Colors.grey.shade600,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildFooterLink(BuildContext context, String title, String route) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => context.go(route),
        child: Text(
          title,
          style: TextStyle(
            color: Colors.grey.shade700,
            fontSize: 14,
            height: 1.5,
          ),
        ),
      ),
    );
  }

  Widget _buildQuickContactItem(
    BuildContext context,
    IconData icon,
    String label,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }


}
