package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.User;

import java.math.BigDecimal;
import java.util.Optional;

@Repository
public interface CompanyRepository extends JpaRepository<Company, Long> {
    
    Optional<Company> findByUser(User user);
    
    Optional<Company> findByRegistrationNumber(String registrationNumber);
    
    Page<Company> findByVerificationStatus(Company.VerificationStatus status, Pageable pageable);
    
    Page<Company> findByType(Company.CompanyType type, Pageable pageable);
    
    @Query("SELECT c FROM Company c WHERE c.name LIKE %:name%")
    Page<Company> findByNameContaining(@Param("name") String name, Pageable pageable);
    
    @Query("SELECT c FROM Company c WHERE c.city = :city")
    Page<Company> findByCity(@Param("city") String city, Pageable pageable);
    
    @Query("SELECT c FROM Company c WHERE c.rating >= :minRating")
    Page<Company> findByRatingGreaterThanEqual(@Param("minRating") BigDecimal minRating, Pageable pageable);
    
    @Query("SELECT c FROM Company c WHERE c.verificationStatus = 'VERIFIED' AND c.type IN ('LOGISTICS_PROVIDER', 'BOTH')")
    Page<Company> findVerifiedLogisticsProviders(Pageable pageable);
    
    Boolean existsByRegistrationNumber(String registrationNumber);

    Boolean existsByTaxNumber(String taxNumber);

    Long countByVerificationStatus(Company.VerificationStatus status);
}
