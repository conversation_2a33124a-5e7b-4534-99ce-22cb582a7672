import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/load_model.dart';

class LoadLifecycleTimeline extends StatelessWidget {
  final LoadModel load;

  const LoadLifecycleTimeline({
    super.key,
    required this.load,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Lifecycle Timeline',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildTimelineEvents(context),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineEvents(BuildContext context) {
    final events = _generateTimelineEvents();
    
    return Column(
      children: events.asMap().entries.map((entry) {
        final index = entry.key;
        final event = entry.value;
        final isLast = index == events.length - 1;
        
        return _buildTimelineEvent(
          context,
          event,
          isLast: isLast,
        );
      }).toList(),
    );
  }

  Widget _buildTimelineEvent(
    BuildContext context,
    TimelineEvent event, {
    required bool isLast,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: event.isCompleted 
                    ? Theme.of(context).primaryColor
                    : Colors.grey.shade300,
                shape: BoxShape.circle,
              ),
              child: Icon(
                event.icon,
                color: event.isCompleted ? Colors.white : Colors.grey[600],
                size: 18,
              ),
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 40,
                color: event.isCompleted 
                    ? Theme.of(context).primaryColor.withOpacity(0.3)
                    : Colors.grey.shade300,
              ),
          ],
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 24),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  event.title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: event.isCompleted 
                        ? Theme.of(context).primaryColor
                        : Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  event.description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                if (event.timestamp != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    DateFormat('MMM dd, yyyy HH:mm').format(event.timestamp!),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[500],
                      fontSize: 11,
                    ),
                  ),
                ],
                if (event.notes != null) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      event.notes!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ],
    );
  }

  List<TimelineEvent> _generateTimelineEvents() {
    final events = <TimelineEvent>[];
    
    // Load Posted
    events.add(TimelineEvent(
      title: 'Load Posted',
      description: 'Load was posted and is available for bidding',
      icon: Icons.post_add,
      timestamp: load.createdAt,
      isCompleted: true,
    ));

    // Load Assigned
    if (load.assignedCompanyId != null) {
      events.add(TimelineEvent(
        title: 'Load Assigned',
        description: 'Load was assigned to a logistics company',
        icon: Icons.assignment,
        timestamp: load.updatedAt, // This should be assignment date
        isCompleted: true,
      ));
    }

    // In Transit
    if (load.status == LoadStatus.inTransit || 
        load.status == LoadStatus.delivered || 
        load.status == LoadStatus.completed) {
      events.add(TimelineEvent(
        title: 'In Transit',
        description: 'Load is being transported to destination',
        icon: Icons.local_shipping,
        timestamp: load.updatedAt, // This should be transit start date
        isCompleted: true,
      ));
    }

    // Delivered
    if (load.status == LoadStatus.delivered || 
        load.status == LoadStatus.completed) {
      events.add(TimelineEvent(
        title: 'Delivered',
        description: 'Load has been delivered to destination',
        icon: Icons.check_circle,
        timestamp: load.updatedAt, // This should be delivery date
        isCompleted: true,
      ));
    }

    // Invoice Generated
    // TODO: Check if invoice exists for this load
    if (load.status == LoadStatus.delivered || 
        load.status == LoadStatus.completed) {
      events.add(TimelineEvent(
        title: 'Invoice Generated',
        description: 'Invoice has been generated for payment',
        icon: Icons.receipt,
        timestamp: null, // This should be invoice generation date
        isCompleted: false, // This should check if invoice exists
      ));
    }

    // Payment Processed
    if (load.status == LoadStatus.completed) {
      events.add(TimelineEvent(
        title: 'Payment Processed',
        description: 'Payment has been completed',
        icon: Icons.payment,
        timestamp: load.updatedAt, // This should be payment date
        isCompleted: true,
      ));
    }

    // Load Completed
    if (load.status == LoadStatus.completed) {
      events.add(TimelineEvent(
        title: 'Load Completed',
        description: 'All processes completed successfully',
        icon: Icons.done_all,
        timestamp: load.updatedAt,
        isCompleted: true,
      ));
    }

    // Add future events based on current status
    if (load.status == LoadStatus.posted) {
      events.add(TimelineEvent(
        title: 'Awaiting Assignment',
        description: 'Waiting for a logistics company to be assigned',
        icon: Icons.hourglass_empty,
        timestamp: null,
        isCompleted: false,
      ));
    } else if (load.status == LoadStatus.assigned) {
      events.add(TimelineEvent(
        title: 'Awaiting Pickup',
        description: 'Waiting for load to be picked up',
        icon: Icons.schedule,
        timestamp: null,
        isCompleted: false,
      ));
    } else if (load.status == LoadStatus.inTransit) {
      events.add(TimelineEvent(
        title: 'Awaiting Delivery',
        description: 'Load is in transit, awaiting delivery',
        icon: Icons.schedule,
        timestamp: null,
        isCompleted: false,
      ));
    } else if (load.status == LoadStatus.delivered) {
      events.add(TimelineEvent(
        title: 'Awaiting Payment',
        description: 'Waiting for payment processing',
        icon: Icons.schedule,
        timestamp: null,
        isCompleted: false,
      ));
    }

    return events;
  }
}

class TimelineEvent {
  final String title;
  final String description;
  final IconData icon;
  final DateTime? timestamp;
  final bool isCompleted;
  final String? notes;

  TimelineEvent({
    required this.title,
    required this.description,
    required this.icon,
    this.timestamp,
    required this.isCompleted,
    this.notes,
  });
}
