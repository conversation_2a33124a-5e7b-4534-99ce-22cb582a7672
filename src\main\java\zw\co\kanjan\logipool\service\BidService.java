package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.bid.BidCreateRequest;
import zw.co.kanjan.logipool.dto.bid.BidResponse;
import zw.co.kanjan.logipool.dto.bid.BidUpdateRequest;
import zw.co.kanjan.logipool.entity.Bid;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Role;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.mapper.BidMapper;
import zw.co.kanjan.logipool.repository.BidRepository;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.UserRepository;
import zw.co.kanjan.logipool.repository.CompanyRepository;

import java.time.LocalDateTime;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class BidService {
    
    private final BidRepository bidRepository;
    private final LoadRepository loadRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final BidMapper bidMapper;
    private final NotificationService notificationService;
    
    public BidResponse createBid(BidCreateRequest request, String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate user has transporter role
        if (!hasTransporterRole(user)) {
            throw new BusinessException("Only transporters can place bids");
        }
        
        Load load = loadRepository.findById(request.getLoadId())
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + request.getLoadId()));
        
        // Validate load is available for bidding
        if (load.getStatus() != Load.LoadStatus.POSTED) {
            throw new BusinessException("Load is not available for bidding");
        }
        
        // Check if bidding period is still open
        if (load.getBiddingClosesAt() != null && LocalDateTime.now().isAfter(load.getBiddingClosesAt())) {
            throw new BusinessException("Bidding period has closed for this load");
        }
        
        // Get user's company
        Company company = companyRepository.findByUser(user)
                .orElseThrow(() -> new BusinessException("User must have a company profile to place bids"));
        
        // Validate company is verified
        if (company.getVerificationStatus() != Company.VerificationStatus.VERIFIED) {
            throw new BusinessException("Company must be verified to place bids");
        }
        
        // Check if company already has a bid for this load
        boolean existingBid = bidRepository.existsByLoadAndCompany(load, company);
        if (existingBid) {
            throw new BusinessException("Company already has a bid for this load");
        }
        
        // Create bid
        Bid bid = bidMapper.toEntity(request);
        bid.setLoad(load);
        bid.setCompany(company);
        bid.setStatus(Bid.BidStatus.PENDING);
        
        Bid savedBid = bidRepository.save(bid);
        
        // Send notification to client
        notificationService.sendBidReceivedNotification(savedBid);
        
        log.info("Bid created successfully: {} for load: {} by company: {}", 
                savedBid.getAmount(), load.getTitle(), company.getName());
        return bidMapper.toResponse(savedBid);
    }
    
    public BidResponse updateBid(Long bidId, BidUpdateRequest request, String username) {
        Bid bid = bidRepository.findById(bidId)
                .orElseThrow(() -> new ResourceNotFoundException("Bid not found with id: " + bidId));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Verify ownership
        if (!bid.getCompany().getUser().getUsername().equals(username)) {
            throw new BusinessException("You can only update your own bids");
        }
        
        // Verify bid can be updated (only pending bids)
        if (bid.getStatus() != Bid.BidStatus.PENDING) {
            throw new BusinessException("Cannot update bid in current status: " + bid.getStatus());
        }
        
        // Check if bidding period is still open
        if (bid.getLoad().getBiddingClosesAt() != null && 
            LocalDateTime.now().isAfter(bid.getLoad().getBiddingClosesAt())) {
            throw new BusinessException("Bidding period has closed for this load");
        }
        
        // Update bid fields
        updateBidFromRequest(bid, request);
        
        Bid savedBid = bidRepository.save(bid);
        log.info("Bid updated successfully: {} by company: {}", savedBid.getId(), bid.getCompany().getName());
        return bidMapper.toResponse(savedBid);
    }
    
    public void withdrawBid(Long bidId, String username) {
        Bid bid = bidRepository.findById(bidId)
                .orElseThrow(() -> new ResourceNotFoundException("Bid not found with id: " + bidId));
        
        // Verify ownership
        if (!bid.getCompany().getUser().getUsername().equals(username)) {
            throw new BusinessException("You can only withdraw your own bids");
        }
        
        // Verify bid can be withdrawn (only pending bids)
        if (bid.getStatus() != Bid.BidStatus.PENDING) {
            throw new BusinessException("Cannot withdraw bid in current status: " + bid.getStatus());
        }
        
        bid.setStatus(Bid.BidStatus.WITHDRAWN);
        bidRepository.save(bid);
        
        log.info("Bid withdrawn successfully: {} by company: {}", bid.getId(), bid.getCompany().getName());
    }
    
    @Transactional(readOnly = true)
    public Page<BidResponse> getBidsForLoad(Long loadId, Pageable pageable) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));
        
        Page<Bid> bids = bidRepository.findByLoadOrderByCreatedAtDesc(load, pageable);
        return bids.map(bidMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<BidResponse> getMyBids(String username, Pageable pageable) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        Company company = companyRepository.findByUser(user)
                .orElseThrow(() -> new BusinessException("User must have a company profile"));
        
        Page<Bid> bids = bidRepository.findByCompanyOrderByCreatedAtDesc(company, pageable);
        return bids.map(bidMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public BidResponse getBidById(Long bidId) {
        Bid bid = bidRepository.findById(bidId)
                .orElseThrow(() -> new ResourceNotFoundException("Bid not found with id: " + bidId));
        return bidMapper.toResponse(bid);
    }
    
    // Helper methods
    private boolean hasTransporterRole(User user) {
        return user.getRoles().stream()
                .anyMatch(role -> role.getName() == Role.RoleName.TRANSPORTER || role.getName() == Role.RoleName.ADMIN);
    }
    
    private void updateBidFromRequest(Bid bid, BidUpdateRequest request) {
        if (request.getAmount() != null) bid.setAmount(request.getAmount());
        if (request.getProposal() != null) bid.setProposal(request.getProposal());
        if (request.getEstimatedPickupTime() != null) bid.setEstimatedPickupTime(request.getEstimatedPickupTime());
        if (request.getEstimatedDeliveryTime() != null) bid.setEstimatedDeliveryTime(request.getEstimatedDeliveryTime());
        if (request.getNotes() != null) bid.setNotes(request.getNotes());
    }
}
