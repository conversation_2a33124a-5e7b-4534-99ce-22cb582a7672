#!/usr/bin/env python3
"""
LogiPool Integration Test Script
Tests the integration between Spring Boot backend and Flutter frontend
"""

import requests
import json
import time
import sys

# Configuration
BASE_URL = "http://localhost:8080/api"
TIMEOUT = 10

def test_backend_health():
    """Test if backend is running and healthy"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Backend health check passed")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend not reachable: {e}")
        return False

def test_auth_endpoints():
    """Test authentication endpoints"""
    print("\n🔐 Testing Authentication Endpoints...")
    
    # Test user registration
    register_data = {
        "email": "<EMAIL>",
        "password": "password123",
        "firstName": "Test",
        "lastName": "User",
        "phoneNumber": "+**********",
        "role": "CLIENT"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/register", 
                               json=register_data, 
                               timeout=TIMEOUT)
        if response.status_code in [200, 201]:
            print("✅ User registration endpoint working")
        else:
            print(f"❌ User registration failed: {response.status_code}")
            print(f"Response: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Registration request failed: {e}")
    
    # Test user login
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", 
                               json=login_data, 
                               timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ User login endpoint working")
            data = response.json()
            if 'token' in data:
                print("✅ JWT token received")
                return data['token']
            else:
                print("❌ No JWT token in response")
        else:
            print(f"❌ User login failed: {response.status_code}")
            print(f"Response: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Login request failed: {e}")
    
    return None

def test_protected_endpoints(token):
    """Test protected endpoints with JWT token"""
    if not token:
        print("❌ No token available for protected endpoint testing")
        return
    
    print("\n🔒 Testing Protected Endpoints...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # Test loads endpoint
    try:
        response = requests.get(f"{BASE_URL}/loads", 
                              headers=headers, 
                              timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Loads endpoint accessible")
        else:
            print(f"❌ Loads endpoint failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Loads request failed: {e}")
    
    # Test companies endpoint
    try:
        response = requests.get(f"{BASE_URL}/companies", 
                              headers=headers, 
                              timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Companies endpoint accessible")
        else:
            print(f"❌ Companies endpoint failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Companies request failed: {e}")

def test_cors_configuration():
    """Test CORS configuration"""
    print("\n🌐 Testing CORS Configuration...")
    
    headers = {
        "Origin": "http://localhost:3000",
        "Access-Control-Request-Method": "POST",
        "Access-Control-Request-Headers": "Content-Type,Authorization"
    }
    
    try:
        response = requests.options(f"{BASE_URL}/auth/login", 
                                  headers=headers, 
                                  timeout=TIMEOUT)
        if response.status_code in [200, 204]:
            print("✅ CORS preflight request successful")
            cors_headers = response.headers
            if 'Access-Control-Allow-Origin' in cors_headers:
                print("✅ CORS headers present")
            else:
                print("❌ CORS headers missing")
        else:
            print(f"❌ CORS preflight failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ CORS request failed: {e}")

def test_api_documentation():
    """Test if API documentation is accessible"""
    print("\n📚 Testing API Documentation...")
    
    try:
        response = requests.get("http://localhost:8080/swagger-ui/index.html", 
                              timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Swagger UI accessible")
        else:
            print(f"❌ Swagger UI not accessible: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Swagger UI request failed: {e}")

def main():
    """Main integration test function"""
    print("🚀 Starting LogiPool Integration Tests...")
    print("=" * 50)
    
    # Test backend health
    if not test_backend_health():
        print("\n❌ Backend is not running. Please start the Spring Boot application first.")
        sys.exit(1)
    
    # Test CORS
    test_cors_configuration()
    
    # Test API documentation
    test_api_documentation()
    
    # Test authentication
    token = test_auth_endpoints()
    
    # Test protected endpoints
    test_protected_endpoints(token)
    
    print("\n" + "=" * 50)
    print("🏁 Integration tests completed!")

if __name__ == "__main__":
    main()
