package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "documents")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Document {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 100)
    private String name;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    private DocumentType type;
    
    @NotBlank
    @Size(max = 500)
    private String filePath;
    
    @Size(max = 100)
    private String fileName;
    
    @Size(max = 100)
    private String fileType;
    
    private Long fileSize;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private DocumentStatus status = DocumentStatus.PENDING;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    private LocalDateTime expiryDate;
    
    @Builder.Default
    private Boolean isRequired = false;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "vehicle_id")
    private Vehicle vehicle;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "equipment_id")
    private Equipment equipment;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "load_id")
    private Load load;
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    private LocalDateTime verifiedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "verified_by")
    private User verifiedBy;
    
    public enum DocumentType {
        // Company Documents
        COMPANY_REGISTRATION,
        TAX_CLEARANCE,
        BUSINESS_LICENSE,
        INSURANCE_CERTIFICATE,
        
        // Vehicle Documents
        VEHICLE_REGISTRATION,
        FITNESS_CERTIFICATE,
        ROAD_PERMIT,
        ZINARA_PERMIT,
        VEHICLE_INSURANCE,
        VEHICLE_PHOTOS,
        
        // Load Documents
        PROOF_OF_DELIVERY,
        INVOICE,
        CONTRACT,
        WAYBILL,
        CUSTOMS_DECLARATION,
        
        // Other
        PROFILE_PHOTO,
        OTHER
    }
    
    public enum DocumentStatus {
        PENDING, VERIFIED, REJECTED, EXPIRED
    }
}
