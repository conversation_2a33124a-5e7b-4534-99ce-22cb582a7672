import 'package:json_annotation/json_annotation.dart';
import 'company_model.dart';

part 'vehicle_model.g.dart';

@JsonSerializable()
class VehicleModel {
  final int? id;
  final String? registrationNumber;
  final String? make;
  final String? model;
  final int? year;
  final VehicleType? type;
  final double? maxWeight;
  final double? maxVolume;
  final String? weightUnit;
  final String? volumeUnit;
  final VehicleStatus? status;
  final String? description;
  final double? dailyRate;
  final String? currency;
  final List<String>? features;
  final String? imageUrl;
  final bool? isAvailableForRent;
  final bool? isFeatured;
  final bool? isPubliclyVisible;
  final PublicApprovalStatus? publicApprovalStatus;
  final CompanyInfo? company;
  final bool? hasInsurance;
  final bool? hasFitnessCertificate;
  final bool? hasPermits;
  final DateTime? insuranceExpiryDate;
  final DateTime? fitnessExpiryDate;
  final DateTime? permitExpiryDate;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  VehicleModel({
    this.id,
    this.registrationNumber,
    this.make,
    this.model,
    this.year,
    this.type,
    this.maxWeight,
    this.maxVolume,
    this.weightUnit,
    this.volumeUnit,
    this.status,
    this.description,
    this.dailyRate,
    this.currency,
    this.features,
    this.imageUrl,
    this.isAvailableForRent,
    this.isFeatured,
    this.isPubliclyVisible,
    this.publicApprovalStatus,
    this.company,
    this.hasInsurance,
    this.hasFitnessCertificate,
    this.hasPermits,
    this.insuranceExpiryDate,
    this.fitnessExpiryDate,
    this.permitExpiryDate,
    this.createdAt,
    this.updatedAt,
  });

  factory VehicleModel.fromJson(Map<String, dynamic> json) =>
      _$VehicleModelFromJson(json);

  Map<String, dynamic> toJson() => _$VehicleModelToJson(this);
}

@JsonSerializable()
class CompanyInfo {
  final int? id;
  final String? name;
  final double? rating;
  final String? verificationStatus;
  final String? location;
  final String? phoneNumber;
  final String? email;

  CompanyInfo({
    this.id,
    this.name,
    this.rating,
    this.verificationStatus,
    this.location,
    this.phoneNumber,
    this.email,
  });

  factory CompanyInfo.fromJson(Map<String, dynamic> json) =>
      _$CompanyInfoFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyInfoToJson(this);
}

enum VehicleType {
  @JsonValue('TRUCK')
  truck,
  @JsonValue('TRAILER')
  trailer,
  @JsonValue('FLATBED')
  flatbed,
  @JsonValue('REFRIGERATED')
  refrigerated,
  @JsonValue('TANKER')
  tanker,
  @JsonValue('CONTAINER')
  container,
  @JsonValue('VAN')
  van,
  @JsonValue('PICKUP')
  pickup,
}

enum VehicleStatus {
  @JsonValue('AVAILABLE')
  available,
  @JsonValue('IN_USE')
  inUse,
  @JsonValue('MAINTENANCE')
  maintenance,
  @JsonValue('OUT_OF_SERVICE')
  outOfService,
}

enum PublicApprovalStatus {
  @JsonValue('PENDING')
  pending,
  @JsonValue('APPROVED')
  approved,
  @JsonValue('REJECTED')
  rejected,
  @JsonValue('SUSPENDED')
  suspended,
}
