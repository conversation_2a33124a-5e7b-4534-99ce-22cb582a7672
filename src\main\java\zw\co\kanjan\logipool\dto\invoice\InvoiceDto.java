package zw.co.kanjan.logipool.dto.invoice;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Data;
import zw.co.kanjan.logipool.entity.Invoice;
import zw.co.kanjan.logipool.entity.InvoiceItem;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class InvoiceDto {
    
    @Data
    public static class InvoiceResponse {
        private Long id;
        private String invoiceNumber;
        private BigDecimal subtotal;
        private BigDecimal taxAmount;
        private BigDecimal discountAmount;
        private BigDecimal totalAmount;
        private BigDecimal commissionAmount;
        private BigDecimal netAmount;
        private Invoice.InvoiceStatus status;
        private Invoice.InvoiceType type;
        private String description;
        private String notes;
        private LocalDateTime dueDate;
        private Long loadId;
        private String loadTitle;
        private Long bidId;
        private Long clientId;
        private String clientName;
        private Long transporterId;
        private String transporterName;
        private Long companyId;
        private String companyName;
        private List<InvoiceItemResponse> items;
        private LocalDateTime sentAt;
        private LocalDateTime paidAt;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }

    @Data
    public static class InvoicePreviewResponse {
        private String invoiceNumber;
        private BigDecimal subtotal;
        private BigDecimal taxAmount;
        private BigDecimal taxRate;
        private BigDecimal discountAmount;
        private BigDecimal totalAmount;
        private BigDecimal commissionAmount;
        private BigDecimal commissionRate;
        private BigDecimal netAmount;
        private Invoice.InvoiceType type;
        private String description;
        private LocalDateTime dueDate;
        private Long loadId;
        private String loadTitle;
        private Long bidId;
        private BigDecimal bidAmount;
        private Long clientId;
        private String clientName;
        private Long transporterId;
        private String transporterName;
        private Long companyId;
        private String companyName;
        private List<InvoiceItemResponse> items;
        private boolean hasExistingInvoiceDocument;
        private String commissionNote;
    }
    
    @Data
    public static class InvoiceCreateRequest {
        @NotNull
        private Invoice.InvoiceType type;
        
        private String description;
        private String notes;
        
        @NotNull
        private LocalDateTime dueDate;
        
        @NotNull
        private Long loadId;
        
        private Long bidId;
        
        @NotNull
        private Long clientId;
        
        private Long transporterId;
        
        private Long companyId;
        
        @NotNull
        private List<InvoiceItemRequest> items;
    }
    
    @Data
    public static class InvoiceUpdateRequest {
        private Invoice.InvoiceStatus status;
        private String description;
        private String notes;
        private LocalDateTime dueDate;
        private List<InvoiceItemRequest> items;
    }

    @Data
    public static class InvoiceModificationRequest {
        private String description;
        private String notes;
        private LocalDateTime dueDate;
        private BigDecimal discountAmount;
        private List<InvoiceItemRequest> items;
    }
    
    @Data
    public static class InvoiceItemResponse {
        private Long id;
        private String description;
        private BigDecimal quantity;
        private BigDecimal unitPrice;
        private BigDecimal totalPrice;
        private String unit;
        private InvoiceItem.ItemType type;
    }
    
    @Data
    public static class InvoiceItemRequest {
        @NotBlank
        @Size(max = 200)
        private String description;
        
        @NotNull
        @Positive
        private BigDecimal quantity;
        
        @NotNull
        @Positive
        private BigDecimal unitPrice;
        
        private String unit;
        
        @NotNull
        private InvoiceItem.ItemType type;
    }
    
    @Data
    public static class InvoiceSummary {
        private BigDecimal totalAmount;
        private BigDecimal totalCommission;
        private BigDecimal totalTax;
        private long totalCount;
        private long draftCount;
        private long sentCount;
        private long paidCount;
        private long overdueCount;
    }
    
    @Data
    public static class InvoicePaymentRequest {
        @NotNull
        private Long invoiceId;
        
        @NotNull
        private BigDecimal amount;
        
        private String paymentMethod;
        private String notes;
    }

    @Data
    public static class UploadedInvoiceDocumentsResponse {
        private boolean hasUploadedInvoices;
        private int totalDocuments;
        private List<UploadedInvoiceDocumentInfo> documents;
        private String message;
        private String recommendation;
    }

    @Data
    public static class UploadedInvoiceDocumentInfo {
        private Long documentId;
        private String name;
        private String status;
        private LocalDateTime uploadedAt;
        private String uploadedBy;
        private Long fileSize;
        private String downloadUrl;
        private boolean isLatest;
        private boolean isValid;
    }
}
