import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/notification_model.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../../../core/utils/app_router.dart';
import '../bloc/notification_bloc.dart';
import '../widgets/notification_card.dart';
import '../widgets/notification_filter_sheet.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  NotificationType? _selectedType;
  NotificationPriority? _selectedPriority;
  bool? _selectedReadStatus;
  bool _showUnreadOnly = false;
  String _searchQuery = '';
  bool _isSelectionMode = false;
  final Set<String> _selectedNotifications = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadNotifications();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadNotifications() {
    context.read<NotificationBloc>().add(const LoadNotifications());
    context.read<NotificationBloc>().add(const LoadUnreadCount());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _isSelectionMode ? _buildSelectionAppBar() : _buildNormalAppBar(),
      body: Column(
        children: [
          _buildTabBar(),
          _buildSearchBar(),
          _buildFilterChips(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildNotificationsList(null), // All notifications
                _buildNotificationsList(false), // Unread only
                _buildNotificationsList(true), // Read only
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  PreferredSizeWidget _buildNormalAppBar() {
    return CustomAppBar(
      title: 'Notifications',
      actions: [
        IconButton(
          icon: const Icon(Icons.filter_list),
          onPressed: _showFilterSheet,
        ),
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: _showSearchDialog,
        ),
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'mark_all_read':
                context
                    .read<NotificationBloc>()
                    .add(const MarkAllNotificationsAsRead());
                break;
              case 'clear_read':
                context
                    .read<NotificationBloc>()
                    .add(const ClearExpiredNotifications());
                break;
              case 'settings':
                context.go('/notifications/settings');
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'mark_all_read',
              child: Text('Mark All as Read'),
            ),
            const PopupMenuItem(
              value: 'clear_read',
              child: Text('Clear Read'),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: Text('Settings'),
            ),
          ],
        ),
      ],
    );
  }

  PreferredSizeWidget _buildSelectionAppBar() {
    return AppBar(
      leading: IconButton(
        icon: const Icon(Icons.close),
        onPressed: _exitSelectionMode,
      ),
      title: Text('${_selectedNotifications.length} selected'),
      actions: [
        IconButton(
          icon: const Icon(Icons.mark_email_read),
          onPressed: _markSelectedAsRead,
        ),
        IconButton(
          icon: const Icon(Icons.delete),
          onPressed: _deleteSelected,
        ),
      ],
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Theme.of(context).scaffoldBackgroundColor,
      child: TabBar(
        controller: _tabController,
        tabs: [
          const Tab(text: 'All'),
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Unread'),
                const SizedBox(width: 4),
                BlocBuilder<NotificationBloc, NotificationState>(
                  builder: (context, state) {
                    if (state is UnreadCountLoaded && state.count > 0) {
                      return Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          state.count.toString(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
          const Tab(text: 'Read'),
        ],
        onTap: (index) {
          setState(() {
            _showUnreadOnly = index == 1;
          });
        },
      ),
    );
  }

  Widget _buildSearchBar() {
    if (_searchQuery.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search notifications...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: IconButton(
            icon: const Icon(Icons.clear),
            onPressed: () {
              setState(() {
                _searchQuery = '';
              });
            },
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('All', null),
          const SizedBox(width: 8),
          _buildFilterChip('Load Updates', NotificationType.loadStatusUpdate),
          const SizedBox(width: 8),
          _buildFilterChip('Bid Notifications', NotificationType.bidReceived),
          const SizedBox(width: 8),
          _buildFilterChip('Payment Updates', NotificationType.paymentReceived),
          const SizedBox(width: 8),
          _buildFilterChip(
              'System Alerts', NotificationType.systemAnnouncement),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, NotificationType? type) {
    return FilterChip(
      label: Text(label),
      selected: _selectedType == type,
      onSelected: (selected) {
        setState(() {
          _selectedType = selected ? type : null;
        });
      },
      backgroundColor: Colors.grey[200],
      selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
    );
  }

  Widget _buildNotificationsList(bool? readStatus) {
    return BlocConsumer<NotificationBloc, NotificationState>(
      listener: (context, state) {
        if (state is NotificationError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
        } else if (state is NotificationOperationSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(state.message)),
          );
          _loadNotifications();
        }
      },
      builder: (context, state) {
        if (state is NotificationLoading) {
          return const LoadingWidget();
        }

        if (state is NotificationError) {
          return CustomErrorWidget(
            message: state.message,
            onRetry: () => _loadNotifications(),
          );
        }

        if (state is NotificationLoaded) {
          final notifications =
              _filterNotifications(state.notifications, readStatus);

          if (notifications.isEmpty) {
            return EmptyStateWidget(
              icon: const Icon(Icons.notifications_none),
              title: 'No Notifications',
              message: _getEmptyMessage(readStatus),
            );
          }

          return RefreshIndicator(
            onRefresh: () async => _loadNotifications(),
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: notifications.length,
              itemBuilder: (context, index) {
                final notification = notifications[index];
                final isSelected =
                    _selectedNotifications.contains(notification.id);

                return NotificationCard(
                  notification: notification,
                  isSelected: isSelected,
                  isSelectionMode: _isSelectionMode,
                  onTap: () {
                    if (_isSelectionMode) {
                      _toggleSelection(notification.id!.toString());
                    } else {
                      _handleNotificationTap(notification);
                    }
                  },
                  onLongPress: () {
                    if (!_isSelectionMode) {
                      _enterSelectionMode(notification.id!.toString());
                    }
                  },
                  onMarkAsRead: () {
                    context.read<NotificationBloc>().add(
                          MarkNotificationAsRead(notification.id!),
                        );
                  },
                  onDelete: () {
                    _showDeleteConfirmation(() {
                      context.read<NotificationBloc>().add(
                            DeleteNotification(notification.id!),
                          );
                    });
                  },
                );
              },
            ),
          );
        }

        return const EmptyStateWidget(
          icon: Icon(Icons.notifications_none),
          title: 'No Notifications',
          message: 'You have no notifications at the moment.',
        );
      },
    );
  }

  List<NotificationModel> _filterNotifications(
      List<NotificationModel> notifications, bool? readStatus) {
    var filtered = notifications;

    if (readStatus != null) {
      filtered = filtered.where((n) => n.read == readStatus).toList();
    }

    if (_selectedType != null) {
      filtered = filtered.where((n) => n.type == _selectedType).toList();
    }

    if (_searchQuery.isNotEmpty) {
      filtered = filtered
          .where((n) =>
              n.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
              n.message.toLowerCase().contains(_searchQuery.toLowerCase()))
          .toList();
    }

    return filtered;
  }

  String _getEmptyMessage(bool? readStatus) {
    if (readStatus == false) {
      return 'No unread notifications';
    } else if (readStatus == true) {
      return 'No read notifications';
    }
    return 'You have no notifications at the moment.';
  }

  Widget? _buildFloatingActionButton() {
    if (_isSelectionMode) return null;

    return FloatingActionButton(
      onPressed: () {
        context.go('/notifications/settings');
      },
      child: const Icon(Icons.settings),
    );
  }

  void _enterSelectionMode(String notificationId) {
    setState(() {
      _isSelectionMode = true;
      _selectedNotifications.add(notificationId);
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedNotifications.clear();
    });
  }

  void _toggleSelection(String notificationId) {
    setState(() {
      if (_selectedNotifications.contains(notificationId)) {
        _selectedNotifications.remove(notificationId);
        if (_selectedNotifications.isEmpty) {
          _isSelectionMode = false;
        }
      } else {
        _selectedNotifications.add(notificationId);
      }
    });
  }

  void _markSelectedAsRead() {
    for (final id in _selectedNotifications) {
      context
          .read<NotificationBloc>()
          .add(MarkNotificationAsRead(int.parse(id)));
    }
    _exitSelectionMode();
  }

  void _deleteSelected() {
    _showDeleteConfirmation(() {
      for (final id in _selectedNotifications) {
        context.read<NotificationBloc>().add(DeleteNotification(int.parse(id)));
      }
      _exitSelectionMode();
    });
  }

  void _handleNotificationTap(NotificationModel notification) {
    // Navigate to notification details screen
    // The details screen will handle marking as read
    context.push('/notifications/${notification.id}');
  }

  void _showNotificationDetails(NotificationModel notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Text(notification.message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showDeleteConfirmation(VoidCallback onConfirm) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Notifications'),
        content: Text(
          _selectedNotifications.length == 1
              ? 'Are you sure you want to delete this notification?'
              : 'Are you sure you want to delete ${_selectedNotifications.length} notifications?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onConfirm();
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => NotificationFilterSheet(
        selectedType: _selectedType,
        selectedPriority: _selectedPriority,
        selectedReadStatus: _selectedReadStatus,
        onApplyFilter: (type, priority, readStatus) {
          setState(() {
            _selectedType = type;
            _selectedPriority = priority;
            _selectedReadStatus = readStatus;
          });
          Navigator.pop(context);
        },
        onClearFilter: () {
          setState(() {
            _selectedType = null;
            _selectedPriority = null;
            _selectedReadStatus = null;
          });
          Navigator.pop(context);
        },
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Notifications'),
        content: TextField(
          decoration: const InputDecoration(
            hintText: 'Enter search terms...',
          ),
          onChanged: (value) {
            setState(() {
              _searchQuery = value;
            });
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }
}
