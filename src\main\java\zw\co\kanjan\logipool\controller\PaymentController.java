package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.payment.PaymentDto;
import zw.co.kanjan.logipool.entity.Payment;
import zw.co.kanjan.logipool.service.PaymentService;

@Slf4j
@RestController
@RequestMapping("/api/payments")
@RequiredArgsConstructor
@Tag(name = "Payment Management", description = "APIs for managing payments and transactions")
@SecurityRequirement(name = "bearerAuth")
public class PaymentController {
    
    private final PaymentService paymentService;
    
    @PostMapping
    @Operation(summary = "Create a new payment", description = "Create a payment for a completed load")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Payment created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid payment data"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Load or user not found")
    })
    @PreAuthorize("hasRole('CLIENT') or hasRole('ADMIN')")
    public ResponseEntity<PaymentDto.PaymentResponse> createPayment(
            @Valid @RequestBody PaymentDto.PaymentCreateRequest request,
            Authentication authentication) {
        
        log.info("Creating payment for load: {} by user: {}", request.getLoadId(), authentication.getName());
        PaymentDto.PaymentResponse response = paymentService.createPayment(request, authentication.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }
    
    @PostMapping("/{paymentId}/process")
    @Operation(summary = "Process a payment", description = "Process a pending payment using specified payment method")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment processed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid payment processing data"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Payment not found")
    })
    @PreAuthorize("hasRole('CLIENT') or hasRole('ADMIN')")
    public ResponseEntity<PaymentDto.PaymentStatusResponse> processPayment(
            @PathVariable Long paymentId,
            @Valid @RequestBody PaymentDto.PaymentProcessRequest request,
            Authentication authentication) {
        
        log.info("Processing payment: {} by user: {}", paymentId, authentication.getName());
        request.setPaymentId(paymentId);
        PaymentDto.PaymentStatusResponse response = paymentService.processPayment(request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/{paymentId}")
    @Operation(summary = "Get payment by ID", description = "Retrieve payment details by payment ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment found"),
        @ApiResponse(responseCode = "404", description = "Payment not found")
    })
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    public ResponseEntity<PaymentDto.PaymentResponse> getPaymentById(
            @Parameter(description = "Payment ID") @PathVariable Long paymentId) {
        
        PaymentDto.PaymentResponse response = paymentService.getPaymentById(paymentId);
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/my-payments")
    @Operation(summary = "Get my payments", description = "Get payments made by the current user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully")
    })
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    public ResponseEntity<Page<PaymentDto.PaymentResponse>> getMyPayments(
            @PageableDefault(size = 20) Pageable pageable,
            Authentication authentication) {
        
        Page<PaymentDto.PaymentResponse> payments = paymentService.getMyPayments(authentication.getName(), pageable);
        return ResponseEntity.ok(payments);
    }
    
    @GetMapping("/payments-to-me")
    @Operation(summary = "Get payments to me", description = "Get payments received by the current user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully")
    })
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    public ResponseEntity<Page<PaymentDto.PaymentResponse>> getPaymentsToMe(
            @PageableDefault(size = 20) Pageable pageable,
            Authentication authentication) {
        
        Page<PaymentDto.PaymentResponse> payments = paymentService.getPaymentsToMe(authentication.getName(), pageable);
        return ResponseEntity.ok(payments);
    }
    
    @GetMapping
    @Operation(summary = "Get all payments", description = "Get all payments (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Page<PaymentDto.PaymentResponse>> getAllPayments(
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<PaymentDto.PaymentResponse> payments = paymentService.getAllPayments(pageable);
        return ResponseEntity.ok(payments);
    }
    
    @GetMapping("/status/{status}")
    @Operation(summary = "Get payments by status", description = "Get payments filtered by status (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payments retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Page<PaymentDto.PaymentResponse>> getPaymentsByStatus(
            @Parameter(description = "Payment status") @PathVariable Payment.PaymentStatus status,
            @PageableDefault(size = 20) Pageable pageable) {
        
        Page<PaymentDto.PaymentResponse> payments = paymentService.getPaymentsByStatus(status, pageable);
        return ResponseEntity.ok(payments);
    }
    
    @PutMapping("/{paymentId}")
    @Operation(summary = "Update payment", description = "Update payment details")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid payment data"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Payment not found")
    })
    @PreAuthorize("hasRole('CLIENT') or hasRole('ADMIN')")
    public ResponseEntity<PaymentDto.PaymentResponse> updatePayment(
            @PathVariable Long paymentId,
            @Valid @RequestBody PaymentDto.PaymentUpdateRequest request,
            Authentication authentication) {
        
        log.info("Updating payment: {} by user: {}", paymentId, authentication.getName());
        PaymentDto.PaymentResponse response = paymentService.updatePayment(paymentId, request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @DeleteMapping("/{paymentId}")
    @Operation(summary = "Cancel payment", description = "Cancel a pending payment")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment cancelled successfully"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions"),
        @ApiResponse(responseCode = "404", description = "Payment not found"),
        @ApiResponse(responseCode = "409", description = "Payment cannot be cancelled")
    })
    @PreAuthorize("hasRole('CLIENT') or hasRole('ADMIN')")
    public ResponseEntity<PaymentDto.PaymentResponse> cancelPayment(
            @PathVariable Long paymentId,
            Authentication authentication) {
        
        log.info("Cancelling payment: {} by user: {}", paymentId, authentication.getName());
        PaymentDto.PaymentResponse response = paymentService.cancelPayment(paymentId, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/summary")
    @Operation(summary = "Get payment summary", description = "Get payment statistics and summary (admin only)")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Payment summary retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Insufficient permissions")
    })
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<PaymentDto.PaymentSummary> getPaymentSummary() {

        PaymentDto.PaymentSummary summary = paymentService.getPaymentSummary();
        return ResponseEntity.ok(summary);
    }

    @GetMapping("/user/summary")
    @Operation(summary = "Get user payment summary", description = "Get payment statistics and summary for the current user")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User payment summary retrieved successfully"),
        @ApiResponse(responseCode = "404", description = "User not found")
    })
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    public ResponseEntity<PaymentDto.PaymentSummary> getUserPaymentSummary(Authentication authentication) {

        PaymentDto.PaymentSummary summary = paymentService.getUserPaymentSummary(authentication.getName());
        return ResponseEntity.ok(summary);
    }
}
