package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Entity
@Table(name = "invoice_items")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceItem {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 200)
    private String description;
    
    @NotNull
    @Positive
    private BigDecimal quantity;
    
    @NotNull
    @Positive
    private BigDecimal unitPrice;
    
    @NotNull
    @Positive
    private BigDecimal totalPrice;
    
    @Size(max = 20)
    private String unit;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    private ItemType type;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "invoice_id")
    private Invoice invoice;
    
    public enum ItemType {
        TRANSPORTATION_FEE, COMMISSION_FEE, FUEL_SURCHARGE, INSURANCE_FEE, 
        HANDLING_FEE, STORAGE_FEE, PENALTY_FEE, DISCOUNT, OTHER
    }
}
