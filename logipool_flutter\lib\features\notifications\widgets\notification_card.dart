import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../shared/models/notification_model.dart';

class NotificationCard extends StatelessWidget {
  final NotificationModel notification;
  final bool isSelected;
  final bool isSelectionMode;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onMarkAsRead;
  final VoidCallback? onDelete;

  const NotificationCard({
    super.key,
    required this.notification,
    this.isSelected = false,
    this.isSelectionMode = false,
    this.onTap,
    this.onLongPress,
    this.onMarkAsRead,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      elevation: notification.read ? 1 : 3,
      color: isSelected 
          ? Theme.of(context).primaryColor.withOpacity(0.1)
          : notification.read 
              ? null 
              : Theme.of(context).colorScheme.surface,
      child: InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (isSelectionMode)
                Padding(
                  padding: const EdgeInsets.only(right: 12),
                  child: Checkbox(
                    value: isSelected,
                    onChanged: (_) => onTap?.call(),
                  ),
                ),
              _buildNotificationIcon(),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeader(context),
                    const SizedBox(height: 4),
                    _buildTitle(context),
                    const SizedBox(height: 8),
                    _buildMessage(context),
                    const SizedBox(height: 12),
                    _buildFooter(context),
                  ],
                ),
              ),
              if (!isSelectionMode) _buildActionMenu(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIcon() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: _getTypeColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(
        _getTypeIcon(),
        color: _getTypeColor(),
        size: 20,
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: _getTypeColor().withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            notification.type.displayName,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: _getTypeColor(),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 8),
        _buildPriorityIndicator(context),
        const Spacer(),
        if (!notification.read)
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
      ],
    );
  }

  Widget _buildPriorityIndicator(BuildContext context) {
    if (notification.priority == NotificationPriority.low) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: _getPriorityColor().withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getPriorityIcon(),
            size: 12,
            color: _getPriorityColor(),
          ),
          const SizedBox(width: 2),
          Text(
            notification.priority.displayName,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: _getPriorityColor(),
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitle(BuildContext context) {
    return Text(
      notification.title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: notification.read ? FontWeight.normal : FontWeight.w600,
        color: notification.read 
            ? Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.7)
            : null,
      ),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildMessage(BuildContext context) {
    return Text(
      notification.message,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: notification.read 
            ? Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.6)
            : Theme.of(context).textTheme.bodyMedium?.color?.withOpacity(0.8),
      ),
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.access_time,
          size: 14,
          color: Theme.of(context).textTheme.bodySmall?.color,
        ),
        const SizedBox(width: 4),
        Text(
          _formatTimestamp(notification.timestamp),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.7),
          ),
        ),
        if (notification.expiresAt != null) ...[
          const SizedBox(width: 16),
          Icon(
            Icons.schedule,
            size: 14,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(width: 4),
          Text(
            'Expires ${_formatTimestamp(notification.expiresAt!)}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        ],
        if (notification.referenceId != null) ...[
          const Spacer(),
          Icon(
            Icons.arrow_forward_ios,
            size: 12,
            color: Theme.of(context).textTheme.bodySmall?.color?.withOpacity(0.5),
          ),
        ],
      ],
    );
  }

  Widget _buildActionMenu(BuildContext context) {
    return PopupMenuButton<String>(
      onSelected: (value) {
        switch (value) {
          case 'mark_read':
            onMarkAsRead?.call();
            break;
          case 'delete':
            onDelete?.call();
            break;
        }
      },
      itemBuilder: (context) => [
        if (!notification.read)
          const PopupMenuItem(
            value: 'mark_read',
            child: Row(
              children: [
                Icon(Icons.mark_email_read),
                SizedBox(width: 8),
                Text('Mark as Read'),
              ],
            ),
          ),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete),
              SizedBox(width: 8),
              Text('Delete'),
            ],
          ),
        ),
      ],
      child: const Icon(Icons.more_vert),
    );
  }

  IconData _getTypeIcon() {
    switch (notification.type) {
      case NotificationType.loadPosted:
        return Icons.local_shipping;
      case NotificationType.bidReceived:
        return Icons.gavel;
      case NotificationType.bidAccepted:
        return Icons.check_circle;
      case NotificationType.bidRejected:
        return Icons.cancel;
      case NotificationType.loadStatusUpdate:
        return Icons.update;
      case NotificationType.paymentReceived:
        return Icons.payment;
      case NotificationType.documentRequired:
        return Icons.description;
      case NotificationType.systemAnnouncement:
        return Icons.announcement;
      case NotificationType.accountVerification:
        return Icons.verified_user;
      case NotificationType.securityAlert:
        return Icons.security;
      case NotificationType.trackingUpdate:
        return Icons.location_on;
    }
  }

  Color _getTypeColor() {
    switch (notification.type) {
      case NotificationType.loadPosted:
        return Colors.blue;
      case NotificationType.bidReceived:
        return Colors.orange;
      case NotificationType.bidAccepted:
        return Colors.green;
      case NotificationType.bidRejected:
        return Colors.red;
      case NotificationType.loadStatusUpdate:
        return Colors.purple;
      case NotificationType.paymentReceived:
        return Colors.teal;
      case NotificationType.documentRequired:
        return Colors.amber;
      case NotificationType.systemAnnouncement:
        return Colors.indigo;
      case NotificationType.accountVerification:
        return Colors.cyan;
      case NotificationType.securityAlert:
        return Colors.red;
      case NotificationType.trackingUpdate:
        return Colors.green;
    }
  }

  IconData _getPriorityIcon() {
    switch (notification.priority) {
      case NotificationPriority.low:
        return Icons.keyboard_arrow_down;
      case NotificationPriority.medium:
        return Icons.remove;
      case NotificationPriority.high:
        return Icons.keyboard_arrow_up;
      case NotificationPriority.urgent:
        return Icons.priority_high;
    }
  }

  Color _getPriorityColor() {
    switch (notification.priority) {
      case NotificationPriority.low:
        return Colors.grey;
      case NotificationPriority.medium:
        return Colors.blue;
      case NotificationPriority.high:
        return Colors.orange;
      case NotificationPriority.urgent:
        return Colors.red;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return DateFormat('MMM d, y').format(timestamp);
    }
  }
}
