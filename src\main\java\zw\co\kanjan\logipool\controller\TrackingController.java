package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.tracking.TrackingDto;
import zw.co.kanjan.logipool.service.TrackingService;

import java.util.List;
import java.util.Optional;

@Slf4j
@RestController
@RequestMapping("/api/tracking")
@RequiredArgsConstructor
@Tag(name = "Tracking", description = "Real-time tracking and GPS location APIs")
public class TrackingController {

    private final TrackingService trackingService;

    @PostMapping("/update")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Update tracking status", 
               description = "Update tracking status and location for a load")
    public ResponseEntity<TrackingDto.TrackingResponse> updateTracking(
            @Valid @RequestBody TrackingDto.TrackingUpdateRequest request,
            Authentication authentication) {
        
        log.info("Updating tracking for load: {} by user: {}", request.getLoadId(), authentication.getName());
        TrackingDto.TrackingResponse response = trackingService.updateTracking(request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @PostMapping("/location")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Update GPS location", 
               description = "Update real-time GPS location for a load")
    public ResponseEntity<TrackingDto.TrackingResponse> updateLocation(
            @Valid @RequestBody TrackingDto.LocationUpdateRequest request,
            Authentication authentication) {
        
        log.info("Updating location for load: {} by user: {}", request.getLoadId(), authentication.getName());
        TrackingDto.TrackingResponse response = trackingService.updateLocation(request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/load/{loadId}/history")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Get load tracking history", 
               description = "Get complete tracking history for a specific load")
    public ResponseEntity<TrackingDto.LoadTrackingHistory> getLoadTrackingHistory(
            @Parameter(description = "Load ID") @PathVariable Long loadId) {
        
        TrackingDto.LoadTrackingHistory history = trackingService.getLoadTrackingHistory(loadId);
        return ResponseEntity.ok(history);
    }

    @GetMapping("/load/{loadId}")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Get load tracking with pagination", 
               description = "Get paginated tracking history for a specific load")
    public ResponseEntity<Page<TrackingDto.TrackingResponse>> getTrackingHistory(
            @Parameter(description = "Load ID") @PathVariable Long loadId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "timestamp") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<TrackingDto.TrackingResponse> trackingPage = trackingService.getTrackingHistory(loadId, pageable);
        return ResponseEntity.ok(trackingPage);
    }

    @GetMapping("/load/{loadId}/latest")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Get latest tracking", 
               description = "Get the most recent tracking entry for a load")
    public ResponseEntity<TrackingDto.TrackingResponse> getLatestTracking(
            @Parameter(description = "Load ID") @PathVariable Long loadId) {
        
        Optional<TrackingDto.TrackingResponse> latest = trackingService.getLatestTracking(loadId);
        return latest.map(ResponseEntity::ok)
                    .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/active-locations")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Get active loads locations", 
               description = "Get real-time locations of all loads currently in transit")
    public ResponseEntity<List<TrackingDto.RealTimeLocation>> getActiveLoadsLocation() {
        
        List<TrackingDto.RealTimeLocation> locations = trackingService.getActiveLoadsLocation();
        return ResponseEntity.ok(locations);
    }

    @GetMapping("/statistics")
    @PreAuthorize("hasRole('ADMIN') or hasRole('CLIENT') or hasRole('TRANSPORTER')")
    @Operation(summary = "Get tracking statistics", 
               description = "Get overall tracking statistics and metrics")
    public ResponseEntity<TrackingDto.TrackingStatistics> getTrackingStatistics() {
        
        TrackingDto.TrackingStatistics statistics = trackingService.getTrackingStatistics();
        return ResponseEntity.ok(statistics);
    }

    @PostMapping("/delivery/confirm")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Confirm delivery", 
               description = "Confirm delivery completion with signature and details")
    public ResponseEntity<TrackingDto.TrackingResponse> confirmDelivery(
            @Valid @RequestBody TrackingDto.DeliveryConfirmationRequest request,
            Authentication authentication) {
        
        log.info("Confirming delivery for load: {} by user: {}", request.getLoadId(), authentication.getName());
        TrackingDto.TrackingResponse response = trackingService.confirmDelivery(request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/delayed")
    @PreAuthorize("hasRole('ADMIN') or hasRole('CLIENT')")
    @Operation(summary = "Get delayed loads", 
               description = "Get all loads currently marked as delayed")
    public ResponseEntity<List<TrackingDto.TrackingResponse>> getDelayedLoads() {
        
        List<TrackingDto.TrackingResponse> delayedLoads = trackingService.getDelayedLoads();
        return ResponseEntity.ok(delayedLoads);
    }

    @GetMapping("/issues")
    @PreAuthorize("hasRole('ADMIN') or hasRole('CLIENT')")
    @Operation(summary = "Get loads with issues", 
               description = "Get all loads with reported issues")
    public ResponseEntity<List<TrackingDto.TrackingResponse>> getLoadsWithIssues() {
        
        List<TrackingDto.TrackingResponse> loadsWithIssues = trackingService.getLoadsWithIssues();
        return ResponseEntity.ok(loadsWithIssues);
    }

    @GetMapping("/company/{companyId}")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get company loads tracking",
               description = "Get tracking status for all loads assigned to a company")
    public ResponseEntity<List<TrackingDto.TrackingResponse>> getCompanyLoadsTracking(
            @Parameter(description = "Company ID") @PathVariable Long companyId) {

        List<TrackingDto.TrackingResponse> companyTracking = trackingService.getCompanyLoadsTracking(companyId);
        return ResponseEntity.ok(companyTracking);
    }

    // Live Tracking Endpoints

    @PostMapping("/live/{loadId}/start")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Start live tracking",
               description = "Start live location tracking for a load (only one device can track per load)")
    public ResponseEntity<TrackingDto.LiveTrackingResponse> startLiveTracking(
            @Parameter(description = "Load ID") @PathVariable Long loadId,
            Authentication authentication) {

        log.info("Starting live tracking for load: {} by user: {}", loadId, authentication.getName());
        TrackingDto.LiveTrackingResponse response = trackingService.startLiveTracking(loadId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @PostMapping("/live/{loadId}/stop")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Stop live tracking",
               description = "Stop live location tracking for a load")
    public ResponseEntity<TrackingDto.LiveTrackingResponse> stopLiveTracking(
            @Parameter(description = "Load ID") @PathVariable Long loadId,
            Authentication authentication) {

        log.info("Stopping live tracking for load: {} by user: {}", loadId, authentication.getName());
        TrackingDto.LiveTrackingResponse response = trackingService.stopLiveTracking(loadId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/live/{loadId}/status")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Get live tracking status",
               description = "Get current live tracking status for a load")
    public ResponseEntity<TrackingDto.LiveTrackingStatus> getLiveTrackingStatus(
            @Parameter(description = "Load ID") @PathVariable Long loadId) {

        TrackingDto.LiveTrackingStatus status = trackingService.getLiveTrackingStatus(loadId);
        return ResponseEntity.ok(status);
    }

    @GetMapping("/live/active")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Get active live tracking sessions",
               description = "Get all currently active live tracking sessions")
    public ResponseEntity<List<TrackingDto.LiveTrackingStatus>> getActiveLiveTrackingSessions() {

        List<TrackingDto.LiveTrackingStatus> activeSessions = trackingService.getActiveLiveTrackingSessions();
        return ResponseEntity.ok(activeSessions);
    }
}
