import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/models/location_model.dart';
import '../../../shared/models/paginated_response.dart';
import '../../../shared/services/location_tracking_service.dart';

// Events
abstract class LocationTrackingEvent extends Equatable {
  const LocationTrackingEvent();

  @override
  List<Object?> get props => [];
}

class UpdateLocation extends LocationTrackingEvent {
  final LocationUpdateRequest request;

  const UpdateLocation(this.request);

  @override
  List<Object?> get props => [request];
}

class LoadDriverLocation extends LocationTrackingEvent {
  final int driverId;

  const LoadDriverLocation(this.driverId);

  @override
  List<Object?> get props => [driverId];
}

class LoadDriverLocationHistory extends LocationTrackingEvent {
  final int driverId;
  final int page;
  final int size;

  const LoadDriverLocationHistory(this.driverId,
      {this.page = 0, this.size = 20});

  @override
  List<Object?> get props => [driverId, page, size];
}

class LoadLoadTrackingHistory extends LocationTrackingEvent {
  final int loadId;
  final int page;
  final int size;

  const LoadLoadTrackingHistory(this.loadId, {this.page = 0, this.size = 20});

  @override
  List<Object?> get props => [loadId, page, size];
}

class LoadCompanyTrackingSummary extends LocationTrackingEvent {
  final int companyId;

  const LoadCompanyTrackingSummary(this.companyId);

  @override
  List<Object?> get props => [companyId];
}

class RequestLocationPermission extends LocationTrackingEvent {
  final LocationPermissionRequest request;

  const RequestLocationPermission(this.request);

  @override
  List<Object?> get props => [request];
}

class GrantLocationPermission extends LocationTrackingEvent {
  final int permissionId;

  const GrantLocationPermission(this.permissionId);

  @override
  List<Object?> get props => [permissionId];
}

class RevokeLocationPermission extends LocationTrackingEvent {
  final int permissionId;
  final String? reason;

  const RevokeLocationPermission(this.permissionId, {this.reason});

  @override
  List<Object?> get props => [permissionId, reason];
}

// States
abstract class LocationTrackingState extends Equatable {
  const LocationTrackingState();

  @override
  List<Object?> get props => [];
}

class LocationTrackingInitial extends LocationTrackingState {}

class LocationTrackingLoading extends LocationTrackingState {}

class LocationUpdated extends LocationTrackingState {
  final LocationResponse location;

  const LocationUpdated(this.location);

  @override
  List<Object?> get props => [location];
}

class DriverLocationLoaded extends LocationTrackingState {
  final LocationResponse location;

  const DriverLocationLoaded(this.location);

  @override
  List<Object?> get props => [location];
}

class LocationHistoryLoaded extends LocationTrackingState {
  final PaginatedResponse<LocationResponse> history;

  const LocationHistoryLoaded(this.history);

  @override
  List<Object?> get props => [history];
}

class TrackingSummaryLoaded extends LocationTrackingState {
  final TrackingSummary summary;

  const TrackingSummaryLoaded(this.summary);

  @override
  List<Object?> get props => [summary];
}

class LocationPermissionOperationSuccess extends LocationTrackingState {
  final String message;
  final LocationPermissionResponse? permission;

  const LocationPermissionOperationSuccess(this.message, {this.permission});

  @override
  List<Object?> get props => [message, permission];
}

class LocationTrackingError extends LocationTrackingState {
  final String message;

  const LocationTrackingError(this.message);

  @override
  List<Object?> get props => [message];
}

// BLoC
class LocationTrackingBloc
    extends Bloc<LocationTrackingEvent, LocationTrackingState> {
  final LocationTrackingService _locationTrackingService;

  LocationTrackingBloc(this._locationTrackingService)
      : super(LocationTrackingInitial()) {
    on<UpdateLocation>(_onUpdateLocation);
    on<LoadDriverLocation>(_onLoadDriverLocation);
    on<LoadDriverLocationHistory>(_onLoadDriverLocationHistory);
    on<LoadLoadTrackingHistory>(_onLoadLoadTrackingHistory);
    on<LoadCompanyTrackingSummary>(_onLoadCompanyTrackingSummary);
    on<RequestLocationPermission>(_onRequestLocationPermission);
    on<GrantLocationPermission>(_onGrantLocationPermission);
    on<RevokeLocationPermission>(_onRevokeLocationPermission);
  }

  Future<void> _onUpdateLocation(
    UpdateLocation event,
    Emitter<LocationTrackingState> emit,
  ) async {
    emit(LocationTrackingLoading());
    try {
      final location =
          await _locationTrackingService.updateLocation(event.request);
      emit(LocationUpdated(location));
    } catch (e) {
      emit(LocationTrackingError(e.toString()));
    }
  }

  Future<void> _onLoadDriverLocation(
    LoadDriverLocation event,
    Emitter<LocationTrackingState> emit,
  ) async {
    emit(LocationTrackingLoading());
    try {
      final location =
          await _locationTrackingService.getDriverLocation(event.driverId);
      emit(DriverLocationLoaded(location));
    } catch (e) {
      emit(LocationTrackingError(e.toString()));
    }
  }

  Future<void> _onLoadDriverLocationHistory(
    LoadDriverLocationHistory event,
    Emitter<LocationTrackingState> emit,
  ) async {
    emit(LocationTrackingLoading());
    try {
      final history = await _locationTrackingService.getDriverLocationHistory(
        event.driverId,
        page: event.page,
        size: event.size,
      );
      emit(LocationHistoryLoaded(history));
    } catch (e) {
      emit(LocationTrackingError(e.toString()));
    }
  }

  Future<void> _onLoadLoadTrackingHistory(
    LoadLoadTrackingHistory event,
    Emitter<LocationTrackingState> emit,
  ) async {
    emit(LocationTrackingLoading());
    try {
      final history = await _locationTrackingService.getLoadTrackingHistory(
        event.loadId,
        page: event.page,
        size: event.size,
      );
      emit(LocationHistoryLoaded(history));
    } catch (e) {
      emit(LocationTrackingError(e.toString()));
    }
  }

  Future<void> _onLoadCompanyTrackingSummary(
    LoadCompanyTrackingSummary event,
    Emitter<LocationTrackingState> emit,
  ) async {
    emit(LocationTrackingLoading());
    try {
      final summary = await _locationTrackingService
          .getCompanyTrackingSummary(event.companyId);
      emit(TrackingSummaryLoaded(summary));
    } catch (e) {
      emit(LocationTrackingError(e.toString()));
    }
  }

  Future<void> _onRequestLocationPermission(
    RequestLocationPermission event,
    Emitter<LocationTrackingState> emit,
  ) async {
    emit(LocationTrackingLoading());
    try {
      final permission = await _locationTrackingService
          .requestDriverLocationPermission(event.request);
      emit(LocationPermissionOperationSuccess(
        'Location permission requested successfully',
        permission: permission,
      ));
    } catch (e) {
      emit(LocationTrackingError(e.toString()));
    }
  }

  Future<void> _onGrantLocationPermission(
    GrantLocationPermission event,
    Emitter<LocationTrackingState> emit,
  ) async {
    emit(LocationTrackingLoading());
    try {
      final permission = await _locationTrackingService
          .grantLocationPermission(event.permissionId);
      emit(LocationPermissionOperationSuccess(
        'Location permission granted successfully',
        permission: permission,
      ));
    } catch (e) {
      emit(LocationTrackingError(e.toString()));
    }
  }

  Future<void> _onRevokeLocationPermission(
    RevokeLocationPermission event,
    Emitter<LocationTrackingState> emit,
  ) async {
    emit(LocationTrackingLoading());
    try {
      await _locationTrackingService.revokeLocationPermission(
        event.permissionId,
        reason: event.reason,
      );
      emit(const LocationPermissionOperationSuccess(
          'Location permission revoked successfully'));
    } catch (e) {
      emit(LocationTrackingError(e.toString()));
    }
  }
}
