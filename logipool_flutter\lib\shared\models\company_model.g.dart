// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'company_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CompanyModel _$CompanyModelFromJson(Map<String, dynamic> json) => CompanyModel(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      registrationNumber: json['registrationNumber'] as String,
      taxNumber: json['taxNumber'] as String?,
      type: $enumDecode(_$CompanyTypeEnumMap, json['type']),
      address: json['address'] as String,
      city: json['city'] as String,
      country: json['country'] as String,
      phoneNumber: json['phoneNumber'] as String?,
      email: json['email'] as String?,
      website: json['website'] as String?,
      verificationStatus: $enumDecodeNullable(
              _$VerificationStatusEnumMap, json['verificationStatus']) ??
          VerificationStatus.pending,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalJobs: (json['totalJobs'] as num?)?.toInt() ?? 0,
      completedJobs: (json['completedJobs'] as num?)?.toInt() ?? 0,
      userId: (json['userId'] as num?)?.toInt(),
      userName: json['userName'] as String?,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$CompanyModelToJson(CompanyModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'registrationNumber': instance.registrationNumber,
      'taxNumber': instance.taxNumber,
      'type': _$CompanyTypeEnumMap[instance.type]!,
      'address': instance.address,
      'city': instance.city,
      'country': instance.country,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
      'website': instance.website,
      'verificationStatus':
          _$VerificationStatusEnumMap[instance.verificationStatus]!,
      'rating': instance.rating,
      'totalJobs': instance.totalJobs,
      'completedJobs': instance.completedJobs,
      'userId': instance.userId,
      'userName': instance.userName,
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$CompanyTypeEnumMap = {
  CompanyType.logisticsProvider: 'LOGISTICS_PROVIDER',
  CompanyType.client: 'CLIENT',
  CompanyType.both: 'BOTH',
};

const _$VerificationStatusEnumMap = {
  VerificationStatus.pending: 'PENDING',
  VerificationStatus.verified: 'VERIFIED',
  VerificationStatus.rejected: 'REJECTED',
  VerificationStatus.suspended: 'SUSPENDED',
};

CompanyCreateRequest _$CompanyCreateRequestFromJson(
        Map<String, dynamic> json) =>
    CompanyCreateRequest(
      name: json['name'] as String,
      description: json['description'] as String?,
      registrationNumber: json['registrationNumber'] as String,
      taxNumber: json['taxNumber'] as String?,
      type: $enumDecode(_$CompanyTypeEnumMap, json['type']),
      address: json['address'] as String,
      city: json['city'] as String,
      country: json['country'] as String,
      phoneNumber: json['phoneNumber'] as String?,
      email: json['email'] as String?,
      website: json['website'] as String?,
    );

Map<String, dynamic> _$CompanyCreateRequestToJson(
        CompanyCreateRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'registrationNumber': instance.registrationNumber,
      'taxNumber': instance.taxNumber,
      'type': _$CompanyTypeEnumMap[instance.type]!,
      'address': instance.address,
      'city': instance.city,
      'country': instance.country,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
      'website': instance.website,
    };

CompanyUpdateRequest _$CompanyUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    CompanyUpdateRequest(
      name: json['name'] as String?,
      description: json['description'] as String?,
      registrationNumber: json['registrationNumber'] as String?,
      taxNumber: json['taxNumber'] as String?,
      type: $enumDecodeNullable(_$CompanyTypeEnumMap, json['type']),
      address: json['address'] as String?,
      city: json['city'] as String?,
      country: json['country'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      email: json['email'] as String?,
      website: json['website'] as String?,
    );

Map<String, dynamic> _$CompanyUpdateRequestToJson(
        CompanyUpdateRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
      'registrationNumber': instance.registrationNumber,
      'taxNumber': instance.taxNumber,
      'type': _$CompanyTypeEnumMap[instance.type],
      'address': instance.address,
      'city': instance.city,
      'country': instance.country,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
      'website': instance.website,
    };
