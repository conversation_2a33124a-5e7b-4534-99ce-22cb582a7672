import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

// Conditional import for web-specific functionality
import 'web_download_stub.dart' if (dart.library.html) 'web_download_web.dart'
    as web_download;

/// Service for downloading files to device storage
class FileDownloadService {
  static const String _downloadsFolder = 'LogiPool Downloads';

  /// Download a file from URL and save it to device storage
  static Future<bool> downloadFile({
    required String url,
    required String fileName,
    required Dio dio,
    void Function(int received, int total)? onProgress,
  }) async {
    try {
      if (kIsWeb) {
        // On web, use the browser's download functionality
        return await _downloadFileWeb(url, fileName, dio, onProgress);
      } else {
        // On mobile/desktop, save to downloads directory
        return await _downloadFileMobile(url, fileName, dio, onProgress);
      }
    } on Exception catch (e) {
      if (kDebugMode) {
        print('Error downloading file: $e');
      }
      return false;
    }
  }

  /// Save PDF bytes directly to device storage
  static Future<String?> savePdfBytes({
    required Uint8List pdfBytes,
    required String fileName,
  }) async {
    try {
      if (kIsWeb) {
        // On web, trigger download using blob URL
        _triggerWebDownload(pdfBytes, fileName);
        return 'Downloaded to browser downloads';
      } else {
        // On mobile/desktop, save to downloads directory
        return await _saveBytesToFile(pdfBytes, fileName);
      }
    } on Exception catch (e) {
      if (kDebugMode) {
        print('Error saving PDF bytes: $e');
      }
      return null;
    }
  }

  /// Download file on web platform
  static Future<bool> _downloadFileWeb(
    String url,
    String fileName,
    Dio dio,
    void Function(int received, int total)? onProgress,
  ) async {
    try {
      // For web platform, use direct link approach with authentication
      // This is more reliable than downloading bytes and creating blob URLs

      if (kDebugMode) {
        print('Attempting web download from URL: $url');
        print('Expected filename: $fileName');
      }

      // For now, always use the blob method as it's more reliable
      // The direct link method requires backend changes for token-based auth
      if (kDebugMode) {
        print('Using blob method for web download');
      }
      return await _downloadFileWebBlob(url, fileName, dio, onProgress);
    } on Exception catch (e) {
      if (kDebugMode) {
        print('Error in web download: $e');
      }
      // Fallback to the blob method
      return _downloadFileWebBlob(url, fileName, dio, onProgress);
    }
  }

  /// Fallback web download method using blob URLs
  static Future<bool> _downloadFileWebBlob(
    String url,
    String fileName,
    Dio dio,
    void Function(int received, int total)? onProgress,
  ) async {
    try {
      // Download the file as bytes and create a blob URL
      final response = await dio.get<List<int>>(
        url,
        options: Options(
          responseType: ResponseType.bytes,
          followRedirects: false,
          validateStatus: (status) => status! < 500,
        ),
        onReceiveProgress: onProgress,
      );

      if (response.statusCode == 200 && response.data != null) {
        // Try to get filename from Content-Disposition header
        var finalFileName = fileName;
        final contentDisposition =
            response.headers.value('content-disposition');

        if (kDebugMode) {
          print('Web download response headers:');
          response.headers.forEach((name, values) {
            print('  $name: ${values.join(', ')}');
          });
          print('Content-Disposition: $contentDisposition');
        }

        if (contentDisposition != null) {
          final filenameMatch =
              RegExp(r'filename="([^"]*)"').firstMatch(contentDisposition);
          if (filenameMatch != null) {
            final headerFileName = filenameMatch.group(1) ?? '';
            if (headerFileName.isNotEmpty) {
              finalFileName = headerFileName;
              if (kDebugMode) {
                print('Extracted filename from quoted header: $finalFileName');
              }
            }
          } else {
            // Try without quotes
            final filenameMatchNoQuotes =
                RegExp(r'filename=([^;]*)').firstMatch(contentDisposition);
            if (filenameMatchNoQuotes != null) {
              final headerFileName =
                  filenameMatchNoQuotes.group(1)?.trim() ?? '';
              if (headerFileName.isNotEmpty) {
                finalFileName = headerFileName;
                if (kDebugMode) {
                  print(
                      'Extracted filename from unquoted header: $finalFileName');
                }
              }
            }
          }
        } else {
          if (kDebugMode) {
            print(
                'No Content-Disposition header found, using provided filename: $finalFileName');
          }
        }

        // Create blob URL and trigger download
        final bytes = response.data!;
        _triggerWebDownload(bytes, finalFileName);
        return true;
      } else {
        if (kDebugMode) {
          print('Download failed with status: ${response.statusCode}');
        }
        return false;
      }
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Dio error downloading file on web: ${e.message}');
      }
      return false;
    } on Exception catch (e) {
      if (kDebugMode) {
        print('Error downloading file on web: $e');
      }
      return false;
    }
  }

  /// Trigger download in web browser
  static void _triggerWebDownload(List<int> bytes, String fileName) {
    if (kIsWeb) {
      try {
        // Use the web-specific download implementation
        web_download.downloadFileWithBlob(bytes, fileName);

        if (kDebugMode) {
          print('Web download triggered for file: $fileName');
        }
      } on Exception catch (e) {
        if (kDebugMode) {
          print('Error triggering web download: $e');
        }
        // Fallback to data URL approach
        _fallbackDataUrlDownload(bytes, fileName);
      }
    }
  }

  /// Fallback download method using data URL
  static void _fallbackDataUrlDownload(List<int> bytes, String fileName) {
    try {
      final base64 = _bytesToBase64(bytes);

      // Determine MIME type from file extension
      var mimeType = 'application/octet-stream';
      if (fileName.toLowerCase().endsWith('.pdf')) {
        mimeType = 'application/pdf';
      } else if (fileName.toLowerCase().endsWith('.png')) {
        mimeType = 'image/png';
      } else if (fileName.toLowerCase().endsWith('.txt')) {
        mimeType = 'text/plain';
      }

      final dataUrl = 'data:$mimeType;base64,$base64';

      launchUrl(
        Uri.parse(dataUrl),
        mode: LaunchMode.externalApplication,
      ).then((_) {
        if (kDebugMode) {
          print('Fallback data URL download triggered for: $fileName');
        }
      }).catchError((Object error) {
        if (kDebugMode) {
          print('Error in fallback download: $error');
        }
      });
    } on Exception catch (e) {
      if (kDebugMode) {
        print('Error in fallback data URL download: $e');
      }
    }
  }

  /// Save bytes to file on mobile/desktop platform
  static Future<String?> _saveBytesToFile(
      Uint8List bytes, String fileName) async {
    try {
      // Request storage permission on Android
      if (Platform.isAndroid) {
        final permission = await Permission.storage.request();
        if (!permission.isGranted) {
          if (kDebugMode) {
            print('Storage permission denied');
          }
          return null;
        }
      }

      // Get the appropriate downloads directory
      Directory? downloadsDir;

      if (Platform.isAndroid) {
        // Try to use the public Downloads directory
        downloadsDir =
            Directory('/storage/emulated/0/Download/$_downloadsFolder');
        if (!await downloadsDir.exists()) {
          await downloadsDir.create(recursive: true);
        }
      } else if (Platform.isIOS) {
        // On iOS, save to app documents directory
        downloadsDir = await getApplicationDocumentsDirectory();
        final logiPoolDir = Directory('${downloadsDir.path}/$_downloadsFolder');
        if (!await logiPoolDir.exists()) {
          await logiPoolDir.create(recursive: true);
        }
        downloadsDir = logiPoolDir;
      } else {
        // Desktop platforms
        downloadsDir = await getDownloadsDirectory();
        if (downloadsDir != null) {
          final logiPoolDir =
              Directory('${downloadsDir.path}/$_downloadsFolder');
          if (!await logiPoolDir.exists()) {
            await logiPoolDir.create(recursive: true);
          }
          downloadsDir = logiPoolDir;
        }
      }

      if (downloadsDir == null) {
        // Fallback to documents directory
        downloadsDir = await getApplicationDocumentsDirectory();
        final logiPoolDir = Directory('${downloadsDir.path}/$_downloadsFolder');
        if (!await logiPoolDir.exists()) {
          await logiPoolDir.create(recursive: true);
        }
        downloadsDir = logiPoolDir;
      }

      // Ensure unique filename
      String finalFileName = fileName;
      String baseName = fileName.split('.').first;
      String extension =
          fileName.contains('.') ? '.${fileName.split('.').last}' : '';
      int counter = 1;

      while (await File('${downloadsDir.path}/$finalFileName').exists()) {
        finalFileName = '${baseName}_$counter$extension';
        counter++;
      }

      final filePath = '${downloadsDir.path}/$finalFileName';
      final file = File(filePath);

      // Write bytes to file
      await file.writeAsBytes(bytes);

      if (kDebugMode) {
        print('File saved successfully to: $filePath');
      }

      return filePath;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving bytes to file: $e');
      }
      return null;
    }
  }

  /// Convert bytes to base64 string
  static String _bytesToBase64(List<int> bytes) {
    const chars =
        'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
    String result = '';
    int i = 0;

    while (i < bytes.length) {
      int a = bytes[i++];
      int b = i < bytes.length ? bytes[i++] : 0;
      int c = i < bytes.length ? bytes[i++] : 0;

      int bitmap = (a << 16) | (b << 8) | c;

      result += chars[(bitmap >> 18) & 63];
      result += chars[(bitmap >> 12) & 63];
      result += i - 2 < bytes.length ? chars[(bitmap >> 6) & 63] : '=';
      result += i - 1 < bytes.length ? chars[bitmap & 63] : '=';
    }

    return result;
  }

  /// Download file on mobile/desktop platform
  static Future<bool> _downloadFileMobile(
    String url,
    String fileName,
    Dio dio,
    void Function(int received, int total)? onProgress,
  ) async {
    try {
      // Request storage permission on Android
      if (Platform.isAndroid) {
        final permission = await Permission.storage.request();
        if (!permission.isGranted) {
          if (kDebugMode) {
            print('Storage permission denied');
          }
          return false;
        }
      }

      // Get the appropriate downloads directory
      Directory? downloadsDir;

      if (Platform.isAndroid) {
        // Try to use the public Downloads directory
        downloadsDir =
            Directory('/storage/emulated/0/Download/$_downloadsFolder');
        if (!await downloadsDir.exists()) {
          await downloadsDir.create(recursive: true);
        }
      } else if (Platform.isIOS) {
        // On iOS, save to app documents directory
        downloadsDir = await getApplicationDocumentsDirectory();
        final logiPoolDir = Directory('${downloadsDir.path}/$_downloadsFolder');
        if (!await logiPoolDir.exists()) {
          await logiPoolDir.create(recursive: true);
        }
        downloadsDir = logiPoolDir;
      } else {
        // Desktop platforms
        downloadsDir = await getDownloadsDirectory();
        if (downloadsDir != null) {
          final logiPoolDir =
              Directory('${downloadsDir.path}/$_downloadsFolder');
          if (!await logiPoolDir.exists()) {
            await logiPoolDir.create(recursive: true);
          }
          downloadsDir = logiPoolDir;
        }
      }

      if (downloadsDir == null) {
        // Fallback to documents directory
        downloadsDir = await getApplicationDocumentsDirectory();
        final logiPoolDir = Directory('${downloadsDir.path}/$_downloadsFolder');
        if (!await logiPoolDir.exists()) {
          await logiPoolDir.create(recursive: true);
        }
        downloadsDir = logiPoolDir;
      }

      // First, make a HEAD request to get the Content-Disposition header
      String finalFileName = fileName;
      try {
        final headResponse = await dio.head<dynamic>(url);
        final contentDisposition =
            headResponse.headers.value('content-disposition');
        if (contentDisposition != null) {
          final filenameMatch =
              RegExp(r'filename="([^"]*)"').firstMatch(contentDisposition);
          if (filenameMatch != null) {
            final headerFileName = filenameMatch.group(1) ?? '';
            if (headerFileName.isNotEmpty) {
              finalFileName = headerFileName;
            }
          } else {
            // Try without quotes
            final filenameMatchNoQuotes =
                RegExp(r'filename=([^;]*)').firstMatch(contentDisposition);
            if (filenameMatchNoQuotes != null) {
              final headerFileName =
                  filenameMatchNoQuotes.group(1)?.trim() ?? '';
              if (headerFileName.isNotEmpty) {
                finalFileName = headerFileName;
              }
            }
          }
        }
      } on Exception catch (e) {
        // If HEAD request fails, continue with the provided filename
        if (kDebugMode) {
          print('HEAD request failed, using provided filename: $e');
        }
      }

      // Ensure unique filename
      final baseName = finalFileName.contains('.')
          ? finalFileName.substring(0, finalFileName.lastIndexOf('.'))
          : finalFileName;
      final extension = finalFileName.contains('.')
          ? finalFileName.substring(finalFileName.lastIndexOf('.'))
          : '';
      var counter = 1;
      var uniqueFileName = finalFileName;

      while (await File('${downloadsDir.path}/$uniqueFileName').exists()) {
        uniqueFileName = '${baseName}_$counter$extension';
        counter++;
      }

      final filePath = '${downloadsDir.path}/$uniqueFileName';

      await dio.download(
        url,
        filePath,
        onReceiveProgress: onProgress,
      );

      if (kDebugMode) {
        print('File downloaded successfully to: $filePath');
      }

      return true;
    } on Exception catch (e) {
      if (kDebugMode) {
        print('Error downloading file on mobile: $e');
      }
      return false;
    }
  }

  /// Get the downloads directory path for display purposes
  static Future<String?> getDownloadsPath() async {
    try {
      if (kIsWeb) {
        return 'Browser Downloads';
      }

      Directory? downloadsDir;

      if (Platform.isAndroid) {
        downloadsDir =
            Directory('/storage/emulated/0/Download/$_downloadsFolder');
      } else if (Platform.isIOS) {
        final appDir = await getApplicationDocumentsDirectory();
        downloadsDir = Directory('${appDir.path}/$_downloadsFolder');
      } else {
        final systemDownloads = await getDownloadsDirectory();
        if (systemDownloads != null) {
          downloadsDir = Directory('${systemDownloads.path}/$_downloadsFolder');
        }
      }

      return downloadsDir?.path;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting downloads path: $e');
      }
      return null;
    }
  }
}
