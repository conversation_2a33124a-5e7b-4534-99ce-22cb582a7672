package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.entity.PasswordResetToken;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.repository.PasswordResetTokenRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class PasswordResetService {
    
    private final UserRepository userRepository;
    private final PasswordResetTokenRepository passwordResetTokenRepository;
    private final NotificationService notificationService;
    private final EmailTemplateService emailTemplateService;
    private final PasswordEncoder passwordEncoder;
    
    @Value("${app.base-url:http://localhost:8080}")
    private String baseUrl;
    
    @Value("${app.password-reset.token-expiry-hours:24}")
    private int tokenExpiryHours;
    
    @Value("${app.password-reset.max-requests-per-hour:3}")
    private int maxRequestsPerHour;
    
    private static final SecureRandom secureRandom = new SecureRandom();
    
    @Transactional
    public void initiatePasswordReset(String email) {
        log.info("Initiating password reset for email: {}", email);
        
        User user = userRepository.findByEmail(email)
                .orElseThrow(() -> new ResourceNotFoundException("User not found with email: " + email));
        
        // Check rate limiting
        LocalDateTime oneHourAgo = LocalDateTime.now().minusHours(1);
        long recentRequests = passwordResetTokenRepository.countByUserAndCreatedAtAfter(user, oneHourAgo);
        
        if (recentRequests >= maxRequestsPerHour) {
            throw new BusinessException("Too many password reset requests. Please try again later.");
        }
        
        // Invalidate any existing tokens for this user
        passwordResetTokenRepository.deleteAllByUser(user);
        
        // Generate new token
        String token = generateSecureToken();
        LocalDateTime expiryDate = LocalDateTime.now().plusHours(tokenExpiryHours);
        
        PasswordResetToken resetToken = PasswordResetToken.builder()
                .token(token)
                .user(user)
                .expiryDate(expiryDate)
                .build();
        
        passwordResetTokenRepository.save(resetToken);
        
        // Send password reset email
        sendPasswordResetEmail(user, token);
        
        log.info("Password reset token generated and email sent for user: {}", user.getUsername());
    }
    
    @Transactional
    public void resetPassword(String token, String newPassword, String confirmPassword) {
        log.info("Attempting to reset password with token");
        
        if (!newPassword.equals(confirmPassword)) {
            throw new BusinessException("Passwords do not match");
        }
        
        PasswordResetToken resetToken = passwordResetTokenRepository.findByTokenAndUsedFalse(token)
                .orElseThrow(() -> new BusinessException("Invalid or expired reset token"));
        
        if (!resetToken.isValid()) {
            throw new BusinessException("Reset token has expired or been used");
        }
        
        User user = resetToken.getUser();
        
        // Update password
        user.setPassword(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        
        // Mark token as used
        resetToken.setUsed(true);
        resetToken.setUsedAt(LocalDateTime.now());
        passwordResetTokenRepository.save(resetToken);
        
        // Send confirmation notification
        notificationService.sendNotification(
                user,
                "Password Reset Successful",
                "Your password has been successfully reset. If you didn't make this change, please contact support immediately.",
                "PASSWORD_RESET_SUCCESS"
        );
        
        log.info("Password successfully reset for user: {}", user.getUsername());
    }
    
    @Transactional
    public void cleanupExpiredTokens() {
        log.info("Cleaning up expired password reset tokens");
        passwordResetTokenRepository.deleteExpiredTokens(LocalDateTime.now());
    }
    
    private String generateSecureToken() {
        byte[] randomBytes = new byte[32];
        secureRandom.nextBytes(randomBytes);
        return Base64.getUrlEncoder().withoutPadding().encodeToString(randomBytes);
    }
    
    private void sendPasswordResetEmail(User user, String token) {
        try {
            String resetUrl = baseUrl + "/reset-password?token=" + token;
            String userName = user.getFirstName() + " " + user.getLastName();
            
            Map<String, Object> variables = emailTemplateService.createPasswordResetVariables(
                    userName,
                    resetUrl,
                    String.valueOf(tokenExpiryHours),
                    baseUrl + "/support",
                    baseUrl + "/security"
            );
            
            String htmlContent = emailTemplateService.processTemplate("password-reset", variables);
            notificationService.sendHtmlEmail(
                    user.getEmail(),
                    "Reset Your LogiPool Password",
                    htmlContent
            );
            
        } catch (Exception e) {
            log.error("Failed to send password reset email to: {}", user.getEmail(), e);
            throw new BusinessException("Failed to send password reset email");
        }
    }
}
