-- Create tracking_audit_log table for security monitoring and audit trails
CREATE TABLE tracking_audit_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    tracking_number VARCHAR(20),
    contact_info VARCHAR(100),
    ip_address VARCHAR(45),
    user_agent TEXT,
    details VARCHAR(500),
    severity ENUM('INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO',
    timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verification_token VARCHAR(100),
    request_successful BOOLEAN,
    
    -- Indexes for performance
    INDEX idx_audit_event_type (event_type),
    INDEX idx_audit_tracking_number (tracking_number),
    INDEX idx_audit_ip_address (ip_address),
    INDEX idx_audit_timestamp (timestamp),
    INDEX idx_audit_severity (severity),
    INDEX idx_audit_verification_token (verification_token),
    INDEX idx_audit_request_successful (request_successful),
    
    -- Composite indexes for common queries
    INDEX idx_audit_event_timestamp (event_type, timestamp),
    INDEX idx_audit_tracking_timestamp (tracking_number, timestamp),
    INDEX idx_audit_ip_timestamp (ip_address, timestamp),
    INDEX idx_audit_severity_timestamp (severity, timestamp),
    
    -- Foreign key constraint to tracking_verification table (optional)
    CONSTRAINT fk_audit_verification_token 
        FOREIGN KEY (verification_token) 
        REFERENCES tracking_verification(token) 
        ON DELETE SET NULL 
        ON UPDATE CASCADE
);

-- Add comments for documentation
ALTER TABLE tracking_audit_log 
COMMENT = 'Audit log for tracking verification security events and monitoring';

ALTER TABLE tracking_audit_log 
MODIFY COLUMN event_type VARCHAR(50) NOT NULL 
COMMENT 'Type of security event (e.g., VERIFICATION_REQUESTED, RATE_LIMIT_EXCEEDED)';

ALTER TABLE tracking_audit_log 
MODIFY COLUMN tracking_number VARCHAR(20) 
COMMENT 'Load tracking number involved in the event';

ALTER TABLE tracking_audit_log 
MODIFY COLUMN contact_info VARCHAR(100) 
COMMENT 'Masked contact information (email or phone)';

ALTER TABLE tracking_audit_log 
MODIFY COLUMN ip_address VARCHAR(45) 
COMMENT 'IP address of the requester (supports IPv6)';

ALTER TABLE tracking_audit_log 
MODIFY COLUMN severity ENUM('INFO', 'WARNING', 'ERROR', 'CRITICAL') DEFAULT 'INFO' 
COMMENT 'Severity level of the security event';

ALTER TABLE tracking_audit_log 
MODIFY COLUMN verification_token VARCHAR(100) 
COMMENT 'Associated verification token if applicable';

ALTER TABLE tracking_audit_log 
MODIFY COLUMN request_successful BOOLEAN 
COMMENT 'Whether the request was successful or failed';

-- Create a view for security dashboard
CREATE VIEW security_dashboard AS
SELECT 
    DATE(timestamp) as event_date,
    event_type,
    severity,
    COUNT(*) as event_count,
    COUNT(DISTINCT ip_address) as unique_ips,
    COUNT(DISTINCT tracking_number) as unique_tracking_numbers,
    SUM(CASE WHEN request_successful = true THEN 1 ELSE 0 END) as successful_requests,
    SUM(CASE WHEN request_successful = false THEN 1 ELSE 0 END) as failed_requests
FROM tracking_audit_log
WHERE timestamp >= DATE_SUB(CURRENT_DATE, INTERVAL 30 DAY)
GROUP BY DATE(timestamp), event_type, severity
ORDER BY event_date DESC, event_count DESC;

-- Create a view for suspicious activity monitoring
CREATE VIEW suspicious_activity_monitor AS
SELECT 
    ip_address,
    COUNT(*) as total_events,
    COUNT(CASE WHEN severity IN ('ERROR', 'CRITICAL') THEN 1 END) as high_severity_events,
    COUNT(CASE WHEN event_type = 'RATE_LIMIT_EXCEEDED' THEN 1 END) as rate_limit_violations,
    COUNT(CASE WHEN event_type = 'VERIFICATION_FAILED' THEN 1 END) as failed_verifications,
    COUNT(DISTINCT tracking_number) as unique_tracking_numbers,
    MIN(timestamp) as first_seen,
    MAX(timestamp) as last_seen,
    CASE 
        WHEN COUNT(CASE WHEN severity IN ('ERROR', 'CRITICAL') THEN 1 END) > 5 THEN 'HIGH_RISK'
        WHEN COUNT(CASE WHEN event_type = 'RATE_LIMIT_EXCEEDED' THEN 1 END) > 3 THEN 'MEDIUM_RISK'
        WHEN COUNT(*) > 20 THEN 'MEDIUM_RISK'
        ELSE 'LOW_RISK'
    END as risk_level
FROM tracking_audit_log
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
  AND ip_address IS NOT NULL
GROUP BY ip_address
HAVING total_events > 1
ORDER BY high_severity_events DESC, total_events DESC;

-- Create a procedure for generating security reports
DELIMITER //
CREATE PROCEDURE GenerateSecurityReport(IN report_days INT)
BEGIN
    DECLARE report_start_date DATETIME;
    SET report_start_date = DATE_SUB(NOW(), INTERVAL report_days DAY);
    
    -- Summary statistics
    SELECT 
        'SUMMARY' as report_section,
        COUNT(*) as total_events,
        COUNT(DISTINCT ip_address) as unique_ips,
        COUNT(DISTINCT tracking_number) as unique_tracking_numbers,
        COUNT(CASE WHEN severity = 'CRITICAL' THEN 1 END) as critical_events,
        COUNT(CASE WHEN severity = 'ERROR' THEN 1 END) as error_events,
        COUNT(CASE WHEN severity = 'WARNING' THEN 1 END) as warning_events,
        COUNT(CASE WHEN request_successful = false THEN 1 END) as failed_requests
    FROM tracking_audit_log
    WHERE timestamp >= report_start_date;
    
    -- Top event types
    SELECT 
        'TOP_EVENTS' as report_section,
        event_type,
        COUNT(*) as event_count,
        COUNT(CASE WHEN request_successful = false THEN 1 END) as failed_count
    FROM tracking_audit_log
    WHERE timestamp >= report_start_date
    GROUP BY event_type
    ORDER BY event_count DESC
    LIMIT 10;
    
    -- Most active IPs
    SELECT 
        'ACTIVE_IPS' as report_section,
        ip_address,
        COUNT(*) as request_count,
        COUNT(CASE WHEN severity IN ('ERROR', 'CRITICAL') THEN 1 END) as high_severity_count,
        COUNT(DISTINCT tracking_number) as unique_tracking_numbers
    FROM tracking_audit_log
    WHERE timestamp >= report_start_date
      AND ip_address IS NOT NULL
    GROUP BY ip_address
    ORDER BY request_count DESC
    LIMIT 20;
    
    -- Most accessed tracking numbers
    SELECT 
        'POPULAR_TRACKING' as report_section,
        tracking_number,
        COUNT(*) as access_count,
        COUNT(DISTINCT ip_address) as unique_ips
    FROM tracking_audit_log
    WHERE timestamp >= report_start_date
      AND tracking_number IS NOT NULL
      AND event_type = 'TRACKING_ACCESSED'
    GROUP BY tracking_number
    ORDER BY access_count DESC
    LIMIT 15;
    
END //
DELIMITER ;

-- Create an event to automatically cleanup old audit logs (keep 90 days)
CREATE EVENT IF NOT EXISTS cleanup_audit_logs
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
  DELETE FROM tracking_audit_log 
  WHERE timestamp < DATE_SUB(NOW(), INTERVAL 90 DAY);
