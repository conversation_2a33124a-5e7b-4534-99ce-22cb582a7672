# LogiPool Deployment Guide

This guide provides comprehensive instructions for deploying LogiPool to production environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Database Setup](#database-setup)
4. [Application Deployment](#application-deployment)
5. [Monitoring Setup](#monitoring-setup)
6. [SSL/TLS Configuration](#ssltls-configuration)
7. [Backup and Recovery](#backup-and-recovery)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### System Requirements

- **Operating System**: Ubuntu 20.04 LTS or CentOS 8+ (recommended)
- **CPU**: Minimum 4 cores, 8 cores recommended
- **Memory**: Minimum 8GB RAM, 16GB recommended
- **Storage**: Minimum 100GB SSD, 500GB recommended
- **Network**: Static IP address, domain name configured

### Software Requirements

- Docker 24.0+ and Docker Compose 2.0+
- Git 2.30+
- OpenSSL (for SSL certificate generation)
- Nginx (if not using containerized version)

### External Services

- **Domain Name**: Configured with DNS pointing to your server
- **Email Service**: SMTP credentials (Gmail, SendGrid, etc.)
- **SMS Service**: Twilio account (optional)
- **Payment Gateway**: Stripe account
- **Cloud Storage**: AWS S3 bucket (optional)

## Environment Setup

### 1. Clone the Repository

```bash
git clone https://github.com/your-org/logipool.git
cd logipool
```

### 2. Configure Environment Variables

```bash
# Copy the example environment file
cp .env.example .env

# Edit the environment file with your production values
nano .env
```

### 3. Required Environment Variables

Fill in the following critical variables in your `.env` file:

```bash
# Database
DATABASE_PASSWORD=your_secure_database_password
DATABASE_USERNAME=logipool
DATABASE_NAME=logipool_prod

# Redis
REDIS_PASSWORD=your_secure_redis_password

# JWT
JWT_SECRET=your_very_long_and_secure_jwt_secret_key_here_minimum_256_bits

# Application
APP_BASE_URL=https://api.yourdomain.com

# Email
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_app_password

# Payment (Stripe)
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_PUBLIC_KEY=pk_live_your_stripe_public_key

# SMS (Twilio)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=+**********

# AWS S3 (if using cloud storage)
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
S3_BUCKET_NAME=logipool-documents-prod
```

## Database Setup

### 1. Initialize Database

The database will be automatically initialized when you start the containers. The initialization script creates:

- Required extensions (uuid-ossp, postgis, pg_trgm)
- Custom types for enums
- Audit logging tables
- Performance optimization settings

### 2. Database Migration

For production deployments, ensure you have proper database migration strategy:

```bash
# Run database migrations (if using Flyway or Liquibase)
# This is handled automatically by Spring Boot with Hibernate
```

## Application Deployment

### 1. Production Deployment

Use the deployment script for automated deployment:

```bash
# Make the script executable
chmod +x scripts/deploy.sh

# Run the deployment
./scripts/deploy.sh
```

### 2. Manual Deployment Steps

If you prefer manual deployment:

```bash
# Build and start the application
docker-compose -f docker-compose.prod.yml up -d

# Check the status
docker-compose -f docker-compose.prod.yml ps

# View logs
docker-compose -f docker-compose.prod.yml logs -f logipool-backend
```

### 3. Health Check

Verify the deployment:

```bash
# Check application health
curl http://localhost:8080/actuator/health

# Check API endpoints
curl http://localhost:8080/api/auth/test
```

## Monitoring Setup

### 1. Start Monitoring Stack

```bash
# Start monitoring services
docker-compose -f monitoring/docker-compose.monitoring.yml up -d
```

### 2. Access Monitoring Dashboards

- **Grafana**: http://your-domain:3000 (admin/admin)
- **Prometheus**: http://your-domain:9090
- **AlertManager**: http://your-domain:9093
- **Kibana**: http://your-domain:5601

### 3. Configure Alerts

Edit `monitoring/alertmanager/alertmanager.yml` to configure notification channels:

```yaml
route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  email_configs:
  - to: '<EMAIL>'
    from: '<EMAIL>'
    smarthost: 'smtp.gmail.com:587'
    auth_username: '<EMAIL>'
    auth_password: 'your_email_password'
```

## SSL/TLS Configuration

### 1. Obtain SSL Certificate

Using Let's Encrypt (recommended):

```bash
# Install certbot
sudo apt-get install certbot

# Obtain certificate
sudo certbot certonly --standalone -d api.yourdomain.com

# Certificates will be saved to /etc/letsencrypt/live/api.yourdomain.com/
```

### 2. Configure Nginx for HTTPS

Update `nginx/conf.d/logipool.conf` to enable HTTPS:

```bash
# Uncomment the HTTPS server block
# Update certificate paths
# Enable HTTP to HTTPS redirect
```

### 3. Auto-renewal Setup

```bash
# Add cron job for certificate renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

## Backup and Recovery

### 1. Database Backup

```bash
# Manual backup
docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U logipool logipool_prod > backup_$(date +%Y%m%d_%H%M%S).sql

# Automated backup script
./scripts/backup.sh
```

### 2. File Backup

```bash
# Backup uploaded files
tar -czf uploads_backup_$(date +%Y%m%d_%H%M%S).tar.gz uploads/
```

### 3. Recovery

```bash
# Restore database
docker-compose -f docker-compose.prod.yml exec -T postgres psql -U logipool logipool_prod < backup_file.sql

# Restore files
tar -xzf uploads_backup.tar.gz
```

## Security Considerations

### 1. Firewall Configuration

```bash
# Allow only necessary ports
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

### 2. Regular Updates

```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Update Docker images
docker-compose -f docker-compose.prod.yml pull
./scripts/deploy.sh
```

### 3. Security Monitoring

- Monitor failed login attempts
- Set up intrusion detection
- Regular security audits
- Keep dependencies updated

## Performance Optimization

### 1. Database Optimization

- Regular VACUUM and ANALYZE
- Monitor slow queries
- Optimize indexes
- Connection pooling tuning

### 2. Application Optimization

- JVM tuning
- Connection pool optimization
- Caching strategy
- Load balancing (if multiple instances)

### 3. Infrastructure Optimization

- CDN for static assets
- Database read replicas
- Redis clustering
- Horizontal scaling

## Troubleshooting

### Common Issues

1. **Application won't start**
   - Check environment variables
   - Verify database connectivity
   - Check logs: `docker-compose logs logipool-backend`

2. **Database connection errors**
   - Verify database is running
   - Check connection string
   - Verify credentials

3. **High memory usage**
   - Adjust JVM heap settings
   - Monitor for memory leaks
   - Check garbage collection logs

4. **SSL certificate issues**
   - Verify certificate validity
   - Check certificate chain
   - Verify domain configuration

### Log Locations

- Application logs: `/app/logs/` (inside container)
- Nginx logs: `/var/log/nginx/`
- Database logs: Check PostgreSQL container logs
- System logs: `/var/log/syslog`

### Support

For additional support:
- Check application logs
- Review monitoring dashboards
- Consult troubleshooting guides
- Contact development team
