package zw.co.kanjan.logipool.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.repository.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Set;

import static org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(properties = {"app.test-data.enabled=false"})
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class TrackingControllerIntegrationTest {

    @Autowired
    private WebApplicationContext context;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private LoadRepository loadRepository;

    @Autowired
    private LoadTrackingRepository trackingRepository;

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;
    private User testUser;
    private Role adminRole;
    private Load testLoad;
    private LoadTracking testTracking;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders
                .webAppContextSetup(context)
                .apply(springSecurity())
                .build();

        // Create admin role if it doesn't exist
        adminRole = roleRepository.findByName(Role.RoleName.ADMIN)
                .orElseGet(() -> roleRepository.save(Role.builder()
                        .name(Role.RoleName.ADMIN)
                        .description("Admin role")
                        .build()));

        // Create test user
        testUser = User.builder()
                .username("<EMAIL>") // Use email as username for backward compatibility
                .firstName("Admin")
                .lastName("User")
                .email("<EMAIL>")
                .password("$2a$10$dXJ3SW6G7P50lGmMkkmwe.20cQQubK3.HI/2P9W9QV6.ZFDLTrQ8.")
                .roles(Set.of(adminRole))
                .build();
        testUser = userRepository.save(testUser);

        // Create test company
        Company testCompany = Company.builder()
                .name("Test Transport Company")
                .user(testUser)
                .build();
        testCompany = companyRepository.save(testCompany);

        // Create test load
        testLoad = Load.builder()
                .title("Test Load")
                .description("Test load for tracking")
                .cargoType("General Cargo")
                .pickupLocation("Test Pickup Location")
                .deliveryLocation("Test Delivery Location")
                .pickupDate(LocalDateTime.now().plusDays(1))
                .status(Load.LoadStatus.IN_TRANSIT)
                .assignedCompany(testCompany)
                .deliveryDate(LocalDateTime.now().plusDays(2))
                .build();
        testLoad = loadRepository.save(testLoad);

        // Create test tracking
        testTracking = LoadTracking.builder()
                .load(testLoad)
                .location("Test Location")
                .latitude(new BigDecimal("-17.8252"))
                .longitude(new BigDecimal("31.0335"))
                .status(LoadTracking.TrackingStatus.IN_TRANSIT_TO_DELIVERY)
                .updatedBy(testUser)
                .timestamp(LocalDateTime.now())
                .isAutomated(false)
                .build();
        testTracking = trackingRepository.save(testTracking);
    }

    @Test
    @WithMockUser(username = "admin", roles = {"ADMIN"})
    void testGetActiveLocations_Success() throws Exception {
        mockMvc.perform(get("/api/tracking/active-locations")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(1))
                .andExpect(jsonPath("$[0].loadId").value(testLoad.getId()))
                .andExpect(jsonPath("$[0].latitude").value(testTracking.getLatitude()))
                .andExpect(jsonPath("$[0].longitude").value(testTracking.getLongitude()))
                .andExpect(jsonPath("$[0].status").value(testTracking.getStatus().name()));
    }

    @Test
    @WithMockUser(username = "admin", roles = {"ADMIN"})
    void testGetActiveLocations_EmptyResult() throws Exception {
        // Delete the tracking record to test empty result
        trackingRepository.delete(testTracking);

        mockMvc.perform(get("/api/tracking/active-locations")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$.length()").value(0));
    }

    @Test
    void testGetActiveLocations_Unauthorized() throws Exception {
        mockMvc.perform(get("/api/tracking/active-locations")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(username = "admin", roles = {"ADMIN"})
    void testGetTrackingStatistics_Success() throws Exception {
        mockMvc.perform(get("/api/tracking/statistics")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.totalLoads").exists())
                .andExpect(jsonPath("$.loadsInTransitToPickup").exists())
                .andExpect(jsonPath("$.loadsInTransitToDelivery").exists())
                .andExpect(jsonPath("$.loadsDelivered").exists())
                .andExpect(jsonPath("$.loadsDelayed").exists())
                .andExpect(jsonPath("$.loadsWithIssues").exists())
                .andExpect(jsonPath("$.generatedAt").exists());
    }
}
