class Validators {
  // Email validation
  static String? email(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  // Password validation
  static String? password(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    
    if (value.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    
    // Check for at least one uppercase letter
    if (!RegExp(r'[A-Z]').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter';
    }
    
    // Check for at least one lowercase letter
    if (!RegExp(r'[a-z]').hasMatch(value)) {
      return 'Password must contain at least one lowercase letter';
    }
    
    // Check for at least one digit
    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'Password must contain at least one number';
    }
    
    return null;
  }

  // Confirm password validation
  static String? confirmPassword(String? value, String originalPassword) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    
    if (value != originalPassword) {
      return 'Passwords do not match';
    }
    
    return null;
  }



  // Required field validation
  static String? required(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'This field is required';
    }
    return null;
  }

  // Phone number validation
  static String? phoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Optional field
    }
    
    // Remove all non-digit characters for validation
    final digitsOnly = value.replaceAll(RegExp(r'[^\d]'), '');
    
    if (digitsOnly.length < 10) {
      return 'Phone number must be at least 10 digits';
    }
    
    if (digitsOnly.length > 15) {
      return 'Phone number must be less than 15 digits';
    }
    
    return null;
  }

  // Name validation (for first name, last name, etc.)
  static String? name(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Name is required';
    }
    
    if (value.trim().length < 2) {
      return 'Name must be at least 2 characters long';
    }
    
    if (value.trim().length > 50) {
      return 'Name must be less than 50 characters';
    }
    
    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    if (!RegExp(r"^[a-zA-Z\s\-']+$").hasMatch(value.trim())) {
      return 'Name can only contain letters, spaces, hyphens, and apostrophes';
    }
    
    return null;
  }

  // Company name validation
  static String? companyName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Optional field
    }
    
    if (value.trim().length < 2) {
      return 'Company name must be at least 2 characters long';
    }
    
    if (value.trim().length > 100) {
      return 'Company name must be less than 100 characters';
    }
    
    return null;
  }

  // Amount validation (for prices, costs, etc.)
  static String? amount(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Amount is required';
    }
    
    final amount = double.tryParse(value.trim());
    if (amount == null) {
      return 'Please enter a valid amount';
    }
    
    if (amount < 0) {
      return 'Amount cannot be negative';
    }
    
    if (amount > *********) {
      return 'Amount is too large';
    }
    
    return null;
  }

  // Weight validation
  static String? weight(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Weight is required';
    }
    
    final weight = double.tryParse(value.trim());
    if (weight == null) {
      return 'Please enter a valid weight';
    }
    
    if (weight <= 0) {
      return 'Weight must be greater than 0';
    }
    
    if (weight > 100000) {
      return 'Weight is too large';
    }
    
    return null;
  }

  // Distance validation
  static String? distance(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Distance is required';
    }
    
    final distance = double.tryParse(value.trim());
    if (distance == null) {
      return 'Please enter a valid distance';
    }
    
    if (distance <= 0) {
      return 'Distance must be greater than 0';
    }
    
    if (distance > 10000) {
      return 'Distance is too large';
    }
    
    return null;
  }

  // Description validation
  static String? description(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Description is required';
    }
    
    if (value.trim().length < 10) {
      return 'Description must be at least 10 characters long';
    }
    
    if (value.trim().length > 1000) {
      return 'Description must be less than 1000 characters';
    }
    
    return null;
  }

  // Vehicle registration validation
  static String? vehicleRegistration(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Vehicle registration is required';
    }
    
    if (value.trim().length < 3) {
      return 'Vehicle registration must be at least 3 characters long';
    }
    
    if (value.trim().length > 20) {
      return 'Vehicle registration must be less than 20 characters';
    }
    
    return null;
  }

  // License number validation
  static String? licenseNumber(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'License number is required';
    }
    
    if (value.trim().length < 5) {
      return 'License number must be at least 5 characters long';
    }
    
    if (value.trim().length > 30) {
      return 'License number must be less than 30 characters';
    }
    
    return null;
  }
}
