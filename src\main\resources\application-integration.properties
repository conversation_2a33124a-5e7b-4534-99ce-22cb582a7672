# Integration Test Configuration
spring.application.name=logipool-integration

# H2 In-Memory Database for Integration Testing
spring.datasource.url=jdbc:h2:mem:integrationdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Configuration for Integration Testing
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.format_sql=false

# JWT Configuration for Integration Testing
app.jwt.secret=integration-test-secret-key-for-jwt-token-generation-2025
app.jwt.expiration=3600000
app.jwt.refresh-expiration=7200000

# Disable Redis for integration testing
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration

# CORS Configuration for Integration Testing
app.cors.allowed-origins=http://localhost:3000,http://localhost:8081,http://127.0.0.1:3000
app.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
app.cors.allowed-headers=*
app.cors.allow-credentials=true

# File Upload Configuration for Integration Testing
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
app.file.upload-dir=./integration-test-uploads
app.file.max-size-mb=10
app.file.allowed-types=pdf,jpg,jpeg,png,doc,docx

# Payment Configuration for Integration Testing
app.payment.commission-rate=7.5
app.payment.min-commission=5.00
app.payment.max-commission=500.00
app.payment.gateway=MOCK

# Notification Configuration for Integration Testing
app.notification.email.enabled=false
app.notification.sms.enabled=false

# Logging Configuration for Integration Testing
logging.level.zw.co.kanjan.logipool=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.springframework.web=INFO

# Server Configuration
server.port=8080
server.servlet.context-path=/

# Management Endpoints
management.endpoints.web.exposure.include=health,info
management.endpoint.health.show-details=always

# H2 Console (for debugging)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
