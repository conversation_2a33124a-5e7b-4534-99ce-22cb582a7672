package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Transaction;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.entity.Payment;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface TransactionRepository extends JpaRepository<Transaction, Long> {
    
    // Find transaction by transaction ID
    Optional<Transaction> findByTransactionId(String transactionId);
    
    // Find transaction by gateway transaction ID
    Optional<Transaction> findByGatewayTransactionId(String gatewayTransactionId);
    
    // Find transactions by status
    Page<Transaction> findByStatus(Transaction.TransactionStatus status, Pageable pageable);
    
    // Find transactions by type
    List<Transaction> findByType(Transaction.TransactionType type);
    
    // Find transactions by payment
    List<Transaction> findByPayment(Payment payment);
    
    // Find transactions by from user
    Page<Transaction> findByFromUserOrderByCreatedAtDesc(User fromUser, Pageable pageable);
    
    // Find transactions by to user
    Page<Transaction> findByToUserOrderByCreatedAtDesc(User toUser, Pageable pageable);
    
    // Find transactions for user (either from or to)
    @Query("SELECT t FROM Transaction t WHERE t.fromUser = :user OR t.toUser = :user ORDER BY t.createdAt DESC")
    Page<Transaction> findByUserOrderByCreatedAtDesc(@Param("user") User user, Pageable pageable);
    
    // Find transactions by date range
    @Query("SELECT t FROM Transaction t WHERE t.createdAt BETWEEN :startDate AND :endDate")
    List<Transaction> findByDateRange(@Param("startDate") LocalDateTime startDate, 
                                    @Param("endDate") LocalDateTime endDate);
    
    // Calculate total transaction amount by status
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.status = :status")
    BigDecimal getTotalAmountByStatus(@Param("status") Transaction.TransactionStatus status);
    
    // Calculate total transaction amount by type
    @Query("SELECT SUM(t.amount) FROM Transaction t WHERE t.type = :type AND t.status = 'COMPLETED'")
    BigDecimal getTotalAmountByType(@Param("type") Transaction.TransactionType type);
    
    // Find failed transactions
    List<Transaction> findByStatusOrderByCreatedAtDesc(Transaction.TransactionStatus status);
    
    // Count transactions by status
    long countByStatus(Transaction.TransactionStatus status);
    
    // Find pending transactions older than specified time
    @Query("SELECT t FROM Transaction t WHERE t.status = 'PENDING' AND t.createdAt < :cutoffTime")
    List<Transaction> findStaleTransactions(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    // Find recent transactions
    @Query("SELECT t FROM Transaction t ORDER BY t.createdAt DESC")
    Page<Transaction> findRecentTransactions(Pageable pageable);
    
    // Check if transaction ID exists
    boolean existsByTransactionId(String transactionId);
    
    // Transaction statistics by date range
    @Query("SELECT t.type, t.status, COUNT(t), SUM(t.amount) FROM Transaction t WHERE t.createdAt BETWEEN :startDate AND :endDate GROUP BY t.type, t.status")
    List<Object[]> getTransactionStatsByDateRange(@Param("startDate") LocalDateTime startDate, 
                                                @Param("endDate") LocalDateTime endDate);
}
