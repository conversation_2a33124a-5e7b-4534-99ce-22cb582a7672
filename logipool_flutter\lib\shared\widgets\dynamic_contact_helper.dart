import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/contact_config_service.dart';
import '../models/contact_config_model.dart';

class DynamicContactHelper {
  static ContactConfigService get _contactService => ContactConfigService.instance;

  // Get current contact configuration
  static ContactConfigModel get contactConfig => _contactService.contactConfig;

  // Launch phone call
  static Future<void> launchPhone([BuildContext? context]) async {
    final phoneNumber = 'tel:${contactConfig.phoneNumberForDialing}';
    try {
      if (await canLaunchUrl(Uri.parse(phoneNumber))) {
        await launchUrl(Uri.parse(phoneNumber));
      } else {
        if (context != null && context.mounted) {
          _showErrorSnackBar(context, 'Could not make phone call.');
        }
      }
    } catch (e) {
      if (context != null && context.mounted) {
        _showErrorSnackBar(context, 'Error making phone call.');
      }
    }
  }

  // Launch WhatsApp with custom message
  static Future<void> launchWhatsApp([BuildContext? context, String? customMessage]) async {
    final phoneNumber = contactConfig.whatsappNumberForDialing;
    final message = customMessage ?? 'Hello! I need help with LogiPool services. Can you assist me?';
    final url = 'https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}';
    
    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } else {
        if (context != null && context.mounted) {
          _showErrorSnackBar(context, 'Could not open WhatsApp. Please install WhatsApp or contact us directly.');
        }
      }
    } catch (e) {
      if (context != null && context.mounted) {
        _showErrorSnackBar(context, 'Error opening WhatsApp. Please try again.');
      }
    }
  }

  // Launch email with custom subject and body
  static Future<void> launchEmail([
    BuildContext? context, 
    String? customSubject, 
    String? customBody,
    String? emailType = 'general' // 'general', 'support', 'legal'
  ]) async {
    String emailAddress;
    switch (emailType) {
      case 'support':
        emailAddress = contactConfig.supportEmail;
        break;
      case 'legal':
        emailAddress = contactConfig.legalEmail;
        break;
      default:
        emailAddress = contactConfig.email;
    }

    final subject = customSubject ?? 'LogiPool Inquiry';
    final body = customBody ?? 'Hello,\n\nI have an inquiry about LogiPool services.\n\nBest regards';
    final email = 'mailto:$emailAddress?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}';
    
    try {
      if (await canLaunchUrl(Uri.parse(email))) {
        await launchUrl(Uri.parse(email));
      } else {
        if (context != null && context.mounted) {
          _showErrorSnackBar(context, 'Could not open email client.');
        }
      }
    } catch (e) {
      if (context != null && context.mounted) {
        _showErrorSnackBar(context, 'Error opening email client.');
      }
    }
  }

  // Launch Tawk.to live chat
  // static Future<void> launchTawkTo([BuildContext? context]) async {
  //   try {
  //     final tawkToUrl = contactConfig.tawkToUrl;
  //     if (await canLaunchUrl(Uri.parse(tawkToUrl))) {
  //       await launchUrl(Uri.parse(tawkToUrl), mode: LaunchMode.externalApplication);
  //     } else {
  //       if (context != null && context.mounted) {
  //         _showLiveChatDialog(context);
  //       }
  //     }
  //   } catch (e) {
  //     if (context != null && context.mounted) {
  //       _showLiveChatDialog(context);
  //     }
  //   }
  // }

  // Launch website
  static Future<void> launchWebsite([BuildContext? context]) async {
    try {
      final websiteUrl = contactConfig.websiteUrl;
      if (await canLaunchUrl(Uri.parse(websiteUrl))) {
        await launchUrl(Uri.parse(websiteUrl), mode: LaunchMode.externalApplication);
      } else {
        if (context != null && context.mounted) {
          _showErrorSnackBar(context, 'Could not open website.');
        }
      }
    } catch (e) {
      if (context != null && context.mounted) {
        _showErrorSnackBar(context, 'Error opening website.');
      }
    }
  }

  // Get WhatsApp message based on current route
  static String getContextualWhatsAppMessage(String? currentRoute) {
    switch (currentRoute) {
      case '/vehicles':
        return 'Hi! I\'m looking for vehicle rental services. Can you provide more information?';
      case '/equipment':
        return 'Hello! I need equipment rental services. Can you assist me?';
      case '/services':
        return 'Hi! I\'d like to know more about your logistics services.';
      case '/track':
        return 'Hello! I need help with tracking my shipment.';
      case '/contact':
        return 'Hi! I have some questions about LogiPool services.';
      case '/guest-load-posting':
        return 'Hello! I need help with shipping a load. Can you assist me?';
      default:
        return 'Hello! I\'m interested in LogiPool services. Can you help me?';
    }
  }

  // Show error snackbar
  static void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }


}
