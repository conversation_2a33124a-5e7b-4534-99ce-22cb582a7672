import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../shared/models/company_member_model.dart';
import '../../../shared/models/company_model.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../bloc/company_member_bloc.dart';
import '../widgets/company_member_card.dart';
import '../widgets/invite_user_dialog.dart';
import '../widgets/member_details_dialog.dart';
import '../widgets/edit_member_dialog.dart';

class CompanyMembersScreen extends StatefulWidget {
  final CompanyModel company;

  const CompanyMembersScreen({
    super.key,
    required this.company,
  });

  @override
  State<CompanyMembersScreen> createState() => _CompanyMembersScreenState();
}

class _CompanyMembersScreenState extends State<CompanyMembersScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  int? get _companyId => widget.company.id;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    final companyId = widget.company.id;
    if (companyId != null) {
      context.read<CompanyMemberBloc>().add(LoadCompanyMembers(companyId));
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.company.name} - Team'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Active', icon: Icon(Icons.people)),
            Tab(text: 'Pending', icon: Icon(Icons.pending)),
            Tab(text: 'All', icon: Icon(Icons.group)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: () => _showInviteDialog(context),
            tooltip: 'Invite User',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              final companyId = _companyId;
              if (companyId != null) {
                context.read<CompanyMemberBloc>().add(
                      LoadCompanyMembers(companyId),
                    );
              }
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: BlocConsumer<CompanyMemberBloc, CompanyMemberState>(
        listener: (context, state) {
          if (state is CompanyMemberOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
            // Reload members after successful operation
            final companyId = _companyId;
            if (companyId != null) {
              context.read<CompanyMemberBloc>().add(
                    LoadCompanyMembers(companyId),
                  );
            }
          } else if (state is CompanyMemberError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is CompanyMemberLoading) {
            return const LoadingWidget();
          } else if (state is CompanyMemberError) {
            return CustomErrorWidget(
              message: state.message,
              onRetry: () {
                final companyId = _companyId;
                if (companyId != null) {
                  context.read<CompanyMemberBloc>().add(
                        LoadCompanyMembers(companyId),
                      );
                }
              },
            );
          } else if (state is CompanyMemberLoaded) {
            return Column(
              children: [
                _buildSummaryCard(state.memberList),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      _buildMembersList(
                        state.memberList.members
                            .where((m) => m.status == MemberStatus.active)
                            .toList(),
                      ),
                      _buildMembersList(
                        state.memberList.members
                            .where((m) => m.status == MemberStatus.pending)
                            .toList(),
                      ),
                      _buildMembersList(state.memberList.members),
                    ],
                  ),
                ),
              ],
            );
          }
          return const Center(child: Text('No data available'));
        },
      ),
    );
  }

  Widget _buildSummaryCard(CompanyMemberListResponse memberList) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildSummaryItem(
              'Total Members',
              memberList.totalMembers.toString(),
              Icons.people,
              Colors.blue,
            ),
            _buildSummaryItem(
              'Active',
              memberList.activeMembers.toString(),
              Icons.check_circle,
              Colors.green,
            ),
            _buildSummaryItem(
              'Pending',
              memberList.pendingInvitations.toString(),
              Icons.pending,
              Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 32),
        const SizedBox(height: 8),
        Text(
          value,
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildMembersList(List<CompanyMemberModel> members) {
    if (members.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No members found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: members.length,
      itemBuilder: (context, index) {
        final member = members[index];
        return CompanyMemberCard(
          member: member,
          onTap: () => _showMemberDetails(context, member),
          onEdit: (member) => _showEditMemberDialog(context, member),
          onRemove: (member) => _showRemoveConfirmation(context, member),
        );
      },
    );
  }

  void _showInviteDialog(BuildContext context) {
    final companyId = _companyId;
    if (companyId == null) return;

    showDialog<void>(
      context: context,
      builder: (context) => InviteUserDialog(
        companyId: companyId,
        onInvite: (request) {
          context.read<CompanyMemberBloc>().add(
                InviteUser(companyId, request),
              );
        },
      ),
    );
  }

  void _showMemberDetails(BuildContext context, CompanyMemberModel member) {
    showDialog<void>(
      context: context,
      builder: (context) => MemberDetailsDialog(member: member),
    );
  }

  void _showEditMemberDialog(BuildContext context, CompanyMemberModel member) {
    showDialog<void>(
      context: context,
      builder: (context) => EditMemberDialog(
        member: member,
        onUpdate: (request) {
          context.read<CompanyMemberBloc>().add(
                UpdateMember(request),
              );
        },
      ),
    );
  }

  void _showRemoveConfirmation(
      BuildContext context, CompanyMemberModel member) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Remove Member'),
        content: Text(
          'Are you sure you want to remove ${member.displayName} from the company?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<CompanyMemberBloc>().add(RemoveMember(member.id));
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }
}
