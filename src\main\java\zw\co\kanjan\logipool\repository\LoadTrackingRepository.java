package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.LoadTracking;
import zw.co.kanjan.logipool.entity.User;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface LoadTrackingRepository extends JpaRepository<LoadTracking, Long> {
    
    // Find tracking history for a specific load
    List<LoadTracking> findByLoadOrderByTimestampDesc(Load load);
    
    // Find tracking history for a specific load with pagination
    Page<LoadTracking> findByLoadOrderByTimestampDesc(Load load, Pageable pageable);
    
    // Find latest tracking entry for a load
    @Query("SELECT lt FROM LoadTracking lt WHERE lt.load = :load ORDER BY lt.timestamp DESC LIMIT 1")
    Optional<LoadTracking> findLatestByLoad(@Param("load") Load load);
    
    // Find tracking entries by status
    List<LoadTracking> findByStatus(LoadTracking.TrackingStatus status);
    
    // Find tracking entries by load and status
    List<LoadTracking> findByLoadAndStatus(Load load, LoadTracking.TrackingStatus status);
    
    // Find tracking entries updated by a specific user
    Page<LoadTracking> findByUpdatedByOrderByTimestampDesc(User updatedBy, Pageable pageable);
    
    // Find automated tracking entries
    List<LoadTracking> findByIsAutomatedTrue();
    
    // Find manual tracking entries
    List<LoadTracking> findByIsAutomatedFalse();
    
    // Find tracking entries within a date range
    @Query("SELECT lt FROM LoadTracking lt WHERE lt.timestamp BETWEEN :startDate AND :endDate ORDER BY lt.timestamp DESC")
    List<LoadTracking> findByTimestampBetween(@Param("startDate") LocalDateTime startDate, 
                                             @Param("endDate") LocalDateTime endDate);
    
    // Find tracking entries within a geographic area
    @Query("SELECT lt FROM LoadTracking lt WHERE lt.latitude BETWEEN :minLat AND :maxLat " +
           "AND lt.longitude BETWEEN :minLon AND :maxLon")
    List<LoadTracking> findByLocationBounds(@Param("minLat") BigDecimal minLatitude,
                                           @Param("maxLat") BigDecimal maxLatitude,
                                           @Param("minLon") BigDecimal minLongitude,
                                           @Param("maxLon") BigDecimal maxLongitude);
    
    // Find loads currently in transit
    @Query("SELECT DISTINCT lt.load FROM LoadTracking lt WHERE lt.status IN " +
           "('IN_TRANSIT_TO_PICKUP', 'IN_TRANSIT_TO_DELIVERY') AND " +
           "lt.timestamp = (SELECT MAX(lt2.timestamp) FROM LoadTracking lt2 WHERE lt2.load = lt.load)")
    List<Load> findLoadsInTransit();

    // Alternative method to find loads in transit with better performance
    @Query(value = "SELECT DISTINCT l.* FROM loads l " +
           "INNER JOIN load_tracking lt ON l.id = lt.load_id " +
           "WHERE lt.status IN ('IN_TRANSIT_TO_PICKUP', 'IN_TRANSIT_TO_DELIVERY') " +
           "AND lt.timestamp = (SELECT MAX(lt2.timestamp) FROM load_tracking lt2 WHERE lt2.load_id = l.id)",
           nativeQuery = true)
    List<Load> findLoadsInTransitNative();
    
    // Find loads with delayed status
    @Query("SELECT DISTINCT lt.load FROM LoadTracking lt WHERE lt.status = 'DELAYED' AND " +
           "lt.timestamp = (SELECT MAX(lt2.timestamp) FROM LoadTracking lt2 WHERE lt2.load = lt.load)")
    List<Load> findDelayedLoads();
    
    // Find loads with issues reported
    @Query("SELECT DISTINCT lt.load FROM LoadTracking lt WHERE lt.status = 'ISSUE_REPORTED' AND " +
           "lt.timestamp = (SELECT MAX(lt2.timestamp) FROM LoadTracking lt2 WHERE lt2.load = lt.load)")
    List<Load> findLoadsWithIssues();
    
    // Count tracking entries for a load
    Long countByLoad(Load load);
    
    // Find recent tracking updates
    @Query("SELECT lt FROM LoadTracking lt ORDER BY lt.timestamp DESC")
    Page<LoadTracking> findRecentUpdates(Pageable pageable);
    
    // Find tracking entries by load ID
    @Query("SELECT lt FROM LoadTracking lt WHERE lt.load.id = :loadId ORDER BY lt.timestamp DESC")
    List<LoadTracking> findByLoadId(@Param("loadId") Long loadId);
    
    // Find loads by current status (latest tracking entry)
    @Query("SELECT DISTINCT lt.load FROM LoadTracking lt WHERE lt.status = :status AND " +
           "lt.timestamp = (SELECT MAX(lt2.timestamp) FROM LoadTracking lt2 WHERE lt2.load = lt.load)")
    List<Load> findLoadsByCurrentStatus(@Param("status") LoadTracking.TrackingStatus status);
    
    // Check if load has specific status
    @Query("SELECT COUNT(lt) > 0 FROM LoadTracking lt WHERE lt.load = :load AND lt.status = :status")
    boolean existsByLoadAndStatus(@Param("load") Load load, @Param("status") LoadTracking.TrackingStatus status);
    
    // Find tracking entries with coordinates
    @Query("SELECT lt FROM LoadTracking lt WHERE lt.latitude IS NOT NULL AND lt.longitude IS NOT NULL")
    List<LoadTracking> findEntriesWithCoordinates();
    
    // Find tracking entries near a location
    @Query("SELECT lt FROM LoadTracking lt WHERE " +
           "SQRT(POWER(lt.latitude - :latitude, 2) + POWER(lt.longitude - :longitude, 2)) <= :radiusInDegrees")
    List<LoadTracking> findNearLocation(@Param("latitude") BigDecimal latitude,
                                       @Param("longitude") BigDecimal longitude,
                                       @Param("radiusInDegrees") BigDecimal radiusInDegrees);
    
    // Find loads that haven't been updated in a while
    @Query("SELECT DISTINCT lt.load FROM LoadTracking lt WHERE " +
           "lt.timestamp = (SELECT MAX(lt2.timestamp) FROM LoadTracking lt2 WHERE lt2.load = lt.load) " +
           "AND lt.timestamp < :cutoffTime AND lt.status IN ('IN_TRANSIT_TO_PICKUP', 'IN_TRANSIT_TO_DELIVERY')")
    List<Load> findStaleLoads(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    // Get tracking statistics
    @Query("SELECT COUNT(DISTINCT lt.load) FROM LoadTracking lt WHERE lt.status = :status")
    Long countLoadsByStatus(@Param("status") LoadTracking.TrackingStatus status);
    
    // Find loads assigned to a company with current tracking status
    @Query("SELECT DISTINCT lt.load FROM LoadTracking lt WHERE lt.load.assignedCompany.id = :companyId AND " +
           "lt.timestamp = (SELECT MAX(lt2.timestamp) FROM LoadTracking lt2 WHERE lt2.load = lt.load)")
    List<Load> findLoadsByAssignedCompany(@Param("companyId") Long companyId);
}
