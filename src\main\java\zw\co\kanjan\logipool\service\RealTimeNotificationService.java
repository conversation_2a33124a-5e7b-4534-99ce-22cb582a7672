package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import zw.co.kanjan.logipool.dto.NotificationDto;

import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class RealTimeNotificationService {
    
    private final SimpMessagingTemplate messagingTemplate;
    
    public void sendNotificationToUser(Long userId, NotificationDto notification) {
        try {
            String destination = "/user/" + userId + "/queue/notifications";
            messagingTemplate.convertAndSend(destination, notification);
            log.info("Real-time notification sent to user {}: {}", userId, notification.getTitle());
        } catch (Exception e) {
            log.error("Failed to send real-time notification to user {}: {}", userId, notification.getTitle(), e);
        }
    }
    
    public void sendNotificationToRole(String role, NotificationDto notification) {
        try {
            String destination = "/topic/notifications/" + role.toLowerCase();
            messagingTemplate.convertAndSend(destination, notification);
            log.info("Real-time notification sent to role {}: {}", role, notification.getTitle());
        } catch (Exception e) {
            log.error("Failed to send real-time notification to role {}: {}", role, notification.getTitle(), e);
        }
    }
    
    public void broadcastNotification(NotificationDto notification) {
        try {
            messagingTemplate.convertAndSend("/topic/notifications/all", notification);
            log.info("Broadcast notification sent: {}", notification.getTitle());
        } catch (Exception e) {
            log.error("Failed to broadcast notification: {}", notification.getTitle(), e);
        }
    }
    
    public void sendLoadPostedNotification(Long userId, String loadTitle, String location) {
        NotificationDto notification = NotificationDto.builder()
                .title("New Load Available")
                .message(String.format("A new load '%s' is available near %s", loadTitle, location))
                .type("LOAD_POSTED")
                .timestamp(LocalDateTime.now())
                .data(Map.of(
                    "loadTitle", loadTitle,
                    "location", location
                ))
                .build();
        
        sendNotificationToUser(userId, notification);
    }
    
    public void sendBidReceivedNotification(Long userId, String companyName, String bidAmount, String loadTitle) {
        NotificationDto notification = NotificationDto.builder()
                .title("New Bid Received")
                .message(String.format("You received a bid of $%s from %s for '%s'", bidAmount, companyName, loadTitle))
                .type("BID_RECEIVED")
                .timestamp(LocalDateTime.now())
                .data(Map.of(
                    "companyName", companyName,
                    "bidAmount", bidAmount,
                    "loadTitle", loadTitle
                ))
                .build();
        
        sendNotificationToUser(userId, notification);
    }
    
    public void sendBidAcceptedNotification(Long userId, String loadTitle, String bidAmount) {
        NotificationDto notification = NotificationDto.builder()
                .title("Bid Accepted!")
                .message(String.format("Congratulations! Your bid of $%s for '%s' has been accepted", bidAmount, loadTitle))
                .type("BID_ACCEPTED")
                .timestamp(LocalDateTime.now())
                .data(Map.of(
                    "loadTitle", loadTitle,
                    "bidAmount", bidAmount
                ))
                .build();
        
        sendNotificationToUser(userId, notification);
    }
    
    public void sendBidRejectedNotification(Long userId, String loadTitle) {
        NotificationDto notification = NotificationDto.builder()
                .title("Bid Update")
                .message(String.format("Your bid for '%s' was not selected", loadTitle))
                .type("BID_REJECTED")
                .timestamp(LocalDateTime.now())
                .data(Map.of(
                    "loadTitle", loadTitle
                ))
                .build();
        
        sendNotificationToUser(userId, notification);
    }
    
    public void sendLoadStatusUpdateNotification(Long userId, String loadTitle, String status) {
        NotificationDto notification = NotificationDto.builder()
                .title("Load Status Update")
                .message(String.format("Load '%s' status updated to %s", loadTitle, status))
                .type("LOAD_STATUS_UPDATE")
                .timestamp(LocalDateTime.now())
                .data(Map.of(
                    "loadTitle", loadTitle,
                    "status", status
                ))
                .build();
        
        sendNotificationToUser(userId, notification);
    }
    
    public void sendSystemNotification(String title, String message, String type) {
        NotificationDto notification = NotificationDto.builder()
                .title(title)
                .message(message)
                .type(type)
                .timestamp(LocalDateTime.now())
                .build();
        
        broadcastNotification(notification);
    }
    
    public void sendRoleBasedNotification(String role, String title, String message, String type, Map<String, Object> data) {
        NotificationDto notification = NotificationDto.builder()
                .title(title)
                .message(message)
                .type(type)
                .timestamp(LocalDateTime.now())
                .data(data)
                .build();
        
        sendNotificationToRole(role, notification);
    }
}
