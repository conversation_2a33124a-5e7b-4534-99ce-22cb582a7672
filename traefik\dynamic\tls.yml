# TLS Configuration for Let's Encrypt with Traefik
tls:
  options:
    # Default TLS configuration optimized for Let's Encrypt
    default:
      minVersion: "VersionTLS12"
      maxVersion: "VersionTLS13"
      cipherSuites:
        # TLS 1.3 cipher suites (preferred)
        - "TLS_AES_256_GCM_SHA384"
        - "TLS_CHACHA20_POLY1305_SHA256"
        - "TLS_AES_128_GCM_SHA256"
        # TLS 1.2 cipher suites (fallback)
        - "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256"
        - "TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384"
        - "TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305"
        - "TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256"
      curvePreferences:
        - "X25519"
        - "CurveP256"
        - "CurveP384"
      alpnProtocols:
        - "h2"
        - "http/1.1"
      sniStrict: true

    # Modern TLS configuration (TLS 1.3 only)
    modern:
      minVersion: "VersionTLS13"
      maxVersion: "VersionTLS13"
      cipherSuites:
        - "TLS_AES_256_GCM_SHA384"
        - "TLS_CHACHA20_POLY1305_SHA256"
        - "TLS_AES_128_GCM_SHA256"
      curvePreferences:
        - "X25519"
        - "CurveP256"
      alpnProtocols:
        - "h2"
        - "http/1.1"
      sniStrict: true

  # Certificate stores - Let's Encrypt will automatically manage certificates
  stores:
    default:
      # Let's Encrypt certificates will be stored in /letsencrypt/acme.json
      # No need to specify certificate files as they're managed automatically
