# Development Configuration

# Database Configuration
spring.datasource.url=*****************************************
spring.datasource.username=logipool
spring.datasource.password=logipool123
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Redis Configuration
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.timeout=60000

# JWT Configuration
app.jwtSecret=logiPoolSecretKey2025DevelopmentOnly
app.jwtExpirationMs=86400000
app.jwtRefreshExpirationMs=604800000

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
app.upload.dir=./uploads

# Logging Configuration
logging.level.zw.co.kanjan.logipool=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN

# Development Tools
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true

# Test Data Configuration - ENABLED for development
app.test-data.enabled=true
