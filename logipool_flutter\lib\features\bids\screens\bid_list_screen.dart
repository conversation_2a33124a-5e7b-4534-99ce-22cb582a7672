import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/bid_model.dart';
import '../../../shared/models/load_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/enums/user_role.dart';
import '../bloc/bid_bloc.dart';
import '../widgets/bid_card.dart';
import '../../loads/bloc/load_bloc.dart';

class BidListScreen extends StatefulWidget {
  final LoadModel load;

  const BidListScreen({
    super.key,
    required this.load,
  });

  @override
  State<BidListScreen> createState() => _BidListScreenState();
}

class _BidListScreenState extends State<BidListScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isLoadingMore = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _fetchBids();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _fetchBids() {
    context.read<BidBloc>().add(
          BidsForLoadFetchRequested(loadId: widget.load.id!),
        );
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
            _scrollController.position.maxScrollExtent - 200 &&
        !_isLoadingMore) {
      _loadMore();
    }
  }

  void _loadMore() {
    final state = context.read<BidBloc>().state;
    if (state is BidsForLoadSuccess && !state.hasReachedMax) {
      setState(() {
        _isLoadingMore = true;
      });

      context.read<BidBloc>().add(
            BidsForLoadLoadMoreRequested(
              loadId: widget.load.id!,
              page: state.currentPage + 1,
            ),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('Bids for ${widget.load.title}'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: Column(
        children: [
          // Load summary card
          _buildLoadSummaryCard(context),

          // Bids list
          Expanded(
            child: BlocConsumer<BidBloc, BidState>(
              listener: (context, state) {
                if (state is BidsForLoadSuccess) {
                  setState(() {
                    _isLoadingMore = false;
                  });
                } else if (state is BidError) {
                  setState(() {
                    _isLoadingMore = false;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                } else if (state is BidOperationSuccess) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _fetchBids(); // Refresh the list
                }
              },
              builder: (context, state) {
                if (state is BidLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                } else if (state is BidsForLoadSuccess) {
                  return _buildBidsList(context, state);
                } else if (state is BidError) {
                  return _buildErrorState(context, state.message);
                } else {
                  return const Center(
                    child: Text('No bids available'),
                  );
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadSummaryCard(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              widget.load.title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  size: 16,
                  color: Colors.grey[600],
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    '${widget.load.pickupLocation} → ${widget.load.deliveryLocation}',
                    style: theme.textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(widget.load.status).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.load.status.displayName,
                    style: TextStyle(
                      color: _getStatusColor(widget.load.status),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  '${widget.load.bidCount} bids',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBidsList(BuildContext context, BidsForLoadSuccess state) {
    if (state.bids.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.local_offer_outlined,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'No bids yet',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Bids will appear here once transporters submit them',
              style: TextStyle(
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        _fetchBids();
      },
      child: ListView.builder(
        controller: _scrollController,
        itemCount: state.bids.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= state.bids.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final bid = state.bids[index];
          return FutureBuilder<UserModel?>(
            future: context.read<AuthService>().getCurrentUser(),
            builder: (context, snapshot) {
              final currentUser = snapshot.data;

              // Debug logging
              print('DEBUG BidListScreen:');
              print(
                  '  Current user ID: ${currentUser?.id} (type: ${currentUser?.id.runtimeType})');
              print(
                  '  Load client ID: ${widget.load.clientId} (type: ${widget.load.clientId.runtimeType})');
              print('  Current user role: ${currentUser?.role}');
              print('  Bid status: ${bid.status}');

              // Fix the comparison - ensure both are the same type
              final isLoadOwner = currentUser?.id != null &&
                  widget.load.clientId != null &&
                  int.parse(currentUser!.id) == widget.load.clientId;

              print('  Is load owner: $isLoadOwner');
              print(
                  '  Should show accept button: ${isLoadOwner && bid.status == BidStatus.pending}');

              return BidCard(
                bid: bid,
                currentUserRole: currentUser?.role != null
                    ? UserRoleExtension.fromString(currentUser!.role)
                    : null,
                isLoadOwner: isLoadOwner,
                onAccept: isLoadOwner && bid.status == BidStatus.pending
                    ? () => _acceptBid(bid)
                    : null,
                onReject: isLoadOwner && bid.status == BidStatus.pending
                    ? () => _rejectBid(bid)
                    : null,
                onViewDetails: () => _viewBidDetails(bid),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Error loading bids',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: const TextStyle(color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _fetchBids,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(LoadStatus status) {
    switch (status) {
      case LoadStatus.posted:
        return Colors.blue;
      case LoadStatus.biddingClosed:
        return Colors.orange;
      case LoadStatus.assigned:
        return Colors.purple;
      case LoadStatus.inTransit:
        return Colors.green;
      case LoadStatus.delivered:
        return Colors.teal;
      case LoadStatus.completed:
        return Colors.green;
      case LoadStatus.cancelled:
        return Colors.red;
    }
  }

  void _acceptBid(BidModel bid) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Accept Bid'),
        content: Text(
          'Are you sure you want to accept the bid from ${bid.companyName} for \$${bid.amount.toStringAsFixed(2)}?\n\nThis will assign the load to this company and reject all other pending bids.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Use LoadBloc to assign the load to the company
              context.read<LoadBloc>().add(
                    LoadAssignRequested(
                      loadId: widget.load.id!,
                      companyId: bid.companyId,
                    ),
                  );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Accept'),
          ),
        ],
      ),
    );
  }

  void _rejectBid(BidModel bid) {
    showDialog<void>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reject Bid'),
        content: Text(
          'Are you sure you want to reject the bid from ${bid.companyName}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // Note: Backend doesn't have individual bid rejection endpoint
              // This would need to be implemented in the backend
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Individual bid rejection not yet implemented'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  void _viewBidDetails(BidModel bid) {
    // Navigate to bid details screen
    // This will be implemented when we create the bid details screen
    context.push('/bids/${bid.id}');
  }
}
