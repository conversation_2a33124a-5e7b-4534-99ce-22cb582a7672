#!/bin/bash

# LogiPool Production Deployment Script
# This script deploys the LogiPool application to production

set -e  # Exit on any error

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_COMPOSE_FILE="docker-compose.prod.yml"
ENV_FILE=".env"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "This script should not be run as root for security reasons"
        exit 1
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check if .env file exists
    if [[ ! -f "$PROJECT_ROOT/$ENV_FILE" ]]; then
        log_error "Environment file $ENV_FILE not found. Please copy .env.example to .env and configure it."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

# Backup current deployment
backup_current() {
    log_info "Creating backup of current deployment..."
    
    BACKUP_DIR="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup database
    if docker-compose -f "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE" ps postgres | grep -q "Up"; then
        log_info "Backing up database..."
        docker-compose -f "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE" exec -T postgres pg_dump -U logipool logipool_prod > "$BACKUP_DIR/database_backup.sql"
        log_success "Database backup created"
    fi
    
    # Backup uploaded files
    if [[ -d "$PROJECT_ROOT/uploads" ]]; then
        log_info "Backing up uploaded files..."
        cp -r "$PROJECT_ROOT/uploads" "$BACKUP_DIR/"
        log_success "Files backup created"
    fi
    
    log_success "Backup created at $BACKUP_DIR"
}

# Build application
build_application() {
    log_info "Building application..."
    
    cd "$PROJECT_ROOT"
    
    # Build Docker image
    docker-compose -f "$DOCKER_COMPOSE_FILE" build --no-cache
    
    log_success "Application built successfully"
}

# Deploy application
deploy_application() {
    log_info "Deploying application..."
    
    cd "$PROJECT_ROOT"
    
    # Stop current containers
    log_info "Stopping current containers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Start new containers
    log_info "Starting new containers..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log_success "Application deployed successfully"
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    local max_attempts=30
    local attempt=1
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f http://localhost:8080/actuator/health &> /dev/null; then
            log_success "Health check passed"
            return 0
        fi
        
        log_info "Health check attempt $attempt/$max_attempts failed, retrying in 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Health check failed after $max_attempts attempts"
    return 1
}

# Cleanup old images
cleanup() {
    log_info "Cleaning up old Docker images..."
    
    # Remove dangling images
    docker image prune -f
    
    # Remove old images (keep last 3 versions)
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}" | \
    grep logipool | \
    tail -n +4 | \
    awk '{print $3}' | \
    xargs -r docker rmi
    
    log_success "Cleanup completed"
}

# Rollback function
rollback() {
    log_warning "Rolling back to previous version..."
    
    # This is a simplified rollback - in production you might want to restore from backup
    docker-compose -f "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE" down
    
    # Here you would restore the previous version
    # For now, we'll just restart the services
    docker-compose -f "$PROJECT_ROOT/$DOCKER_COMPOSE_FILE" up -d
    
    log_warning "Rollback completed"
}

# Main deployment function
main() {
    log_info "Starting LogiPool production deployment..."
    
    # Change to project root
    cd "$PROJECT_ROOT"
    
    # Run checks
    check_root
    check_prerequisites
    
    # Create backup
    backup_current
    
    # Build and deploy
    if build_application && deploy_application; then
        # Health check
        if health_check; then
            cleanup
            log_success "Deployment completed successfully!"
        else
            log_error "Health check failed, rolling back..."
            rollback
            exit 1
        fi
    else
        log_error "Deployment failed, rolling back..."
        rollback
        exit 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "backup")
        backup_current
        ;;
    "build")
        build_application
        ;;
    "deploy")
        deploy_application
        ;;
    "health")
        health_check
        ;;
    "cleanup")
        cleanup
        ;;
    "rollback")
        rollback
        ;;
    *)
        main
        ;;
esac
