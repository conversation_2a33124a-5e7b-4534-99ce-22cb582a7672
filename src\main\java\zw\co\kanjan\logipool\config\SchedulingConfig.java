package zw.co.kanjan.logipool.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

@Slf4j
@Configuration
@EnableScheduling
public class SchedulingConfig implements SchedulingConfigurer {

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        // Use a thread pool for scheduled tasks to avoid blocking
        taskRegistrar.setScheduler(taskExecutor());
    }

    private Executor taskExecutor() {
        return Executors.newScheduledThreadPool(5, r -> {
            Thread thread = new Thread(r);
            thread.setName("LogiPool-Scheduler-" + thread.getId());
            thread.setDaemon(true);
            return thread;
        });
    }
}
