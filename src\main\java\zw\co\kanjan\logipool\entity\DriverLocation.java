package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "driver_locations")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DriverLocation {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "driver_id", nullable = false)
    @NotNull
    private User driver;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "load_id")
    private Load load;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
    
    @NotNull
    @Column(precision = 10, scale = 8)
    private BigDecimal latitude;
    
    @NotNull
    @Column(precision = 11, scale = 8)
    private BigDecimal longitude;
    
    @Column(precision = 8, scale = 2)
    private BigDecimal accuracy; // in meters
    
    @Column(precision = 8, scale = 2)
    private BigDecimal speed; // in km/h
    
    @Column(precision = 6, scale = 2)
    private BigDecimal heading; // in degrees (0-360)
    
    @Column(precision = 8, scale = 2)
    private BigDecimal altitude; // in meters
    
    private String address;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Builder.Default
    private LocationSource source = LocationSource.GPS;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Builder.Default
    private LocationStatus status = LocationStatus.ACTIVE;
    
    @Builder.Default
    private Boolean isShared = true;
    
    @Builder.Default
    private Boolean isOnDuty = false;
    
    private String deviceId;
    
    private String sessionId;
    
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    @CreationTimestamp
    private LocalDateTime timestamp;
    
    private LocalDateTime expiresAt;
    
    public enum LocationSource {
        GPS,            // GPS coordinates
        NETWORK,        // Network-based location
        MANUAL,         // Manually entered
        ESTIMATED       // Estimated based on route
    }
    
    public enum LocationStatus {
        ACTIVE,         // Currently active location
        HISTORICAL,     // Historical location data
        ESTIMATED,      // Estimated location
        OFFLINE         // Driver is offline
    }
}
