import 'package:json_annotation/json_annotation.dart';

part 'payment_model.g.dart';

@JsonSerializable()
class PaymentModel {
  final int? id;
  final double amount;
  final double commissionAmount;
  final double netAmount;
  final double commissionRate;
  final PaymentStatus status;
  final PaymentMethod method;
  final PaymentType type;
  final String? transactionId;
  final String? paymentGatewayReference;
  final String? description;
  final String? notes;
  final int? loadId;
  final String? loadTitle;
  final int? bidId;
  final int? payerId;
  final String? payerName;
  final int? payeeId;
  final String? payeeName;
  final int? invoiceId;
  final String? invoiceNumber;
  final DateTime? paidAt;
  final DateTime? dueDate;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const PaymentModel({
    this.id,
    required this.amount,
    required this.commissionAmount,
    required this.netAmount,
    required this.commissionRate,
    required this.status,
    required this.method,
    required this.type,
    this.transactionId,
    this.paymentGatewayReference,
    this.description,
    this.notes,
    this.loadId,
    this.loadTitle,
    this.bidId,
    this.payerId,
    this.payerName,
    this.payeeId,
    this.payeeName,
    this.invoiceId,
    this.invoiceNumber,
    this.paidAt,
    this.dueDate,
    this.createdAt,
    this.updatedAt,
  });

  factory PaymentModel.fromJson(Map<String, dynamic> json) => _$PaymentModelFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentModelToJson(this);

  PaymentModel copyWith({
    int? id,
    double? amount,
    double? commissionAmount,
    double? netAmount,
    double? commissionRate,
    PaymentStatus? status,
    PaymentMethod? method,
    PaymentType? type,
    String? transactionId,
    String? paymentGatewayReference,
    String? description,
    String? notes,
    int? loadId,
    String? loadTitle,
    int? bidId,
    int? payerId,
    String? payerName,
    int? payeeId,
    String? payeeName,
    int? invoiceId,
    String? invoiceNumber,
    DateTime? paidAt,
    DateTime? dueDate,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentModel(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      netAmount: netAmount ?? this.netAmount,
      commissionRate: commissionRate ?? this.commissionRate,
      status: status ?? this.status,
      method: method ?? this.method,
      type: type ?? this.type,
      transactionId: transactionId ?? this.transactionId,
      paymentGatewayReference: paymentGatewayReference ?? this.paymentGatewayReference,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      loadId: loadId ?? this.loadId,
      loadTitle: loadTitle ?? this.loadTitle,
      bidId: bidId ?? this.bidId,
      payerId: payerId ?? this.payerId,
      payerName: payerName ?? this.payerName,
      payeeId: payeeId ?? this.payeeId,
      payeeName: payeeName ?? this.payeeName,
      invoiceId: invoiceId ?? this.invoiceId,
      invoiceNumber: invoiceNumber ?? this.invoiceNumber,
      paidAt: paidAt ?? this.paidAt,
      dueDate: dueDate ?? this.dueDate,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

enum PaymentStatus {
  @JsonValue('PENDING')
  pending,
  @JsonValue('PROCESSING')
  processing,
  @JsonValue('COMPLETED')
  completed,
  @JsonValue('FAILED')
  failed,
  @JsonValue('CANCELLED')
  cancelled,
  @JsonValue('REFUNDED')
  refunded,
  @JsonValue('DISPUTED')
  disputed;

  String get displayName {
    switch (this) {
      case PaymentStatus.pending:
        return 'Pending';
      case PaymentStatus.processing:
        return 'Processing';
      case PaymentStatus.completed:
        return 'Completed';
      case PaymentStatus.failed:
        return 'Failed';
      case PaymentStatus.cancelled:
        return 'Cancelled';
      case PaymentStatus.refunded:
        return 'Refunded';
      case PaymentStatus.disputed:
        return 'Disputed';
    }
  }

  String get description {
    switch (this) {
      case PaymentStatus.pending:
        return 'Payment is awaiting processing';
      case PaymentStatus.processing:
        return 'Payment is being processed';
      case PaymentStatus.completed:
        return 'Payment has been completed successfully';
      case PaymentStatus.failed:
        return 'Payment processing failed';
      case PaymentStatus.cancelled:
        return 'Payment was cancelled';
      case PaymentStatus.refunded:
        return 'Payment has been refunded';
      case PaymentStatus.disputed:
        return 'Payment is under dispute';
    }
  }

  bool get isCompleted => this == PaymentStatus.completed;
  bool get isPending => this == PaymentStatus.pending;
  bool get isProcessing => this == PaymentStatus.processing;
  bool get isFailed => this == PaymentStatus.failed;
  bool get isCancelled => this == PaymentStatus.cancelled;
  bool get isRefunded => this == PaymentStatus.refunded;
  bool get isDisputed => this == PaymentStatus.disputed;
  bool get isActive => isPending || isProcessing;
  bool get isFinalized => isCompleted || isFailed || isCancelled || isRefunded;
}

enum PaymentMethod {
  @JsonValue('CREDIT_CARD')
  creditCard,
  @JsonValue('DEBIT_CARD')
  debitCard,
  @JsonValue('BANK_TRANSFER')
  bankTransfer,
  @JsonValue('MOBILE_MONEY')
  mobileMoney,
  @JsonValue('PAYPAL')
  paypal,
  @JsonValue('STRIPE')
  stripe,
  @JsonValue('CASH')
  cash;

  String get displayName {
    switch (this) {
      case PaymentMethod.creditCard:
        return 'Credit Card';
      case PaymentMethod.debitCard:
        return 'Debit Card';
      case PaymentMethod.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethod.mobileMoney:
        return 'Mobile Money';
      case PaymentMethod.paypal:
        return 'PayPal';
      case PaymentMethod.stripe:
        return 'Stripe';
      case PaymentMethod.cash:
        return 'Cash';
    }
  }

  String get description {
    switch (this) {
      case PaymentMethod.creditCard:
        return 'Pay using credit card';
      case PaymentMethod.debitCard:
        return 'Pay using debit card';
      case PaymentMethod.bankTransfer:
        return 'Direct bank transfer';
      case PaymentMethod.mobileMoney:
        return 'Mobile money payment';
      case PaymentMethod.paypal:
        return 'PayPal payment';
      case PaymentMethod.stripe:
        return 'Stripe payment gateway';
      case PaymentMethod.cash:
        return 'Cash payment';
    }
  }

  bool get requiresCardDetails => this == PaymentMethod.creditCard || this == PaymentMethod.debitCard;
  bool get requiresBankDetails => this == PaymentMethod.bankTransfer;
  bool get requiresMobileDetails => this == PaymentMethod.mobileMoney;
  bool get isOnline => this != PaymentMethod.cash;
}

enum PaymentType {
  @JsonValue('LOAD_PAYMENT')
  loadPayment,
  @JsonValue('COMMISSION_PAYMENT')
  commissionPayment,
  @JsonValue('REFUND')
  refund,
  @JsonValue('PENALTY')
  penalty,
  @JsonValue('BONUS')
  bonus;

  String get displayName {
    switch (this) {
      case PaymentType.loadPayment:
        return 'Load Payment';
      case PaymentType.commissionPayment:
        return 'Commission Payment';
      case PaymentType.refund:
        return 'Refund';
      case PaymentType.penalty:
        return 'Penalty';
      case PaymentType.bonus:
        return 'Bonus';
    }
  }

  String get description {
    switch (this) {
      case PaymentType.loadPayment:
        return 'Payment for load transportation';
      case PaymentType.commissionPayment:
        return 'Platform commission payment';
      case PaymentType.refund:
        return 'Refund payment';
      case PaymentType.penalty:
        return 'Penalty payment';
      case PaymentType.bonus:
        return 'Bonus payment';
    }
  }
}

// Request DTOs
@JsonSerializable()
class PaymentCreateRequest {
  final double amount;
  final PaymentMethod method;
  final PaymentType type;
  final String? description;
  final String? notes;
  final int loadId;
  final int? bidId;
  final int payeeId;
  final DateTime? dueDate;

  const PaymentCreateRequest({
    required this.amount,
    required this.method,
    required this.type,
    this.description,
    this.notes,
    required this.loadId,
    this.bidId,
    required this.payeeId,
    this.dueDate,
  });

  factory PaymentCreateRequest.fromJson(Map<String, dynamic> json) => _$PaymentCreateRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentCreateRequestToJson(this);
}

@JsonSerializable()
class PaymentProcessRequest {
  final int paymentId;
  final PaymentMethod method;
  final String? paymentToken;
  final String? cardNumber;
  final String? expiryMonth;
  final String? expiryYear;
  final String? cvv;
  final String? cardHolderName;
  final String? bankAccount;
  final String? bankCode;
  final String? mobileNumber;
  final String? mobileProvider;

  const PaymentProcessRequest({
    required this.paymentId,
    required this.method,
    this.paymentToken,
    this.cardNumber,
    this.expiryMonth,
    this.expiryYear,
    this.cvv,
    this.cardHolderName,
    this.bankAccount,
    this.bankCode,
    this.mobileNumber,
    this.mobileProvider,
  });

  factory PaymentProcessRequest.fromJson(Map<String, dynamic> json) => _$PaymentProcessRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentProcessRequestToJson(this);
}

@JsonSerializable()
class PaymentUpdateRequest {
  final PaymentStatus? status;
  final PaymentMethod? method;
  final String? notes;
  final DateTime? dueDate;

  const PaymentUpdateRequest({
    this.status,
    this.method,
    this.notes,
    this.dueDate,
  });

  factory PaymentUpdateRequest.fromJson(Map<String, dynamic> json) => _$PaymentUpdateRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentUpdateRequestToJson(this);
}

@JsonSerializable()
class PaymentStatusResponse {
  final int paymentId;
  final PaymentStatus status;
  final String? transactionId;
  final String? gatewayReference;
  final String? message;
  final DateTime? processedAt;

  const PaymentStatusResponse({
    required this.paymentId,
    required this.status,
    this.transactionId,
    this.gatewayReference,
    this.message,
    this.processedAt,
  });

  factory PaymentStatusResponse.fromJson(Map<String, dynamic> json) => _$PaymentStatusResponseFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentStatusResponseToJson(this);
}

@JsonSerializable()
class PaymentSummary {
  final double totalAmount;
  final double totalCommission;
  final double netAmount;
  final int totalCount;
  final int pendingCount;
  final int completedCount;
  final int failedCount;

  const PaymentSummary({
    required this.totalAmount,
    required this.totalCommission,
    required this.netAmount,
    required this.totalCount,
    required this.pendingCount,
    required this.completedCount,
    required this.failedCount,
  });

  factory PaymentSummary.fromJson(Map<String, dynamic> json) => _$PaymentSummaryFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentSummaryToJson(this);
}
