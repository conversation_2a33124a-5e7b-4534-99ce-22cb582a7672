package zw.co.kanjan.logipool.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessagePreparator;

import jakarta.mail.Session;
import jakarta.mail.internet.MimeMessage;
import java.io.InputStream;
import java.util.Properties;

/**
 * Test configuration for JavaMailSender when email is disabled
 * This provides a mock implementation to avoid dependency injection issues
 */
@Slf4j
@Configuration
public class TestMailConfig {

    @Bean
    @Primary
    @ConditionalOnProperty(name = "app.notification.email.enabled", havingValue = "false", matchIfMissing = false)
    public JavaMailSender mockJavaMailSender() {
        log.info("Creating mock JavaMailSender for testing (email disabled)");
        return new MockJavaMailSender();
    }

    /**
     * Mock implementation of JavaMailSender for testing purposes
     */
    private static class MockJavaMailSender implements JavaMailSender {

        @Override
        public MimeMessage createMimeMessage() {
            Properties props = new Properties();
            Session session = Session.getDefaultInstance(props);
            return new MimeMessage(session);
        }

        @Override
        public MimeMessage createMimeMessage(InputStream contentStream) {
            Properties props = new Properties();
            Session session = Session.getDefaultInstance(props);
            try {
                return new MimeMessage(session, contentStream);
            } catch (Exception e) {
                log.warn("Failed to create MimeMessage from InputStream in mock", e);
                return new MimeMessage(session);
            }
        }

        @Override
        public void send(MimeMessage mimeMessage) {
            log.debug("Mock JavaMailSender: Email sending disabled - message not sent");
        }

        @Override
        public void send(MimeMessage... mimeMessages) {
            log.debug("Mock JavaMailSender: Email sending disabled - {} messages not sent", mimeMessages.length);
        }

        @Override
        public void send(MimeMessagePreparator mimeMessagePreparator) {
            log.debug("Mock JavaMailSender: Email sending disabled - prepared message not sent");
        }

        @Override
        public void send(MimeMessagePreparator... mimeMessagePreparators) {
            log.debug("Mock JavaMailSender: Email sending disabled - {} prepared messages not sent", mimeMessagePreparators.length);
        }

        @Override
        public void send(SimpleMailMessage simpleMessage) {
            log.debug("Mock JavaMailSender: Email sending disabled - simple message not sent");
        }

        @Override
        public void send(SimpleMailMessage... simpleMessages) {
            log.debug("Mock JavaMailSender: Email sending disabled - {} simple messages not sent", simpleMessages.length);
        }
    }
}
