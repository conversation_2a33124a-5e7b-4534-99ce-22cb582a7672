# LogiPool Deployment with <PERSON><PERSON><PERSON><PERSON> and Let's Encrypt

This guide covers deploying the LogiPool backend application using Docker containers with <PERSON><PERSON><PERSON><PERSON> as a reverse proxy and Let's Encrypt for SSL certificates on Ubuntu Linux.

## Overview

The deployment includes:
- **Traefik**: Reverse proxy with automatic SSL certificate management
- **PostgreSQL**: Primary database
- **Redis**: Caching and session storage
- **LogiPool Backend**: Spring Boot application
- **pgAdmin**: Database administration tool (optional)

## Prerequisites

### System Requirements
- Ubuntu 20.04 LTS or newer
- Docker 20.10+ and Docker Compose 2.0+
- Domain name pointing to your server: `api-logistics.kanjan.co.zw`
- Minimum 4-8GB RAM, 100GB disk space
- Ports 80 and 443 open for HTTP/HTTPS traffic

### Domain Configuration
Ensure your domain `api-logistics.kanjan.co.zw` points to your server's IP address:
```bash
# Check DNS resolution
nslookup api-logistics.kanjan.co.zw
```

## Installation Steps

### 1. Install Docker and Docker Compose

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Add user to docker group
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Logout and login again for group changes to take effect
```

### 2. Clone and Configure Application

```bash
# Clone the repository (or upload your code)
git clone <your-repo-url> /opt/logipool
cd /opt/logipool

# Copy environment template
cp .env.production .env

# Edit environment variables
nano .env
```

### 3. Configure Environment Variables

Edit the `.env` file with your specific values:

```bash
# Domain Configuration
DOMAIN_NAME=api-logistics.kanjan.co.zw
ACME_EMAIL=<EMAIL>

# Database Configuration
DATABASE_PASSWORD=your_secure_database_password
PGADMIN_PASSWORD=your_secure_pgadmin_password

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password

# JWT Configuration
JWT_SECRET=your_very_long_and_secure_jwt_secret_key

# Email Configuration
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_email_password

# AWS S3 Configuration (if using cloud storage)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET_NAME=logipool-documents-prod

# Other service configurations...
```

### 4. Setup Docker Networks

```bash
# Run network setup script
./scripts/setup-networks.sh
```

### 5. Setup SSL Certificates

```bash
# Run SSL setup script
./scripts/ssl-setup.sh
```

### 6. Deploy Application

```bash
# Run deployment script
./scripts/deploy-traefik.sh
```

## Manual Deployment Steps

If you prefer manual deployment:

### 1. Create Docker Network
```bash
docker network create traefik-public
```

### 2. Setup SSL Storage
```bash
mkdir -p traefik_letsencrypt
echo '{}' > traefik_letsencrypt/acme.json
chmod 600 traefik_letsencrypt/acme.json
```

### 3. Start Services
```bash
docker-compose -f docker-compose.traefik.yml up -d
```

### 4. Monitor Deployment
```bash
# Check service status
docker-compose -f docker-compose.traefik.yml ps

# View logs
docker-compose -f docker-compose.traefik.yml logs -f

# Check health
./scripts/health-check.sh
```

## Service URLs

After successful deployment:

- **Main API**: https://api-logistics.kanjan.co.zw
- **API Health Check**: https://api-logistics.kanjan.co.zw/actuator/health
- **pgAdmin**: https://pgadmin.api-logistics.kanjan.co.zw
- **Traefik Dashboard**: http://your-server-ip:8080 (disable in production)

## Monitoring and Maintenance

### Health Checks
```bash
# Run comprehensive health check
./scripts/health-check.sh

# Check specific components
./scripts/health-check.sh docker
./scripts/health-check.sh ssl
./scripts/health-check.sh database
```

### SSL Certificate Management
```bash
# Check certificate status
./scripts/ssl-setup.sh status

# Backup certificates
./scripts/ssl-setup.sh backup

# Verify DNS configuration
./scripts/ssl-setup.sh verify-dns
```

### Backup and Restore
```bash
# Create backup
./scripts/deploy-traefik.sh backup

# View deployment status
./scripts/deploy-traefik.sh status
```

### Log Management
```bash
# View application logs
docker-compose -f docker-compose.traefik.yml logs logipool-backend

# View Traefik logs
docker-compose -f docker-compose.traefik.yml logs traefik

# View all logs
docker-compose -f docker-compose.traefik.yml logs
```

## Troubleshooting

### Common Issues

1. **SSL Certificate Not Generated**
   - Check DNS configuration: `./scripts/ssl-setup.sh verify-dns`
   - Verify domain points to server IP
   - Check Let's Encrypt rate limits
   - Review Traefik logs: `docker logs logipool-traefik`

2. **Application Not Accessible**
   - Check service health: `./scripts/health-check.sh`
   - Verify Docker networks: `docker network ls`
   - Check firewall settings: `sudo ufw status`

3. **Database Connection Issues**
   - Check PostgreSQL logs: `docker logs logipool-postgres-prod`
   - Verify environment variables in `.env`
   - Test database connectivity: `./scripts/health-check.sh database`

4. **High Resource Usage**
   - Monitor resources: `./scripts/health-check.sh memory`
   - Check disk space: `./scripts/health-check.sh disk`
   - Review container resource limits

### Debug Commands
```bash
# Check container status
docker ps -a

# Inspect container configuration
docker inspect logipool-backend-prod

# Check network connectivity
docker exec logipool-backend-prod ping postgres

# View environment variables
docker exec logipool-backend-prod env | grep -E "(DATABASE|REDIS|JWT)"
```

## Security Considerations

1. **Environment Variables**: Store sensitive data securely
2. **Firewall**: Only expose ports 80, 443, and 22 (SSH)
3. **Updates**: Regularly update Docker images and system packages
4. **Backups**: Implement automated backup strategy
5. **Monitoring**: Set up log monitoring and alerting
6. **Access Control**: Restrict pgAdmin access with IP whitelist

## Production Optimizations

1. **Disable Traefik Dashboard**: Set `TRAEFIK_DASHBOARD_ENABLED=false`
2. **Resource Limits**: Configure Docker resource constraints
3. **Log Rotation**: Implement log rotation for container logs
4. **Monitoring**: Add Prometheus/Grafana for metrics
5. **Backup Automation**: Schedule automated backups
6. **Health Checks**: Set up external monitoring services

## Support

For issues and questions:
- Check logs: `docker-compose -f docker-compose.traefik.yml logs`
- Run health checks: `./scripts/health-check.sh`
- Review this documentation
- Contact system administrator
