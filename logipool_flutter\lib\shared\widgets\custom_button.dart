import 'package:flutter/material.dart';

enum ButtonVariant {
  primary,
  secondary,
  outline,
  text,
  danger,
}

enum ButtonSize {
  small,
  medium,
  large,
}

class CustomButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonVariant variant;
  final ButtonSize size;
  final bool isLoading;
  final bool isFullWidth;
  final Widget? icon;
  final Widget? leadingIcon;
  final Widget? trailingIcon;

  const CustomButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.variant = ButtonVariant.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
    this.icon,
    this.leadingIcon,
    this.trailingIcon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Button dimensions based on size
    double height;
    EdgeInsets padding;
    TextStyle textStyle;

    switch (size) {
      case ButtonSize.small:
        height = 32;
        padding = const EdgeInsets.symmetric(horizontal: 12, vertical: 6);
        textStyle = theme.textTheme.labelSmall!;
        break;
      case ButtonSize.medium:
        height = 40;
        padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 8);
        textStyle = theme.textTheme.labelMedium!;
        break;
      case ButtonSize.large:
        height = 48;
        padding = const EdgeInsets.symmetric(horizontal: 24, vertical: 12);
        textStyle = theme.textTheme.labelLarge!;
        break;
    }

    // Button colors based on variant
    Color backgroundColor;
    Color foregroundColor;
    Color? borderColor;

    switch (variant) {
      case ButtonVariant.primary:
        backgroundColor = colorScheme.primary;
        foregroundColor = colorScheme.onPrimary;
        borderColor = null;
        break;
      case ButtonVariant.secondary:
        backgroundColor = colorScheme.secondary;
        foregroundColor = colorScheme.onSecondary;
        borderColor = null;
        break;
      case ButtonVariant.outline:
        backgroundColor = Colors.transparent;
        foregroundColor = colorScheme.primary;
        borderColor = colorScheme.outline;
        break;
      case ButtonVariant.text:
        backgroundColor = Colors.transparent;
        foregroundColor = colorScheme.primary;
        borderColor = null;
        break;
      case ButtonVariant.danger:
        backgroundColor = colorScheme.error;
        foregroundColor = colorScheme.onError;
        borderColor = null;
        break;
    }

    Widget buttonChild = Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (leadingIcon != null) ...[
          leadingIcon!,
          const SizedBox(width: 8),
        ],
        if (icon != null) ...[
          icon!,
          const SizedBox(width: 8),
        ],
        if (isLoading)
          SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(foregroundColor),
            ),
          )
        else
          Text(
            text,
            style: textStyle.copyWith(
              color: foregroundColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        if (trailingIcon != null) ...[
          const SizedBox(width: 8),
          trailingIcon!,
        ],
      ],
    );

    Widget button = SizedBox(
      height: height,
      width: isFullWidth ? double.infinity : null,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          padding: padding,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: borderColor != null
                ? BorderSide(color: borderColor)
                : BorderSide.none,
          ),
          elevation: variant == ButtonVariant.text || variant == ButtonVariant.outline ? 0 : 2,
        ),
        child: buttonChild,
      ),
    );

    return button;
  }
}
