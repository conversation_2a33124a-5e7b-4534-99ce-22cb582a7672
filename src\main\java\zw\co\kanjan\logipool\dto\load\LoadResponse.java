package zw.co.kanjan.logipool.dto.load;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.Load;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoadResponse {
    
    private Long id;
    private String trackingNumber;
    private String title;
    private String description;
    private String cargoType;
    private BigDecimal weight;
    private String weightUnit;
    private BigDecimal volume;
    private String volumeUnit;
    private String pickupLocation;
    private BigDecimal pickupLatitude;
    private BigDecimal pickupLongitude;
    private String deliveryLocation;
    private BigDecimal deliveryLatitude;
    private BigDecimal deliveryLongitude;
    private LocalDateTime pickupDate;
    private LocalDateTime deliveryDate;
    private BigDecimal estimatedDistance;
    private String distanceUnit;
    private Load.LoadType loadType;
    private Load.PaymentType paymentType;
    private BigDecimal paymentRate;
    private String paymentUnit;
    private BigDecimal estimatedValue;
    private Load.LoadStatus status;
    private Load.Priority priority;
    private Boolean isVerified;
    private Boolean requiresInsurance;
    private Boolean requiresSpecialHandling;
    private String specialInstructions;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime biddingClosesAt;
    private Long clientId;
    private String clientName;
    private Long assignedCompanyId;
    private String assignedCompanyName;
    private Integer bidCount;
}
