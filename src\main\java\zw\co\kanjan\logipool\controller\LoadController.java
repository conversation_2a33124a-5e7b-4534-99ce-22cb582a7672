package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.load.LoadCreateRequest;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.dto.load.LoadStatusUpdateRequest;
import zw.co.kanjan.logipool.dto.load.LoadUpdateRequest;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.service.LoadService;

@RestController
@RequestMapping("/api/loads")
@RequiredArgsConstructor
@Tag(name = "Loads", description = "Load management APIs")
public class LoadController {
    
    private final LoadService loadService;
    
    @PostMapping
    @PreAuthorize("hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Create new load", description = "Create a new load posting")
    public ResponseEntity<LoadResponse> createLoad(@Valid @RequestBody LoadCreateRequest request,
                                                  Authentication authentication) {
        LoadResponse response = loadService.createLoad(request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @GetMapping
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get all loads", description = "Get paginated list of loads visible to current user")
    public ResponseEntity<Page<LoadResponse>> getAllLoads(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            Authentication authentication) {

        Sort sort = sortDir.equalsIgnoreCase("desc") ?
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<LoadResponse> loads = loadService.getAllLoads(authentication.getName(), pageable);
        return ResponseEntity.ok(loads);
    }
    
    @GetMapping("/verified")
    @Operation(summary = "Get verified loads", description = "Get paginated list of verified loads")
    public ResponseEntity<Page<LoadResponse>> getVerifiedLoads(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<LoadResponse> loads = loadService.getVerifiedLoads(pageable);
        return ResponseEntity.ok(loads);
    }
    
    @GetMapping("/status/{status}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get loads by status", description = "Get paginated list of loads by status visible to current user")
    public ResponseEntity<Page<LoadResponse>> getLoadsByStatus(
            @PathVariable Load.LoadStatus status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            Authentication authentication) {

        Sort sort = sortDir.equalsIgnoreCase("desc") ?
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<LoadResponse> loads = loadService.getLoadsByStatus(status, authentication.getName(), pageable);
        return ResponseEntity.ok(loads);
    }
    
    @GetMapping("/{id}")
    @Operation(summary = "Get load by ID", description = "Get load details by ID")
    public ResponseEntity<LoadResponse> getLoadById(@PathVariable Long id) {
        LoadResponse load = loadService.getLoadById(id);
        return ResponseEntity.ok(load);
    }

    @GetMapping("/track/{trackingNumber}")
    @Operation(summary = "Track load by tracking number", description = "Get load details by tracking number for public access")
    public ResponseEntity<LoadResponse> trackLoad(@PathVariable String trackingNumber) {
        LoadResponse load = loadService.getLoadByTrackingNumber(trackingNumber);
        return ResponseEntity.ok(load);
    }
    
    @GetMapping("/my-loads")
    @PreAuthorize("hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Get my loads", description = "Get paginated list of current user's loads")
    public ResponseEntity<Page<LoadResponse>> getMyLoads(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            Authentication authentication) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<LoadResponse> loads = loadService.getMyLoads(authentication.getName(), pageable);
        return ResponseEntity.ok(loads);
    }
    
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Update load", description = "Update a load posting")
    public ResponseEntity<LoadResponse> updateLoad(@PathVariable Long id,
                                                  @Valid @RequestBody LoadUpdateRequest request,
                                                  Authentication authentication) {
        LoadResponse response = loadService.updateLoad(id, request, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Delete load", description = "Delete a load posting")
    public ResponseEntity<Void> deleteLoad(@PathVariable Long id, Authentication authentication) {
        loadService.deleteLoad(id, authentication.getName());
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/{id}/assign/{companyId}")
    @PreAuthorize("hasRole('CLIENT') or hasRole('ADMIN')")
    @Operation(summary = "Assign load", description = "Assign load to a company")
    public ResponseEntity<LoadResponse> assignLoad(@PathVariable Long id,
                                                  @PathVariable Long companyId,
                                                  Authentication authentication) {
        LoadResponse response = loadService.assignLoad(id, companyId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}/status")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Update load status", description = "Update load status (Admin only)")
    public ResponseEntity<LoadResponse> updateLoadStatus(@PathVariable Long id,
                                                        @RequestParam Load.LoadStatus status) {
        LoadResponse response = loadService.updateLoadStatus(id, status);
        return ResponseEntity.ok(response);
    }

    @PutMapping("/{id}/status/update")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Update load status with validation",
               description = "Update load status with proper permission validation and status transition checks")
    public ResponseEntity<LoadResponse> updateLoadStatusWithValidation(
            @PathVariable Long id,
            @RequestParam Load.LoadStatus status,
            Authentication authentication) {
        LoadResponse response = loadService.updateLoadStatus(id, status, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @PutMapping("/status/update")
    @PreAuthorize("hasRole('CLIENT') or hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Update load status with notes",
               description = "Update load status with notes and location information")
    public ResponseEntity<LoadResponse> updateLoadStatusWithNotes(
            @Valid @RequestBody LoadStatusUpdateRequest request,
            Authentication authentication) {
        LoadResponse response = loadService.updateLoadStatusWithNotes(
                request.getLoadId(),
                request.getStatus(),
                request.getNotes(),
                authentication.getName());
        return ResponseEntity.ok(response);
    }
}
