import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:logipool_flutter/main.dart' as app;
import 'package:logipool_flutter/core/constants/app_constants.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('LogiPool Integration Tests', () {
    testWidgets('App launches and shows login screen', (WidgetTester tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Verify that the app launches successfully
      expect(find.byType(MaterialApp), findsOneWidget);
      
      // Look for login-related elements
      expect(find.text('LogiPool'), findsOneWidget);
    });

    testWidgets('Navigation between screens works', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test navigation (this will depend on your actual navigation structure)
      // For now, just verify the app doesn't crash
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('API configuration is correct', (WidgetTester tester) async {
      // Test that API constants are properly configured
      expect(AppConstants.baseUrl, equals('http://localhost:8080/api'));
      expect(AppConstants.wsUrl, equals('ws://localhost:8080/ws'));
      expect(AppConstants.authEndpoint, equals('/auth'));
      expect(AppConstants.loadsEndpoint, equals('/loads'));
      expect(AppConstants.bidsEndpoint, equals('/bids'));
      expect(AppConstants.companiesEndpoint, equals('/companies'));
      expect(AppConstants.trackingEndpoint, equals('/tracking'));
      expect(AppConstants.documentsEndpoint, equals('/documents'));
      expect(AppConstants.paymentsEndpoint, equals('/payments'));
      expect(AppConstants.notificationsEndpoint, equals('/notifications'));
      expect(AppConstants.adminEndpoint, equals('/admin'));
    });

    testWidgets('App constants are properly defined', (WidgetTester tester) async {
      // Test app configuration constants
      expect(AppConstants.appName, equals('LogiPool'));
      expect(AppConstants.appVersion, equals('1.0.0'));
      expect(AppConstants.apiTimeoutSeconds, equals(30));
      expect(AppConstants.maxRetryAttempts, equals(3));
      
      // Test file upload constants
      expect(AppConstants.maxFileSize, equals(10 * 1024 * 1024));
      expect(AppConstants.allowedImageTypes, isNotEmpty);
      expect(AppConstants.allowedDocumentTypes, isNotEmpty);
      
      // Test business rule constants
      expect(AppConstants.commissionRate, equals(0.075));
      expect(AppConstants.minCommission, equals(5.0));
      expect(AppConstants.maxCommission, equals(500.0));
      
      // Test role constants
      expect(AppConstants.roleAdmin, equals('ADMIN'));
      expect(AppConstants.roleClient, equals('CLIENT'));
      expect(AppConstants.roleTransporter, equals('TRANSPORTER'));
    });

    testWidgets('Status constants are properly defined', (WidgetTester tester) async {
      // Test load status constants
      expect(AppConstants.loadStatusPosted, equals('POSTED'));
      expect(AppConstants.loadStatusAssigned, equals('ASSIGNED'));
      expect(AppConstants.loadStatusInTransit, equals('IN_TRANSIT'));
      expect(AppConstants.loadStatusDelivered, equals('DELIVERED'));
      expect(AppConstants.loadStatusCancelled, equals('CANCELLED'));
      
      // Test bid status constants
      expect(AppConstants.bidStatusPending, equals('PENDING'));
      expect(AppConstants.bidStatusAccepted, equals('ACCEPTED'));
      expect(AppConstants.bidStatusRejected, equals('REJECTED'));
      expect(AppConstants.bidStatusWithdrawn, equals('WITHDRAWN'));
      
      // Test payment status constants
      expect(AppConstants.paymentStatusPending, equals('PENDING'));
      expect(AppConstants.paymentStatusProcessing, equals('PROCESSING'));
      expect(AppConstants.paymentStatusCompleted, equals('COMPLETED'));
      expect(AppConstants.paymentStatusFailed, equals('FAILED'));
      
      // Test verification status constants
      expect(AppConstants.verificationStatusPending, equals('PENDING'));
      expect(AppConstants.verificationStatusVerified, equals('VERIFIED'));
      expect(AppConstants.verificationStatusRejected, equals('REJECTED'));
    });

    testWidgets('Error and success message constants are defined', (WidgetTester tester) async {
      // Test error message constants
      expect(AppConstants.errorNetworkConnection, equals('Network connection error'));
      expect(AppConstants.errorServerError, equals('Server error occurred'));
      expect(AppConstants.errorUnauthorized, equals('Unauthorized access'));
      expect(AppConstants.errorNotFound, equals('Resource not found'));
      expect(AppConstants.errorValidation, equals('Validation error'));
      expect(AppConstants.errorUnknown, equals('Unknown error occurred'));
      
      // Test success message constants
      expect(AppConstants.successLogin, equals('Login successful'));
      expect(AppConstants.successRegistration, equals('Registration successful'));
      expect(AppConstants.successLogout, equals('Logout successful'));
      expect(AppConstants.successUpdate, equals('Update successful'));
      expect(AppConstants.successDelete, equals('Delete successful'));
      expect(AppConstants.successCreate, equals('Created successfully'));
    });

    testWidgets('Map configuration constants are defined', (WidgetTester tester) async {
      // Test map configuration
      expect(AppConstants.defaultLatitude, equals(-17.8292));
      expect(AppConstants.defaultLongitude, equals(31.0522));
      expect(AppConstants.defaultZoom, equals(12.0));
    });

    testWidgets('Pagination constants are defined', (WidgetTester tester) async {
      // Test pagination configuration
      expect(AppConstants.defaultPageSize, equals(20));
      expect(AppConstants.maxPageSize, equals(100));
    });

    testWidgets('Validation constants are defined', (WidgetTester tester) async {
      // Test validation configuration
      expect(AppConstants.minPasswordLength, equals(8));
      expect(AppConstants.maxPasswordLength, equals(128));
      expect(AppConstants.minUsernameLength, equals(3));
      expect(AppConstants.maxUsernameLength, equals(50));
    });

    testWidgets('Storage key constants are defined', (WidgetTester tester) async {
      // Test storage key constants
      expect(AppConstants.tokenKey, equals('auth_token'));
      expect(AppConstants.refreshTokenKey, equals('refresh_token'));
      expect(AppConstants.userKey, equals('user_data'));
      expect(AppConstants.settingsKey, equals('app_settings'));
    });

    testWidgets('Notification type constants are defined', (WidgetTester tester) async {
      // Test notification type constants
      expect(AppConstants.notificationTypeLoad, equals('LOAD'));
      expect(AppConstants.notificationTypeBid, equals('BID'));
      expect(AppConstants.notificationTypePayment, equals('PAYMENT'));
      expect(AppConstants.notificationTypeTracking, equals('TRACKING'));
      expect(AppConstants.notificationTypeSystem, equals('SYSTEM'));
    });

    testWidgets('Tracking status constants are defined', (WidgetTester tester) async {
      // Test tracking status constants
      expect(AppConstants.trackingStatusLoadPosted, equals('LOAD_POSTED'));
      expect(AppConstants.trackingStatusBidAccepted, equals('BID_ACCEPTED'));
      expect(AppConstants.trackingStatusPickupScheduled, equals('PICKUP_SCHEDULED'));
      expect(AppConstants.trackingStatusInTransitToPickup, equals('IN_TRANSIT_TO_PICKUP'));
      expect(AppConstants.trackingStatusArrivedAtPickup, equals('ARRIVED_AT_PICKUP'));
      expect(AppConstants.trackingStatusLoadingInProgress, equals('LOADING_IN_PROGRESS'));
      expect(AppConstants.trackingStatusLoaded, equals('LOADED'));
      expect(AppConstants.trackingStatusInTransitToDelivery, equals('IN_TRANSIT_TO_DELIVERY'));
      expect(AppConstants.trackingStatusArrivedAtDelivery, equals('ARRIVED_AT_DELIVERY'));
      expect(AppConstants.trackingStatusUnloadingInProgress, equals('UNLOADING_IN_PROGRESS'));
      expect(AppConstants.trackingStatusDelivered, equals('DELIVERED'));
      expect(AppConstants.trackingStatusDelayed, equals('DELAYED'));
      expect(AppConstants.trackingStatusIssueReported, equals('ISSUE_REPORTED'));
    });

    testWidgets('Document status constants are defined', (WidgetTester tester) async {
      // Test document status constants
      expect(AppConstants.documentStatusPending, equals('PENDING'));
      expect(AppConstants.documentStatusVerified, equals('VERIFIED'));
      expect(AppConstants.documentStatusRejected, equals('REJECTED'));
      expect(AppConstants.documentStatusExpired, equals('EXPIRED'));
    });
  });
}
