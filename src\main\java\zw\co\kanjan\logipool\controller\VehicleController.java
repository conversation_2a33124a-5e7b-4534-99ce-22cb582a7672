package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import zw.co.kanjan.logipool.dto.VehicleDto;
import zw.co.kanjan.logipool.entity.Vehicle;
import zw.co.kanjan.logipool.service.VehicleService;

import java.security.Principal;

@RestController
@RequestMapping("/api/vehicles")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Vehicle Management", description = "Vehicle management endpoints for companies")
@SecurityRequirement(name = "bearerAuth")
public class VehicleController {

    private final VehicleService vehicleService;

    @PostMapping
    @Operation(summary = "Create a new vehicle", description = "Add a new vehicle to the company fleet")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<VehicleDto.VehicleResponse> createVehicle(
            @Valid @RequestBody VehicleDto.VehicleCreateRequest request,
            Principal principal) {
        
        log.info("Creating vehicle for user: {}", principal.getName());
        VehicleDto.VehicleResponse vehicle = vehicleService.createVehicle(request, principal.getName());
        return ResponseEntity.status(HttpStatus.CREATED).body(vehicle);
    }

    @GetMapping
    @Operation(summary = "Get company vehicles", description = "Get paginated list of company vehicles")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN') or hasRole('DRIVER')")
    public ResponseEntity<Page<VehicleDto.VehicleResponse>> getCompanyVehicles(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "Sort by field") @RequestParam(defaultValue = "createdAt") String sortBy,
            @Parameter(description = "Sort direction") @RequestParam(defaultValue = "desc") String sortDir,
            @Parameter(description = "Filter by status") @RequestParam(required = false) Vehicle.VehicleStatus status,
            @Parameter(description = "Filter by type") @RequestParam(required = false) Vehicle.VehicleType type,
            Principal principal) {
        
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<VehicleDto.VehicleResponse> vehicles = vehicleService.getCompanyVehicles(
                principal.getName(), pageable, status, type);
        
        return ResponseEntity.ok(vehicles);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get vehicle details", description = "Get detailed information about a specific vehicle")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN') or hasRole('DRIVER')")
    public ResponseEntity<VehicleDto.VehicleResponse> getVehicle(
            @Parameter(description = "Vehicle ID") @PathVariable Long id,
            Principal principal) {
        
        VehicleDto.VehicleResponse vehicle = vehicleService.getVehicle(id, principal.getName());
        return ResponseEntity.ok(vehicle);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update vehicle", description = "Update vehicle information")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<VehicleDto.VehicleResponse> updateVehicle(
            @Parameter(description = "Vehicle ID") @PathVariable Long id,
            @Valid @RequestBody VehicleDto.VehicleUpdateRequest request,
            Principal principal) {
        
        log.info("Updating vehicle {} for user: {}", id, principal.getName());
        VehicleDto.VehicleResponse vehicle = vehicleService.updateVehicle(id, request, principal.getName());
        return ResponseEntity.ok(vehicle);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete vehicle", description = "Remove vehicle from the fleet")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<Void> deleteVehicle(
            @Parameter(description = "Vehicle ID") @PathVariable Long id,
            Principal principal) {
        
        log.info("Deleting vehicle {} for user: {}", id, principal.getName());
        vehicleService.deleteVehicle(id, principal.getName());
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{id}/image")
    @Operation(summary = "Upload vehicle image", description = "Upload an image for the vehicle")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<String> uploadVehicleImage(
            @Parameter(description = "Vehicle ID") @PathVariable Long id,
            @Parameter(description = "Image file") @RequestParam("file") MultipartFile file,
            Principal principal) {
        
        log.info("Uploading image for vehicle {} by user: {}", id, principal.getName());
        String imageUrl = vehicleService.uploadVehicleImage(id, file, principal.getName());
        return ResponseEntity.ok(imageUrl);
    }

    @PostMapping("/{id}/request-public-approval")
    @Operation(summary = "Request public approval", description = "Submit vehicle for public visibility approval")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<String> requestPublicApproval(
            @Parameter(description = "Vehicle ID") @PathVariable Long id,
            Principal principal) {
        
        log.info("Requesting public approval for vehicle {} by user: {}", id, principal.getName());
        vehicleService.requestPublicApproval(id, principal.getName());
        return ResponseEntity.ok("Public approval request submitted successfully");
    }

    @PostMapping("/{id}/toggle-public-visibility")
    @Operation(summary = "Toggle public visibility", description = "Enable or disable public visibility for approved vehicles")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<String> togglePublicVisibility(
            @Parameter(description = "Vehicle ID") @PathVariable Long id,
            @Parameter(description = "Enable public visibility") @RequestParam boolean enable,
            Principal principal) {
        
        log.info("Toggling public visibility for vehicle {} to {} by user: {}", id, enable, principal.getName());
        vehicleService.togglePublicVisibility(id, enable, principal.getName());
        return ResponseEntity.ok("Public visibility updated successfully");
    }

    @PostMapping("/{id}/toggle-rental-availability")
    @Operation(summary = "Toggle rental availability", description = "Enable or disable vehicle for rental")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<String> toggleRentalAvailability(
            @Parameter(description = "Vehicle ID") @PathVariable Long id,
            @Parameter(description = "Enable rental availability") @RequestParam boolean enable,
            Principal principal) {
        
        log.info("Toggling rental availability for vehicle {} to {} by user: {}", id, enable, principal.getName());
        vehicleService.toggleRentalAvailability(id, enable, principal.getName());
        return ResponseEntity.ok("Rental availability updated successfully");
    }

    @GetMapping("/types")
    @Operation(summary = "Get vehicle types", description = "Get list of available vehicle types")
    public ResponseEntity<Vehicle.VehicleType[]> getVehicleTypes() {
        return ResponseEntity.ok(Vehicle.VehicleType.values());
    }

    @GetMapping("/statuses")
    @Operation(summary = "Get vehicle statuses", description = "Get list of available vehicle statuses")
    public ResponseEntity<Vehicle.VehicleStatus[]> getVehicleStatuses() {
        return ResponseEntity.ok(Vehicle.VehicleStatus.values());
    }

    @GetMapping("/{id}/approval-status")
    @Operation(summary = "Get approval status", description = "Get public approval status for a vehicle")
    @PreAuthorize("hasRole('COMPANY_OWNER') or hasRole('COMPANY_ADMIN')")
    public ResponseEntity<Vehicle.PublicApprovalStatus> getApprovalStatus(
            @Parameter(description = "Vehicle ID") @PathVariable Long id,
            Principal principal) {
        
        Vehicle.PublicApprovalStatus status = vehicleService.getApprovalStatus(id, principal.getName());
        return ResponseEntity.ok(status);
    }
}
