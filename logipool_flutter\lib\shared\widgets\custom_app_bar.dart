import 'package:flutter/material.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool centerTitle;
  final Widget? bottom;
  final double? bottomHeight;
  final VoidCallback? onBackPressed;

  const CustomAppBar({
    Key? key,
    required this.title,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.centerTitle = true,
    this.bottom,
    this.bottomHeight,
    this.onBackPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: foregroundColor ?? Theme.of(context).colorScheme.onPrimary,
              fontWeight: FontWeight.w600,
            ),
      ),
      actions: actions,
      leading: leading ??
          (automaticallyImplyLeading && Navigator.of(context).canPop()
              ? IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                )
              : null),
      automaticallyImplyLeading: false, // We handle this manually
      backgroundColor: backgroundColor ?? Theme.of(context).colorScheme.primary,
      foregroundColor:
          foregroundColor ?? Theme.of(context).colorScheme.onPrimary,
      elevation: elevation ?? 2,
      centerTitle: centerTitle,
      bottom: bottom != null
          ? PreferredSize(
              preferredSize: Size.fromHeight(bottomHeight ?? 48),
              child: bottom!,
            )
          : null,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom != null ? (bottomHeight ?? 48) : 0),
      );
}

class SearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final String? hintText;
  final ValueChanged<String>? onSearchChanged;
  final VoidCallback? onSearchClear;
  final List<Widget>? actions;
  final bool automaticallyImplyLeading;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const SearchAppBar({
    Key? key,
    required this.title,
    this.hintText,
    this.onSearchChanged,
    this.onSearchClear,
    this.actions,
    this.automaticallyImplyLeading = true,
    this.backgroundColor,
    this.foregroundColor,
  }) : super(key: key);

  @override
  State<SearchAppBar> createState() => _SearchAppBarState();

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _SearchAppBarState extends State<SearchAppBar> {
  bool _isSearching = false;
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _startSearch() {
    setState(() {
      _isSearching = true;
    });
  }

  void _stopSearch() {
    setState(() {
      _isSearching = false;
      _searchController.clear();
    });
    widget.onSearchClear?.call();
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: _isSearching
          ? TextField(
              controller: _searchController,
              autofocus: true,
              decoration: InputDecoration(
                hintText: widget.hintText ?? 'Search...',
                border: InputBorder.none,
                hintStyle: TextStyle(
                  color: widget.foregroundColor?.withOpacity(0.7) ??
                      Theme.of(context).colorScheme.onPrimary.withOpacity(0.7),
                ),
              ),
              style: TextStyle(
                color: widget.foregroundColor ??
                    Theme.of(context).colorScheme.onPrimary,
              ),
              onChanged: widget.onSearchChanged,
            )
          : Text(
              widget.title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: widget.foregroundColor ??
                        Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.w600,
                  ),
            ),
      automaticallyImplyLeading:
          widget.automaticallyImplyLeading && !_isSearching,
      leading: _isSearching
          ? IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: _stopSearch,
              tooltip: 'Cancel Search',
            )
          : null,
      actions: _isSearching
          ? [
              IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  widget.onSearchChanged?.call('');
                },
                tooltip: 'Clear Search',
              ),
            ]
          : [
              IconButton(
                icon: const Icon(Icons.search),
                onPressed: _startSearch,
                tooltip: 'Search',
              ),
              ...?widget.actions,
            ],
      backgroundColor:
          widget.backgroundColor ?? Theme.of(context).colorScheme.primary,
      foregroundColor:
          widget.foregroundColor ?? Theme.of(context).colorScheme.onPrimary,
    );
  }
}
