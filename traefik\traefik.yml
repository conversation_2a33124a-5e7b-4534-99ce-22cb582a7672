# Traefik Static Configuration
# This file contains the static configuration for Traefik

# Global configuration
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# API and Dashboard configuration
api:
  dashboard: true
  insecure: false  # Set to false in production
  debug: false

# Entry Points
entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entrypoint:
          to: websecure
          scheme: https
          permanent: true
  websecure:
    address: ":443"
    http:
      tls:
        options: default

# Providers
providers:
  # Docker provider
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: traefik-public
    watch: true
  
  # File provider for dynamic configuration
  file:
    directory: /etc/traefik/dynamic
    watch: true

# Certificate Resolvers
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>
      storage: /letsencrypt/acme.json
      httpChallenge:
        entryPoint: web
      # Use DNS challenge for wildcard certificates (optional)
      # dnsChallenge:
      #   provider: cloudflare
      #   resolvers:
      #     - "*******:53"
      #     - "*******:53"

# Logging
log:
  level: INFO
  filePath: /var/log/traefik/traefik.log
  format: json

# Access Logs
accessLog:
  filePath: /var/log/traefik/access.log
  format: json
  bufferingSize: 100
  filters:
    statusCodes:
      - "400-499"
      - "500-599"
    retryAttempts: true
    minDuration: "10ms"

# Metrics
metrics:
  prometheus:
    addEntryPointsLabels: true
    addServicesLabels: true
    addRoutersLabels: true
    buckets:
      - 0.1
      - 0.3
      - 1.2
      - 5.0

# Ping endpoint for health checks
ping:
  entryPoint: "web"

# Tracing (optional)
# tracing:
#   jaeger:
#     samplingServerURL: http://jaeger:14268/api/sampling
#     localAgentHostPort: jaeger:6831

# Pilot (optional - Traefik Cloud)
# pilot:
#   token: "your-pilot-token"

# Cluster configuration (for multiple Traefik instances)
# cluster:
#   store: consul
#   prefix: traefik
#   endpoints:
#     - "consul:8500"
