import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../shared/models/company_member_model.dart';
import '../../../shared/services/company_member_service.dart';

// Events
abstract class CompanyMemberEvent extends Equatable {
  const CompanyMemberEvent();

  @override
  List<Object?> get props => [];
}

class LoadCompanyMembers extends CompanyMemberEvent {
  final int companyId;

  const LoadCompanyMembers(this.companyId);

  @override
  List<Object?> get props => [companyId];
}

class InviteUser extends CompanyMemberEvent {
  final int companyId;
  final InviteUserRequest request;

  const InviteUser(this.companyId, this.request);

  @override
  List<Object?> get props => [companyId, request];
}

class UpdateMember extends CompanyMemberEvent {
  final UpdateMemberRequest request;

  const UpdateMember(this.request);

  @override
  List<Object?> get props => [request];
}

class RemoveMember extends CompanyMemberEvent {
  final int memberId;

  const RemoveMember(this.memberId);

  @override
  List<Object?> get props => [memberId];
}

class AcceptInvitation extends CompanyMemberEvent {
  final int memberId;

  const AcceptInvitation(this.memberId);

  @override
  List<Object?> get props => [memberId];
}

class DeclineInvitation extends CompanyMemberEvent {
  final int memberId;

  const DeclineInvitation(this.memberId);

  @override
  List<Object?> get props => [memberId];
}

// States
abstract class CompanyMemberState extends Equatable {
  const CompanyMemberState();

  @override
  List<Object?> get props => [];
}

class CompanyMemberInitial extends CompanyMemberState {}

class CompanyMemberLoading extends CompanyMemberState {}

class CompanyMemberLoaded extends CompanyMemberState {
  final CompanyMemberListResponse memberList;

  const CompanyMemberLoaded(this.memberList);

  @override
  List<Object?> get props => [memberList];
}

class CompanyMemberOperationSuccess extends CompanyMemberState {
  final String message;
  final CompanyMemberModel? member;

  const CompanyMemberOperationSuccess(this.message, {this.member});

  @override
  List<Object?> get props => [message, member];
}

class CompanyMemberError extends CompanyMemberState {
  final String message;

  const CompanyMemberError(this.message);

  @override
  List<Object?> get props => [message];
}

// BLoC
class CompanyMemberBloc extends Bloc<CompanyMemberEvent, CompanyMemberState> {
  final CompanyMemberService _companyMemberService;

  CompanyMemberBloc(this._companyMemberService) : super(CompanyMemberInitial()) {
    on<LoadCompanyMembers>(_onLoadCompanyMembers);
    on<InviteUser>(_onInviteUser);
    on<UpdateMember>(_onUpdateMember);
    on<RemoveMember>(_onRemoveMember);
    on<AcceptInvitation>(_onAcceptInvitation);
    on<DeclineInvitation>(_onDeclineInvitation);
  }

  Future<void> _onLoadCompanyMembers(
    LoadCompanyMembers event,
    Emitter<CompanyMemberState> emit,
  ) async {
    emit(CompanyMemberLoading());
    try {
      final memberList = await _companyMemberService.getCompanyMembers(event.companyId);
      emit(CompanyMemberLoaded(memberList));
    } catch (e) {
      emit(CompanyMemberError(e.toString()));
    }
  }

  Future<void> _onInviteUser(
    InviteUser event,
    Emitter<CompanyMemberState> emit,
  ) async {
    emit(CompanyMemberLoading());
    try {
      final member = await _companyMemberService.inviteUser(event.companyId, event.request);
      emit(CompanyMemberOperationSuccess(
        'Invitation sent successfully to ${event.request.email}',
        member: member,
      ));
      // Reload members list
      add(LoadCompanyMembers(event.companyId));
    } catch (e) {
      emit(CompanyMemberError(e.toString()));
    }
  }

  Future<void> _onUpdateMember(
    UpdateMember event,
    Emitter<CompanyMemberState> emit,
  ) async {
    emit(CompanyMemberLoading());
    try {
      final member = await _companyMemberService.updateMember(event.request);
      emit(CompanyMemberOperationSuccess(
        'Member updated successfully',
        member: member,
      ));
    } catch (e) {
      emit(CompanyMemberError(e.toString()));
    }
  }

  Future<void> _onRemoveMember(
    RemoveMember event,
    Emitter<CompanyMemberState> emit,
  ) async {
    emit(CompanyMemberLoading());
    try {
      await _companyMemberService.removeMember(event.memberId);
      emit(const CompanyMemberOperationSuccess('Member removed successfully'));
    } catch (e) {
      emit(CompanyMemberError(e.toString()));
    }
  }

  Future<void> _onAcceptInvitation(
    AcceptInvitation event,
    Emitter<CompanyMemberState> emit,
  ) async {
    emit(CompanyMemberLoading());
    try {
      final member = await _companyMemberService.acceptInvitation(event.memberId);
      emit(CompanyMemberOperationSuccess(
        'Invitation accepted successfully',
        member: member,
      ));
    } catch (e) {
      emit(CompanyMemberError(e.toString()));
    }
  }

  Future<void> _onDeclineInvitation(
    DeclineInvitation event,
    Emitter<CompanyMemberState> emit,
  ) async {
    emit(CompanyMemberLoading());
    try {
      await _companyMemberService.declineInvitation(event.memberId);
      emit(const CompanyMemberOperationSuccess('Invitation declined'));
    } catch (e) {
      emit(CompanyMemberError(e.toString()));
    }
  }
}
