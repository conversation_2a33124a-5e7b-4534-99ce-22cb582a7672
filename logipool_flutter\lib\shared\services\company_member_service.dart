import 'package:dio/dio.dart';
import '../models/company_member_model.dart';
import '../models/paginated_response.dart';
import '../../core/network/api_client.dart';

class CompanyMemberService {
  final ApiClient _apiClient;

  CompanyMemberService(this._apiClient);

  /// Get all members of a company
  Future<CompanyMemberListResponse> getCompanyMembers(int companyId) async {
    try {
      final response = await _apiClient
          .get<Map<String, dynamic>>('/companies/$companyId/members');
      return CompanyMemberListResponse.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Invite a user to join the company
  Future<CompanyMemberModel> inviteUser(
    int companyId,
    InviteUserRequest request,
  ) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/companies/$companyId/members/invite',
        data: request.toJson(),
      );
      return CompanyMemberModel.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Update a company member's role and permissions
  Future<CompanyMemberModel> updateMember(UpdateMemberRequest request) async {
    try {
      final response = await _apiClient.put<Map<String, dynamic>>(
        '/companies/members',
        data: request.toJson(),
      );
      return CompanyMemberModel.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Remove a member from the company
  Future<void> removeMember(int memberId) async {
    try {
      await _apiClient.delete<void>('/companies/members/$memberId');
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Accept a company invitation
  Future<CompanyMemberModel> acceptInvitation(int memberId) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/companies/members/accept-invitation',
        data: {'memberId': memberId, 'accept': true},
      );
      return CompanyMemberModel.fromJson(response.data!);
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Decline a company invitation
  Future<void> declineInvitation(int memberId) async {
    try {
      await _apiClient.post<void>(
        '/companies/members/accept-invitation',
        data: {'memberId': memberId, 'accept': false},
      );
    } on DioException catch (e) {
      throw _handleError(e);
    }
  }

  /// Get default permissions for a role
  Map<String, bool> getDefaultPermissions(CompanyRole role) {
    switch (role) {
      case CompanyRole.owner:
        return {
          'canManageMembers': true,
          'canManageLoads': true,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': true,
          'canViewFinancials': true,
          'canTrackLocation': false,
        };
      case CompanyRole.manager:
        return {
          'canManageMembers': true,
          'canManageLoads': true,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': true,
          'canViewFinancials': true,
          'canTrackLocation': false,
        };
      case CompanyRole.dispatcher:
        return {
          'canManageMembers': false,
          'canManageLoads': true,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': false,
          'canViewFinancials': false,
          'canTrackLocation': false,
        };
      case CompanyRole.driver:
        return {
          'canManageMembers': false,
          'canManageLoads': false,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': false,
          'canViewFinancials': false,
          'canTrackLocation': true,
        };
      case CompanyRole.accountant:
        return {
          'canManageMembers': false,
          'canManageLoads': false,
          'canUpdateLoadStatus': false,
          'canUploadDocuments': true,
          'canGenerateInvoices': true,
          'canViewFinancials': true,
          'canTrackLocation': false,
        };
      case CompanyRole.viewer:
        return {
          'canManageMembers': false,
          'canManageLoads': false,
          'canUpdateLoadStatus': false,
          'canUploadDocuments': false,
          'canGenerateInvoices': false,
          'canViewFinancials': false,
          'canTrackLocation': false,
        };
    }
  }

  /// Get role description
  String getRoleDescription(CompanyRole role) {
    switch (role) {
      case CompanyRole.owner:
        return 'Full access to all company features and settings';
      case CompanyRole.manager:
        return 'Can manage loads, members, and company operations';
      case CompanyRole.dispatcher:
        return 'Can manage loads and coordinate transportation';
      case CompanyRole.driver:
        return 'Can update load status and share location while driving';
      case CompanyRole.accountant:
        return 'Can manage invoices, payments, and financial data';
      case CompanyRole.viewer:
        return 'Read-only access to company information';
    }
  }

  Exception _handleError(DioException e) {
    if (e.response?.statusCode == 404) {
      return Exception('Company or member not found');
    } else if (e.response?.statusCode == 403) {
      return Exception('You don\'t have permission to perform this action');
    } else if (e.response?.statusCode == 409) {
      return Exception('User is already a member of this company');
    } else if (e.response?.data != null &&
        e.response?.data['message'] != null) {
      return Exception(e.response?.data['message']);
    } else {
      return Exception('An error occurred while managing company members');
    }
  }
}
