import 'dart:convert';
import 'package:flutter/foundation.dart';
import '../models/contact_config_model.dart';
import 'storage_service.dart';
import '../../core/network/api_client.dart';

class ContactConfigService extends ChangeNotifier {
  static ContactConfigService? _instance;
  ContactConfigModel _contactConfig = ContactConfigModel.defaultConfig;
  bool _isLoading = false;
  String? _error;

  final StorageService _storageService = StorageService();
  final ApiClient? _apiClient;

  ContactConfigService._({ApiClient? apiClient}) : _apiClient = apiClient;

  static ContactConfigService get instance {
    _instance ??= ContactConfigService._();
    return _instance!;
  }

  factory ContactConfigService({ApiClient? apiClient}) {
    if (_instance == null) {
      _instance = ContactConfigService._(apiClient: apiClient);
    }
    return _instance!;
  }

  // Getters
  ContactConfigModel get contactConfig => _contactConfig;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Initialize the service - load from cache first, then fetch from API
  Future<void> initialize() async {
    try {
      _isLoading = true;
      _error = null;
      notifyListeners();

      // Load from cache first
      await _loadFromCache();

      // Then try to fetch from API in background
      _fetchFromApiInBackground();
    } catch (e) {
      _error = 'Failed to initialize contact configuration: $e';
      if (kDebugMode) {
        print('ContactConfigService initialization error: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load contact configuration from local cache
  Future<void> _loadFromCache() async {
    try {
      final cachedData = await _storageService.getString('contact_config');
      if (cachedData != null) {
        final Map<String, dynamic> json = jsonDecode(cachedData);
        _contactConfig = ContactConfigModel.fromJson(json);
        if (kDebugMode) {
          print('Loaded contact config from cache');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to load contact config from cache: $e');
      }
      // Use default config if cache fails
      _contactConfig = ContactConfigModel.defaultConfig;
    }
  }

  // Fetch contact configuration from API in background
  Future<void> _fetchFromApiInBackground() async {
    if (_apiClient == null) {
      if (kDebugMode) {
        print('API client not available, using cached/default config');
      }
      return;
    }

    try {
      // This would be the actual API endpoint for contact configuration
      // For now, we'll simulate an API call
      await Future.delayed(const Duration(milliseconds: 500));
      
      // In a real implementation, this would be:
      // final response = await _apiClient.get('/public/contact-config');
      // final contactConfig = ContactConfigModel.fromJson(response.data);
      
      // For now, we'll use the default config
      final contactConfig = ContactConfigModel.defaultConfig;
      
      await _updateContactConfig(contactConfig);
      
      if (kDebugMode) {
        print('Contact config updated from API');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to fetch contact config from API: $e');
      }
      // Continue using cached/default config
    }
  }

  // Update contact configuration and save to cache
  Future<void> _updateContactConfig(ContactConfigModel newConfig) async {
    try {
      _contactConfig = newConfig;
      
      // Save to cache
      final jsonString = jsonEncode(newConfig.toJson());
      await _storageService.setString('contact_config', jsonString);
      
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Failed to update contact config: $e');
      }
    }
  }

  // Force refresh from API
  Future<void> refresh() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _fetchFromApiInBackground();
    } catch (e) {
      _error = 'Failed to refresh contact configuration: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear cache and reset to default
  Future<void> clearCache() async {
    try {
      await _storageService.remove('contact_config');
      _contactConfig = ContactConfigModel.defaultConfig;
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Failed to clear contact config cache: $e');
      }
    }
  }
}
