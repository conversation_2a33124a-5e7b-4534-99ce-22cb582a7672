import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../shared/models/load_model.dart';
import '../../../shared/utils/distance_calculator.dart';

class LoadsMapView extends StatefulWidget {
  final List<LoadModel> loads;
  final VoidCallback? onRefresh;

  const LoadsMapView({
    super.key,
    required this.loads,
    this.onRefresh,
  });

  @override
  State<LoadsMapView> createState() => _LoadsMapViewState();
}

class _LoadsMapViewState extends State<LoadsMapView> {
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {};
  LoadModel? _selectedLoad;
  bool _showOnlyBiddingLoads = false;
  bool _showDestinations = true;

  static const CameraPosition _defaultPosition = CameraPosition(
    target: LatLng(-17.8252, 31.0335), // Harare, Zimbabwe
    zoom: 8,
  );

  @override
  void initState() {
    super.initState();
    _updateMarkers();
  }

  @override
  void didUpdateWidget(LoadsMapView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.loads != widget.loads) {
      _updateMarkers();
    }
  }

  void _updateMarkers() {
    final markers = <Marker>{};
    final polylines = <Polyline>{};

    // Debug: Print load data
    print('DEBUG: Total loads received: ${widget.loads.length}');
    for (int i = 0; i < widget.loads.length && i < 3; i++) {
      final load = widget.loads[i];
      print('DEBUG: Load ${load.id}: ${load.title}');
      print('  Status: ${load.status}');
      print('  Bidding closes at: ${load.biddingClosesAt}');
      print('  Current time: ${DateTime.now()}');
      final isAvailableForBidding = load.status == LoadStatus.posted &&
          load.biddingClosesAt != null &&
          load.biddingClosesAt!.isAfter(DateTime.now());
      print('  Available for bidding: $isAvailableForBidding');
      print(
          '  Pickup: ${load.pickupLocation} (${load.pickupLatitude}, ${load.pickupLongitude})');
      print(
          '  Delivery: ${load.deliveryLocation} (${load.deliveryLatitude}, ${load.deliveryLongitude})');
    }

    // Filter loads based on the toggle
    final loadsToShow = _showOnlyBiddingLoads
        ? widget.loads
            .where((load) =>
                load.status == LoadStatus.posted &&
                load.biddingClosesAt != null &&
                load.biddingClosesAt!.isAfter(DateTime.now()))
            .toList()
        : widget.loads;

    for (final load in loadsToShow) {
      // Determine if load is available for bidding
      final isAvailableForBidding = load.status == LoadStatus.posted &&
          load.biddingClosesAt != null &&
          load.biddingClosesAt!.isAfter(DateTime.now());

      // Add pickup marker
      if (load.pickupLatitude != null && load.pickupLongitude != null) {
        // Use explicit hue values to ensure proper color rendering
        final pickupHue = isAvailableForBidding
            ? 240.0 // Explicit blue hue value
            : 120.0; // Explicit green hue value

        print(
            'DEBUG: Adding pickup marker for load ${load.id} with explicit hue: $pickupHue (bidding: $isAvailableForBidding)');
        print('DEBUG: BitmapDescriptor.hueBlue = ${BitmapDescriptor.hueBlue}');
        print(
            'DEBUG: BitmapDescriptor.hueGreen = ${BitmapDescriptor.hueGreen}');

        final markerIcon = BitmapDescriptor.defaultMarkerWithHue(pickupHue);
        print('DEBUG: Created pickup marker icon with hue $pickupHue');

        markers.add(
          Marker(
            markerId: MarkerId('pickup_${load.id}'),
            position: LatLng(load.pickupLatitude!, load.pickupLongitude!),
            icon: markerIcon,
            infoWindow: InfoWindow(
              title: isAvailableForBidding
                  ? '🔥 Pickup: ${load.title}'
                  : 'Pickup: ${load.title}',
              snippet: isAvailableForBidding
                  ? '${load.pickupLocation} • Bidding Open!'
                  : load.pickupLocation,
              onTap: () => _showLoadDetails(load),
            ),
          ),
        );
      }

      // Add delivery marker (only if destinations are enabled)
      if (_showDestinations &&
          load.deliveryLatitude != null &&
          load.deliveryLongitude != null) {
        // Use explicit hue values to ensure proper color rendering
        final deliveryHue = isAvailableForBidding
            ? 30.0 // Explicit orange hue value
            : 0.0; // Explicit red hue value

        print(
            'DEBUG: Adding delivery marker for load ${load.id} with explicit hue: $deliveryHue (bidding: $isAvailableForBidding)');
        print(
            'DEBUG: BitmapDescriptor.hueOrange = ${BitmapDescriptor.hueOrange}');
        print('DEBUG: BitmapDescriptor.hueRed = ${BitmapDescriptor.hueRed}');

        final markerIcon = BitmapDescriptor.defaultMarkerWithHue(deliveryHue);
        print('DEBUG: Created delivery marker icon with hue $deliveryHue');

        markers.add(
          Marker(
            markerId: MarkerId('delivery_${load.id}'),
            position: LatLng(load.deliveryLatitude!, load.deliveryLongitude!),
            icon: markerIcon,
            infoWindow: InfoWindow(
              title: isAvailableForBidding
                  ? '🎯 Delivery: ${load.title}'
                  : 'Delivery: ${load.title}',
              snippet: isAvailableForBidding
                  ? '${load.deliveryLocation} • Bidding Open!'
                  : load.deliveryLocation,
              onTap: () => _showLoadDetails(load),
            ),
          ),
        );
      }

      // Add route polyline if both coordinates exist and destinations are enabled
      if (_showDestinations &&
          load.pickupLatitude != null &&
          load.pickupLongitude != null &&
          load.deliveryLatitude != null &&
          load.deliveryLongitude != null) {
        polylines.add(
          Polyline(
            polylineId: PolylineId('route_${load.id}'),
            points: [
              LatLng(load.pickupLatitude!, load.pickupLongitude!),
              LatLng(load.deliveryLatitude!, load.deliveryLongitude!),
            ],
            color: _getLoadColor(load),
            width: 3,
            patterns: load.status == LoadStatus.delivered
                ? [PatternItem.dash(10), PatternItem.gap(5)]
                : [],
          ),
        );
      }
    }

    // Debug: Print marker creation results
    print(
        'DEBUG: Created ${markers.length} markers and ${polylines.length} polylines');

    // Debug: Print marker details
    for (final marker in markers) {
      print('DEBUG: Marker ${marker.markerId.value} at ${marker.position}');
    }

    setState(() {
      _markers = markers;
      _polylines = polylines;
    });

    // Fit camera to show all markers
    if (markers.isNotEmpty && _mapController != null) {
      _fitCameraToMarkers();
    }
  }

  Color _getLoadColor(LoadModel load) {
    // Check if load is available for bidding first
    final isAvailableForBidding = load.status == LoadStatus.posted &&
        load.biddingClosesAt != null &&
        load.biddingClosesAt!.isAfter(DateTime.now());

    if (isAvailableForBidding) {
      return Colors.blue.withValues(alpha: 0.8); // Bright blue for bidding
    }

    switch (load.status) {
      case LoadStatus.posted:
        return Colors.green;
      case LoadStatus.biddingClosed:
        return Colors.orange;
      case LoadStatus.assigned:
        return Colors.blue;
      case LoadStatus.inTransit:
        return Colors.purple;
      case LoadStatus.delivered:
        return Colors.teal;
      case LoadStatus.completed:
        return Colors.green;
      case LoadStatus.cancelled:
        return Colors.red;
    }
  }

  void _fitCameraToMarkers() {
    if (_markers.isEmpty) return;

    double minLat = _markers.first.position.latitude;
    double maxLat = _markers.first.position.latitude;
    double minLng = _markers.first.position.longitude;
    double maxLng = _markers.first.position.longitude;

    for (final marker in _markers) {
      minLat =
          minLat < marker.position.latitude ? minLat : marker.position.latitude;
      maxLat =
          maxLat > marker.position.latitude ? maxLat : marker.position.latitude;
      minLng = minLng < marker.position.longitude
          ? minLng
          : marker.position.longitude;
      maxLng = maxLng > marker.position.longitude
          ? maxLng
          : marker.position.longitude;
    }

    _mapController?.animateCamera(
      CameraUpdate.newLatLngBounds(
        LatLngBounds(
          southwest: LatLng(minLat, minLng),
          northeast: LatLng(maxLat, maxLng),
        ),
        100.0, // padding
      ),
    );
  }

  void _showLoadDetails(LoadModel load) {
    // Use context.go instead of context.pushNamed for better compatibility
    context.go('/loads/${load.id}');
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    if (_markers.isNotEmpty) {
      _fitCameraToMarkers();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if platform supports Google Maps
    if (!kIsWeb && (Platform.isWindows || Platform.isLinux)) {
      return _buildFallbackView();
    }

    if (widget.loads.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.map_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'No loads to display on map',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.outline,
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Loads with location coordinates will appear here',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.outline,
                  ),
            ),
            if (widget.onRefresh != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: widget.onRefresh,
                icon: const Icon(Icons.refresh),
                label: const Text('Refresh'),
              ),
            ],
          ],
        ),
      );
    }

    return Stack(
      children: [
        GoogleMap(
          onMapCreated: _onMapCreated,
          initialCameraPosition: _defaultPosition,
          markers: _markers,
          polylines: _polylines,
          myLocationEnabled: true,
          myLocationButtonEnabled: true,
          mapToolbarEnabled: true,
          compassEnabled: true,
          zoomControlsEnabled: true,
        ),

        // Legend
        Positioned(
          top: 16,
          right: 16,
          child: Card(
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Legend',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  _buildLegendItem(
                    color: Colors.blue,
                    icon: Icons.location_on,
                    label: '🔥 Pickup (Bidding)',
                  ),
                  if (_showDestinations) ...[
                    _buildLegendItem(
                      color: Colors.orange,
                      icon: Icons.location_on,
                      label: '🎯 Delivery (Bidding)',
                    ),
                  ],
                  const Divider(height: 16),
                  _buildLegendItem(
                    color: Colors.green,
                    icon: Icons.location_on,
                    label: 'Regular Pickup',
                  ),
                  if (_showDestinations) ...[
                    _buildLegendItem(
                      color: Colors.red,
                      icon: Icons.location_on,
                      label: 'Regular Delivery',
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),

        // Load count and info
        Positioned(
          bottom: 16,
          left: 16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Info about loads without coordinates
              if (widget.loads.any((load) =>
                  load.pickupLatitude == null ||
                  load.pickupLongitude == null ||
                  load.deliveryLatitude == null ||
                  load.deliveryLongitude == null))
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 16,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Some loads hidden (no coordinates)',
                          style: Theme.of(context)
                              .textTheme
                              .bodySmall
                              ?.copyWith(
                                color: Theme.of(context).colorScheme.primary,
                              ),
                        ),
                      ],
                    ),
                  ),
                ),
              const SizedBox(height: 8),
              // Load count
              Card(
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Text(
                    _showOnlyBiddingLoads
                        ? '${widget.loads.where((load) => load.status == LoadStatus.posted && load.biddingClosesAt != null && load.biddingClosesAt!.isAfter(DateTime.now())).length} bidding loads'
                        : '${widget.loads.length} loads',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Map control buttons
        Positioned(
          bottom: 16,
          right: 16,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Destination toggle button
              FloatingActionButton.extended(
                onPressed: () {
                  setState(() {
                    _showDestinations = !_showDestinations;
                  });
                  _updateMarkers();
                },
                icon: Icon(
                    _showDestinations ? Icons.location_off : Icons.location_on),
                label: Text(_showDestinations ? 'Hide Routes' : 'Show Routes'),
                backgroundColor: _showDestinations
                    ? Theme.of(context).colorScheme.secondary
                    : Colors.grey,
                foregroundColor: Colors.white,
              ),
              const SizedBox(height: 8),
              // Filter toggle button
              FloatingActionButton.extended(
                onPressed: () {
                  setState(() {
                    _showOnlyBiddingLoads = !_showOnlyBiddingLoads;
                  });
                  _updateMarkers();
                },
                icon: Icon(_showOnlyBiddingLoads ? Icons.map : Icons.gavel),
                label:
                    Text(_showOnlyBiddingLoads ? 'Show All' : 'Bidding Only'),
                backgroundColor: _showOnlyBiddingLoads
                    ? Colors.blue
                    : Theme.of(context).colorScheme.primary,
                foregroundColor: Colors.white,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem({
    required Color color,
    required IconData icon,
    required String label,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 6),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildFallbackView() {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Map view is not available on this platform. Showing list view instead.',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: ListView.builder(
            itemCount: widget.loads.length,
            itemBuilder: (context, index) {
              final load = widget.loads[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: ListTile(
                  title: Text(load.title),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('${load.pickupLocation} → ${load.deliveryLocation}'),
                      if (load.pickupLatitude != null &&
                          load.pickupLongitude != null &&
                          load.deliveryLatitude != null &&
                          load.deliveryLongitude != null)
                        Text(
                          'Distance: ${DistanceCalculator.formatDistance(
                            DistanceCalculator.calculateDistance(
                              lat1: load.pickupLatitude!,
                              lon1: load.pickupLongitude!,
                              lat2: load.deliveryLatitude!,
                              lon2: load.deliveryLongitude!,
                            ),
                          )}',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                    ],
                  ),
                  trailing: Icon(
                    Icons.arrow_forward_ios,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                  onTap: () => _showLoadDetails(load),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
