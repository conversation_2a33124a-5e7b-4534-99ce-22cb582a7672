import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/load_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/services/location_tracking_service.dart';
import '../../../core/di/service_locator.dart';

class LoadTrackingSection extends StatefulWidget {
  final LoadModel load;

  const LoadTrackingSection({
    super.key,
    required this.load,
  });

  @override
  State<LoadTrackingSection> createState() => _LoadTrackingSectionState();
}

class _LoadTrackingSectionState extends State<LoadTrackingSection> {
  UserModel? _currentUser;
  LocationTrackingService? _locationService;
  bool _isTrackingEnabled = false;
  bool _isLocationPermissionGranted = false;
  Position? _currentPosition;

  @override
  void initState() {
    super.initState();
    _currentUser = context.read<AuthService>().currentUser;
    _initializeLocationService();
  }

  void _initializeLocationService() {
    // Initialize location service if user can track
    if (_canTrackLocation()) {
      _locationService = getIt<LocationTrackingService>();
      _locationService?.initialize();
      _initializeLocationPermission();

      // Listen to location service changes
      _locationService?.addListener(_onLocationServiceChanged);
    }
  }

  void _onLocationServiceChanged() {
    if (mounted) {
      setState(() {
        _isTrackingEnabled = _locationService?.isTracking ?? false;
        _currentPosition = _locationService?.currentPosition;
      });
    }
  }

  Future<void> _initializeLocationPermission() async {
    try {
      // Grant location permission by default
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        await Geolocator.requestPermission();
      }

      final finalPermission = await Geolocator.checkPermission();
      setState(() {
        _isLocationPermissionGranted =
            finalPermission == LocationPermission.always ||
                finalPermission == LocationPermission.whileInUse;
      });

      // Get initial location if permission is granted
      if (_isLocationPermissionGranted) {
        await _getCurrentLocation();
      }
    } on Exception {
      setState(() {
        _isLocationPermissionGranted = false;
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    if (!_isLocationPermissionGranted) {
      await _initializeLocationPermission();
      return;
    }

    try {
      final position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
        ),
      );
      setState(() {
        _currentPosition = position;
      });

      // Update the load location in the system
      await _updateLoadLocation(position);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Location updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } on Exception catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error getting location: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateLoadLocation(Position position) async {
    try {
      // TODO: Call API to update load location
      // This would typically call a service to update the load's current location
      // For now, we'll just log the location update
      print(
          'Updating load ${widget.load.id} location: ${position.latitude}, ${position.longitude}');

      // You can implement the actual API call here when the backend endpoint is ready
      // await _trackingService.updateLoadLocation(widget.load.id!, position);
    } on Exception catch (e) {
      print('Error updating load location: $e');
    }
  }

  Future<void> _startTracking() async {
    if (_locationService != null) {
      try {
        // Start live tracking on the server
        await _locationService!.startLiveTracking(widget.load.id!);

        // Start local location tracking
        final success = await _locationService!.startTracking(widget.load.id!);

        if (mounted) {
          if (success) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Live tracking started successfully'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to start location tracking'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error starting live tracking: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _stopTracking() async {
    if (_locationService != null) {
      try {
        // Stop local location tracking
        await _locationService!.stopTracking();

        // Stop live tracking on the server
        await _locationService!.stopLiveTracking(widget.load.id!);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Live tracking stopped successfully'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error stopping live tracking: ${e.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  bool _canTrackLocation() {
    // Check if user has permission to track location
    // Provider can track if load is assigned to their company and in transit
    if (_currentUser == null) return false;

    // Check if load is in a trackable status
    final trackableStatuses = [LoadStatus.assigned, LoadStatus.inTransit];
    if (!trackableStatuses.contains(widget.load.status)) return false;

    // Check if user is from the assigned company (provider)
    if (widget.load.assignedCompanyId != null &&
        _currentUser!.companyId == widget.load.assignedCompanyId) {
      return true;
    }

    return false;
  }

  @override
  void dispose() {
    _locationService?.removeListener(_onLocationServiceChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTrackingStatusCard(),
          const SizedBox(height: 16),
          _buildLocationPermissionCard(),
          const SizedBox(height: 16),
          if (_canTrackLocation()) _buildLiveTrackingControlCard(),
          const SizedBox(height: 16),
          _buildCurrentLocationCard(),
          const SizedBox(height: 16),
          _buildRouteInfoCard(),
        ],
      ),
    );
  }

  Widget _buildTrackingStatusCard() {
    final isTracking = _locationService?.isTracking ?? false;
    final isTrackingThisLoad = _locationService?.currentLoadId == widget.load.id;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Live Tracking Status',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: isTrackingThisLoad
                        ? Colors.green
                        : isTracking
                            ? Colors.orange
                            : Colors.grey,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    isTrackingThisLoad
                        ? 'Live tracking active for this load'
                        : isTracking
                            ? 'Tracking another load (Load #${_locationService?.currentLoadId})'
                            : 'Live tracking inactive',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ),
              ],
            ),
            if (isTrackingThisLoad) ...[
              const SizedBox(height: 8),
              Text(
                'Location updates every 3 minutes',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                'Last updated: ${DateFormat('HH:mm:ss').format(DateTime.now())}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildLocationPermissionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _isLocationPermissionGranted
                      ? Icons.check_circle
                      : Icons.warning,
                  color: _isLocationPermissionGranted
                      ? Colors.green
                      : Colors.orange,
                ),
                const SizedBox(width: 8),
                Text(
                  'Location Permission',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              _isLocationPermissionGranted
                  ? 'Location permission is granted. You can track your location for this load.'
                  : 'Location permission is required to track your location for this load.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLiveTrackingControlCard() {
    final isCurrentlyTracking = _locationService?.isTracking ?? false;
    final isTrackingThisLoad = _locationService?.currentLoadId == widget.load.id;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.live_tv,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Live Location Tracking',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Enable live tracking to share your location every 3 minutes. This allows clients and other users to track the real-time progress of this load.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (isCurrentlyTracking && !isTrackingThisLoad) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.orange, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'You are currently tracking another load. Stop that tracking first.',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.orange[800],
                            ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isLocationPermissionGranted && (!isCurrentlyTracking || isTrackingThisLoad)
                    ? (isTrackingThisLoad ? _stopTracking : _startTracking)
                    : null,
                icon: Icon(isTrackingThisLoad ? Icons.stop : Icons.play_arrow),
                label: Text(isTrackingThisLoad ? 'Stop Live Tracking' : 'Start Live Tracking'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: isTrackingThisLoad ? Colors.red : Colors.green,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentLocationCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.gps_fixed,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Current Location',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _getCurrentLocation,
                  tooltip: 'Refresh Location',
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_currentPosition != null) ...[
              Text(
                'Latitude: ${_currentPosition!.latitude.toStringAsFixed(6)}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 4),
              Text(
                'Longitude: ${_currentPosition!.longitude.toStringAsFixed(6)}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(height: 4),
              Text(
                'Accuracy: ${_currentPosition!.accuracy.toStringAsFixed(1)}m',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ] else ...[
              Text(
                'Location not available',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _getCurrentLocation,
                icon: const Icon(Icons.my_location),
                label: const Text('Update Location'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRouteInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.route,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Route Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildRoutePoint(
              'Pickup',
              widget.load.pickupLocation,
              Icons.location_on,
              Colors.green,
            ),
            const SizedBox(height: 8),
            _buildRoutePoint(
              'Delivery',
              widget.load.deliveryLocation,
              Icons.flag,
              Colors.red,
            ),
            if (widget.load.estimatedDistance != null) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Icon(
                    Icons.straighten,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Distance: ${widget.load.estimatedDistance!.toStringAsFixed(1)} ${widget.load.distanceUnit ?? 'km'}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRoutePoint(
      String label, String location, IconData icon, Color color) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
              ),
              Text(
                location,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
