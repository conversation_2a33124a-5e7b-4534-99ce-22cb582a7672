package zw.co.kanjan.logipool.dto.company;

import lombok.Data;
import zw.co.kanjan.logipool.entity.Company;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class CompanyResponse {
    
    private Long id;
    private String name;
    private String description;
    private String registrationNumber;
    private String taxNumber;
    private Company.CompanyType type;
    private String address;
    private String city;
    private String country;
    private String phoneNumber;
    private String email;
    private String website;
    private Company.VerificationStatus verificationStatus;
    private BigDecimal rating;
    private Integer totalJobs;
    private Integer completedJobs;
    private Long userId;
    private String userName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
