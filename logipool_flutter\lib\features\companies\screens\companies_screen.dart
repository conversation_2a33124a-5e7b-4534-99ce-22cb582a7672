import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/di/service_locator.dart';
import '../../../shared/models/company_model.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../bloc/company_bloc.dart';
import '../widgets/company_card.dart';

class CompaniesScreen extends StatefulWidget {
  final String? companyId;

  const CompaniesScreen({super.key, this.companyId});

  @override
  State<CompaniesScreen> createState() => _CompaniesScreenState();
}

class _CompaniesScreenState extends State<CompaniesScreen> {
  @override
  void initState() {
    super.initState();
    if (widget.companyId != null) {
      final companyId = int.tryParse(widget.companyId!);
      if (companyId != null) {
        context.read<CompanyBloc>().add(
              CompanyFetchByIdRequested(companyId: companyId),
            );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (widget.companyId != null) {
      return _buildCompanyDetailView(context, theme);
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Companies'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: BlocProvider(
        create: (context) => CompanyBloc()..add(const CompaniesListRequested()),
        child: _buildCompanyListView(context, theme),
      ),
    );
  }

  Widget _buildCompanyDetailView(BuildContext context, ThemeData theme) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Company Details'),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
      ),
      body: BlocConsumer<CompanyBloc, CompanyState>(
        listener: (context, state) {
          if (state is CompanyError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is CompanyLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state is CompanyLoaded) {
            return _buildCompanyDetail(context, state.company);
          }

          if (state is CompanyError) {
            return _buildErrorView(context, state.message);
          }

          return const Center(child: CircularProgressIndicator());
        },
      ),
    );
  }

  Widget _buildCompanyDetail(BuildContext context, CompanyModel company) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: CompanyCard(
        company: company,
        showActions: true,
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, String message) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Company',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                if (widget.companyId != null) {
                  final companyId = int.tryParse(widget.companyId!);
                  if (companyId != null) {
                    context.read<CompanyBloc>().add(
                          CompanyFetchByIdRequested(companyId: companyId),
                        );
                  }
                }
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyListView(BuildContext context, ThemeData theme) {
    return Column(
      children: [
        _buildSearchBar(),
        _buildFilterChips(),
        Expanded(
          child: BlocBuilder<CompanyBloc, CompanyState>(
            builder: (context, state) {
              if (state is CompanyLoading) {
                return const LoadingWidget();
              }

              if (state is CompanyError) {
                return CustomErrorWidget(
                  message: state.message,
                  onRetry: () => context.read<CompanyBloc>().add(
                        const CompaniesListRequested(),
                      ),
                );
              }

              if (state is CompaniesListLoaded) {
                final companies = state.companies;

                if (companies.isEmpty) {
                  return const EmptyStateWidget(
                    icon: Icon(Icons.business),
                    title: 'No Companies Found',
                    message: 'Register your company to get started',
                  );
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    context.read<CompanyBloc>().add(
                          const CompaniesListRequested(),
                        );
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: companies.length,
                    itemBuilder: (context, index) {
                      final company = companies[index];
                      return _buildCompanyCard(context, company);
                    },
                  ),
                );
              }

              return const EmptyStateWidget(
                icon: Icon(Icons.business),
                title: 'No Companies Found',
                message: 'Register your company to get started',
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'Search companies...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: (value) {
          // TODO: Implement search
        },
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('All', null),
          const SizedBox(width: 8),
          _buildFilterChip('Verified', VerificationStatus.verified),
          const SizedBox(width: 8),
          _buildFilterChip('Pending', VerificationStatus.pending),
          const SizedBox(width: 8),
          _buildFilterChip(
              'Logistics Providers', CompanyType.logisticsProvider),
          const SizedBox(width: 8),
          _buildFilterChip('Clients', CompanyType.client),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, dynamic filter) {
    return FilterChip(
      label: Text(label),
      selected: false, // TODO: Implement filter state
      onSelected: (selected) {
        // TODO: Implement filtering
      },
      backgroundColor: Colors.grey[200],
      selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
    );
  }

  Widget _buildCompanyCard(BuildContext context, CompanyModel company) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: InkWell(
        onTap: () => context.push('/companies/${company.id}'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      company.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                  _buildVerificationBadge(company.verificationStatus),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.category, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    company.type.toString().split('.').last.toUpperCase(),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      '${company.city}, ${company.country}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
              if (company.description != null &&
                  company.description!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  company.description!,
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildRatingStars(company.rating),
                  const SizedBox(width: 8),
                  Text(
                    '${company.rating.toStringAsFixed(1)} (${company.totalJobs} jobs)',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVerificationBadge(VerificationStatus status) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case VerificationStatus.verified:
        color = Colors.green;
        text = 'Verified';
        icon = Icons.verified;
        break;
      case VerificationStatus.pending:
        color = Colors.orange;
        text = 'Pending';
        icon = Icons.pending;
        break;
      case VerificationStatus.rejected:
        color = Colors.red;
        text = 'Rejected';
        icon = Icons.cancel;
        break;
      case VerificationStatus.suspended:
        color = Colors.grey;
        text = 'Suspended';
        icon = Icons.block;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingStars(double rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          index < rating.floor() ? Icons.star : Icons.star_border,
          size: 16,
          color: Colors.amber,
        );
      }),
    );
  }
}
