import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/invoice_model.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../core/utils/app_router.dart';
import '../../../core/utils/date_formatter.dart';
import '../bloc/payment_bloc.dart';
import '../widgets/invoice_card.dart';
import '../widgets/invoice_filter_sheet.dart';

class InvoiceListScreen extends StatefulWidget {
  const InvoiceListScreen({super.key});

  @override
  State<InvoiceListScreen> createState() => _InvoiceListScreenState();
}

class _InvoiceListScreenState extends State<InvoiceListScreen> {
  final _scrollController = ScrollController();
  final _searchController = TextEditingController();

  List<InvoiceStatus>? _statusFilter;
  DateTime? _startDate;
  DateTime? _endDate;
  String? _searchQuery;
  bool _hasReachedMax = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    context.read<PaymentBloc>().add(const LoadInvoices());
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom && !_hasReachedMax) {
      context.read<PaymentBloc>().add(const LoadMoreInvoices());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Invoices'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterSheet,
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _createInvoice,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildFilterChips(),
          Expanded(
            child: BlocConsumer<PaymentBloc, PaymentState>(
              listener: (context, state) {
                if (state is PaymentError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                } else if (state is PaymentOperationSuccess) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _refreshInvoices();
                }
              },
              builder: (context, state) {
                if (state is PaymentLoading && state is! InvoiceLoaded) {
                  return const LoadingWidget();
                }

                if (state is InvoiceLoaded) {
                  _hasReachedMax = state.hasReachedMax;
                  return _buildInvoiceList(state.invoices, state.isLoadingMore);
                }

                if (state is PaymentError) {
                  return AppErrorWidget(
                    message: state.message,
                    onRetry: () {
                      context.read<PaymentBloc>().add(const LoadInvoices());
                    },
                  );
                }

                return const LoadingWidget();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search invoices...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery != null
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: _clearSearch,
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        onChanged: _onSearchChanged,
      ),
    );
  }

  Widget _buildFilterChips() {
    final hasFilters =
        _statusFilter != null || _startDate != null || _endDate != null;

    if (!hasFilters) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      height: 50,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          if (_statusFilter != null) ...[
            for (final status in _statusFilter!)
              Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Text(status.displayName),
                  onDeleted: () => _removeStatusFilter(status),
                ),
              ),
          ],
          if (_startDate != null)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text('From: ${DateFormatter.formatDate(_startDate!)}'),
                onDeleted: () => _clearDateFilter(),
              ),
            ),
          if (_endDate != null)
            Padding(
              padding: const EdgeInsets.only(right: 8),
              child: FilterChip(
                label: Text('To: ${DateFormatter.formatDate(_endDate!)}'),
                onDeleted: () => _clearDateFilter(),
              ),
            ),
          Padding(
            padding: const EdgeInsets.only(right: 8),
            child: ActionChip(
              label: const Text('Clear All'),
              onPressed: _clearAllFilters,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceList(List<InvoiceModel> invoices, bool isLoadingMore) {
    if (invoices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No invoices found',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'Create your first invoice to get started',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[500],
                  ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _createInvoice,
              icon: const Icon(Icons.add),
              label: const Text('Create Invoice'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _refreshInvoices,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: invoices.length + (isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index >= invoices.length) {
            return const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          final invoice = invoices[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: InvoiceCard(
              invoice: invoice,
              onTap: () => _viewInvoiceDetails(invoice.id),
              onSend: () => _sendInvoice(invoice.id),
              onMarkAsPaid: () => _markInvoiceAsPaid(invoice.id),
              onDownload: () => _downloadInvoice(invoice.id),
              onEdit: () => _editInvoice(invoice.id),
              onDelete: () => _deleteInvoice(invoice.id),
            ),
          );
        },
      ),
    );
  }

  void _showFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => InvoiceFilterSheet(
        selectedStatuses: _statusFilter,
        startDate: _startDate,
        endDate: _endDate,
        onApplyFilter: _applyFilter,
      ),
    );
  }

  void _applyFilter({
    List<InvoiceStatus>? statuses,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    setState(() {
      _statusFilter = statuses;
      _startDate = startDate;
      _endDate = endDate;
    });
    _refreshInvoices();
  }

  void _removeStatusFilter(InvoiceStatus status) {
    setState(() {
      _statusFilter?.remove(status);
      if (_statusFilter?.isEmpty == true) {
        _statusFilter = null;
      }
    });
    _refreshInvoices();
  }

  void _clearDateFilter() {
    setState(() {
      _startDate = null;
      _endDate = null;
    });
    _refreshInvoices();
  }

  void _clearAllFilters() {
    setState(() {
      _statusFilter = null;
      _startDate = null;
      _endDate = null;
    });
    _refreshInvoices();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.isNotEmpty ? query : null;
    });
    // TODO: Implement search functionality
    _refreshInvoices();
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchQuery = null;
    });
    _refreshInvoices();
  }

  Future<void> _refreshInvoices() async {
    context.read<PaymentBloc>().add(const LoadInvoices());
  }

  void _createInvoice() {
    // TODO: Implement invoice creation screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Invoice creation feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _viewInvoiceDetails(int invoiceId) {
    context.push('/payments/invoices/$invoiceId');
  }

  void _editInvoice(int invoiceId) {
    // TODO: Implement invoice edit screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Invoice editing feature coming soon!'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _sendInvoice(int invoiceId) {
    context.read<PaymentBloc>().add(SendInvoice(invoiceId));
  }

  void _markInvoiceAsPaid(int invoiceId) {
    context.read<PaymentBloc>().add(MarkInvoiceAsPaid(invoiceId));
  }

  void _downloadInvoice(int invoiceId) {
    context.read<PaymentBloc>().add(DownloadInvoicePdf(invoiceId));
  }

  void _deleteInvoice(int invoiceId) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Invoice'),
        content: const Text(
            'Are you sure you want to delete this invoice? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: Implement delete invoice
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content:
                      Text('Delete invoice functionality will be implemented'),
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
