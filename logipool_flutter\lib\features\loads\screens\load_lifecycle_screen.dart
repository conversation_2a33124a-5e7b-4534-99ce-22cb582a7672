import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/load_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/widgets/unified_header.dart';
import '../bloc/load_bloc.dart';
import '../widgets/load_lifecycle_timeline.dart';
import '../widgets/load_payment_status_card.dart';
import '../widgets/load_completion_actions.dart';

class LoadLifecycleScreen extends StatefulWidget {
  final int loadId;

  const LoadLifecycleScreen({
    super.key,
    required this.loadId,
  });

  @override
  State<LoadLifecycleScreen> createState() => _LoadLifecycleScreenState();
}

class _LoadLifecycleScreenState extends State<LoadLifecycleScreen> {
  LoadModel? _currentLoad;
  UserModel? _currentUser;

  @override
  void initState() {
    super.initState();
    _currentUser = context.read<AuthService>().currentUser;
    _loadLoadDetails();
  }

  void _loadLoadDetails() {
    context.read<LoadBloc>().add(LoadFetchByIdRequested(id: widget.loadId));
  }

  bool _canManageLifecycle() {
    if (_currentLoad == null || _currentUser == null) return false;

    // Check if user is the load owner or assigned company
    final isOwner = _currentLoad!.clientId == _currentUser!.id;
    final isAssignedCompany = _currentUser!.company != null &&
        int.tryParse(_currentUser!.company!.id) ==
            _currentLoad!.assignedCompanyId;

    return isOwner || isAssignedCompany;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedHeader(
        title: 'Load Lifecycle',
        showBackButton: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadLoadDetails,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: BlocListener<LoadBloc, LoadState>(
        listener: (context, state) {
          if (state is LoadDetailLoaded) {
            setState(() {
              _currentLoad = state.load;
            });
          } else if (state is LoadUpdateSuccess) {
            // Refresh load details to show updated information
            _loadLoadDetails();
          } else if (state is LoadError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        child: BlocBuilder<LoadBloc, LoadState>(
          builder: (context, state) {
            if (state is LoadLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state is LoadDetailLoaded) {
              final load = state.load;

              return SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildLoadHeader(load),
                    const SizedBox(height: 24),
                    _buildLifecycleProgress(load),
                    const SizedBox(height: 24),
                    LoadLifecycleTimeline(load: load),
                    const SizedBox(height: 24),
                    LoadPaymentStatusCard(load: load),
                    const SizedBox(height: 24),
                    if (_canManageLifecycle())
                      LoadCompletionActions(
                        load: load,
                        onActionCompleted: _loadLoadDetails,
                      ),
                  ],
                ),
              );
            }

            if (state is LoadError) {
              return _buildErrorView(state.message);
            }

            return const Center(
              child: Text('Load not found'),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLoadHeader(LoadModel load) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    load.title,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
                _buildStatusChip(load.status),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Load #${load.id}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    '${load.pickupLocation} → ${load.deliveryLocation}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'Created: ${DateFormat('MMM dd, yyyy').format(load.createdAt ?? DateTime.now())}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(width: 16),
                Icon(Icons.flag, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'Due: ${DateFormat('MMM dd, yyyy').format(load.deliveryDate)}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(LoadStatus status) {
    Color backgroundColor;
    Color textColor;

    switch (status) {
      case LoadStatus.posted:
        backgroundColor = Colors.blue.shade100;
        textColor = Colors.blue.shade800;
        break;
      case LoadStatus.assigned:
        backgroundColor = Colors.orange.shade100;
        textColor = Colors.orange.shade800;
        break;
      case LoadStatus.inTransit:
        backgroundColor = Colors.purple.shade100;
        textColor = Colors.purple.shade800;
        break;
      case LoadStatus.delivered:
        backgroundColor = Colors.green.shade100;
        textColor = Colors.green.shade800;
        break;
      case LoadStatus.completed:
        backgroundColor = Colors.teal.shade100;
        textColor = Colors.teal.shade800;
        break;
      case LoadStatus.cancelled:
        backgroundColor = Colors.red.shade100;
        textColor = Colors.red.shade800;
        break;
      default:
        backgroundColor = Colors.grey.shade100;
        textColor = Colors.grey.shade800;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildLifecycleProgress(LoadModel load) {
    final stages = [
      LoadStatus.posted,
      LoadStatus.assigned,
      LoadStatus.inTransit,
      LoadStatus.delivered,
      LoadStatus.completed,
    ];

    final currentIndex = stages.indexOf(load.status);
    final progress =
        currentIndex >= 0 ? (currentIndex + 1) / stages.length : 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.timeline,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Lifecycle Progress',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const Spacer(),
                Text(
                  '${(progress * 100).toInt()}%',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey.shade300,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: stages.map((stage) {
                final index = stages.indexOf(stage);
                final isCompleted = index <= currentIndex;
                final isCurrent = index == currentIndex;

                return Column(
                  children: [
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: isCompleted
                            ? Theme.of(context).primaryColor
                            : Colors.grey.shade300,
                        shape: BoxShape.circle,
                        border: isCurrent
                            ? Border.all(
                                color: Theme.of(context).primaryColor,
                                width: 3,
                              )
                            : null,
                      ),
                      child: isCompleted
                          ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : null,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      stage.displayName,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontSize: 10,
                            fontWeight:
                                isCurrent ? FontWeight.bold : FontWeight.normal,
                            color: isCompleted
                                ? Theme.of(context).primaryColor
                                : Colors.grey[600],
                          ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Error',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.red[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton(
                onPressed: _loadLoadDetails,
                child: const Text('Retry'),
              ),
              const SizedBox(width: 16),
              OutlinedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Go Back'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
