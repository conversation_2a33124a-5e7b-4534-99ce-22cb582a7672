<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Tracking Access - LogiPool</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f4f4f4;
        }
        .container {
            background-color: #ffffff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #2c5aa0;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        .tracking-info {
            background-color: #f8f9fa;
            border-left: 4px solid #2c5aa0;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .tracking-number {
            font-size: 24px;
            font-weight: bold;
            color: #2c5aa0;
            margin-bottom: 10px;
        }
        .route-info {
            display: flex;
            justify-content: space-between;
            margin: 15px 0;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
        .location {
            text-align: center;
            flex: 1;
        }
        .location-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .location-name {
            font-weight: bold;
            color: #333;
        }
        .arrow {
            align-self: center;
            margin: 0 15px;
            color: #2c5aa0;
            font-size: 18px;
        }
        .access-button {
            display: block;
            width: 100%;
            max-width: 300px;
            margin: 30px auto;
            padding: 15px 30px;
            background-color: #2c5aa0;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        .access-button:hover {
            background-color: #1e3d6f;
        }
        .security-notice {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .security-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 10px;
        }
        .security-text {
            color: #856404;
            font-size: 14px;
        }
        .expiry-info {
            text-align: center;
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        .expiry-time {
            font-weight: bold;
            color: #0066cc;
            font-size: 18px;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 14px;
        }
        .support-link {
            color: #2c5aa0;
            text-decoration: none;
        }
        .support-link:hover {
            text-decoration: underline;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .route-info {
                flex-direction: column;
                text-align: center;
            }
            .arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">LogiPool</div>
            <div class="subtitle">Secure Shipment Tracking Access</div>
        </div>

        <div class="tracking-info">
            <div class="tracking-number">{{trackingNumber}}</div>
            <div><strong>Shipment:</strong> {{loadTitle}}</div>
        </div>

        <div class="route-info">
            <div class="location">
                <div class="location-label">From</div>
                <div class="location-name">{{pickupLocation}}</div>
            </div>
            <div class="arrow">→</div>
            <div class="location">
                <div class="location-label">To</div>
                <div class="location-name">{{deliveryLocation}}</div>
            </div>
        </div>

        <p>You have requested secure access to track your shipment. Click the button below to view real-time tracking information and delivery updates.</p>

        <a href="{{trackingUrl}}" class="access-button">
            🔒 Access Secure Tracking
        </a>

        <div class="expiry-info">
            <div>This secure link expires in</div>
            <div class="expiry-time">{{expiryHours}} hours</div>
        </div>

        <div class="security-notice">
            <div class="security-title">🛡️ Security Notice</div>
            <div class="security-text">
                • This link is unique and secure for your tracking request<br>
                • Do not share this link with others<br>
                • The link will expire automatically for your security<br>
                • If you didn't request this tracking access, please ignore this email
            </div>
        </div>

        <p><strong>What you can track:</strong></p>
        <ul>
            <li>Real-time location updates</li>
            <li>Delivery status and timeline</li>
            <li>Estimated delivery time</li>
            <li>Important shipment notifications</li>
        </ul>

        <div class="footer">
            <p>If you're having trouble accessing your tracking information, please contact our support team.</p>
            <p><a href="{{supportUrl}}" class="support-link">Contact Support</a></p>
            <p>&copy; {{currentYear}} LogiPool. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
