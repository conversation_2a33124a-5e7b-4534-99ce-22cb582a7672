package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "tracking_audit_log")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrackingAuditLog {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 50)
    @Column(name = "event_type", nullable = false)
    private String eventType;
    
    @Size(max = 20)
    @Column(name = "tracking_number")
    private String trackingNumber;
    
    @Size(max = 100)
    @Column(name = "contact_info")
    private String contactInfo;
    
    @Size(max = 45)
    @Column(name = "ip_address")
    private String ipAddress;
    
    @Column(name = "user_agent", columnDefinition = "TEXT")
    private String userAgent;
    
    @Size(max = 500)
    @Column(name = "details")
    private String details;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "severity", length = 20)
    private Severity severity;
    
    @CreationTimestamp
    @Column(name = "timestamp", nullable = false)
    private LocalDateTime timestamp;
    
    @Size(max = 100)
    @Column(name = "verification_token")
    private String verificationToken;
    
    @Column(name = "request_successful")
    private Boolean requestSuccessful;
    
    public enum Severity {
        INFO, WARNING, ERROR, CRITICAL
    }
    
    // Static factory methods for common audit events
    public static TrackingAuditLog verificationRequested(String trackingNumber, String contact, 
                                                        String ipAddress, String userAgent) {
        return TrackingAuditLog.builder()
                .eventType("VERIFICATION_REQUESTED")
                .trackingNumber(trackingNumber)
                .contactInfo(maskContact(contact))
                .ipAddress(ipAddress)
                .userAgent(userAgent)
                .severity(Severity.INFO)
                .requestSuccessful(true)
                .build();
    }
    
    public static TrackingAuditLog verificationSent(String trackingNumber, String contact, 
                                                   String verificationToken, String method) {
        return TrackingAuditLog.builder()
                .eventType("VERIFICATION_SENT")
                .trackingNumber(trackingNumber)
                .contactInfo(maskContact(contact))
                .verificationToken(verificationToken)
                .details("Method: " + method)
                .severity(Severity.INFO)
                .requestSuccessful(true)
                .build();
    }
    
    public static TrackingAuditLog verificationFailed(String trackingNumber, String contact, 
                                                     String ipAddress, String reason) {
        return TrackingAuditLog.builder()
                .eventType("VERIFICATION_FAILED")
                .trackingNumber(trackingNumber)
                .contactInfo(maskContact(contact))
                .ipAddress(ipAddress)
                .details(reason)
                .severity(Severity.WARNING)
                .requestSuccessful(false)
                .build();
    }
    
    public static TrackingAuditLog trackingAccessed(String trackingNumber, String verificationToken, 
                                                   String ipAddress, int accessCount) {
        return TrackingAuditLog.builder()
                .eventType("TRACKING_ACCESSED")
                .trackingNumber(trackingNumber)
                .verificationToken(verificationToken)
                .ipAddress(ipAddress)
                .details("Access count: " + accessCount)
                .severity(Severity.INFO)
                .requestSuccessful(true)
                .build();
    }
    
    public static TrackingAuditLog rateLimitExceeded(String trackingNumber, String contact, 
                                                    String ipAddress, String limitType) {
        return TrackingAuditLog.builder()
                .eventType("RATE_LIMIT_EXCEEDED")
                .trackingNumber(trackingNumber)
                .contactInfo(maskContact(contact))
                .ipAddress(ipAddress)
                .details("Limit type: " + limitType)
                .severity(Severity.WARNING)
                .requestSuccessful(false)
                .build();
    }
    
    public static TrackingAuditLog suspiciousActivity(String trackingNumber, String ipAddress, 
                                                     String activityType, String details) {
        return TrackingAuditLog.builder()
                .eventType("SUSPICIOUS_ACTIVITY")
                .trackingNumber(trackingNumber)
                .ipAddress(ipAddress)
                .details(activityType + ": " + details)
                .severity(Severity.ERROR)
                .requestSuccessful(false)
                .build();
    }
    
    public static TrackingAuditLog securityViolation(String trackingNumber, String ipAddress, 
                                                    String violationType, String details) {
        return TrackingAuditLog.builder()
                .eventType("SECURITY_VIOLATION")
                .trackingNumber(trackingNumber)
                .ipAddress(ipAddress)
                .details(violationType + ": " + details)
                .severity(Severity.CRITICAL)
                .requestSuccessful(false)
                .build();
    }
    
    private static String maskContact(String contact) {
        if (contact == null || contact.length() <= 3) {
            return "***";
        }
        
        if (contact.contains("@")) {
            // Email masking
            String[] parts = contact.split("@");
            String username = parts[0];
            String domain = parts[1];
            
            if (username.length() <= 2) {
                return "**@" + domain;
            }
            
            return username.substring(0, 2) + "***@" + domain;
        } else {
            // Phone number masking
            if (contact.length() <= 4) {
                return "***" + contact.substring(contact.length() - 1);
            }
            
            return "***" + contact.substring(contact.length() - 4);
        }
    }
}
