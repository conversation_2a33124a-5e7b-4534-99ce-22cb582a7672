import 'dart:convert';
import 'package:http/http.dart' as http;

import '../models/tracking_model.dart';
import '../models/paginated_response.dart';
import '../../core/network/api_client.dart';
import '../../core/constants/app_constants.dart';

class TrackingService {
  final ApiClient _apiClient;

  TrackingService({required ApiClient apiClient}) : _apiClient = apiClient;

  // Update tracking status
  Future<TrackingModel> updateTracking(TrackingUpdateRequest request) async {
    try {
      final response = await _apiClient.post(
        '/tracking/update',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        return TrackingModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw Exception('Failed to update tracking: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error updating tracking: $e');
    }
  }

  // Update GPS location
  Future<TrackingModel> updateLocation(LocationUpdateRequest request) async {
    try {
      final response = await _apiClient.post(
        '/tracking/location',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        return TrackingModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw Exception('Failed to update location: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error updating location: $e');
    }
  }

  // Get tracking history for a load
  Future<PaginatedResponse<TrackingModel>> getTrackingHistory(
    int loadId, {
    int page = 0,
    int size = 20,
    String sortBy = 'timestamp',
    String sortDir = 'desc',
  }) async {
    try {
      final response = await _apiClient.get(
        '/tracking/load/$loadId',
        queryParameters: {
          'page': page.toString(),
          'size': size.toString(),
          'sortBy': sortBy,
          'sortDir': sortDir,
        },
      );

      if (response.statusCode == 200) {
        return PaginatedResponse<TrackingModel>.fromJson(
          response.data as Map<String, dynamic>,
          (json) => TrackingModel.fromJson(json as Map<String, dynamic>),
        );
      } else {
        throw Exception(
            'Failed to get tracking history: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error getting tracking history: $e');
    }
  }

  // Get latest tracking for a load
  Future<TrackingModel?> getLatestTracking(int loadId) async {
    try {
      final response = await _apiClient.get('/tracking/load/$loadId/latest');

      if (response.statusCode == 200) {
        return TrackingModel.fromJson(response.data as Map<String, dynamic>);
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw Exception(
            'Failed to get latest tracking: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error getting latest tracking: $e');
    }
  }

  // Get active loads locations
  Future<List<RealTimeLocation>> getActiveLoadsLocation() async {
    try {
      final response = await _apiClient.get('/tracking/active-locations');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data
            .map((json) =>
                RealTimeLocation.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception(
            'Failed to get active locations: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error getting active locations: $e');
    }
  }

  // Get tracking statistics
  Future<TrackingStatistics> getTrackingStatistics() async {
    try {
      final response = await _apiClient.get('/tracking/statistics');

      if (response.statusCode == 200) {
        return TrackingStatistics.fromJson(
            response.data as Map<String, dynamic>);
      } else {
        throw Exception(
            'Failed to get tracking statistics: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error getting tracking statistics: $e');
    }
  }

  // Confirm delivery
  Future<TrackingModel> confirmDelivery(
      DeliveryConfirmationRequest request) async {
    try {
      final response = await _apiClient.post(
        '/tracking/confirm-delivery',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        return TrackingModel.fromJson(response.data as Map<String, dynamic>);
      } else {
        throw Exception(
            'Failed to confirm delivery: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error confirming delivery: $e');
    }
  }

  // Get delayed loads
  Future<List<TrackingModel>> getDelayedLoads() async {
    try {
      final response = await _apiClient.get('/tracking/delayed');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data
            .map((json) => TrackingModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception(
            'Failed to get delayed loads: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error getting delayed loads: $e');
    }
  }

  // Get loads with issues
  Future<List<TrackingModel>> getLoadsWithIssues() async {
    try {
      final response = await _apiClient.get('/tracking/issues');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data
            .map((json) => TrackingModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception(
            'Failed to get loads with issues: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error getting loads with issues: $e');
    }
  }

  // Get company loads tracking
  Future<List<TrackingModel>> getCompanyLoadsTracking(int companyId) async {
    try {
      final response = await _apiClient.get('/tracking/company/$companyId');

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data
            .map((json) => TrackingModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception(
            'Failed to get company loads tracking: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error getting company loads tracking: $e');
    }
  }

  // Get load tracking history with details
  Future<LoadTrackingHistory> getLoadTrackingHistory(int loadId) async {
    try {
      final response = await _apiClient.get('/tracking/load/$loadId/history');

      if (response.statusCode == 200) {
        return LoadTrackingHistory.fromJson(
            response.data as Map<String, dynamic>);
      } else {
        throw Exception(
            'Failed to get load tracking history: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error getting load tracking history: $e');
    }
  }

  // Search tracking entries
  Future<PaginatedResponse<TrackingModel>> searchTracking({
    String? location,
    TrackingStatus? status,
    DateTime? startDate,
    DateTime? endDate,
    double? minLatitude,
    double? maxLatitude,
    double? minLongitude,
    double? maxLongitude,
    int page = 0,
    int size = 20,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'size': size.toString(),
      };

      if (location != null) queryParams['location'] = location;
      if (status != null) queryParams['status'] = status.name.toUpperCase();
      if (startDate != null)
        queryParams['startDate'] = startDate.toIso8601String();
      if (endDate != null) queryParams['endDate'] = endDate.toIso8601String();
      if (minLatitude != null)
        queryParams['minLatitude'] = minLatitude.toString();
      if (maxLatitude != null)
        queryParams['maxLatitude'] = maxLatitude.toString();
      if (minLongitude != null)
        queryParams['minLongitude'] = minLongitude.toString();
      if (maxLongitude != null)
        queryParams['maxLongitude'] = maxLongitude.toString();

      final response = await _apiClient.get(
        '/tracking/search',
        queryParameters: queryParams,
      );

      if (response.statusCode == 200) {
        return PaginatedResponse<TrackingModel>.fromJson(
          response.data as Map<String, dynamic>,
          (json) => TrackingModel.fromJson(json as Map<String, dynamic>),
        );
      } else {
        throw Exception('Failed to search tracking: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error searching tracking: $e');
    }
  }

  // Get tracking entries near location
  Future<List<TrackingModel>> getTrackingNearLocation(
    double latitude,
    double longitude,
    double radiusInKm,
  ) async {
    try {
      final response = await _apiClient.get(
        '/tracking/near',
        queryParameters: {
          'latitude': latitude.toString(),
          'longitude': longitude.toString(),
          'radius': radiusInKm.toString(),
        },
      );

      if (response.statusCode == 200) {
        final List<dynamic> data = response.data as List<dynamic>;
        return data
            .map((json) => TrackingModel.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw Exception(
            'Failed to get tracking near location: ${response.statusMessage}');
      }
    } catch (e) {
      throw Exception('Error getting tracking near location: $e');
    }
  }
}
