#!/usr/bin/env python3
"""
Test script to verify document endpoints are working correctly.
This script tests the fixes made to the document API endpoints.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8080/api"
TIMEOUT = 10

def test_backend_health():
    """Test if backend is running"""
    try:
        response = requests.get("http://localhost:8080/actuator/health", timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Backend is running")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend health check failed: {e}")
        return False

def register_and_login():
    """Register a test user and login to get JWT token"""
    print("\n🔐 Setting up test user...")
    
    # Register test user
    register_data = {
        "email": "<EMAIL>",
        "password": "password123",
        "firstName": "Doc",
        "lastName": "Test",
        "phoneNumber": "+**********",
        "role": "CLIENT"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/register", 
                               json=register_data, 
                               timeout=TIMEOUT)
        if response.status_code in [200, 201]:
            print("✅ Test user registered")
        elif response.status_code == 400:
            print("ℹ️ Test user already exists")
        else:
            print(f"❌ User registration failed: {response.status_code}")
            print(f"Response: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Registration request failed: {e}")
    
    # Login to get token
    login_data = {
        "email": "<EMAIL>",
        "password": "password123"
    }
    
    try:
        response = requests.post(f"{BASE_URL}/auth/login", 
                               json=login_data, 
                               timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ Test user logged in")
            data = response.json()
            if 'token' in data:
                print("✅ JWT token received")
                return data['token']
            else:
                print("❌ No JWT token in response")
        else:
            print(f"❌ User login failed: {response.status_code}")
            print(f"Response: {response.text}")
    except requests.exceptions.RequestException as e:
        print(f"❌ Login request failed: {e}")
    
    return None

def test_document_endpoints(token):
    """Test document endpoints"""
    if not token:
        print("❌ No token available for document endpoint testing")
        return False
    
    print("\n📄 Testing Document Endpoints...")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    all_passed = True
    
    # Test general documents endpoint
    try:
        response = requests.get(f"{BASE_URL}/documents", 
                              headers=headers, 
                              timeout=TIMEOUT)
        if response.status_code == 200:
            print("✅ GET /documents endpoint working")
        else:
            print(f"❌ GET /documents failed: {response.status_code}")
            print(f"Response: {response.text}")
            all_passed = False
    except requests.exceptions.RequestException as e:
        print(f"❌ GET /documents request failed: {e}")
        all_passed = False
    
    # Test load documents endpoint (the one that was failing)
    test_load_id = 5  # Using the same load ID from the error
    try:
        response = requests.get(f"{BASE_URL}/documents/loads/{test_load_id}", 
                              headers=headers, 
                              timeout=TIMEOUT)
        if response.status_code == 200:
            print(f"✅ GET /documents/loads/{test_load_id} endpoint working")
        elif response.status_code == 404:
            print(f"ℹ️ GET /documents/loads/{test_load_id} - Load not found (expected)")
        else:
            print(f"❌ GET /documents/loads/{test_load_id} failed: {response.status_code}")
            print(f"Response: {response.text}")
            all_passed = False
    except requests.exceptions.RequestException as e:
        print(f"❌ GET /documents/loads/{test_load_id} request failed: {e}")
        all_passed = False
    
    # Test vehicle documents endpoint
    test_vehicle_id = 1
    try:
        response = requests.get(f"{BASE_URL}/documents/vehicle/{test_vehicle_id}", 
                              headers=headers, 
                              timeout=TIMEOUT)
        if response.status_code == 200:
            print(f"✅ GET /documents/vehicle/{test_vehicle_id} endpoint working")
        elif response.status_code == 404:
            print(f"ℹ️ GET /documents/vehicle/{test_vehicle_id} - Vehicle not found (expected)")
        else:
            print(f"❌ GET /documents/vehicle/{test_vehicle_id} failed: {response.status_code}")
            print(f"Response: {response.text}")
            all_passed = False
    except requests.exceptions.RequestException as e:
        print(f"❌ GET /documents/vehicle/{test_vehicle_id} request failed: {e}")
        all_passed = False
    
    return all_passed

def main():
    """Main test function"""
    print("🚀 Testing Document Endpoints Fix...")
    print("=" * 50)
    
    # Test backend health
    if not test_backend_health():
        print("\n❌ Backend is not running. Please start the Spring Boot application first.")
        sys.exit(1)
    
    # Setup test user and get token
    token = register_and_login()
    
    # Test document endpoints
    success = test_document_endpoints(token)
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 All document endpoint tests passed!")
        print("✅ The fix for /api/documents/load/5 error is working correctly")
    else:
        print("❌ Some document endpoint tests failed")
    
    print("🏁 Document endpoint tests completed!")

if __name__ == "__main__":
    main()
