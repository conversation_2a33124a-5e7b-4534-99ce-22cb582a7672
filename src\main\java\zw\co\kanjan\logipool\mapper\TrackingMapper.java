package zw.co.kanjan.logipool.mapper;

import org.mapstruct.*;
import zw.co.kanjan.logipool.dto.tracking.TrackingDto;
import zw.co.kanjan.logipool.entity.LoadTracking;

import java.util.List;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TrackingMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "load", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "timestamp", ignore = true)
    LoadTracking toEntity(TrackingDto.TrackingUpdateRequest request);
    
    @Mapping(source = "load.id", target = "loadId")
    @Mapping(source = "load.title", target = "loadTitle")
    @Mapping(source = "updatedBy.id", target = "updatedById")
    @Mapping(source = "updatedBy.firstName", target = "updatedByName", 
             qualifiedByName = "mapUpdatedByName")
    TrackingDto.TrackingResponse toResponse(LoadTracking tracking);
    
    List<TrackingDto.TrackingResponse> toResponseList(List<LoadTracking> trackingList);
    
    @Named("mapUpdatedByName")
    default String mapUpdatedByName(String firstName) {
        return firstName != null ? firstName : "System";
    }
    
    @Mapping(source = "load.id", target = "loadId")
    @Mapping(source = "load.title", target = "loadTitle")
    @Mapping(source = "status", target = "currentStatus")
    @Mapping(source = "location", target = "currentLocation")
    @Mapping(source = "latitude", target = "currentLatitude")
    @Mapping(source = "longitude", target = "currentLongitude")
    @Mapping(source = "timestamp", target = "lastUpdated")
    @Mapping(target = "trackingHistory", ignore = true)
    @Mapping(target = "totalEntries", ignore = true)
    TrackingDto.LoadTrackingHistory toTrackingHistory(LoadTracking latestTracking);
    
    @Mapping(source = "loadId", target = "loadId")
    @Mapping(source = "latitude", target = "latitude")
    @Mapping(source = "longitude", target = "longitude")
    @Mapping(target = "vehicleId", ignore = true)
    @Mapping(target = "speed", ignore = true)
    @Mapping(target = "heading", ignore = true)
    @Mapping(target = "accuracy", ignore = true)
    @Mapping(target = "timestamp", expression = "java(java.time.LocalDateTime.now())")
    @Mapping(target = "status", ignore = true)
    TrackingDto.RealTimeLocation toRealTimeLocation(TrackingDto.LocationUpdateRequest request);
}
