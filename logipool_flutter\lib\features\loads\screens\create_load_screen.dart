import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../shared/models/load_model.dart';
import '../../../shared/utils/validators.dart';
import '../../../shared/utils/distance_calculator.dart';
import '../../../shared/widgets/location_picker.dart';
import '../bloc/load_bloc.dart';

class CreateLoadScreen extends StatefulWidget {
  const CreateLoadScreen({super.key});

  @override
  State<CreateLoadScreen> createState() => _CreateLoadScreenState();
}

class _CreateLoadScreenState extends State<CreateLoadScreen> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  // Form controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _cargoTypeController = TextEditingController();
  final _weightController = TextEditingController();
  final _volumeController = TextEditingController();
  final _pickupLocationController = TextEditingController();
  final _deliveryLocationController = TextEditingController();
  final _estimatedDistanceController = TextEditingController();
  final _paymentRateController = TextEditingController();
  final _paymentUnitController = TextEditingController();
  final _estimatedValueController = TextEditingController();
  final _specialInstructionsController = TextEditingController();

  // Form values
  String _weightUnit = 'kg';
  String _volumeUnit = 'm3';
  String _distanceUnit = 'km';
  LoadType? _loadType;
  PaymentType? _paymentType;
  Priority _priority = Priority.normal;
  bool _requiresInsurance = false;
  bool _requiresSpecialHandling = false;

  DateTime? _pickupDate;
  DateTime? _deliveryDate;
  DateTime? _biddingClosesAt;

  // Location data
  LocationData? _pickupLocationData;
  LocationData? _deliveryLocationData;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _cargoTypeController.dispose();
    _weightController.dispose();
    _volumeController.dispose();
    _pickupLocationController.dispose();
    _deliveryLocationController.dispose();
    _estimatedDistanceController.dispose();
    _paymentRateController.dispose();
    _paymentUnitController.dispose();
    _estimatedValueController.dispose();
    _specialInstructionsController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      if (_pickupDate == null) {
        _showError('Please select a pickup date');
        return;
      }

      if (_deliveryDate == null) {
        _showError('Please select a delivery date');
        return;
      }

      if (_deliveryDate!.isBefore(_pickupDate!)) {
        _showError('Delivery date must be after pickup date');
        return;
      }

      // Calculate distance if both locations have coordinates
      double? calculatedDistance;
      if (_pickupLocationData?.hasCoordinates == true &&
          _deliveryLocationData?.hasCoordinates == true) {
        calculatedDistance = DistanceCalculator.calculateDistance(
          lat1: _pickupLocationData!.latitude!,
          lon1: _pickupLocationData!.longitude!,
          lat2: _deliveryLocationData!.latitude!,
          lon2: _deliveryLocationData!.longitude!,
        );
      }

      final load = LoadModel(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isNotEmpty
            ? _descriptionController.text.trim()
            : null,
        cargoType: _cargoTypeController.text.trim(),
        weight: double.parse(_weightController.text.trim()),
        weightUnit: _weightUnit,
        volume: _volumeController.text.trim().isNotEmpty
            ? double.parse(_volumeController.text.trim())
            : null,
        volumeUnit: _volumeUnit,
        pickupLocation: _pickupLocationData?.address ??
            _pickupLocationController.text.trim(),
        pickupLatitude: _pickupLocationData?.latitude,
        pickupLongitude: _pickupLocationData?.longitude,
        deliveryLocation: _deliveryLocationData?.address ??
            _deliveryLocationController.text.trim(),
        deliveryLatitude: _deliveryLocationData?.latitude,
        deliveryLongitude: _deliveryLocationData?.longitude,
        pickupDate: _pickupDate!,
        deliveryDate: _deliveryDate!,
        estimatedDistance: calculatedDistance ??
            (_estimatedDistanceController.text.trim().isNotEmpty
                ? double.parse(_estimatedDistanceController.text.trim())
                : null),
        distanceUnit: _distanceUnit,
        loadType: _loadType,
        paymentType: _paymentType,
        paymentRate: _paymentRateController.text.trim().isNotEmpty
            ? double.parse(_paymentRateController.text.trim())
            : null,
        paymentUnit: _paymentUnitController.text.trim().isNotEmpty
            ? _paymentUnitController.text.trim()
            : null,
        estimatedValue: _estimatedValueController.text.trim().isNotEmpty
            ? double.parse(_estimatedValueController.text.trim())
            : null,
        priority: _priority,
        requiresInsurance: _requiresInsurance,
        requiresSpecialHandling: _requiresSpecialHandling,
        specialInstructions:
            _specialInstructionsController.text.trim().isNotEmpty
                ? _specialInstructionsController.text.trim()
                : null,
        biddingClosesAt: _biddingClosesAt,
      );

      context.read<LoadBloc>().add(LoadCreateRequested(load: load));
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isPickup) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      final TimeOfDay? time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );

      if (time != null) {
        final dateTime = DateTime(
          picked.year,
          picked.month,
          picked.day,
          time.hour,
          time.minute,
        );

        setState(() {
          if (isPickup) {
            _pickupDate = dateTime;
          } else {
            _deliveryDate = dateTime;
          }
        });
      }
    }
  }

  Future<void> _selectBiddingCloseDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );

    if (picked != null) {
      final TimeOfDay? time = await showTimePicker(
        context: context,
        initialTime: const TimeOfDay(hour: 23, minute: 59),
      );

      if (time != null) {
        final dateTime = DateTime(
          picked.year,
          picked.month,
          picked.day,
          time.hour,
          time.minute,
        );

        setState(() {
          _biddingClosesAt = dateTime;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Load'),
        actions: [
          TextButton(
            onPressed: _submitForm,
            child: const Text('POST'),
          ),
        ],
      ),
      body: BlocListener<LoadBloc, LoadState>(
        listener: (context, state) {
          if (state is LoadCreateSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Load created successfully!'),
                backgroundColor: Colors.green,
              ),
            );
            Navigator.pop(context);
          } else if (state is LoadError) {
            _showError(state.message);
          }
        },
        child: BlocBuilder<LoadBloc, LoadState>(
          builder: (context, state) {
            final isLoading = state is LoadLoading;

            return Form(
              key: _formKey,
              child: ListView(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                children: [
                  // Basic Information
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Basic Information',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _titleController,
                            decoration: const InputDecoration(
                              labelText: 'Load Title *',
                              hintText: 'e.g., Electronics shipment to Harare',
                            ),
                            validator: Validators.required,
                            enabled: !isLoading,
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _descriptionController,
                            decoration: const InputDecoration(
                              labelText: 'Description',
                              hintText: 'Additional details about the load...',
                            ),
                            maxLines: 3,
                            enabled: !isLoading,
                          ),
                          const SizedBox(height: 16),
                          TextFormField(
                            controller: _cargoTypeController,
                            decoration: const InputDecoration(
                              labelText: 'Cargo Type *',
                              hintText: 'e.g., Electronics, Furniture, Food',
                            ),
                            validator: Validators.required,
                            enabled: !isLoading,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Load Details
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Load Details',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 16),

                          // Weight
                          Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: TextFormField(
                                  controller: _weightController,
                                  decoration: const InputDecoration(
                                    labelText: 'Weight *',
                                  ),
                                  keyboardType: TextInputType.number,
                                  validator: Validators.weight,
                                  enabled: !isLoading,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: DropdownButtonFormField<String>(
                                  value: _weightUnit,
                                  decoration: const InputDecoration(
                                    labelText: 'Unit',
                                  ),
                                  items: ['kg', 'tonnes', 'lbs'].map((unit) {
                                    return DropdownMenuItem(
                                      value: unit,
                                      child: Text(unit),
                                    );
                                  }).toList(),
                                  onChanged: isLoading
                                      ? null
                                      : (value) {
                                          setState(() {
                                            _weightUnit = value!;
                                          });
                                        },
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Volume (optional)
                          Row(
                            children: [
                              Expanded(
                                flex: 2,
                                child: TextFormField(
                                  controller: _volumeController,
                                  decoration: const InputDecoration(
                                    labelText: 'Volume',
                                  ),
                                  keyboardType: TextInputType.number,
                                  enabled: !isLoading,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: DropdownButtonFormField<String>(
                                  value: _volumeUnit,
                                  decoration: const InputDecoration(
                                    labelText: 'Unit',
                                  ),
                                  items: ['m3', 'ft3', 'liters'].map((unit) {
                                    return DropdownMenuItem(
                                      value: unit,
                                      child: Text(unit),
                                    );
                                  }).toList(),
                                  onChanged: isLoading
                                      ? null
                                      : (value) {
                                          setState(() {
                                            _volumeUnit = value!;
                                          });
                                        },
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Load Type
                          DropdownButtonFormField<LoadType>(
                            value: _loadType,
                            decoration: const InputDecoration(
                              labelText: 'Load Type',
                            ),
                            items: LoadType.values.map((type) {
                              return DropdownMenuItem(
                                value: type,
                                child: Text(type.displayName),
                              );
                            }).toList(),
                            onChanged: isLoading
                                ? null
                                : (value) {
                                    setState(() {
                                      _loadType = value;
                                    });
                                  },
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Location Information
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Location Information',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 16),

                          LocationPicker(
                            label: 'Pickup Location',
                            hintText: 'Enter pickup address',
                            isRequired: true,
                            onLocationChanged: (location) {
                              setState(() {
                                _pickupLocationData = location;
                                _pickupLocationController.text =
                                    location.address;
                              });
                            },
                          ),

                          const SizedBox(height: 16),

                          LocationPicker(
                            label: 'Delivery Location',
                            hintText: 'Enter delivery address',
                            isRequired: true,
                            onLocationChanged: (location) {
                              setState(() {
                                _deliveryLocationData = location;
                                _deliveryLocationController.text =
                                    location.address;
                              });
                            },
                          ),

                          const SizedBox(height: 16),

                          // Distance display
                          if (_pickupLocationData?.hasCoordinates == true &&
                              _deliveryLocationData?.hasCoordinates == true)
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .colorScheme
                                    .primaryContainer,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.route,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onPrimaryContainer,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'Estimated Distance: ${DistanceCalculator.formatDistance(
                                      DistanceCalculator.calculateDistance(
                                        lat1: _pickupLocationData!.latitude!,
                                        lon1: _pickupLocationData!.longitude!,
                                        lat2: _deliveryLocationData!.latitude!,
                                        lon2: _deliveryLocationData!.longitude!,
                                      ),
                                    )}',
                                    style: TextStyle(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onPrimaryContainer,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          // Map visibility info
                          if (_pickupLocationData?.hasCoordinates != true ||
                              _deliveryLocationData?.hasCoordinates != true)
                            const SizedBox(height: 16),
                          if (_pickupLocationData?.hasCoordinates != true ||
                              _deliveryLocationData?.hasCoordinates != true)
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .colorScheme
                                    .surfaceContainerHighest,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .outline
                                      .withValues(alpha: 0.5),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    color:
                                        Theme.of(context).colorScheme.onSurface,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'To display this load on the map, use the map picker (📍) to set coordinates for both pickup and delivery locations.',
                                      style: TextStyle(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface,
                                        fontSize: 13,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Schedule Information
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Schedule Information',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                          const SizedBox(height: 16),

                          // Pickup Date
                          InkWell(
                            onTap: isLoading
                                ? null
                                : () => _selectDate(context, true),
                            child: InputDecorator(
                              decoration: InputDecoration(
                                labelText: 'Pickup Date & Time *',
                                suffixIcon: const Icon(Icons.calendar_today),
                                border: const OutlineInputBorder(),
                                errorText: _pickupDate == null ? null : null,
                              ),
                              child: Text(
                                _pickupDate != null
                                    ? '${_pickupDate!.day}/${_pickupDate!.month}/${_pickupDate!.year} at ${_pickupDate!.hour.toString().padLeft(2, '0')}:${_pickupDate!.minute.toString().padLeft(2, '0')}'
                                    : 'Select pickup date and time',
                                style: TextStyle(
                                  color: _pickupDate != null
                                      ? Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color
                                      : Theme.of(context).hintColor,
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Delivery Date
                          InkWell(
                            onTap: isLoading
                                ? null
                                : () => _selectDate(context, false),
                            child: InputDecorator(
                              decoration: InputDecoration(
                                labelText: 'Delivery Date & Time *',
                                suffixIcon: const Icon(Icons.calendar_today),
                                border: const OutlineInputBorder(),
                                errorText: _deliveryDate == null ? null : null,
                              ),
                              child: Text(
                                _deliveryDate != null
                                    ? '${_deliveryDate!.day}/${_deliveryDate!.month}/${_deliveryDate!.year} at ${_deliveryDate!.hour.toString().padLeft(2, '0')}:${_deliveryDate!.minute.toString().padLeft(2, '0')}'
                                    : 'Select delivery date and time',
                                style: TextStyle(
                                  color: _deliveryDate != null
                                      ? Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color
                                      : Theme.of(context).hintColor,
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Bidding Close Date (Optional)
                          InkWell(
                            onTap: isLoading
                                ? null
                                : () => _selectBiddingCloseDate(context),
                            child: InputDecorator(
                              decoration: const InputDecoration(
                                labelText: 'Bidding Closes At (Optional)',
                                suffixIcon: Icon(Icons.timer),
                                border: OutlineInputBorder(),
                              ),
                              child: Text(
                                _biddingClosesAt != null
                                    ? '${_biddingClosesAt!.day}/${_biddingClosesAt!.month}/${_biddingClosesAt!.year} at ${_biddingClosesAt!.hour.toString().padLeft(2, '0')}:${_biddingClosesAt!.minute.toString().padLeft(2, '0')}'
                                    : 'Set bidding deadline (optional)',
                                style: TextStyle(
                                  color: _biddingClosesAt != null
                                      ? Theme.of(context)
                                          .textTheme
                                          .bodyLarge
                                          ?.color
                                      : Theme.of(context).hintColor,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(
                      height: 100), // Space for floating action button
                ],
              ),
            );
          },
        ),
      ),
      floatingActionButton: BlocBuilder<LoadBloc, LoadState>(
        builder: (context, state) {
          final isLoading = state is LoadLoading;

          return FloatingActionButton.extended(
            onPressed: isLoading ? null : _submitForm,
            icon: isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.send),
            label: Text(isLoading ? 'Creating...' : 'Post Load'),
          );
        },
      ),
    );
  }
}
