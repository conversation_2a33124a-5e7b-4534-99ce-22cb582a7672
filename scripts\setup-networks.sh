#!/bin/bash

# Docker Network Setup Script for LogiPool with <PERSON>raefik
# This script creates the necessary Docker networks for the application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    log_info "Checking if Docker is running..."
    
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    log_success "Docker is running"
}

# Create Traefik public network
create_traefik_network() {
    log_info "Creating Traefik public network..."
    
    # Check if network already exists
    if docker network ls | grep -q "traefik-public"; then
        log_warning "Network 'traefik-public' already exists"
        return 0
    fi
    
    # Create the network
    docker network create \
        --driver bridge \
        --subnet=**********/16 \
        --ip-range=************/20 \
        --gateway=********** \
        traefik-public
    
    log_success "Created 'traefik-public' network"
}

# Create application internal network (if needed)
create_app_network() {
    log_info "Checking application internal network..."
    
    # Check if network already exists
    if docker network ls | grep -q "logipool-network"; then
        log_warning "Network 'logipool-network' already exists"
        return 0
    fi
    
    # This network will be created by docker-compose, but we can create it manually if needed
    log_info "Application network will be created by docker-compose"
}

# List all networks
list_networks() {
    log_info "Current Docker networks:"
    docker network ls
}

# Cleanup old networks (optional)
cleanup_networks() {
    log_info "Cleaning up unused networks..."
    docker network prune -f
    log_success "Network cleanup completed"
}

# Main function
main() {
    log_info "Setting up Docker networks for LogiPool with Traefik..."
    
    check_docker
    create_traefik_network
    create_app_network
    list_networks
    
    log_success "Network setup completed successfully!"
    echo
    log_info "You can now run: docker-compose -f docker-compose.traefik.yml up -d"
}

# Handle script arguments
case "${1:-}" in
    "create")
        create_traefik_network
        ;;
    "cleanup")
        cleanup_networks
        ;;
    "list")
        list_networks
        ;;
    *)
        main
        ;;
esac
