package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.bid.BidCreateRequest;
import zw.co.kanjan.logipool.dto.bid.BidResponse;
import zw.co.kanjan.logipool.dto.bid.BidUpdateRequest;
import zw.co.kanjan.logipool.service.BidService;

@RestController
@RequestMapping("/api/bids")
@RequiredArgsConstructor
@Tag(name = "Bids", description = "Bid management APIs")
public class BidController {
    
    private final BidService bidService;
    
    @PostMapping
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Create new bid", description = "Create a new bid for a load")
    public ResponseEntity<BidResponse> createBid(@Valid @RequestBody BidCreateRequest request,
                                               Authentication authentication) {
        BidResponse response = bidService.createBid(request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @PutMapping("/{bidId}")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Update bid", description = "Update an existing bid")
    public ResponseEntity<BidResponse> updateBid(@PathVariable Long bidId,
                                               @Valid @RequestBody BidUpdateRequest request,
                                               Authentication authentication) {
        BidResponse response = bidService.updateBid(bidId, request, authentication.getName());
        return ResponseEntity.ok(response);
    }
    
    @DeleteMapping("/{bidId}/withdraw")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Withdraw bid", description = "Withdraw a pending bid")
    public ResponseEntity<Void> withdrawBid(@PathVariable Long bidId,
                                          Authentication authentication) {
        bidService.withdrawBid(bidId, authentication.getName());
        return ResponseEntity.ok().build();
    }
    
    @GetMapping("/{bidId}")
    @Operation(summary = "Get bid by ID", description = "Get bid details by ID")
    public ResponseEntity<BidResponse> getBidById(@PathVariable Long bidId) {
        BidResponse response = bidService.getBidById(bidId);
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/load/{loadId}")
    @Operation(summary = "Get bids for load", description = "Get paginated list of bids for a specific load")
    public ResponseEntity<Page<BidResponse>> getBidsForLoad(
            @PathVariable Long loadId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<BidResponse> bids = bidService.getBidsForLoad(loadId, pageable);
        return ResponseEntity.ok(bids);
    }
    
    @GetMapping("/my-bids")
    @PreAuthorize("hasRole('TRANSPORTER') or hasRole('ADMIN')")
    @Operation(summary = "Get my bids", description = "Get paginated list of current user's bids")
    public ResponseEntity<Page<BidResponse>> getMyBids(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir,
            Authentication authentication) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") ? 
                Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        
        Page<BidResponse> bids = bidService.getMyBids(authentication.getName(), pageable);
        return ResponseEntity.ok(bids);
    }
}
