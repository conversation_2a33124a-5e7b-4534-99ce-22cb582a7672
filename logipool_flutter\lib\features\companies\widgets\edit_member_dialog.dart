import 'package:flutter/material.dart';
import '../../../shared/models/company_member_model.dart';

class EditMemberDialog extends StatefulWidget {
  final CompanyMemberModel member;
  final Function(UpdateMemberRequest) onUpdate;

  const EditMemberDialog({
    super.key,
    required this.member,
    required this.onUpdate,
  });

  @override
  State<EditMemberDialog> createState() => _EditMemberDialogState();
}

class _EditMemberDialogState extends State<EditMemberDialog> {
  final _formKey = GlobalKey<FormState>();

  late CompanyRole _selectedRole;
  bool _useCustomPermissions = false;
  final Map<String, bool> _permissions = {};

  @override
  void initState() {
    super.initState();
    _selectedRole = widget.member.role;
    _initializePermissions();
  }

  void _initializePermissions() {
    _permissions['canManageMembers'] = widget.member.canManageMembers;
    _permissions['canManageLoads'] = widget.member.canManageLoads;
    _permissions['canUpdateLoadStatus'] = widget.member.canUpdateLoadStatus;
    _permissions['canUploadDocuments'] = widget.member.canUploadDocuments;
    _permissions['canGenerateInvoices'] = widget.member.canGenerateInvoices;
    _permissions['canViewFinancials'] = widget.member.canViewFinancials;
    _permissions['canTrackLocation'] = widget.member.canTrackLocation;

    // Check if using custom permissions
    _useCustomPermissions = !_hasDefaultPermissions();
  }

  bool _hasDefaultPermissions() {
    final defaultPermissions = _getDefaultPermissions(_selectedRole);
    return _permissions.entries.every(
        (entry) => entry.value == (defaultPermissions[entry.key] ?? false));
  }

  Map<String, bool> _getDefaultPermissions(CompanyRole role) {
    switch (role) {
      case CompanyRole.owner:
        return {
          'canManageMembers': true,
          'canManageLoads': true,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': true,
          'canViewFinancials': true,
          'canTrackLocation': true,
        };
      case CompanyRole.manager:
        return {
          'canManageMembers': true,
          'canManageLoads': true,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': true,
          'canViewFinancials': true,
          'canTrackLocation': false,
        };
      case CompanyRole.dispatcher:
        return {
          'canManageMembers': false,
          'canManageLoads': true,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': false,
          'canViewFinancials': false,
          'canTrackLocation': false,
        };
      case CompanyRole.driver:
        return {
          'canManageMembers': false,
          'canManageLoads': false,
          'canUpdateLoadStatus': true,
          'canUploadDocuments': true,
          'canGenerateInvoices': false,
          'canViewFinancials': false,
          'canTrackLocation': true,
        };
      case CompanyRole.accountant:
        return {
          'canManageMembers': false,
          'canManageLoads': false,
          'canUpdateLoadStatus': false,
          'canUploadDocuments': true,
          'canGenerateInvoices': true,
          'canViewFinancials': true,
          'canTrackLocation': false,
        };
      case CompanyRole.viewer:
        return {
          'canManageMembers': false,
          'canManageLoads': false,
          'canUpdateLoadStatus': false,
          'canUploadDocuments': false,
          'canGenerateInvoices': false,
          'canViewFinancials': false,
          'canTrackLocation': false,
        };
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.edit,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Edit Member: ${widget.member.displayName}',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimary,
                          ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Role selection
                      Text(
                        'Role',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      DropdownButtonFormField<CompanyRole>(
                        value: _selectedRole,
                        decoration: const InputDecoration(
                          border: OutlineInputBorder(),
                          hintText: 'Select role',
                        ),
                        items: CompanyRole.values.map((role) {
                          return DropdownMenuItem(
                            value: role,
                            child: Text(_getRoleDisplayName(role)),
                          );
                        }).toList(),
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedRole = value;
                              if (!_useCustomPermissions) {
                                _permissions
                                    .addAll(_getDefaultPermissions(value));
                              }
                            });
                          }
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'Please select a role';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 20),

                      // Custom permissions toggle
                      Row(
                        children: [
                          Switch(
                            value: _useCustomPermissions,
                            onChanged: (value) {
                              setState(() {
                                _useCustomPermissions = value;
                                if (!value) {
                                  _permissions.addAll(
                                      _getDefaultPermissions(_selectedRole));
                                }
                              });
                            },
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Custom Permissions',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                        ],
                      ),

                      if (_useCustomPermissions) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Permissions',
                          style: Theme.of(context).textTheme.titleMedium,
                        ),
                        const SizedBox(height: 8),
                        ..._buildPermissionSwitches(),
                      ],
                    ],
                  ),
                ),
              ),
            ),
            // Actions
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 12),
                  ElevatedButton(
                    onPressed: _updateMember,
                    child: const Text('Update Member'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> _buildPermissionSwitches() {
    final permissionLabels = {
      'canManageMembers': 'Manage Team Members',
      'canManageLoads': 'Manage Loads',
      'canUpdateLoadStatus': 'Update Load Status',
      'canUploadDocuments': 'Upload Documents',
      'canGenerateInvoices': 'Generate Invoices',
      'canViewFinancials': 'View Financial Data',
      'canTrackLocation': 'Track Location',
    };

    return permissionLabels.entries.map((entry) {
      return Padding(
        padding: const EdgeInsets.only(bottom: 8),
        child: Row(
          children: [
            Switch(
              value: _permissions[entry.key] ?? false,
              onChanged: (value) {
                setState(() {
                  _permissions[entry.key] = value;
                });
              },
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(entry.value),
            ),
          ],
        ),
      );
    }).toList();
  }

  String _getRoleDisplayName(CompanyRole role) {
    switch (role) {
      case CompanyRole.owner:
        return 'Owner';
      case CompanyRole.manager:
        return 'Manager';
      case CompanyRole.dispatcher:
        return 'Dispatcher';
      case CompanyRole.driver:
        return 'Driver';
      case CompanyRole.accountant:
        return 'Accountant';
      case CompanyRole.viewer:
        return 'Viewer';
    }
  }

  void _updateMember() {
    if (_formKey.currentState?.validate() ?? false) {
      final request = UpdateMemberRequest(
        memberId: widget.member.id,
        role: _selectedRole,
        canManageMembers: _useCustomPermissions
            ? (_permissions['canManageMembers'] ?? false)
            : null,
        canManageLoads: _useCustomPermissions
            ? (_permissions['canManageLoads'] ?? false)
            : null,
        canUpdateLoadStatus: _useCustomPermissions
            ? (_permissions['canUpdateLoadStatus'] ?? false)
            : null,
        canUploadDocuments: _useCustomPermissions
            ? (_permissions['canUploadDocuments'] ?? false)
            : null,
        canGenerateInvoices: _useCustomPermissions
            ? (_permissions['canGenerateInvoices'] ?? false)
            : null,
        canViewFinancials: _useCustomPermissions
            ? (_permissions['canViewFinancials'] ?? false)
            : null,
        canTrackLocation: _useCustomPermissions
            ? (_permissions['canTrackLocation'] ?? false)
            : null,
      );

      widget.onUpdate(request);
      Navigator.of(context).pop();
    }
  }
}
