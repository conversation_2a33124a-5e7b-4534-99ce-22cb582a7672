-- Add tracking_number column to loads table
-- This script adds the tracking_number column to the loads table for load tracking functionality

-- Check if the column already exists
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'loads' 
        AND column_name = 'tracking_number'
    ) THEN
        -- Add the tracking_number column
        ALTER TABLE loads ADD COLUMN tracking_number VARCHAR(20) UNIQUE;
        
        -- Create an index for better performance
        CREATE INDEX IF NOT EXISTS idx_loads_tracking_number ON loads(tracking_number);
        
        -- Update existing loads with tracking numbers
        -- Generate tracking numbers for existing loads
        UPDATE loads 
        SET tracking_number = 'LP-' || TO_CHAR(created_at, 'YYYYMMDD') || '-' || LPAD((id % 90000 + 10000)::text, 5, '0')
        WHERE tracking_number IS NULL;
        
        RAISE NOTICE 'tracking_number column added to loads table successfully';
    ELSE
        RAISE NOTICE 'tracking_number column already exists in loads table';
    END IF;
END $$;

-- Verify the column was added
SELECT 
    column_name, 
    data_type, 
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'loads' 
  AND column_name = 'tracking_number';
