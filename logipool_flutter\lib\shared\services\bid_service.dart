import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/bid_model.dart';
import '../models/paginated_response.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/logger.dart';
import 'auth_service.dart';

class BidService {
  static const String _bidsEndpoint = '${AppConstants.baseUrl}/bids';
  final AuthService _authService;

  BidService(this._authService);

  // Create a new bid
  Future<BidModel> createBid(BidCreateRequest request) async {
    try {
      Logger.info('Creating bid for load ${request.loadId}');
      
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.post(
        Uri.parse(_bidsEndpoint),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        Logger.info('Bid created successfully');
        return BidModel.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Failed to create bid');
      }
    } catch (e) {
      Logger.error('Error creating bid: $e');
      rethrow;
    }
  }

  // Update an existing bid
  Future<BidModel> updateBid(int bidId, BidUpdateRequest request) async {
    try {
      Logger.info('Updating bid $bidId');
      
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.put(
        Uri.parse('$_bidsEndpoint/$bidId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(request.toJson()),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        Logger.info('Bid updated successfully');
        return BidModel.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Failed to update bid');
      }
    } catch (e) {
      Logger.error('Error updating bid: $e');
      rethrow;
    }
  }

  // Withdraw a bid
  Future<void> withdrawBid(int bidId) async {
    try {
      Logger.info('Withdrawing bid $bidId');
      
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.delete(
        Uri.parse('$_bidsEndpoint/$bidId/withdraw'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        Logger.info('Bid withdrawn successfully');
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Failed to withdraw bid');
      }
    } catch (e) {
      Logger.error('Error withdrawing bid: $e');
      rethrow;
    }
  }

  // Get bid by ID
  Future<BidModel> getBidById(int bidId) async {
    try {
      Logger.info('Fetching bid $bidId');
      
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final response = await http.get(
        Uri.parse('$_bidsEndpoint/$bidId'),
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return BidModel.fromJson(data);
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Failed to fetch bid');
      }
    } catch (e) {
      Logger.error('Error fetching bid: $e');
      rethrow;
    }
  }

  // Get bids for a specific load
  Future<PaginatedResponse<BidModel>> getBidsForLoad(
    int loadId, {
    int page = 0,
    int size = 10,
    String sortBy = 'createdAt',
    String sortDir = 'desc',
  }) async {
    try {
      Logger.info('Fetching bids for load $loadId');
      
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final queryParams = {
        'page': page.toString(),
        'size': size.toString(),
        'sortBy': sortBy,
        'sortDir': sortDir,
      };

      final uri = Uri.parse('$_bidsEndpoint/load/$loadId')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return PaginatedResponse<BidModel>.fromJson(
          data,
          (json) => BidModel.fromJson(json as Map<String, dynamic>),
        );
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Failed to fetch bids');
      }
    } catch (e) {
      Logger.error('Error fetching bids for load: $e');
      rethrow;
    }
  }

  // Get current user's bids
  Future<PaginatedResponse<BidModel>> getMyBids({
    int page = 0,
    int size = 10,
    String sortBy = 'createdAt',
    String sortDir = 'desc',
  }) async {
    try {
      Logger.info('Fetching my bids');
      
      final token = await _authService.getToken();
      if (token == null) {
        throw Exception('Authentication token not found');
      }

      final queryParams = {
        'page': page.toString(),
        'size': size.toString(),
        'sortBy': sortBy,
        'sortDir': sortDir,
      };

      final uri = Uri.parse('$_bidsEndpoint/my-bids')
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: {
          'Authorization': 'Bearer $token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return PaginatedResponse<BidModel>.fromJson(
          data,
          (json) => BidModel.fromJson(json as Map<String, dynamic>),
        );
      } else {
        final error = jsonDecode(response.body);
        throw Exception(error['message'] ?? 'Failed to fetch my bids');
      }
    } catch (e) {
      Logger.error('Error fetching my bids: $e');
      rethrow;
    }
  }
}
