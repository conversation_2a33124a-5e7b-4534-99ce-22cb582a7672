package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "location_permissions", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"driver_id", "company_id"}))
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocationPermission {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "driver_id", nullable = false)
    @NotNull
    private User driver;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id", nullable = false)
    @NotNull
    private Company company;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "load_id")
    private Load load;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20, nullable = false)
    @NotNull
    private PermissionType permissionType;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Builder.Default
    private PermissionStatus status = PermissionStatus.PENDING;
    
    @Builder.Default
    private Boolean isActive = true;
    
    @Builder.Default
    private Boolean allowRealTimeTracking = false;
    
    @Builder.Default
    private Boolean allowHistoricalData = false;
    
    @Builder.Default
    private Boolean allowClientAccess = false;
    
    @Builder.Default
    private Boolean allowEmergencyAccess = true;
    
    private LocalDateTime validFrom;
    
    private LocalDateTime validUntil;
    
    @Column(columnDefinition = "TEXT")
    private String conditions;
    
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "granted_by")
    private User grantedBy;
    
    private LocalDateTime grantedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "revoked_by")
    private User revokedBy;
    
    private LocalDateTime revokedAt;
    
    @Column(columnDefinition = "TEXT")
    private String revokeReason;
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    public enum PermissionType {
        GENERAL,        // General location sharing permission
        LOAD_SPECIFIC,  // Permission for specific load
        EMERGENCY_ONLY, // Only for emergency situations
        COMPANY_WIDE    // Company-wide tracking permission
    }
    
    public enum PermissionStatus {
        PENDING,        // Permission request pending
        GRANTED,        // Permission granted
        DENIED,         // Permission denied
        REVOKED,        // Permission revoked
        EXPIRED         // Permission expired
    }
}
