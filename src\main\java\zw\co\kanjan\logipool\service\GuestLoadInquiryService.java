package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.guest.GuestLoadInquiryRequest;
import zw.co.kanjan.logipool.entity.GuestLoadInquiry;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.repository.GuestLoadInquiryRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class GuestLoadInquiryService {
    
    private final GuestLoadInquiryRepository inquiryRepository;
    private final UserRepository userRepository;
    private final EmailTemplateService emailTemplateService;
    private final NotificationService notificationService;
    
    private static final int MAX_INQUIRIES_PER_EMAIL_PER_DAY = 3;
    private static final int SPAM_PREVENTION_MINUTES = 15;
    
    @Transactional
    public GuestLoadInquiry submitInquiry(GuestLoadInquiryRequest request) {
        log.info("Processing guest load inquiry from: {}", request.getEmail());
        
        // Validate and prevent spam
        validateInquiryRequest(request);
        
        // Create and save inquiry
        GuestLoadInquiry inquiry = createInquiryFromRequest(request);
        inquiry = inquiryRepository.save(inquiry);
        
        // Send notifications
        sendInquiryNotifications(inquiry);
        
        log.info("Guest load inquiry created with ID: {}", inquiry.getId());
        return inquiry;
    }
    
    private void validateInquiryRequest(GuestLoadInquiryRequest request) {
        // Check for recent inquiries from same email (spam prevention)
        LocalDateTime spamCutoff = LocalDateTime.now().minusMinutes(SPAM_PREVENTION_MINUTES);
        List<GuestLoadInquiry> recentInquiries = inquiryRepository.findRecentInquiriesByEmail(
            request.getEmail(), spamCutoff);
        
        if (!recentInquiries.isEmpty()) {
            throw new BusinessException(
                "Please wait " + SPAM_PREVENTION_MINUTES + " minutes before submitting another inquiry.");
        }
        
        // Check daily limit
        LocalDateTime dayStart = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0);
        List<GuestLoadInquiry> todayInquiries = inquiryRepository.findRecentInquiriesByEmail(
            request.getEmail(), dayStart);
        
        if (todayInquiries.size() >= MAX_INQUIRIES_PER_EMAIL_PER_DAY) {
            throw new BusinessException(
                "Maximum " + MAX_INQUIRIES_PER_EMAIL_PER_DAY + " inquiries per day allowed per email address.");
        }
    }
    
    private GuestLoadInquiry createInquiryFromRequest(GuestLoadInquiryRequest request) {
        GuestLoadInquiry inquiry = new GuestLoadInquiry();
        
        // Contact information
        inquiry.setName(request.getName());
        inquiry.setEmail(request.getEmail());
        inquiry.setPhoneNumber(request.getPhoneNumber());
        inquiry.setCompany(request.getCompany());
        
        // Load information
        inquiry.setTitle(request.getTitle());
        inquiry.setDescription(request.getDescription());
        inquiry.setLoadType(request.getLoadType());
        inquiry.setWeight(request.getWeight());
        inquiry.setDimensions(request.getDimensions());
        
        // Location and timing
        inquiry.setPickupLocation(request.getPickupLocation());
        inquiry.setDeliveryLocation(request.getDeliveryLocation());
        inquiry.setPickupDate(request.getPickupDate());
        inquiry.setDeliveryDate(request.getDeliveryDate());
        
        // Additional options
        inquiry.setRequiresSpecialHandling(request.getRequiresSpecialHandling());
        inquiry.setIsUrgent(request.getIsUrgent());
        inquiry.setSpecialInstructions(request.getSpecialInstructions());
        
        // System fields
        inquiry.setIpAddress(request.getIpAddress());
        inquiry.setUserAgent(request.getUserAgent());
        inquiry.setStatus(GuestLoadInquiry.InquiryStatus.NEW);
        
        return inquiry;
    }
    
    private void sendInquiryNotifications(GuestLoadInquiry inquiry) {
        try {
            // Send confirmation email to customer
            sendCustomerConfirmationEmail(inquiry);
            
            // Send notification to admins
            sendAdminNotification(inquiry);
            
        } catch (Exception e) {
            log.error("Failed to send notifications for inquiry ID: {}", inquiry.getId(), e);
            // Don't fail the inquiry creation if notifications fail
        }
    }
    
    private void sendCustomerConfirmationEmail(GuestLoadInquiry inquiry) {
        String subject = "Load Request Received - LogiPool";
        String body = buildCustomerConfirmationEmail(inquiry);
        
        notificationService.sendHtmlEmail(inquiry.getEmail(), subject, body);
        log.info("Confirmation email sent to: {}", inquiry.getEmail());
    }
    
    private void sendAdminNotification(GuestLoadInquiry inquiry) {
        // Find admin users to notify
        List<User> admins = userRepository.findAdminUsers();
        
        String subject = "New Guest Load Inquiry - " + inquiry.getTitle();
        String body = buildAdminNotificationEmail(inquiry);
        
        for (User admin : admins) {
            notificationService.sendHtmlEmail(admin.getEmail(), subject, body);
            
            // Also send in-app notification
            notificationService.sendNotification(
                admin,
                "New Guest Load Inquiry",
                "A new guest load inquiry has been submitted: " + inquiry.getTitle(),
                "GUEST_INQUIRY"
            );
        }
        
        log.info("Admin notifications sent for inquiry ID: {}", inquiry.getId());
    }
    
    private String buildCustomerConfirmationEmail(GuestLoadInquiry inquiry) {
        return String.format("""
            Dear %s,
            
            Thank you for your load request submission. We have received your inquiry with the following details:
            
            Load Title: %s
            Pickup Location: %s
            Delivery Location: %s
            Pickup Date: %s
            Weight: %.1f kg
            %s
            
            Our team will review your request and connect you with verified logistics companies within 24 hours. 
            You will receive competitive quotes via email and phone.
            
            Reference ID: #%d
            
            If you have any questions, please contact us:
            - Phone: +263 4 123 4567
            - WhatsApp: +263 77 123 4567
            - Email: <EMAIL>
            
            Best regards,
            The LogiPool Team
            """,
            inquiry.getName(),
            inquiry.getTitle(),
            inquiry.getPickupLocation(),
            inquiry.getDeliveryLocation(),
            inquiry.getPickupDate(),
            inquiry.getWeight(),
            inquiry.getIsUrgent() ? "\n⚡ URGENT REQUEST" : "",
            inquiry.getId()
        );
    }
    
    private String buildAdminNotificationEmail(GuestLoadInquiry inquiry) {
        return String.format("""
            New Guest Load Inquiry Received
            
            Customer Details:
            - Name: %s
            - Email: %s
            - Phone: %s
            - Company: %s
            
            Load Details:
            - Title: %s
            - Description: %s
            - Type: %s
            - Weight: %.1f kg
            - Dimensions: %s
            - Pickup: %s
            - Delivery: %s
            - Pickup Date: %s
            - Delivery Date: %s
            - Special Handling: %s
            - Urgent: %s
            - Special Instructions: %s
            
            Inquiry ID: #%d
            Submitted: %s
            IP Address: %s
            
            Please review and contact the customer within 24 hours.
            """,
            inquiry.getName(),
            inquiry.getEmail(),
            inquiry.getPhoneNumber(),
            inquiry.getCompany() != null ? inquiry.getCompany() : "Not specified",
            inquiry.getTitle(),
            inquiry.getDescription(),
            inquiry.getLoadType(),
            inquiry.getWeight(),
            inquiry.getDimensions() != null ? inquiry.getDimensions() : "Not specified",
            inquiry.getPickupLocation(),
            inquiry.getDeliveryLocation(),
            inquiry.getPickupDate(),
            inquiry.getDeliveryDate() != null ? inquiry.getDeliveryDate().toString() : "Not specified",
            inquiry.getRequiresSpecialHandling() ? "Yes" : "No",
            inquiry.getIsUrgent() ? "Yes" : "No",
            inquiry.getSpecialInstructions() != null ? inquiry.getSpecialInstructions() : "None",
            inquiry.getId(),
            inquiry.getCreatedAt(),
            inquiry.getIpAddress()
        );
    }
    
    @Transactional(readOnly = true)
    public Page<GuestLoadInquiry> getAllInquiries(Pageable pageable) {
        return inquiryRepository.findAll(pageable);
    }
    
    @Transactional(readOnly = true)
    public Page<GuestLoadInquiry> getInquiriesByStatus(GuestLoadInquiry.InquiryStatus status, Pageable pageable) {
        return inquiryRepository.findByStatus(status, pageable);
    }
    
    @Transactional(readOnly = true)
    public GuestLoadInquiry getInquiryById(Long id) {
        return inquiryRepository.findById(id)
            .orElseThrow(() -> new BusinessException("Guest load inquiry not found with ID: " + id));
    }
    
    @Transactional
    public GuestLoadInquiry updateInquiryStatus(Long id, GuestLoadInquiry.InquiryStatus status, String notes) {
        GuestLoadInquiry inquiry = getInquiryById(id);
        inquiry.setStatus(status);
        
        if (notes != null && !notes.trim().isEmpty()) {
            inquiry.setAdminNotes(notes);
        }
        
        if (status == GuestLoadInquiry.InquiryStatus.CONTACTED) {
            inquiry.setContactedAt(LocalDateTime.now());
        } else if (status == GuestLoadInquiry.InquiryStatus.REVIEWED) {
            inquiry.setProcessedAt(LocalDateTime.now());
        }
        
        return inquiryRepository.save(inquiry);
    }
    
    @Transactional
    public GuestLoadInquiry assignInquiry(Long id, Long adminUserId) {
        GuestLoadInquiry inquiry = getInquiryById(id);
        User admin = userRepository.findById(adminUserId)
            .orElseThrow(() -> new BusinessException("Admin user not found"));
        
        inquiry.setAssignedTo(adminUserId);
        if (inquiry.getStatus() == GuestLoadInquiry.InquiryStatus.NEW) {
            inquiry.setStatus(GuestLoadInquiry.InquiryStatus.REVIEWED);
            inquiry.setProcessedAt(LocalDateTime.now());
        }
        
        return inquiryRepository.save(inquiry);
    }
}
