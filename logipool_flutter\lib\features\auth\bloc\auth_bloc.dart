import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../shared/services/auth_service.dart';
import '../../../shared/models/user_model.dart';
import '../../../core/errors/api_exception.dart';

// Events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;

  const AuthLoginRequested({
    required this.email,
    required this.password,
  });

  @override
  List<Object> get props => [email, password];
}

class AuthRegisterRequested extends AuthEvent {
  final String firstName;
  final String lastName;
  final String email;
  final String password;
  final List<String>? roles;
  final String? phoneNumber;

  const AuthRegisterRequested({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.password,
    this.roles,
    this.phoneNumber,
  });

  @override
  List<Object?> get props => [
        firstName,
        lastName,
        email,
        password,
        roles,
        phoneNumber,
      ];
}

class AuthLogoutRequested extends AuthEvent {}

class AuthProfileUpdateRequested extends AuthEvent {
  final String? firstName;
  final String? lastName;
  final String? phoneNumber;
  final String? email;

  const AuthProfileUpdateRequested({
    this.firstName,
    this.lastName,
    this.phoneNumber,
    this.email,
  });

  @override
  List<Object?> get props => [firstName, lastName, phoneNumber, email];
}

class AuthPasswordChangeRequested extends AuthEvent {
  final String currentPassword;
  final String newPassword;

  const AuthPasswordChangeRequested({
    required this.currentPassword,
    required this.newPassword,
  });

  @override
  List<Object> get props => [currentPassword, newPassword];
}

class AuthForgotPasswordRequested extends AuthEvent {
  final String email;

  const AuthForgotPasswordRequested({required this.email});

  @override
  List<Object> get props => [email];
}

class AuthResetPasswordRequested extends AuthEvent {
  final String token;
  final String newPassword;
  final String confirmPassword;

  const AuthResetPasswordRequested({
    required this.token,
    required this.newPassword,
    required this.confirmPassword,
  });

  @override
  List<Object> get props => [token, newPassword, confirmPassword];
}

class AuthStatusChanged extends AuthEvent {
  final bool isAuthenticated;
  final UserModel? user;

  const AuthStatusChanged({
    required this.isAuthenticated,
    this.user,
  });

  @override
  List<Object?> get props => [isAuthenticated, user];
}

// States
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {}

class AuthLoading extends AuthState {}

class AuthAuthenticated extends AuthState {
  final UserModel user;

  const AuthAuthenticated({required this.user});

  @override
  List<Object> get props => [user];
}

class AuthUnauthenticated extends AuthState {}

class AuthError extends AuthState {
  final String message;
  final String? errorCode;

  const AuthError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

class AuthSuccess extends AuthState {
  final String message;

  const AuthSuccess({required this.message});

  @override
  List<Object> get props => [message];
}

// BLoC
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthService _authService;

  AuthBloc({required AuthService authService})
      : _authService = authService,
        super(AuthInitial()) {
    // Listen to auth service changes
    _authService.addListener(_onAuthServiceChanged);

    // Set initial state based on current auth status
    if (_authService.isLoggedIn && _authService.currentUser != null) {
      emit(AuthAuthenticated(user: _authService.currentUser!));
    } else {
      emit(AuthUnauthenticated());
    }

    on<AuthLoginRequested>(_onLoginRequested);
    on<AuthRegisterRequested>(_onRegisterRequested);
    on<AuthLogoutRequested>(_onLogoutRequested);
    on<AuthProfileUpdateRequested>(_onProfileUpdateRequested);
    on<AuthPasswordChangeRequested>(_onPasswordChangeRequested);
    on<AuthForgotPasswordRequested>(_onForgotPasswordRequested);
    on<AuthResetPasswordRequested>(_onResetPasswordRequested);
    on<AuthStatusChanged>(_onStatusChanged);
  }

  void _onAuthServiceChanged() {
    add(AuthStatusChanged(
      isAuthenticated: _authService.isLoggedIn,
      user: _authService.currentUser,
    ));
  }

  Future<void> _onLoginRequested(
    AuthLoginRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final user = await _authService.login(
        email: event.email,
        password: event.password,
      );
      emit(AuthAuthenticated(user: user));
    } on ApiException catch (e) {
      emit(AuthError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(AuthError(message: 'Login failed: ${e.toString()}'));
    }
  }

  Future<void> _onRegisterRequested(
    AuthRegisterRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      final user = await _authService.register(
        firstName: event.firstName,
        lastName: event.lastName,
        email: event.email,
        password: event.password,
        roles: event.roles,
        phoneNumber: event.phoneNumber,
      );
      emit(AuthAuthenticated(user: user));
    } on ApiException catch (e) {
      emit(AuthError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(AuthError(message: 'Registration failed: ${e.toString()}'));
    }
  }

  Future<void> _onLogoutRequested(
    AuthLogoutRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _authService.logout();
      emit(AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails on server, clear local state
      emit(AuthUnauthenticated());
    }
  }

  Future<void> _onProfileUpdateRequested(
    AuthProfileUpdateRequested event,
    Emitter<AuthState> emit,
  ) async {
    if (state is! AuthAuthenticated) return;

    emit(AuthLoading());

    try {
      await _authService.updateProfile(
        firstName: event.firstName,
        lastName: event.lastName,
        phoneNumber: event.phoneNumber,
        email: event.email,
      );

      if (_authService.currentUser != null) {
        emit(AuthAuthenticated(user: _authService.currentUser!));
        emit(AuthSuccess(message: 'Profile updated successfully'));
      }
    } on ApiException catch (e) {
      emit(AuthError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(AuthError(message: 'Profile update failed: ${e.toString()}'));
    }
  }

  Future<void> _onPasswordChangeRequested(
    AuthPasswordChangeRequested event,
    Emitter<AuthState> emit,
  ) async {
    if (state is! AuthAuthenticated) return;

    emit(AuthLoading());

    try {
      await _authService.changePassword(
        currentPassword: event.currentPassword,
        newPassword: event.newPassword,
      );

      if (_authService.currentUser != null) {
        emit(AuthAuthenticated(user: _authService.currentUser!));
        emit(AuthSuccess(message: 'Password changed successfully'));
      }
    } on ApiException catch (e) {
      emit(AuthError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(AuthError(message: 'Password change failed: ${e.toString()}'));
    }
  }

  Future<void> _onForgotPasswordRequested(
    AuthForgotPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _authService.forgotPassword(event.email);
      emit(AuthSuccess(message: 'Password reset email sent'));
    } on ApiException catch (e) {
      emit(AuthError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(AuthError(message: 'Failed to send reset email: ${e.toString()}'));
    }
  }

  Future<void> _onResetPasswordRequested(
    AuthResetPasswordRequested event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthLoading());

    try {
      await _authService.resetPassword(
        token: event.token,
        newPassword: event.newPassword,
        confirmPassword: event.confirmPassword,
      );
      emit(AuthSuccess(message: 'Password reset successfully'));
    } on ApiException catch (e) {
      emit(AuthError(
        message: e.message,
        errorCode: e.statusCode.toString(),
      ));
    } catch (e) {
      emit(AuthError(message: 'Password reset failed: ${e.toString()}'));
    }
  }

  void _onStatusChanged(
    AuthStatusChanged event,
    Emitter<AuthState> emit,
  ) {
    if (event.isAuthenticated && event.user != null) {
      emit(AuthAuthenticated(user: event.user!));
    } else {
      emit(AuthUnauthenticated());
    }
  }

  @override
  Future<void> close() {
    _authService.removeListener(_onAuthServiceChanged);
    return super.close();
  }
}
