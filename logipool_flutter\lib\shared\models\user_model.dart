import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  final String id;
  final String? username;
  final String firstName;
  final String lastName;
  final String email;
  final String? phoneNumber;
  final String role;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'active')
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final CompanyInfo? company;

  UserModel({
    required this.id,
    this.username,
    required this.firstName,
    required this.lastName,
    required this.email,
    this.phoneNumber,
    required this.role,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    this.company,
  });

  String get fullName => '$firstName $lastName';
  String get initials =>
      '${firstName.isNotEmpty ? firstName[0] : ''}${lastName.isNotEmpty ? lastName[0] : ''}';
  String? get companyId => company?.id;

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  UserModel copyWith({
    String? id,
    String? username,
    String? firstName,
    String? lastName,
    String? email,
    String? phoneNumber,
    String? role,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    CompanyInfo? company,
  }) {
    return UserModel(
      id: id ?? this.id,
      username: username ?? this.username,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      company: company ?? this.company,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'UserModel(id: $id, fullName: $fullName, email: $email, role: $role)';
  }
}

@JsonSerializable()
class CompanyInfo {
  final String id;
  final String name;
  final String? description;
  final String? address;
  final String? phoneNumber;
  final String? email;
  final String? website;
  final String verificationStatus;
  final DateTime createdAt;
  final DateTime updatedAt;

  CompanyInfo({
    required this.id,
    required this.name,
    this.description,
    this.address,
    this.phoneNumber,
    this.email,
    this.website,
    required this.verificationStatus,
    required this.createdAt,
    required this.updatedAt,
  });

  bool get isVerified => verificationStatus == 'VERIFIED';
  bool get isPending => verificationStatus == 'PENDING';
  bool get isRejected => verificationStatus == 'REJECTED';
  bool get isSuspended => verificationStatus == 'SUSPENDED';

  factory CompanyInfo.fromJson(Map<String, dynamic> json) =>
      _$CompanyInfoFromJson(json);
  Map<String, dynamic> toJson() => _$CompanyInfoToJson(this);

  CompanyInfo copyWith({
    String? id,
    String? name,
    String? description,
    String? address,
    String? phoneNumber,
    String? email,
    String? website,
    String? verificationStatus,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CompanyInfo(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      address: address ?? this.address,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      website: website ?? this.website,
      verificationStatus: verificationStatus ?? this.verificationStatus,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CompanyInfo && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'CompanyModel(id: $id, name: $name, verificationStatus: $verificationStatus)';
  }
}
