import 'package:equatable/equatable.dart';

class LoadModel extends Equatable {
  final int? id;
  final String? trackingNumber;
  final String title;
  final String? description;
  final String cargoType;
  final double weight;
  final String weightUnit;
  final double? volume;
  final String? volumeUnit;
  final String pickupLocation;
  final double? pickupLatitude;
  final double? pickupLongitude;
  final String deliveryLocation;
  final double? deliveryLatitude;
  final double? deliveryLongitude;
  final DateTime pickupDate;
  final DateTime deliveryDate;
  final double? estimatedDistance;
  final String? distanceUnit;
  final LoadType? loadType;
  final PaymentType? paymentType;
  final double? paymentRate;
  final String? paymentUnit;
  final double? estimatedValue;
  final LoadStatus status;
  final Priority priority;
  final bool isVerified;
  final bool requiresInsurance;
  final bool requiresSpecialHandling;
  final String? specialInstructions;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? biddingClosesAt;
  final int? clientId;
  final String? clientName;
  final int? assignedCompanyId;
  final String? assignedCompanyName;
  final int bidCount;
  final bool isLiveTrackingActive;
  final String? liveTrackingUser;
  final DateTime? liveTrackingStartedAt;

  const LoadModel({
    this.id,
    this.trackingNumber,
    required this.title,
    this.description,
    required this.cargoType,
    required this.weight,
    this.weightUnit = 'kg',
    this.volume,
    this.volumeUnit = 'm3',
    required this.pickupLocation,
    this.pickupLatitude,
    this.pickupLongitude,
    required this.deliveryLocation,
    this.deliveryLatitude,
    this.deliveryLongitude,
    required this.pickupDate,
    required this.deliveryDate,
    this.estimatedDistance,
    this.distanceUnit = 'km',
    this.loadType,
    this.paymentType,
    this.paymentRate,
    this.paymentUnit,
    this.estimatedValue,
    this.status = LoadStatus.posted,
    this.priority = Priority.normal,
    this.isVerified = false,
    this.requiresInsurance = false,
    this.requiresSpecialHandling = false,
    this.specialInstructions,
    this.createdAt,
    this.updatedAt,
    this.biddingClosesAt,
    this.clientId,
    this.clientName,
    this.assignedCompanyId,
    this.assignedCompanyName,
    this.bidCount = 0,
    this.isLiveTrackingActive = false,
    this.liveTrackingUser,
    this.liveTrackingStartedAt,
  });

  factory LoadModel.fromJson(Map<String, dynamic> json) {
    return LoadModel(
      id: json['id'],
      trackingNumber: json['trackingNumber'],
      title: json['title'] ?? '',
      description: json['description'],
      cargoType: json['cargoType'] ?? '',
      weight: (json['weight'] as num?)?.toDouble() ?? 0.0,
      weightUnit: json['weightUnit'] ?? 'kg',
      volume: (json['volume'] as num?)?.toDouble(),
      volumeUnit: json['volumeUnit'] ?? 'm3',
      pickupLocation: json['pickupLocation'] ?? '',
      pickupLatitude: (json['pickupLatitude'] as num?)?.toDouble(),
      pickupLongitude: (json['pickupLongitude'] as num?)?.toDouble(),
      deliveryLocation: json['deliveryLocation'] ?? '',
      deliveryLatitude: (json['deliveryLatitude'] as num?)?.toDouble(),
      deliveryLongitude: (json['deliveryLongitude'] as num?)?.toDouble(),
      pickupDate: DateTime.parse(json['pickupDate'] as String),
      deliveryDate: DateTime.parse(json['deliveryDate'] as String),
      estimatedDistance: (json['estimatedDistance'] as num?)?.toDouble(),
      distanceUnit: json['distanceUnit'] as String? ?? 'km',
      loadType: json['loadType'] != null
          ? LoadType.values.firstWhere(
              (e) =>
                  e.name.toUpperCase() ==
                  json['loadType'].toString().toUpperCase(),
              orElse: () => LoadType.local,
            )
          : null,
      paymentType: json['paymentType'] != null
          ? PaymentType.values.firstWhere(
              (e) =>
                  e.name.toUpperCase() ==
                  json['paymentType'].toString().toUpperCase(),
              orElse: () => PaymentType.negotiable,
            )
          : null,
      paymentRate: (json['paymentRate'] as num?)?.toDouble(),
      paymentUnit: json['paymentUnit'] as String?,
      estimatedValue: (json['estimatedValue'] as num?)?.toDouble(),
      status: LoadStatus.fromBackendValue(json['status'].toString()),
      priority: Priority.values.firstWhere(
        (e) =>
            e.name.toUpperCase() == json['priority'].toString().toUpperCase(),
        orElse: () => Priority.normal,
      ),
      isVerified: json['isVerified'] as bool? ?? false,
      requiresInsurance: json['requiresInsurance'] as bool? ?? false,
      requiresSpecialHandling:
          json['requiresSpecialHandling'] as bool? ?? false,
      specialInstructions: json['specialInstructions'] as String?,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'] as String)
          : null,
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      biddingClosesAt: json['biddingClosesAt'] != null
          ? DateTime.parse(json['biddingClosesAt'] as String)
          : null,
      clientId: json['clientId'] as int?,
      clientName: json['clientName'] as String?,
      assignedCompanyId: json['assignedCompanyId'] as int?,
      assignedCompanyName: json['assignedCompanyName'] as String?,
      bidCount: json['bidCount'] as int? ?? 0,
      isLiveTrackingActive: json['isLiveTrackingActive'] as bool? ?? false,
      liveTrackingUser: json['liveTrackingUser'] as String?,
      liveTrackingStartedAt: json['liveTrackingStartedAt'] != null
          ? DateTime.parse(json['liveTrackingStartedAt'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) 'id': id,
      if (trackingNumber != null) 'trackingNumber': trackingNumber,
      'title': title,
      if (description != null) 'description': description,
      'cargoType': cargoType,
      'weight': weight,
      'weightUnit': weightUnit,
      if (volume != null) 'volume': volume,
      if (volumeUnit != null) 'volumeUnit': volumeUnit,
      'pickupLocation': pickupLocation,
      if (pickupLatitude != null) 'pickupLatitude': pickupLatitude,
      if (pickupLongitude != null) 'pickupLongitude': pickupLongitude,
      'deliveryLocation': deliveryLocation,
      if (deliveryLatitude != null) 'deliveryLatitude': deliveryLatitude,
      if (deliveryLongitude != null) 'deliveryLongitude': deliveryLongitude,
      'pickupDate': pickupDate.toIso8601String(),
      'deliveryDate': deliveryDate.toIso8601String(),
      if (estimatedDistance != null) 'estimatedDistance': estimatedDistance,
      if (distanceUnit != null) 'distanceUnit': distanceUnit,
      if (loadType != null) 'loadType': loadType!.name.toUpperCase(),
      if (paymentType != null) 'paymentType': paymentType!.name.toUpperCase(),
      if (paymentRate != null) 'paymentRate': paymentRate,
      if (paymentUnit != null) 'paymentUnit': paymentUnit,
      if (estimatedValue != null) 'estimatedValue': estimatedValue,
      'status': status.name.toUpperCase(),
      'priority': priority.name.toUpperCase(),
      'isVerified': isVerified,
      'requiresInsurance': requiresInsurance,
      'requiresSpecialHandling': requiresSpecialHandling,
      if (specialInstructions != null)
        'specialInstructions': specialInstructions,
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
      if (biddingClosesAt != null)
        'biddingClosesAt': biddingClosesAt!.toIso8601String(),
      if (clientId != null) 'clientId': clientId,
      if (clientName != null) 'clientName': clientName,
      if (assignedCompanyId != null) 'assignedCompanyId': assignedCompanyId,
      if (assignedCompanyName != null)
        'assignedCompanyName': assignedCompanyName,
      'bidCount': bidCount,
      'isLiveTrackingActive': isLiveTrackingActive,
      if (liveTrackingUser != null) 'liveTrackingUser': liveTrackingUser,
      if (liveTrackingStartedAt != null)
        'liveTrackingStartedAt': liveTrackingStartedAt!.toIso8601String(),
    };
  }

  LoadModel copyWith({
    int? id,
    String? trackingNumber,
    String? title,
    String? description,
    String? cargoType,
    double? weight,
    String? weightUnit,
    double? volume,
    String? volumeUnit,
    String? pickupLocation,
    double? pickupLatitude,
    double? pickupLongitude,
    String? deliveryLocation,
    double? deliveryLatitude,
    double? deliveryLongitude,
    DateTime? pickupDate,
    DateTime? deliveryDate,
    double? estimatedDistance,
    String? distanceUnit,
    LoadType? loadType,
    PaymentType? paymentType,
    double? paymentRate,
    String? paymentUnit,
    double? estimatedValue,
    LoadStatus? status,
    Priority? priority,
    bool? isVerified,
    bool? requiresInsurance,
    bool? requiresSpecialHandling,
    String? specialInstructions,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? biddingClosesAt,
    int? clientId,
    String? clientName,
    int? assignedCompanyId,
    String? assignedCompanyName,
    int? bidCount,
    bool? isLiveTrackingActive,
    String? liveTrackingUser,
    DateTime? liveTrackingStartedAt,
  }) {
    return LoadModel(
      id: id ?? this.id,
      trackingNumber: trackingNumber ?? this.trackingNumber,
      title: title ?? this.title,
      description: description ?? this.description,
      cargoType: cargoType ?? this.cargoType,
      weight: weight ?? this.weight,
      weightUnit: weightUnit ?? this.weightUnit,
      volume: volume ?? this.volume,
      volumeUnit: volumeUnit ?? this.volumeUnit,
      pickupLocation: pickupLocation ?? this.pickupLocation,
      pickupLatitude: pickupLatitude ?? this.pickupLatitude,
      pickupLongitude: pickupLongitude ?? this.pickupLongitude,
      deliveryLocation: deliveryLocation ?? this.deliveryLocation,
      deliveryLatitude: deliveryLatitude ?? this.deliveryLatitude,
      deliveryLongitude: deliveryLongitude ?? this.deliveryLongitude,
      pickupDate: pickupDate ?? this.pickupDate,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      estimatedDistance: estimatedDistance ?? this.estimatedDistance,
      distanceUnit: distanceUnit ?? this.distanceUnit,
      loadType: loadType ?? this.loadType,
      paymentType: paymentType ?? this.paymentType,
      paymentRate: paymentRate ?? this.paymentRate,
      paymentUnit: paymentUnit ?? this.paymentUnit,
      estimatedValue: estimatedValue ?? this.estimatedValue,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      isVerified: isVerified ?? this.isVerified,
      requiresInsurance: requiresInsurance ?? this.requiresInsurance,
      requiresSpecialHandling:
          requiresSpecialHandling ?? this.requiresSpecialHandling,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      biddingClosesAt: biddingClosesAt ?? this.biddingClosesAt,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      assignedCompanyId: assignedCompanyId ?? this.assignedCompanyId,
      assignedCompanyName: assignedCompanyName ?? this.assignedCompanyName,
      bidCount: bidCount ?? this.bidCount,
      isLiveTrackingActive: isLiveTrackingActive ?? this.isLiveTrackingActive,
      liveTrackingUser: liveTrackingUser ?? this.liveTrackingUser,
      liveTrackingStartedAt: liveTrackingStartedAt ?? this.liveTrackingStartedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        trackingNumber,
        title,
        description,
        cargoType,
        weight,
        weightUnit,
        volume,
        volumeUnit,
        pickupLocation,
        pickupLatitude,
        pickupLongitude,
        deliveryLocation,
        deliveryLatitude,
        deliveryLongitude,
        pickupDate,
        deliveryDate,
        estimatedDistance,
        distanceUnit,
        loadType,
        paymentType,
        paymentRate,
        paymentUnit,
        estimatedValue,
        status,
        priority,
        isVerified,
        requiresInsurance,
        requiresSpecialHandling,
        specialInstructions,
        createdAt,
        updatedAt,
        biddingClosesAt,
        clientId,
        clientName,
        assignedCompanyId,
        assignedCompanyName,
        bidCount,
        isLiveTrackingActive,
        liveTrackingUser,
        liveTrackingStartedAt,
      ];
}

enum LoadType {
  local,
  regional,
  contract,
  onceOff;

  String get displayName {
    switch (this) {
      case LoadType.local:
        return 'Local';
      case LoadType.regional:
        return 'Regional';
      case LoadType.contract:
        return 'Contract';
      case LoadType.onceOff:
        return 'Once Off';
    }
  }
}

enum PaymentType {
  perKm,
  perTonne,
  fixedRate,
  negotiable;

  String get displayName {
    switch (this) {
      case PaymentType.perKm:
        return 'Per KM';
      case PaymentType.perTonne:
        return 'Per Tonne';
      case PaymentType.fixedRate:
        return 'Fixed Rate';
      case PaymentType.negotiable:
        return 'Negotiable';
    }
  }
}

enum LoadStatus {
  posted,
  biddingClosed,
  assigned,
  inTransit,
  delivered,
  completed,
  cancelled;

  String get displayName {
    switch (this) {
      case LoadStatus.posted:
        return 'Posted';
      case LoadStatus.biddingClosed:
        return 'Bidding Closed';
      case LoadStatus.assigned:
        return 'Assigned';
      case LoadStatus.inTransit:
        return 'In Transit';
      case LoadStatus.delivered:
        return 'Delivered';
      case LoadStatus.completed:
        return 'Completed';
      case LoadStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// Converts the enum to the backend format
  String get backendValue {
    switch (this) {
      case LoadStatus.posted:
        return 'POSTED';
      case LoadStatus.biddingClosed:
        return 'BIDDING_CLOSED';
      case LoadStatus.assigned:
        return 'ASSIGNED';
      case LoadStatus.inTransit:
        return 'IN_TRANSIT';
      case LoadStatus.delivered:
        return 'DELIVERED';
      case LoadStatus.completed:
        return 'COMPLETED';
      case LoadStatus.cancelled:
        return 'CANCELLED';
    }
  }

  /// Converts from backend format to enum
  static LoadStatus fromBackendValue(String value) {
    switch (value.toUpperCase()) {
      case 'POSTED':
        return LoadStatus.posted;
      case 'BIDDING_CLOSED':
        return LoadStatus.biddingClosed;
      case 'ASSIGNED':
        return LoadStatus.assigned;
      case 'IN_TRANSIT':
        return LoadStatus.inTransit;
      case 'DELIVERED':
        return LoadStatus.delivered;
      case 'COMPLETED':
        return LoadStatus.completed;
      case 'CANCELLED':
        return LoadStatus.cancelled;
      default:
        return LoadStatus.posted;
    }
  }

  bool get isActive =>
      this == LoadStatus.posted || this == LoadStatus.biddingClosed;
  bool get isCompleted =>
      this == LoadStatus.delivered ||
      this == LoadStatus.completed ||
      this == LoadStatus.cancelled;
}

enum Priority {
  low,
  normal,
  high,
  urgent;

  String get displayName {
    switch (this) {
      case Priority.low:
        return 'Low';
      case Priority.normal:
        return 'Normal';
      case Priority.high:
        return 'High';
      case Priority.urgent:
        return 'Urgent';
    }
  }
}
