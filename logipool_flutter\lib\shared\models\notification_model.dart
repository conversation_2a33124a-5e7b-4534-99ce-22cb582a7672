import 'package:json_annotation/json_annotation.dart';

part 'notification_model.g.dart';

@JsonSerializable()
class NotificationModel {
  final int? id;
  final String title;
  final String message;
  final NotificationType type;
  final DateTime timestamp;
  final Map<String, dynamic>? data;
  final bool read;
  final NotificationPriority priority;
  final int? referenceId;
  final String? referenceType;
  final DateTime? readAt;
  final DateTime? expiresAt;

  const NotificationModel({
    this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.timestamp,
    this.data,
    this.read = false,
    this.priority = NotificationPriority.medium,
    this.referenceId,
    this.referenceType,
    this.readAt,
    this.expiresAt,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) =>
      _$NotificationModelFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationModelToJson(this);

  NotificationModel copyWith({
    int? id,
    String? title,
    String? message,
    NotificationType? type,
    DateTime? timestamp,
    Map<String, dynamic>? data,
    bool? read,
    NotificationPriority? priority,
    int? referenceId,
    String? referenceType,
    DateTime? readAt,
    DateTime? expiresAt,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      timestamp: timestamp ?? this.timestamp,
      data: data ?? this.data,
      read: read ?? this.read,
      priority: priority ?? this.priority,
      referenceId: referenceId ?? this.referenceId,
      referenceType: referenceType ?? this.referenceType,
      readAt: readAt ?? this.readAt,
      expiresAt: expiresAt ?? this.expiresAt,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'NotificationModel{id: $id, title: $title, type: $type, read: $read}';
  }
}

@JsonEnum()
enum NotificationType {
  @JsonValue('LOAD_POSTED')
  loadPosted,
  @JsonValue('BID_RECEIVED')
  bidReceived,
  @JsonValue('BID_ACCEPTED')
  bidAccepted,
  @JsonValue('BID_REJECTED')
  bidRejected,
  @JsonValue('LOAD_STATUS_UPDATE')
  loadStatusUpdate,
  @JsonValue('PAYMENT_RECEIVED')
  paymentReceived,
  @JsonValue('DOCUMENT_REQUIRED')
  documentRequired,
  @JsonValue('SYSTEM_ANNOUNCEMENT')
  systemAnnouncement,
  @JsonValue('ACCOUNT_VERIFICATION')
  accountVerification,
  @JsonValue('SECURITY_ALERT')
  securityAlert,
  @JsonValue('TRACKING_UPDATE')
  trackingUpdate;

  String get displayName {
    switch (this) {
      case NotificationType.loadPosted:
        return 'Load Posted';
      case NotificationType.bidReceived:
        return 'Bid Received';
      case NotificationType.bidAccepted:
        return 'Bid Accepted';
      case NotificationType.bidRejected:
        return 'Bid Rejected';
      case NotificationType.loadStatusUpdate:
        return 'Load Status Update';
      case NotificationType.paymentReceived:
        return 'Payment Received';
      case NotificationType.documentRequired:
        return 'Document Required';
      case NotificationType.systemAnnouncement:
        return 'System Announcement';
      case NotificationType.accountVerification:
        return 'Account Verification';
      case NotificationType.securityAlert:
        return 'Security Alert';
      case NotificationType.trackingUpdate:
        return 'Tracking Update';
    }
  }

  String get description {
    switch (this) {
      case NotificationType.loadPosted:
        return 'New load available for bidding';
      case NotificationType.bidReceived:
        return 'New bid received on your load';
      case NotificationType.bidAccepted:
        return 'Your bid has been accepted';
      case NotificationType.bidRejected:
        return 'Your bid has been rejected';
      case NotificationType.loadStatusUpdate:
        return 'Load status has been updated';
      case NotificationType.paymentReceived:
        return 'Payment has been received';
      case NotificationType.documentRequired:
        return 'Document verification required';
      case NotificationType.systemAnnouncement:
        return 'System announcement';
      case NotificationType.accountVerification:
        return 'Account verification update';
      case NotificationType.securityAlert:
        return 'Security alert';
      case NotificationType.trackingUpdate:
        return 'Load tracking update';
    }
  }
}

@JsonEnum()
enum NotificationPriority {
  @JsonValue('LOW')
  low,
  @JsonValue('MEDIUM')
  medium,
  @JsonValue('HIGH')
  high,
  @JsonValue('URGENT')
  urgent;

  String get displayName {
    switch (this) {
      case NotificationPriority.low:
        return 'Low';
      case NotificationPriority.medium:
        return 'Medium';
      case NotificationPriority.high:
        return 'High';
      case NotificationPriority.urgent:
        return 'Urgent';
    }
  }

  int get level {
    switch (this) {
      case NotificationPriority.low:
        return 1;
      case NotificationPriority.medium:
        return 2;
      case NotificationPriority.high:
        return 3;
      case NotificationPriority.urgent:
        return 4;
    }
  }
}

// Request DTOs
@JsonSerializable()
class NotificationMarkReadRequest {
  final List<int> notificationIds;

  const NotificationMarkReadRequest({
    required this.notificationIds,
  });

  factory NotificationMarkReadRequest.fromJson(Map<String, dynamic> json) =>
      _$NotificationMarkReadRequestFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationMarkReadRequestToJson(this);
}

@JsonSerializable()
class NotificationDeleteRequest {
  final List<int> notificationIds;

  const NotificationDeleteRequest({
    required this.notificationIds,
  });

  factory NotificationDeleteRequest.fromJson(Map<String, dynamic> json) =>
      _$NotificationDeleteRequestFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationDeleteRequestToJson(this);
}

// Response DTOs
@JsonSerializable()
class NotificationSummary {
  final int totalCount;
  final int unreadCount;
  final int todayCount;
  final int weekCount;
  final Map<String, int> typeBreakdown;
  final Map<String, int> priorityBreakdown;

  const NotificationSummary({
    required this.totalCount,
    required this.unreadCount,
    required this.todayCount,
    required this.weekCount,
    required this.typeBreakdown,
    required this.priorityBreakdown,
  });

  factory NotificationSummary.fromJson(Map<String, dynamic> json) =>
      _$NotificationSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationSummaryToJson(this);
}

// Notification Settings
@JsonSerializable()
class NotificationSettings {
  final bool pushNotifications;
  final bool emailNotifications;
  final bool smsNotifications;
  final Map<String, bool> typeSettings;
  final List<String> quietHours;
  final bool doNotDisturb;

  const NotificationSettings({
    this.pushNotifications = true,
    this.emailNotifications = true,
    this.smsNotifications = false,
    this.typeSettings = const {},
    this.quietHours = const [],
    this.doNotDisturb = false,
  });

  factory NotificationSettings.fromJson(Map<String, dynamic> json) =>
      _$NotificationSettingsFromJson(json);

  Map<String, dynamic> toJson() => _$NotificationSettingsToJson(this);

  NotificationSettings copyWith({
    bool? pushNotifications,
    bool? emailNotifications,
    bool? smsNotifications,
    Map<String, bool>? typeSettings,
    List<String>? quietHours,
    bool? doNotDisturb,
  }) {
    return NotificationSettings(
      pushNotifications: pushNotifications ?? this.pushNotifications,
      emailNotifications: emailNotifications ?? this.emailNotifications,
      smsNotifications: smsNotifications ?? this.smsNotifications,
      typeSettings: typeSettings ?? this.typeSettings,
      quietHours: quietHours ?? this.quietHours,
      doNotDisturb: doNotDisturb ?? this.doNotDisturb,
    );
  }
}
