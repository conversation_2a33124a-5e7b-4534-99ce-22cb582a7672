import 'package:flutter/material.dart';
import '../../../shared/models/payment_model.dart';
import '../../../core/utils/date_formatter.dart';

class PaymentCard extends StatelessWidget {
  final PaymentModel payment;
  final VoidCallback? onTap;
  final VoidCallback? onProcess;
  final VoidCallback? onCancel;

  const PaymentCard({
    super.key,
    required this.payment,
    this.onTap,
    this.onProcess,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Payment #${payment.id}',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        if (payment.loadTitle != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            payment.loadTitle!,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey[600],
                                ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  _buildStatusChip(context),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'Amount',
                      '\$${payment.amount.toStringAsFixed(2)}',
                      Icons.attach_money,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'Method',
                      payment.method.displayName,
                      _getPaymentMethodIcon(payment.method),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'Type',
                      payment.type.displayName,
                      Icons.category,
                    ),
                  ),
                  Expanded(
                    child: _buildInfoItem(
                      context,
                      'Date',
                      payment.createdAt != null
                          ? DateFormatter.formatDate(payment.createdAt!)
                          : 'N/A',
                      Icons.calendar_today,
                    ),
                  ),
                ],
              ),
              if (payment.payerName != null || payment.payeeName != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    if (payment.payerName != null)
                      Expanded(
                        child: _buildInfoItem(
                          context,
                          'Payer',
                          payment.payerName!,
                          Icons.person,
                        ),
                      ),
                    if (payment.payeeName != null)
                      Expanded(
                        child: _buildInfoItem(
                          context,
                          'Payee',
                          payment.payeeName!,
                          Icons.person_outline,
                        ),
                      ),
                  ],
                ),
              ],
              if (payment.commissionAmount > 0) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        'Commission',
                        '\$${payment.commissionAmount.toStringAsFixed(2)}',
                        Icons.percent,
                      ),
                    ),
                    Expanded(
                      child: _buildInfoItem(
                        context,
                        'Net Amount',
                        '\$${payment.netAmount.toStringAsFixed(2)}',
                        Icons.account_balance_wallet,
                      ),
                    ),
                  ],
                ),
              ],
              if (payment.description != null) ...[
                const SizedBox(height: 8),
                Text(
                  payment.description!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.grey[600],
                        fontStyle: FontStyle.italic,
                      ),
                ),
              ],
              if (onProcess != null || onCancel != null) ...[
                const SizedBox(height: 12),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (onCancel != null) ...[
                      TextButton.icon(
                        onPressed: onCancel,
                        icon: const Icon(Icons.cancel, size: 16),
                        label: const Text('Cancel'),
                        style: TextButton.styleFrom(
                          foregroundColor: Colors.red,
                        ),
                      ),
                      const SizedBox(width: 8),
                    ],
                    if (onProcess != null)
                      ElevatedButton.icon(
                        onPressed: onProcess,
                        icon: const Icon(Icons.payment, size: 16),
                        label: const Text('Process'),
                      ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    Color backgroundColor;
    Color textColor;

    switch (payment.status) {
      case PaymentStatus.pending:
        backgroundColor = Colors.orange.shade100;
        textColor = Colors.orange.shade800;
        break;
      case PaymentStatus.processing:
        backgroundColor = Colors.blue.shade100;
        textColor = Colors.blue.shade800;
        break;
      case PaymentStatus.completed:
        backgroundColor = Colors.green.shade100;
        textColor = Colors.green.shade800;
        break;
      case PaymentStatus.failed:
        backgroundColor = Colors.red.shade100;
        textColor = Colors.red.shade800;
        break;
      case PaymentStatus.cancelled:
        backgroundColor = Colors.grey.shade100;
        textColor = Colors.grey.shade800;
        break;
      case PaymentStatus.refunded:
        backgroundColor = Colors.purple.shade100;
        textColor = Colors.purple.shade800;
        break;
      case PaymentStatus.disputed:
        backgroundColor = Colors.amber.shade100;
        textColor = Colors.amber.shade800;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        payment.status.displayName,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.creditCard:
      case PaymentMethod.debitCard:
        return Icons.credit_card;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
      case PaymentMethod.mobileMoney:
        return Icons.phone_android;
      case PaymentMethod.paypal:
        return Icons.payment;
      case PaymentMethod.stripe:
        return Icons.payment;
      case PaymentMethod.cash:
        return Icons.money;
    }
  }
}
