import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

import '../../core/network/api_client.dart';
import '../../core/constants/app_constants.dart';
import '../../core/errors/api_exception.dart';
import '../models/user_model.dart';
import 'storage_service.dart';

class AuthService extends ChangeNotifier {
  static AuthService? _instance;
  final ApiClient _apiClient;
  final StorageService _storageService;

  UserModel? _currentUser;
  bool _isLoggedIn = false;
  bool _isLoading = false;

  AuthService({
    required ApiClient apiClient,
    required StorageService storageService,
  })  : _apiClient = apiClient,
        _storageService = storageService {
    _instance = this;
    _initializeAuth();
  }

  static AuthService get instance {
    if (_instance == null) {
      throw Exception('AuthService not initialized. Call AuthService() first.');
    }
    return _instance!;
  }

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;
  bool get isLoading => _isLoading;
  String? get userRole => _currentUser?.role;
  bool get isAdmin => userRole == AppConstants.roleAdmin;
  bool get isClient => userRole == AppConstants.roleClient;
  bool get isTransporter => userRole == AppConstants.roleTransporter;

  // Methods for compatibility
  Future<UserModel?> getCurrentUser() async => _currentUser;
  Future<String?> getToken() async => await _storageService.getToken();

  Future<void> _initializeAuth() async {
    _setLoading(true);

    try {
      final token = await _storageService.getToken();
      if (token != null && !JwtDecoder.isExpired(token)) {
        final userData = await _storageService.getUserData();
        if (userData != null) {
          _currentUser = UserModel.fromJson(userData);
          _isLoggedIn = true;
        } else {
          // Token exists but no user data, fetch user profile
          await _fetchUserProfile();
        }
      } else {
        // Token expired or doesn't exist
        await _storageService.clearTokens();
        await _storageService.clearUserData();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Auth initialization error: $e');
      }
      await _storageService.clearTokens();
      await _storageService.clearUserData();
    } finally {
      _setLoading(false);
    }
  }

  Future<UserModel> login({
    required String email,
    required String password,
  }) async {
    _setLoading(true);

    try {
      final response = await _apiClient.post(
        '${AppConstants.authEndpoint}/signin',
        data: {
          'email': email,
          'password': password,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        final token = data['token'] as String;
        final refreshToken = data['refreshToken'] as String?;

        // Save tokens first
        await _storageService.saveToken(token);
        if (refreshToken != null) {
          await _storageService.saveRefreshToken(refreshToken);
        }

        // Fetch complete user profile after successful login
        await _fetchUserProfile();

        if (_currentUser == null) {
          throw ApiException(
            message: 'Failed to fetch user profile after login',
            statusCode: 0,
          );
        }

        return _currentUser!;
      } else {
        throw ApiException(
          message: 'Login failed',
          statusCode: response.statusCode ?? 0,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'Login failed: ${e.toString()}',
        statusCode: 0,
      );
    } finally {
      _setLoading(false);
    }
  }

  Future<UserModel> register({
    required String firstName,
    required String lastName,
    required String email,
    required String password,
    List<String>? roles,
    String? phoneNumber,
  }) async {
    _setLoading(true);

    try {
      final response = await _apiClient.post(
        '${AppConstants.authEndpoint}/signup',
        data: {
          'firstName': firstName,
          'lastName': lastName,
          'email': email,
          'password': password,
          if (phoneNumber != null) 'phoneNumber': phoneNumber,
          if (roles != null) 'roles': roles,
        },
      );

      if (response.statusCode == 200) {
        // Backend returns success message, need to login to get user data
        return await login(email: email, password: password);
      } else {
        throw ApiException(
          message: 'Registration failed',
          statusCode: response.statusCode ?? 0,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'Registration failed: ${e.toString()}',
        statusCode: 0,
      );
    } finally {
      _setLoading(false);
    }
  }

  Future<void> logout() async {
    _setLoading(true);

    try {
      // Call logout endpoint to invalidate token on server
      await _apiClient.post('${AppConstants.authEndpoint}/logout');
    } catch (e) {
      // Continue with local logout even if server call fails
      if (kDebugMode) {
        print('Logout API call failed: $e');
      }
    }

    // Clear local data
    await _storageService.clearTokens();
    await _storageService.clearUserData();

    _currentUser = null;
    _isLoggedIn = false;

    notifyListeners();
    _setLoading(false);
  }

  Future<void> _fetchUserProfile() async {
    try {
      final response =
          await _apiClient.get('${AppConstants.authEndpoint}/profile');

      if (response.statusCode == 200) {
        final userData = response.data as Map<String, dynamic>;
        await _storageService.saveUserData(userData);
        _currentUser = UserModel.fromJson(userData);
        _isLoggedIn = true;
        notifyListeners();
      } else {
        throw ApiException(
          message: 'Failed to fetch user profile',
          statusCode: response.statusCode ?? 0,
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to fetch user profile: $e');
      }
      await _storageService.clearTokens();
      await _storageService.clearUserData();
      _currentUser = null;
      _isLoggedIn = false;
      notifyListeners();
      rethrow;
    }
  }

  // TODO: Implement when backend profile update endpoint is available
  Future<void> updateProfile({
    String? firstName,
    String? lastName,
    String? phoneNumber,
    String? email,
  }) async {
    if (!_isLoggedIn || _currentUser == null) {
      throw ApiException(message: 'User not logged in', statusCode: 401);
    }

    _setLoading(true);

    try {
      // For now, just update local data
      // TODO: Replace with actual API call when backend endpoint is available
      final updatedUserData = {
        ..._currentUser!.toJson(),
        if (firstName != null) 'firstName': firstName,
        if (lastName != null) 'lastName': lastName,
        if (phoneNumber != null) 'phoneNumber': phoneNumber,
        if (email != null) 'email': email,
      };

      await _storageService.saveUserData(updatedUserData);
      _currentUser = UserModel.fromJson(updatedUserData);
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  // TODO: Implement when backend password change endpoint is available
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    if (!_isLoggedIn) {
      throw ApiException(message: 'User not logged in', statusCode: 401);
    }

    _setLoading(true);

    try {
      // TODO: Replace with actual API call when backend endpoint is available
      throw ApiException(
        message: 'Password change not yet implemented in backend',
        statusCode: 501,
      );
    } finally {
      _setLoading(false);
    }
  }

  Future<void> forgotPassword(String email) async {
    _setLoading(true);

    try {
      final response = await _apiClient.post(
        '${AppConstants.authEndpoint}/forgot-password',
        data: {
          'email': email,
        },
      );

      if (response.statusCode == 200) {
        // Success - password reset email sent
        return;
      } else {
        throw ApiException(
          message: 'Failed to send password reset email',
          statusCode: response.statusCode ?? 0,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'Failed to send password reset email: ${e.toString()}',
        statusCode: 0,
      );
    } finally {
      _setLoading(false);
    }
  }

  Future<void> resetPassword({
    required String token,
    required String newPassword,
    required String confirmPassword,
  }) async {
    _setLoading(true);

    try {
      final response = await _apiClient.post(
        '${AppConstants.authEndpoint}/reset-password',
        data: {
          'token': token,
          'newPassword': newPassword,
          'confirmPassword': confirmPassword,
        },
      );

      if (response.statusCode == 200) {
        // Success - password reset
        return;
      } else {
        throw ApiException(
          message: 'Failed to reset password',
          statusCode: response.statusCode ?? 0,
        );
      }
    } catch (e) {
      if (e is ApiException) {
        rethrow;
      }
      throw ApiException(
        message: 'Failed to reset password: ${e.toString()}',
        statusCode: 0,
      );
    } finally {
      _setLoading(false);
    }
  }

  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }
}
