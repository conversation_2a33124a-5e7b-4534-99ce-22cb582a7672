import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../core/constants/app_constants.dart';

class ContactIntegrationWidget extends StatelessWidget {
  final String? customMessage;
  final bool showLabels;
  final bool isHorizontal;
  final double iconSize;
  final EdgeInsets padding;

  const ContactIntegrationWidget({
    super.key,
    this.customMessage,
    this.showLabels = true,
    this.isHorizontal = true,
    this.iconSize = 24,
    this.padding = const EdgeInsets.all(16),
  });

  @override
  Widget build(BuildContext context) {
    final contacts = [
      ContactOption(
        label: 'WhatsApp',
        icon: Icons.chat,
        color: Colors.green,
        onTap: () => _launchWhatsApp(context),
      ),
      ContactOption(
        label: 'Call',
        icon: Icons.phone,
        color: Colors.blue,
        onTap: () => _launchPhone(context),
      ),
      ContactOption(
        label: 'Email',
        icon: Icons.email,
        color: Colors.orange,
        onTap: () => _launchEmail(context),
      ),
      // Only show live chat if enabled in constants
      if (AppConstants.liveChatEnabled)
        ContactOption(
          label: 'Live Chat',
          icon: Icons.chat_bubble,
          color: Colors.purple,
          onTap: () => _launchTawkTo(context),
        ),
    ];

    return Container(
      padding: padding,
      child: isHorizontal
          ? Wrap(
              alignment: WrapAlignment.center,
              spacing: 12,
              runSpacing: 8,
              children: contacts.map((contact) => _buildContactButton(context, contact)).toList(),
            )
          : Column(
              mainAxisSize: MainAxisSize.min,
              children: contacts.map((contact) => Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: _buildContactButton(context, contact),
              )).toList(),
            ),
    );
  }

  Widget _buildContactButton(BuildContext context, ContactOption contact) {
    if (showLabels) {
      return OutlinedButton.icon(
        onPressed: contact.onTap,
        icon: Icon(contact.icon, size: iconSize),
        label: Text(contact.label),
        style: OutlinedButton.styleFrom(
          foregroundColor: contact.color,
          side: BorderSide(color: contact.color),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
        ),
      );
    } else {
      return IconButton(
        onPressed: contact.onTap,
        icon: Icon(contact.icon, size: iconSize),
        color: contact.color,
        tooltip: contact.label,
        style: IconButton.styleFrom(
          backgroundColor: contact.color.withOpacity(0.1),
          shape: const CircleBorder(),
        ),
      );
    }
  }

  Future<void> _launchWhatsApp(BuildContext context) async {
    final phoneNumber = AppConstants.whatsAppNumberForDialing;
    final message = customMessage ?? 'Hello! I need help with LogiPool services. Can you assist me?';
    final url = 'https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}';

    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          _showErrorSnackBar(context, 'Could not open WhatsApp. Please install WhatsApp or contact us directly.');
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, 'Error opening WhatsApp. Please try again.');
      }
    }
  }

  Future<void> _launchPhone(BuildContext context) async {
    final phoneNumber = 'tel:${AppConstants.phoneNumberForDialing}';
    try {
      if (await canLaunchUrl(Uri.parse(phoneNumber))) {
        await launchUrl(Uri.parse(phoneNumber));
      } else {
        if (context.mounted) {
          _showErrorSnackBar(context, 'Could not make phone call.');
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, 'Error making phone call.');
      }
    }
  }

  Future<void> _launchEmail(BuildContext context) async {
    final subject = 'LogiPool Inquiry';
    final body = customMessage ?? 'Hello,\n\nI have an inquiry about LogiPool services.\n\nBest regards';
    final email = 'mailto:${AppConstants.companyEmail}?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}';

    try {
      if (await canLaunchUrl(Uri.parse(email))) {
        await launchUrl(Uri.parse(email));
      } else {
        if (context.mounted) {
          _showErrorSnackBar(context, 'Could not open email client.');
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showErrorSnackBar(context, 'Error opening email client.');
      }
    }
  }

  Future<void> _launchTawkTo(BuildContext context) async {
    // This would typically integrate with Tawk.to chat widget
    // For now, we'll show a placeholder message
    // In a real implementation, you'd integrate Tawk.to SDK or JavaScript widget

    try {
      // Use URL from constants
      final tawkToUrl = AppConstants.tawkToUrl;
      if (await canLaunchUrl(Uri.parse(tawkToUrl))) {
        await launchUrl(Uri.parse(tawkToUrl), mode: LaunchMode.externalApplication);
      } else {
        if (context.mounted) {
          _showInfoDialog(context);
        }
      }
    } catch (e) {
      if (context.mounted) {
        _showInfoDialog(context);
      }
    }
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Live Chat'),
        content: const Text(
          'Live chat is coming soon! For immediate assistance, please use WhatsApp, call us, or send an email.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class ContactOption {
  final String label;
  final IconData icon;
  final Color color;
  final VoidCallback onTap;

  ContactOption({
    required this.label,
    required this.icon,
    required this.color,
    required this.onTap,
  });
}

class QuickContactCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final List<ContactOption> contacts;

  const QuickContactCard({
    super.key,
    required this.title,
    required this.subtitle,
    required this.contacts,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            Wrap(
              alignment: WrapAlignment.center,
              spacing: 12,
              runSpacing: 8,
              children: contacts.map((contact) => OutlinedButton.icon(
                onPressed: contact.onTap,
                icon: Icon(contact.icon, size: 18),
                label: Text(contact.label),
                style: OutlinedButton.styleFrom(
                  foregroundColor: contact.color,
                  side: BorderSide(color: contact.color),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                ),
              )).toList(),
            ),
          ],
        ),
      ),
    );
  }
}

class FloatingContactButton extends StatelessWidget {
  final String? customMessage;
  final ContactType type;

  const FloatingContactButton({
    super.key,
    this.customMessage,
    this.type = ContactType.whatsapp,
  });

  @override
  Widget build(BuildContext context) {
    final config = _getContactConfig(type);
    
    return FloatingActionButton(
      onPressed: () => _launchContact(context, type, customMessage),
      backgroundColor: config.color,
      child: Icon(config.icon, color: Colors.white),
      tooltip: config.label,
    );
  }

  ContactConfig _getContactConfig(ContactType type) {
    switch (type) {
      case ContactType.whatsapp:
        return ContactConfig(
          label: 'WhatsApp',
          icon: Icons.chat,
          color: Colors.green,
        );
      case ContactType.phone:
        return ContactConfig(
          label: 'Call',
          icon: Icons.phone,
          color: Colors.blue,
        );
      case ContactType.email:
        return ContactConfig(
          label: 'Email',
          icon: Icons.email,
          color: Colors.orange,
        );
      case ContactType.chat:
        return ContactConfig(
          label: 'Live Chat',
          icon: Icons.chat_bubble,
          color: Colors.purple,
        );
    }
  }

  Future<void> _launchContact(BuildContext context, ContactType type, String? message) async {
    final widget = ContactIntegrationWidget(customMessage: message);
    
    switch (type) {
      case ContactType.whatsapp:
        await widget._launchWhatsApp(context);
        break;
      case ContactType.phone:
        await widget._launchPhone(context);
        break;
      case ContactType.email:
        await widget._launchEmail(context);
        break;
      case ContactType.chat:
        await widget._launchTawkTo(context);
        break;
    }
  }
}

enum ContactType { whatsapp, phone, email, chat }

class ContactConfig {
  final String label;
  final IconData icon;
  final Color color;

  ContactConfig({
    required this.label,
    required this.icon,
    required this.color,
  });
}
