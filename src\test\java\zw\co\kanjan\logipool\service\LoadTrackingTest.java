package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import zw.co.kanjan.logipool.dto.load.LoadCreateRequest;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.entity.Role;
import zw.co.kanjan.logipool.mapper.LoadMapper;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.UserRepository;
import zw.co.kanjan.logipool.repository.CompanyRepository;
import zw.co.kanjan.logipool.repository.CompanyMemberRepository;
import zw.co.kanjan.logipool.service.NotificationService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class LoadTrackingTest {

    @Mock
    private LoadRepository loadRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private CompanyRepository companyRepository;

    @Mock
    private CompanyMemberRepository companyMemberRepository;

    @Mock
    private LoadMapper loadMapper;

    @Mock
    private NotificationService notificationService;

    @InjectMocks
    private LoadService loadService;

    private User testUser;
    private LoadCreateRequest loadRequest;
    private Load testLoad;
    private LoadResponse loadResponse;

    @BeforeEach
    void setUp() {
        // Setup test user
        testUser = User.builder()
                .id(1L)
                .username("<EMAIL>") // Use email as username for backward compatibility
                .email("<EMAIL>")
                .firstName("Test")
                .lastName("Client")
                .roles(Set.of(Role.builder().name(Role.RoleName.CLIENT).build()))
                .build();

        // Setup load request
        loadRequest = new LoadCreateRequest();
        loadRequest.setTitle("Test Load");
        loadRequest.setDescription("Test load description");
        loadRequest.setCargoType("General");
        loadRequest.setPickupLocation("Pickup Location");
        loadRequest.setDeliveryLocation("Delivery Location");
        loadRequest.setEstimatedValue(BigDecimal.valueOf(1000));
        loadRequest.setPaymentRate(BigDecimal.valueOf(500));
        loadRequest.setWeight(BigDecimal.valueOf(100));
        loadRequest.setPickupDate(LocalDateTime.now().plusDays(1));
        loadRequest.setDeliveryDate(LocalDateTime.now().plusDays(3));

        // Setup test load
        testLoad = Load.builder()
                .id(1L)
                .title("Test Load")
                .trackingNumber("LP-20250715-12345")
                .client(testUser)
                .status(Load.LoadStatus.POSTED)
                .build();

        // Setup load response
        loadResponse = new LoadResponse();
        loadResponse.setId(1L);
        loadResponse.setTitle("Test Load");
        loadResponse.setTrackingNumber("LP-20250715-12345");
        loadResponse.setStatus(Load.LoadStatus.POSTED);
    }

    @Test
    void createLoad_ShouldGenerateTrackingNumber() {
        // Arrange
        when(userRepository.findByUsername("testclient")).thenReturn(Optional.of(testUser));
        when(loadMapper.toEntity(loadRequest)).thenReturn(testLoad);
        when(loadRepository.findByTrackingNumber(anyString())).thenReturn(Optional.empty());
        when(loadRepository.save(any(Load.class))).thenReturn(testLoad);
        when(loadMapper.toResponse(testLoad)).thenReturn(loadResponse);

        // Act
        LoadResponse result = loadService.createLoad(loadRequest, "testclient");

        // Assert
        assertNotNull(result);
        assertNotNull(result.getTrackingNumber());
        assertTrue(result.getTrackingNumber().startsWith("LP-"));
        assertTrue(result.getTrackingNumber().matches("LP-\\d{8}-\\d{5}"));
        verify(loadRepository).save(any(Load.class));
    }

    @Test
    void getLoadByTrackingNumber_ShouldReturnLoad() {
        // Arrange
        String trackingNumber = "LP-20250715-12345";
        when(loadRepository.findByTrackingNumber(trackingNumber)).thenReturn(Optional.of(testLoad));
        when(loadMapper.toResponse(testLoad)).thenReturn(loadResponse);

        // Act
        LoadResponse result = loadService.getLoadByTrackingNumber(trackingNumber);

        // Assert
        assertNotNull(result);
        assertEquals(trackingNumber, result.getTrackingNumber());
        assertEquals("Test Load", result.getTitle());
        verify(loadRepository).findByTrackingNumber(trackingNumber);
    }

    @Test
    void getLoadByTrackingNumber_WithInvalidTrackingNumber_ShouldThrowException() {
        // Arrange
        String invalidTrackingNumber = "INVALID-123";
        when(loadRepository.findByTrackingNumber(invalidTrackingNumber)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(Exception.class, () -> {
            loadService.getLoadByTrackingNumber(invalidTrackingNumber);
        });
        verify(loadRepository).findByTrackingNumber(invalidTrackingNumber);
    }
}
