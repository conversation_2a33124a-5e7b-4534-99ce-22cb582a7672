-- Add isFeatured column to vehicles table
-- This migration adds a boolean column to mark vehicles as featured for the home page

-- Add the isFeatured column with default value false
ALTER TABLE vehicles 
ADD COLUMN is_featured BOOLEAN NOT NULL DEFAULT FALSE;

-- Add an index for better performance when querying featured vehicles
CREATE INDEX idx_vehicles_featured ON vehicles (is_featured, is_publicly_visible, public_approval_status, status);

-- Add a comment to document the column
ALTER TABLE vehicles 
MODIFY COLUMN is_featured BOOLEAN NOT NULL DEFAULT FALSE 
COMMENT 'Whether this vehicle is featured on the home page';

-- Optionally, mark some existing vehicles as featured for demonstration
-- This can be removed or modified based on your needs
UPDATE vehicles 
SET is_featured = TRUE 
WHERE is_publicly_visible = TRUE 
  AND public_approval_status = 'APPROVED' 
  AND status = 'AVAILABLE'
  AND is_available_for_rent = TRUE
ORDER BY created_at DESC 
LIMIT 6;
