package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import zw.co.kanjan.logipool.entity.TrackingAuditLog;
import zw.co.kanjan.logipool.entity.TrackingVerification;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.repository.TrackingAuditLogRepository;
import zw.co.kanjan.logipool.repository.TrackingVerificationRepository;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TrackingSecurityServiceTest {

    @Mock
    private TrackingVerificationRepository trackingVerificationRepository;

    @Mock
    private TrackingAuditLogRepository auditLogRepository;

    @InjectMocks
    private TrackingSecurityService trackingSecurityService;

    private TrackingVerification testVerification;

    @BeforeEach
    void setUp() {
        // Set configuration values using reflection
        ReflectionTestUtils.setField(trackingSecurityService, "maxRequestsPerHour", 3);
        ReflectionTestUtils.setField(trackingSecurityService, "maxIpRequestsPerHour", 10);
        ReflectionTestUtils.setField(trackingSecurityService, "maxAccessCount", 5);
        ReflectionTestUtils.setField(trackingSecurityService, "monitoringEnabled", true);

        testVerification = TrackingVerification.builder()
                .id(1L)
                .token("test-token-123")
                .trackingNumber("LP123456789")
                .email("<EMAIL>")
                .verificationMethod(TrackingVerification.VerificationMethod.EMAIL)
                .expiryDate(LocalDateTime.now().plusHours(24))
                .isVerified(true)
                .isUsed(false)
                .accessCount(2)
                .ipAddress("***********")
                .build();
    }

    @Test
    void checkRateLimit_WithinLimits_ShouldPass() {
        // Arrange
        when(trackingVerificationRepository.countRecentRequestsByTrackingNumberAndContact(
                anyString(), anyString(), any(LocalDateTime.class))).thenReturn(2L);
        when(trackingVerificationRepository.countRecentRequestsByIpAddress(
                anyString(), any(LocalDateTime.class))).thenReturn(5L);

        // Act & Assert
        assertDoesNotThrow(() ->
                trackingSecurityService.checkRateLimit("LP123456789", "<EMAIL>", "***********")
        );
    }

    @Test
    void checkRateLimit_ContactLimitExceeded_ShouldThrowException() {
        // Arrange
        when(trackingVerificationRepository.countRecentRequestsByTrackingNumberAndContact(
                anyString(), anyString(), any(LocalDateTime.class))).thenReturn(5L);
        when(auditLogRepository.save(any(TrackingAuditLog.class))).thenReturn(new TrackingAuditLog());

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                trackingSecurityService.checkRateLimit("LP123456789", "<EMAIL>", "***********")
        );

        assertEquals("Too many verification requests. Please try again later.", exception.getMessage());
        verify(auditLogRepository).save(any(TrackingAuditLog.class));
    }

    @Test
    void checkRateLimit_IpLimitExceeded_ShouldThrowException() {
        // Arrange
        when(trackingVerificationRepository.countRecentRequestsByTrackingNumberAndContact(
                anyString(), anyString(), any(LocalDateTime.class))).thenReturn(2L);
        when(trackingVerificationRepository.countRecentRequestsByIpAddress(
                anyString(), any(LocalDateTime.class))).thenReturn(15L);
        when(auditLogRepository.save(any(TrackingAuditLog.class))).thenReturn(new TrackingAuditLog());

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                trackingSecurityService.checkRateLimit("LP123456789", "<EMAIL>", "***********")
        );

        assertEquals("Too many requests from this location. Please try again later.", exception.getMessage());
        verify(auditLogRepository).save(any(TrackingAuditLog.class));
    }

    @Test
    void validateTrackingAccess_ValidVerification_ShouldPass() {
        // Act & Assert
        assertDoesNotThrow(() ->
                trackingSecurityService.validateTrackingAccess(testVerification, "***********")
        );
    }

    @Test
    void validateTrackingAccess_ExpiredVerification_ShouldThrowException() {
        // Arrange
        testVerification.setExpiryDate(LocalDateTime.now().minusHours(1));

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                trackingSecurityService.validateTrackingAccess(testVerification, "***********")
        );

        assertEquals("Invalid or expired tracking access", exception.getMessage());
    }

    @Test
    void validateTrackingAccess_MaxAccessExceeded_ShouldThrowException() {
        // Arrange
        testVerification.setAccessCount(10);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                trackingSecurityService.validateTrackingAccess(testVerification, "***********")
        );

        assertEquals("Maximum access limit reached for this tracking session", exception.getMessage());
    }

    @Test
    void validateTrackingAccess_IpAddressChanged_ShouldLogWarning() {
        // Arrange
        testVerification.setIpAddress("***********00");

        // Act & Assert
        assertDoesNotThrow(() ->
                trackingSecurityService.validateTrackingAccess(testVerification, "*************")
        );
        // Note: In a real test, you might want to verify the log output
    }

    @Test
    void detectSuspiciousActivity_WithHighAccessVerifications_ShouldDetect() {
        // Arrange
        TrackingVerification suspiciousVerification = TrackingVerification.builder()
                .trackingNumber("LP987654321")
                .accessCount(8)
                .email("<EMAIL>")
                .build();

        when(trackingVerificationRepository.findHighAccessVerifications(anyInt()))
                .thenReturn(List.of(suspiciousVerification));

        // Act
        assertDoesNotThrow(() -> trackingSecurityService.detectSuspiciousActivity());

        // Assert
        verify(trackingVerificationRepository).findHighAccessVerifications(4);
    }

    @Test
    void performSecurityMaintenance_ShouldCleanupAndReset() {
        // Arrange
        when(trackingVerificationRepository.findExpired(any(LocalDateTime.class)))
                .thenReturn(List.of(testVerification));
        doNothing().when(trackingVerificationRepository).deleteExpired(any(LocalDateTime.class));

        // Act
        assertDoesNotThrow(() -> trackingSecurityService.performSecurityMaintenance());

        // Assert
        verify(trackingVerificationRepository).findExpired(any(LocalDateTime.class));
        verify(trackingVerificationRepository).deleteExpired(any(LocalDateTime.class));
    }

    @Test
    void checkRateLimit_InMemoryLimitExceeded_ShouldThrowException() {
        // Arrange
        when(trackingVerificationRepository.countRecentRequestsByTrackingNumberAndContact(
                anyString(), anyString(), any(LocalDateTime.class))).thenReturn(2L);
        when(trackingVerificationRepository.countRecentRequestsByIpAddress(
                anyString(), any(LocalDateTime.class))).thenReturn(5L);

        // Simulate multiple rapid requests to trigger in-memory rate limiting
        String ipAddress = "***********00";
        
        // Act - Make multiple requests rapidly
        for (int i = 0; i < 10; i++) {
            try {
                trackingSecurityService.checkRateLimit("LP123456789", "<EMAIL>", ipAddress);
            } catch (BusinessException e) {
                // Expected after hitting the limit
                break;
            }
        }

        // Assert - The 11th request should fail
        assertThrows(BusinessException.class, () ->
                trackingSecurityService.checkRateLimit("LP123456789", "<EMAIL>", ipAddress)
        );
    }

    @Test
    void validateTrackingAccess_UsedVerification_ShouldThrowException() {
        // Arrange
        testVerification.setIsUsed(true);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                trackingSecurityService.validateTrackingAccess(testVerification, "***********")
        );

        assertEquals("Invalid or expired tracking access", exception.getMessage());
    }

    @Test
    void validateTrackingAccess_UnverifiedVerification_ShouldThrowException() {
        // Arrange
        testVerification.setIsVerified(false);

        // Act & Assert
        BusinessException exception = assertThrows(BusinessException.class, () ->
                trackingSecurityService.validateTrackingAccess(testVerification, "***********")
        );

        assertEquals("Invalid or expired tracking access", exception.getMessage());
    }
}
