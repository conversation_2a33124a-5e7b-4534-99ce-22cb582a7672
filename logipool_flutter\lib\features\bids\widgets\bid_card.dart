import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../shared/models/bid_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/enums/user_role.dart';

class BidCard extends StatelessWidget {
  final BidModel bid;
  final UserRole? currentUserRole;
  final bool isLoadOwner;
  final VoidCallback? onTap;
  final VoidCallback? onAccept;
  final VoidCallback? onReject;
  final VoidCallback? onWithdraw;
  final VoidCallback? onEdit;
  final VoidCallback? onViewDetails;

  const BidCard({
    super.key,
    required this.bid,
    this.currentUserRole,
    this.isLoadOwner = false,
    this.onTap,
    this.onAccept,
    this.onReject,
    this.onWithdraw,
    this.onEdit,
    this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final currencyFormat = NumberFormat.currency(symbol: '\$');
    final dateFormat = DateFormat('MMM dd, yyyy');
    final timeFormat = DateFormat('HH:mm');

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with company name and status
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          bid.companyName ?? 'Unknown Company',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (bid.loadTitle != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            bid.loadTitle!,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  _buildStatusChip(context, bid.status),
                ],
              ),

              const SizedBox(height: 16),

              // Bid amount
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.attach_money,
                      color: theme.colorScheme.onPrimaryContainer,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Bid Amount: ${currencyFormat.format(bid.amount)}',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 12),

              // Estimated times
              Row(
                children: [
                  Expanded(
                    child: _buildTimeInfo(
                      context,
                      'Pickup',
                      bid.estimatedPickupTime,
                      Icons.upload,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildTimeInfo(
                      context,
                      'Delivery',
                      bid.estimatedDeliveryTime,
                      Icons.download,
                      Colors.blue,
                    ),
                  ),
                ],
              ),

              // Proposal if available
              if (bid.proposal != null && bid.proposal!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Proposal',
                        style: theme.textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        bid.proposal!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Notes if available
              if (bid.notes != null && bid.notes!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notes',
                        style: theme.textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: Colors.grey[700],
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        bid.notes!,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              // Action buttons
              if (_shouldShowActions() || onViewDetails != null) ...[
                const SizedBox(height: 16),
                _buildActionButtons(context),
              ],

              // Timestamps
              const SizedBox(height: 12),
              _buildTimestamps(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context, BidStatus status) {
    Color backgroundColor;
    Color textColor;
    IconData icon;

    switch (status) {
      case BidStatus.pending:
        backgroundColor = Colors.orange[100]!;
        textColor = Colors.orange[700]!;
        icon = Icons.schedule;
        break;
      case BidStatus.accepted:
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[700]!;
        icon = Icons.check_circle;
        break;
      case BidStatus.rejected:
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[700]!;
        icon = Icons.cancel;
        break;
      case BidStatus.withdrawn:
        backgroundColor = Colors.grey[200]!;
        textColor = Colors.grey[700]!;
        icon = Icons.remove_circle;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: textColor,
          ),
          const SizedBox(width: 4),
          Text(
            status.displayName,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeInfo(
    BuildContext context,
    String label,
    DateTime dateTime,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd');
    final timeFormat = DateFormat('HH:mm');

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: color.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 16,
                color: color,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: theme.textTheme.labelSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            dateFormat.format(dateTime),
            style: theme.textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            timeFormat.format(dateTime),
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  bool _shouldShowActions() {
    print('DEBUG BidCard _shouldShowActions:');
    print('  isLoadOwner: $isLoadOwner');
    print('  bid.status: ${bid.status}');
    print('  currentUserRole: $currentUserRole');
    print('  onAccept != null: ${onAccept != null}');
    print('  onReject != null: ${onReject != null}');

    if (isLoadOwner && bid.status == BidStatus.pending) {
      final result = onAccept != null || onReject != null;
      print('  Load owner case - returning: $result');
      return result;
    }
    if (currentUserRole == UserRole.transporter &&
        bid.status == BidStatus.pending) {
      final result = onWithdraw != null || onEdit != null;
      print('  Transporter case - returning: $result');
      return result;
    }
    print('  No case matched - returning: false');
    return false;
  }

  Widget _buildActionButtons(BuildContext context) {
    List<Widget> buttons = [];

    // Always add View Details button if callback is provided
    if (onViewDetails != null) {
      buttons.add(
        Expanded(
          child: OutlinedButton.icon(
            onPressed: onViewDetails,
            icon: const Icon(Icons.visibility),
            label: const Text('View Details'),
          ),
        ),
      );
    }

    // Add role-specific action buttons
    if (isLoadOwner && bid.status == BidStatus.pending) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(width: 12));

      if (onReject != null) {
        buttons.add(
          Expanded(
            child: OutlinedButton.icon(
              onPressed: onReject,
              icon: const Icon(Icons.close),
              label: const Text('Reject'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
              ),
            ),
          ),
        );
      }

      if (onReject != null && onAccept != null) {
        buttons.add(const SizedBox(width: 12));
      }

      if (onAccept != null) {
        buttons.add(
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onAccept,
              icon: const Icon(Icons.check),
              label: const Text('Accept'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        );
      }
    } else if (currentUserRole == UserRole.transporter &&
        bid.status == BidStatus.pending) {
      if (buttons.isNotEmpty) buttons.add(const SizedBox(width: 12));

      if (onWithdraw != null) {
        buttons.add(
          Expanded(
            child: OutlinedButton.icon(
              onPressed: onWithdraw,
              icon: const Icon(Icons.remove_circle),
              label: const Text('Withdraw'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.red,
                side: const BorderSide(color: Colors.red),
              ),
            ),
          ),
        );
      }

      if (onWithdraw != null && onEdit != null) {
        buttons.add(const SizedBox(width: 12));
      }

      if (onEdit != null) {
        buttons.add(
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onEdit,
              icon: const Icon(Icons.edit),
              label: const Text('Edit'),
            ),
          ),
        );
      }
    }

    if (buttons.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(children: buttons);
  }

  Widget _buildTimestamps(BuildContext context) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MMM dd, yyyy HH:mm');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (bid.createdAt != null)
          Text(
            'Submitted: ${dateFormat.format(bid.createdAt!)}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
          ),
        if (bid.acceptedAt != null)
          Text(
            'Accepted: ${dateFormat.format(bid.acceptedAt!)}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.green[600],
            ),
          ),
        if (bid.rejectedAt != null)
          Text(
            'Rejected: ${dateFormat.format(bid.rejectedAt!)}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.red[600],
            ),
          ),
      ],
    );
  }
}
