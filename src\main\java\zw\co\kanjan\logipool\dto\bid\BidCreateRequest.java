package zw.co.kanjan.logipool.dto.bid;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class BidCreateRequest {
    
    @NotNull
    private Long loadId;
    
    @NotNull
    @Positive
    private BigDecimal amount;
    
    @Size(max = 1000)
    private String proposal;
    
    @NotNull
    private LocalDateTime estimatedPickupTime;
    
    @NotNull
    private LocalDateTime estimatedDeliveryTime;
    
    @Size(max = 500)
    private String notes;
}
