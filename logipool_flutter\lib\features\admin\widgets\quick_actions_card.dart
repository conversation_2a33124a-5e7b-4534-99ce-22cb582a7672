import 'package:flutter/material.dart';

class QuickActionsCard extends StatelessWidget {
  final VoidCallback? onUserManagement;
  final VoidCallback? onCompanyManagement;
  final VoidCallback? onLoadManagement;
  final VoidCallback? onSystemSettings;
  final VoidCallback? onAnalytics;
  final VoidCallback? onBackup;

  const QuickActionsCard({
    super.key,
    this.onUserManagement,
    this.onCompanyManagement,
    this.onLoadManagement,
    this.onSystemSettings,
    this.onAnalytics,
    this.onBackup,
  });

  @override
  Widget build(BuildContext context) {
            final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth = screenWidth / (screenWidth > 768 ? 2 : 1);
    final itemHeight = 130 + (screenWidth > 768 ? 0 : 20); // desired height
    final aspectRatio = itemWidth / itemHeight;
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 3,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: aspectRatio,
              children: [
                _buildActionButton(
                  context,
                  'Users',
                  Icons.people,
                  Colors.blue,
                  onUserManagement,
                ),
                _buildActionButton(
                  context,
                  'Companies',
                  Icons.business,
                  Colors.green,
                  onCompanyManagement,
                ),
                _buildActionButton(
                  context,
                  'Loads',
                  Icons.local_shipping,
                  Colors.orange,
                  onLoadManagement,
                ),
                _buildActionButton(
                  context,
                  'Analytics',
                  Icons.analytics,
                  Colors.purple,
                  onAnalytics,
                ),
                _buildActionButton(
                  context,
                  'Settings',
                  Icons.settings,
                  Colors.teal,
                  onSystemSettings,
                ),
                _buildActionButton(
                  context,
                  'Backup',
                  Icons.backup,
                  Colors.red,
                  onBackup,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback? onTap,
  ) {
    return Material(
      color: color.withOpacity(0.1),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: color,
                size: 28,
              ),
              const SizedBox(height: 8),
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
