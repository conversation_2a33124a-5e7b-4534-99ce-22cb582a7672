package zw.co.kanjan.logipool.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import zw.co.kanjan.logipool.dto.admin.AdminDto;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.service.AdminService;

@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
@Tag(name = "Admin Dashboard", description = "Admin dashboard and management APIs")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    private final AdminService adminService;

    @GetMapping("/dashboard")
    @Operation(summary = "Get dashboard overview", 
               description = "Get comprehensive dashboard overview with key metrics and statistics")
    public ResponseEntity<AdminDto.DashboardOverview> getDashboardOverview() {
        AdminDto.DashboardOverview overview = adminService.getDashboardOverview();
        return ResponseEntity.ok(overview);
    }

    // User Management APIs
    @GetMapping("/users")
    @Operation(summary = "Get all users", 
               description = "Get paginated list of all users for admin management")
    public ResponseEntity<Page<AdminDto.UserManagementResponse>> getAllUsers(
            @Parameter(description = "Pagination parameters") Pageable pageable) {
        Page<AdminDto.UserManagementResponse> users = adminService.getAllUsers(pageable);
        return ResponseEntity.ok(users);
    }

    @GetMapping("/users/status/{status}")
    @Operation(summary = "Get users by status", 
               description = "Get paginated list of users filtered by status")
    public ResponseEntity<Page<AdminDto.UserManagementResponse>> getUsersByStatus(
            @PathVariable User.UserStatus status,
            @Parameter(description = "Pagination parameters") Pageable pageable) {
        Page<AdminDto.UserManagementResponse> users = adminService.getUsersByStatus(status, pageable);
        return ResponseEntity.ok(users);
    }

    @GetMapping("/users/search")
    @Operation(summary = "Search users", 
               description = "Search users by username, email, first name, or last name")
    public ResponseEntity<Page<AdminDto.UserManagementResponse>> searchUsers(
            @RequestParam String searchTerm,
            @Parameter(description = "Pagination parameters") Pageable pageable) {
        Page<AdminDto.UserManagementResponse> users = adminService.searchUsers(searchTerm, pageable);
        return ResponseEntity.ok(users);
    }

    @PutMapping("/users")
    @Operation(summary = "Update user", 
               description = "Update user information and status (Admin only)")
    public ResponseEntity<AdminDto.UserManagementResponse> updateUser(
            @Valid @RequestBody AdminDto.UserUpdateRequest request) {
        AdminDto.UserManagementResponse response = adminService.updateUser(request);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/users/bulk-action")
    @Operation(summary = "Perform bulk user action", 
               description = "Perform bulk actions on multiple users (activate, deactivate, suspend)")
    public ResponseEntity<AdminDto.BulkActionResponse> performBulkUserAction(
            @Valid @RequestBody AdminDto.BulkActionRequest request) {
        AdminDto.BulkActionResponse response = adminService.performBulkUserAction(request);
        return ResponseEntity.ok(response);
    }

    // Company Management APIs
    @GetMapping("/companies")
    @Operation(summary = "Get all companies", 
               description = "Get paginated list of all companies for admin management")
    public ResponseEntity<Page<AdminDto.CompanyManagementResponse>> getAllCompanies(
            @Parameter(description = "Pagination parameters") Pageable pageable) {
        Page<AdminDto.CompanyManagementResponse> companies = adminService.getAllCompanies(pageable);
        return ResponseEntity.ok(companies);
    }

    @GetMapping("/companies/verification/{status}")
    @Operation(summary = "Get companies by verification status", 
               description = "Get paginated list of companies filtered by verification status")
    public ResponseEntity<Page<AdminDto.CompanyManagementResponse>> getCompaniesByVerificationStatus(
            @PathVariable Company.VerificationStatus status,
            @Parameter(description = "Pagination parameters") Pageable pageable) {
        Page<AdminDto.CompanyManagementResponse> companies = 
                adminService.getCompaniesByVerificationStatus(status, pageable);
        return ResponseEntity.ok(companies);
    }

    @PutMapping("/companies/verify")
    @Operation(summary = "Verify company", 
               description = "Update company verification status with notes")
    public ResponseEntity<AdminDto.CompanyManagementResponse> verifyCompany(
            @Valid @RequestBody AdminDto.CompanyVerificationRequest request) {
        AdminDto.CompanyManagementResponse response = adminService.verifyCompany(request);
        return ResponseEntity.ok(response);
    }

    // Load Management APIs
    @GetMapping("/loads")
    @Operation(summary = "Get all loads", 
               description = "Get paginated list of all loads for admin oversight")
    public ResponseEntity<Page<AdminDto.LoadManagementResponse>> getAllLoads(
            @Parameter(description = "Pagination parameters") Pageable pageable) {
        Page<AdminDto.LoadManagementResponse> loads = adminService.getAllLoads(pageable);
        return ResponseEntity.ok(loads);
    }

    @GetMapping("/loads/status/{status}")
    @Operation(summary = "Get loads by status", 
               description = "Get paginated list of loads filtered by status")
    public ResponseEntity<Page<AdminDto.LoadManagementResponse>> getLoadsByStatus(
            @PathVariable Load.LoadStatus status,
            @Parameter(description = "Pagination parameters") Pageable pageable) {
        Page<AdminDto.LoadManagementResponse> loads = adminService.getLoadsByStatus(status, pageable);
        return ResponseEntity.ok(loads);
    }

    // Analytics & Reporting APIs
    @GetMapping("/analytics")
    @Operation(summary = "Get system analytics", 
               description = "Get comprehensive system analytics including users, loads, revenue, and performance")
    public ResponseEntity<AdminDto.SystemAnalytics> getSystemAnalytics() {
        AdminDto.SystemAnalytics analytics = adminService.getSystemAnalytics();
        return ResponseEntity.ok(analytics);
    }

    @GetMapping("/analytics/users")
    @Operation(summary = "Get user analytics", 
               description = "Get detailed user analytics and statistics")
    public ResponseEntity<AdminDto.UserAnalytics> getUserAnalytics() {
        AdminDto.SystemAnalytics analytics = adminService.getSystemAnalytics();
        return ResponseEntity.ok(analytics.getUserAnalytics());
    }

    @GetMapping("/analytics/loads")
    @Operation(summary = "Get load analytics", 
               description = "Get detailed load analytics and statistics")
    public ResponseEntity<AdminDto.LoadAnalytics> getLoadAnalytics() {
        AdminDto.SystemAnalytics analytics = adminService.getSystemAnalytics();
        return ResponseEntity.ok(analytics.getLoadAnalytics());
    }

    @GetMapping("/analytics/revenue")
    @Operation(summary = "Get revenue analytics", 
               description = "Get detailed revenue analytics and financial statistics")
    public ResponseEntity<AdminDto.RevenueAnalytics> getRevenueAnalytics() {
        AdminDto.SystemAnalytics analytics = adminService.getSystemAnalytics();
        return ResponseEntity.ok(analytics.getRevenueAnalytics());
    }

    @GetMapping("/analytics/performance")
    @Operation(summary = "Get performance analytics", 
               description = "Get system performance analytics and metrics")
    public ResponseEntity<AdminDto.PerformanceAnalytics> getPerformanceAnalytics() {
        AdminDto.SystemAnalytics analytics = adminService.getSystemAnalytics();
        return ResponseEntity.ok(analytics.getPerformanceAnalytics());
    }

    // System Health & Monitoring APIs
    @GetMapping("/system/health")
    @Operation(summary = "Get system health", 
               description = "Get current system health status and metrics")
    public ResponseEntity<AdminDto.SystemHealth> getSystemHealth() {
        AdminDto.DashboardOverview overview = adminService.getDashboardOverview();
        return ResponseEntity.ok(overview.getSystemHealth());
    }

    @GetMapping("/system/activities")
    @Operation(summary = "Get recent activities", 
               description = "Get recent system activities and events")
    public ResponseEntity<java.util.List<AdminDto.RecentActivity>> getRecentActivities() {
        AdminDto.DashboardOverview overview = adminService.getDashboardOverview();
        return ResponseEntity.ok(overview.getRecentActivities());
    }

    // Export & Reporting APIs
    @GetMapping("/export/users")
    @Operation(summary = "Export users data", 
               description = "Export users data in CSV format")
    public ResponseEntity<String> exportUsers(
            @RequestParam(required = false) User.UserStatus status) {
        // Implementation would generate CSV export
        return ResponseEntity.ok("CSV export functionality to be implemented");
    }

    @GetMapping("/export/companies")
    @Operation(summary = "Export companies data", 
               description = "Export companies data in CSV format")
    public ResponseEntity<String> exportCompanies(
            @RequestParam(required = false) Company.VerificationStatus status) {
        // Implementation would generate CSV export
        return ResponseEntity.ok("CSV export functionality to be implemented");
    }

    @GetMapping("/export/loads")
    @Operation(summary = "Export loads data", 
               description = "Export loads data in CSV format")
    public ResponseEntity<String> exportLoads(
            @RequestParam(required = false) Load.LoadStatus status) {
        // Implementation would generate CSV export
        return ResponseEntity.ok("CSV export functionality to be implemented");
    }

    @GetMapping("/reports/daily")
    @Operation(summary = "Get daily report", 
               description = "Get daily system report with key metrics")
    public ResponseEntity<AdminDto.DashboardOverview> getDailyReport() {
        AdminDto.DashboardOverview overview = adminService.getDashboardOverview();
        return ResponseEntity.ok(overview);
    }

    @GetMapping("/reports/weekly")
    @Operation(summary = "Get weekly report", 
               description = "Get weekly system report with trends and analytics")
    public ResponseEntity<AdminDto.SystemAnalytics> getWeeklyReport() {
        AdminDto.SystemAnalytics analytics = adminService.getSystemAnalytics();
        return ResponseEntity.ok(analytics);
    }

    @GetMapping("/reports/monthly")
    @Operation(summary = "Get monthly report",
               description = "Get monthly system report with comprehensive analytics")
    public ResponseEntity<AdminDto.SystemAnalytics> getMonthlyReport() {
        AdminDto.SystemAnalytics analytics = adminService.getSystemAnalytics();
        return ResponseEntity.ok(analytics);
    }

    // System Configuration APIs
    @GetMapping("/system/configuration")
    @Operation(summary = "Get system configuration",
               description = "Get current system configuration settings")
    public ResponseEntity<AdminDto.SystemConfigurationResponse> getSystemConfiguration() {
        AdminDto.SystemConfigurationResponse config = adminService.getSystemConfiguration();
        return ResponseEntity.ok(config);
    }

    @PutMapping("/system/configuration")
    @Operation(summary = "Update system configuration",
               description = "Update system configuration settings")
    public ResponseEntity<AdminDto.SystemConfigurationResponse> updateSystemConfiguration(
            @Valid @RequestBody AdminDto.SystemConfigurationUpdateRequest request) {
        AdminDto.SystemConfigurationResponse config = adminService.updateSystemConfiguration(request);
        return ResponseEntity.ok(config);
    }

    // Audit & Logging APIs
    @GetMapping("/audit/logs")
    @Operation(summary = "Get audit logs",
               description = "Get paginated list of system audit logs")
    public ResponseEntity<Page<AdminDto.AuditLogResponse>> getAuditLogs(
            @Parameter(description = "Pagination parameters") Pageable pageable) {
        Page<AdminDto.AuditLogResponse> auditLogs = adminService.getAuditLogs(pageable);
        return ResponseEntity.ok(auditLogs);
    }

    // System Alerts APIs
    @GetMapping("/system/alerts")
    @Operation(summary = "Get system alerts",
               description = "Get current system alerts and notifications")
    public ResponseEntity<java.util.List<AdminDto.SystemAlertResponse>> getSystemAlerts() {
        java.util.List<AdminDto.SystemAlertResponse> alerts = adminService.getSystemAlerts();
        return ResponseEntity.ok(alerts);
    }

    @PostMapping("/system/alerts/resolve")
    @Operation(summary = "Resolve system alert",
               description = "Mark a system alert as resolved with resolution details")
    public ResponseEntity<AdminDto.SystemAlertResponse> resolveAlert(
            @Valid @RequestBody AdminDto.AlertResolutionRequest request) {
        AdminDto.SystemAlertResponse alert = adminService.resolveAlert(request);
        return ResponseEntity.ok(alert);
    }

    // System Backup APIs
    @PostMapping("/system/backup")
    @Operation(summary = "Initiate system backup",
               description = "Initiate a system backup (FULL, INCREMENTAL, or CONFIGURATION)")
    public ResponseEntity<AdminDto.SystemBackupResponse> initiateSystemBackup(
            @RequestParam(defaultValue = "FULL") String backupType) {
        AdminDto.SystemBackupResponse backup = adminService.initiateSystemBackup(backupType);
        return ResponseEntity.ok(backup);
    }

    // Emergency & Maintenance APIs
    @PostMapping("/system/maintenance/enable")
    @Operation(summary = "Enable maintenance mode",
               description = "Enable system maintenance mode")
    public ResponseEntity<String> enableMaintenanceMode() {
        // Implementation would enable maintenance mode
        return ResponseEntity.ok("Maintenance mode enabled");
    }

    @PostMapping("/system/maintenance/disable")
    @Operation(summary = "Disable maintenance mode",
               description = "Disable system maintenance mode")
    public ResponseEntity<String> disableMaintenanceMode() {
        // Implementation would disable maintenance mode
        return ResponseEntity.ok("Maintenance mode disabled");
    }

    @PostMapping("/system/cache/clear")
    @Operation(summary = "Clear system cache",
               description = "Clear all system caches")
    public ResponseEntity<String> clearSystemCache() {
        // Implementation would clear caches
        return ResponseEntity.ok("System cache cleared");
    }

    @PostMapping("/system/restart")
    @Operation(summary = "Restart system services",
               description = "Restart specific system services")
    public ResponseEntity<String> restartSystemServices(
            @RequestParam String service) {
        // Implementation would restart services
        return ResponseEntity.ok("Service " + service + " restart initiated");
    }
}
