package zw.co.kanjan.logipool.dto.bid;

import lombok.Data;
import zw.co.kanjan.logipool.entity.Bid;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class BidResponse {
    
    private Long id;
    private BigDecimal amount;
    private String proposal;
    private LocalDateTime estimatedPickupTime;
    private LocalDateTime estimatedDeliveryTime;
    private Bid.BidStatus status;
    private String notes;
    private Long loadId;
    private String loadTitle;
    private Long companyId;
    private String companyName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime acceptedAt;
    private LocalDateTime rejectedAt;
}
