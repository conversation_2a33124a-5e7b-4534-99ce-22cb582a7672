package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.Role;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.UserRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AdminReportServiceTest {

    @Mock
    private LoadRepository loadRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private PersistentNotificationService persistentNotificationService;

    @Mock
    private RealTimeNotificationService realTimeNotificationService;

    @Mock
    private EmailTemplateService emailTemplateService;

    @Mock
    private NotificationService notificationService;

    @InjectMocks
    private AdminReportService adminReportService;

    private User adminUser;
    private Load testLoad1;
    private Load testLoad2;

    @BeforeEach
    void setUp() {
        // Set up test configuration
        ReflectionTestUtils.setField(adminReportService, "emailEnabled", true);
        ReflectionTestUtils.setField(adminReportService, "realtimeEnabled", true);
        ReflectionTestUtils.setField(adminReportService, "persistentEnabled", true);
        ReflectionTestUtils.setField(adminReportService, "dailyReportEnabled", true);

        // Create test admin user
        Role adminRole = new Role();
        adminRole.setName(Role.RoleName.ADMIN);

        adminUser = new User();
        adminUser.setId(1L);
        adminUser.setUsername("admin");
        adminUser.setEmail("<EMAIL>");
        adminUser.setFirstName("Admin");
        adminUser.setLastName("User");
        adminUser.setRoles(new HashSet<>(Collections.singletonList(adminRole)));

        // Create test client user
        User clientUser = new User();
        clientUser.setId(2L);
        clientUser.setUsername("client");
        clientUser.setFirstName("Test");
        clientUser.setLastName("Client");

        // Create test loads
        testLoad1 = Load.builder()
                .id(1L)
                .title("Test Load 1")
                .trackingNumber("LP-20250120-12345")
                .cargoType("General")
                .weight(BigDecimal.valueOf(100))
                .weightUnit("kg")
                .pickupLocation("Pickup Location 1")
                .deliveryLocation("Delivery Location 1")
                .pickupDate(LocalDateTime.now().plusDays(2))
                .deliveryDate(LocalDateTime.now().plusDays(4))
                .status(Load.LoadStatus.POSTED)
                .priority(Load.Priority.NORMAL)
                .client(clientUser)
                .build();

        testLoad2 = Load.builder()
                .id(2L)
                .title("Test Load 2")
                .trackingNumber("LP-20250120-67890")
                .cargoType("Fragile")
                .weight(BigDecimal.valueOf(50))
                .weightUnit("kg")
                .pickupLocation("Pickup Location 2")
                .deliveryLocation("Delivery Location 2")
                .pickupDate(LocalDateTime.now().plusDays(3))
                .deliveryDate(LocalDateTime.now().plusDays(5))
                .status(Load.LoadStatus.ASSIGNED)
                .priority(Load.Priority.HIGH)
                .client(clientUser)
                .build();
    }

    @Test
    void sendDailyUpcomingLoadsReport_WithUpcomingLoads_ShouldSendReports() {
        // Arrange
        List<Load> upcomingLoads = Arrays.asList(testLoad1, testLoad2);
        List<User> admins = Collections.singletonList(adminUser);

        when(loadRepository.findLoadsStartingInNext3Days(any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(upcomingLoads);
        when(userRepository.findAdminUsers()).thenReturn(admins);

        // Act
        adminReportService.sendDailyUpcomingLoadsReport();

        // Assert
        verify(loadRepository).findLoadsStartingInNext3Days(any(LocalDateTime.class), any(LocalDateTime.class));
        verify(userRepository).findAdminUsers();
        verify(persistentNotificationService).createNotification(
                eq(adminUser.getId()),
                eq("Daily Load Report - Upcoming Loads"),
                anyString(),
                anyString(),
                anyString(),
                any(),
                any(),
                anyString(),
                any()
        );
        verify(realTimeNotificationService).sendNotificationToUser(eq(adminUser.getId()), any());
        verify(notificationService).sendHtmlEmail(eq(adminUser.getEmail()), anyString(), anyString());
    }

    @Test
    void sendDailyUpcomingLoadsReport_WithNoUpcomingLoads_ShouldNotSendReports() {
        // Arrange
        when(loadRepository.findLoadsStartingInNext3Days(any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(Collections.emptyList());

        // Act
        adminReportService.sendDailyUpcomingLoadsReport();

        // Assert
        verify(loadRepository).findLoadsStartingInNext3Days(any(LocalDateTime.class), any(LocalDateTime.class));
        verify(userRepository, never()).findAdminUsers();
        verify(persistentNotificationService, never()).createNotification(anyLong(), anyString(), anyString(), anyString(), anyString(), any(), any(), anyString(), any());
        verify(realTimeNotificationService, never()).sendNotificationToUser(anyLong(), any());
        verify(notificationService, never()).sendHtmlEmail(anyString(), anyString(), anyString());
    }

    @Test
    void sendDailyUpcomingLoadsReport_WithNoAdmins_ShouldNotSendReports() {
        // Arrange
        List<Load> upcomingLoads = Arrays.asList(testLoad1, testLoad2);

        when(loadRepository.findLoadsStartingInNext3Days(any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(upcomingLoads);
        when(userRepository.findAdminUsers()).thenReturn(Collections.emptyList());

        // Act
        adminReportService.sendDailyUpcomingLoadsReport();

        // Assert
        verify(loadRepository).findLoadsStartingInNext3Days(any(LocalDateTime.class), any(LocalDateTime.class));
        verify(userRepository).findAdminUsers();
        verify(persistentNotificationService, never()).createNotification(anyLong(), anyString(), anyString(), anyString(), anyString(), any(), any(), anyString(), any());
        verify(realTimeNotificationService, never()).sendNotificationToUser(anyLong(), any());
        verify(notificationService, never()).sendHtmlEmail(anyString(), anyString(), anyString());
    }

    @Test
    void sendDailyUpcomingLoadsReport_WhenDisabled_ShouldNotExecute() {
        // Arrange
        ReflectionTestUtils.setField(adminReportService, "dailyReportEnabled", false);

        // Act
        adminReportService.sendDailyUpcomingLoadsReport();

        // Assert
        verify(loadRepository, never()).findLoadsStartingInNext3Days(any(LocalDateTime.class), any(LocalDateTime.class));
        verify(userRepository, never()).findAdminUsers();
    }

    @Test
    void triggerDailyReport_ShouldCallScheduledMethod() {
        // Arrange
        List<Load> upcomingLoads = Arrays.asList(testLoad1);
        List<User> admins = Collections.singletonList(adminUser);

        when(loadRepository.findLoadsStartingInNext3Days(any(LocalDateTime.class), any(LocalDateTime.class)))
                .thenReturn(upcomingLoads);
        when(userRepository.findAdminUsers()).thenReturn(admins);

        // Act
        adminReportService.triggerDailyReport();

        // Assert
        verify(loadRepository).findLoadsStartingInNext3Days(any(LocalDateTime.class), any(LocalDateTime.class));
        verify(userRepository).findAdminUsers();
        verify(persistentNotificationService).createNotification(anyLong(), anyString(), anyString(), anyString(), anyString(), any(), any(), anyString(), any());
    }
}
