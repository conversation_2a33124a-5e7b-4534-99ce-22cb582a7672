package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.TrackingVerification;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.TrackingVerificationRepository;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class TrackingVerificationServiceTest {

    @Mock
    private TrackingVerificationRepository trackingVerificationRepository;

    @Mock
    private LoadRepository loadRepository;

    @Mock
    private NotificationService notificationService;

    @Mock
    private EmailTemplateService emailTemplateService;

    @Mock
    private PublicBrowsingService publicBrowsingService;

    @Mock
    private TrackingSecurityService trackingSecurityService;

    @InjectMocks
    private TrackingVerificationService trackingVerificationService;

    private Load testLoad;
    private TrackingVerification testVerification;

    @BeforeEach
    void setUp() {
        testLoad = Load.builder()
                .id(1L)
                .trackingNumber("LP123456789")
                .title("Test Shipment")
                .pickupLocation("Harare")
                .deliveryLocation("Bulawayo")
                .status(Load.LoadStatus.IN_TRANSIT)
                .build();

        testVerification = TrackingVerification.builder()
                .id(1L)
                .token("test-token-123")
                .trackingNumber("LP123456789")
                .email("<EMAIL>")
                .verificationMethod(TrackingVerification.VerificationMethod.EMAIL)
                .verificationCode("123456")
                .expiryDate(LocalDateTime.now().plusHours(24))
                .isVerified(true)
                .isUsed(false)
                .accessCount(0)
                .build();
    }

    @Test
    void requestTrackingVerification_ValidEmailRequest_ShouldSucceed() {
        // Arrange
        when(loadRepository.findByTrackingNumber("LP123456789")).thenReturn(Optional.of(testLoad));
        when(trackingVerificationRepository.save(any(TrackingVerification.class))).thenReturn(testVerification);
        doNothing().when(trackingSecurityService).checkRateLimit(anyString(), anyString(), anyString());

        // Act
        String token = trackingVerificationService.requestTrackingVerification(
                "LP123456789", "<EMAIL>", TrackingVerification.VerificationMethod.EMAIL,
                "***********", "Mozilla/5.0"
        );

        // Assert
        assertNotNull(token);
        verify(loadRepository).findByTrackingNumber("LP123456789");
        verify(trackingSecurityService).checkRateLimit("LP123456789", "<EMAIL>", "***********");
        verify(trackingVerificationRepository).save(any(TrackingVerification.class));
    }

    @Test
    void requestTrackingVerification_InvalidTrackingNumber_ShouldThrowException() {
        // Arrange
        when(loadRepository.findByTrackingNumber("INVALID")).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () ->
                trackingVerificationService.requestTrackingVerification(
                        "INVALID", "<EMAIL>", TrackingVerification.VerificationMethod.EMAIL,
                        "***********", "Mozilla/5.0"
                )
        );
    }

    @Test
    void requestTrackingVerification_InvalidEmail_ShouldThrowException() {
        // Arrange
        when(loadRepository.findByTrackingNumber("LP123456789")).thenReturn(Optional.of(testLoad));

        // Act & Assert
        assertThrows(BusinessException.class, () ->
                trackingVerificationService.requestTrackingVerification(
                        "LP123456789", "invalid-email", TrackingVerification.VerificationMethod.EMAIL,
                        "***********", "Mozilla/5.0"
                )
        );
    }

    @Test
    void requestTrackingVerification_RateLimitExceeded_ShouldThrowException() {
        // Arrange
        when(loadRepository.findByTrackingNumber("LP123456789")).thenReturn(Optional.of(testLoad));
        doThrow(new BusinessException("Too many requests")).when(trackingSecurityService)
                .checkRateLimit(anyString(), anyString(), anyString());

        // Act & Assert
        assertThrows(BusinessException.class, () ->
                trackingVerificationService.requestTrackingVerification(
                        "LP123456789", "<EMAIL>", TrackingVerification.VerificationMethod.EMAIL,
                        "***********", "Mozilla/5.0"
                )
        );
    }

    @Test
    void verifyTrackingCode_ValidCode_ShouldReturnToken() {
        // Arrange
        when(trackingVerificationRepository.findByTrackingNumberAndCode(
                eq("LP123456789"), eq("123456"), any(LocalDateTime.class)))
                .thenReturn(Optional.of(testVerification));
        when(trackingVerificationRepository.save(any(TrackingVerification.class))).thenReturn(testVerification);

        // Act
        String token = trackingVerificationService.verifyTrackingCode("LP123456789", "123456", "***********");

        // Assert
        assertNotNull(token);
        assertTrue(testVerification.getIsVerified());
        verify(trackingVerificationRepository).save(testVerification);
    }

    @Test
    void verifyTrackingCode_InvalidCode_ShouldThrowException() {
        // Arrange
        when(trackingVerificationRepository.findByTrackingNumberAndCode(
                eq("LP123456789"), eq("999999"), any(LocalDateTime.class)))
                .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(BusinessException.class, () ->
                trackingVerificationService.verifyTrackingCode("LP123456789", "999999", "***********")
        );
    }

    @Test
    void validateTrackingAccess_ValidToken_ShouldReturnVerification() {
        // Arrange
        when(trackingVerificationRepository.findValidByToken(eq("test-token-123"), any(LocalDateTime.class)))
                .thenReturn(Optional.of(testVerification));
        when(trackingVerificationRepository.save(any(TrackingVerification.class))).thenReturn(testVerification);
        doNothing().when(trackingSecurityService).validateTrackingAccess(any(TrackingVerification.class), anyString());

        // Act
        TrackingVerification result = trackingVerificationService.validateTrackingAccess("test-token-123", "***********");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getAccessCount());
        verify(trackingSecurityService).validateTrackingAccess(testVerification, "***********");
        verify(trackingVerificationRepository).save(testVerification);
    }

    @Test
    void validateTrackingAccess_InvalidToken_ShouldThrowException() {
        // Arrange
        when(trackingVerificationRepository.findValidByToken(eq("invalid-token"), any(LocalDateTime.class)))
                .thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(BusinessException.class, () ->
                trackingVerificationService.validateTrackingAccess("invalid-token", "***********")
        );
    }

    @Test
    void getTrackingDetails_ValidToken_ShouldReturnLoadResponse() {
        // Arrange
        LoadResponse expectedResponse = LoadResponse.builder()
                .trackingNumber("LP123456789")
                .title("Test Shipment")
                .build();

        when(trackingVerificationRepository.findValidByToken(eq("test-token-123"), any(LocalDateTime.class)))
                .thenReturn(Optional.of(testVerification));
        when(trackingVerificationRepository.save(any(TrackingVerification.class))).thenReturn(testVerification);
        when(publicBrowsingService.getSecureTrackingDetails("LP123456789")).thenReturn(expectedResponse);
        doNothing().when(trackingSecurityService).validateTrackingAccess(any(TrackingVerification.class), anyString());

        // Act
        LoadResponse result = trackingVerificationService.getTrackingDetails("test-token-123", "***********");

        // Assert
        assertNotNull(result);
        assertEquals("LP123456789", result.getTrackingNumber());
        assertEquals("Test Shipment", result.getTitle());
        verify(publicBrowsingService).getSecureTrackingDetails("LP123456789");
    }

    @Test
    void cleanupExpiredVerifications_ShouldDeleteExpiredEntries() {
        // Arrange
        when(trackingVerificationRepository.findExpired(any(LocalDateTime.class)))
                .thenReturn(java.util.List.of(testVerification));
        doNothing().when(trackingVerificationRepository).deleteExpired(any(LocalDateTime.class));

        // Act
        trackingVerificationService.cleanupExpiredVerifications();

        // Assert
        verify(trackingVerificationRepository).findExpired(any(LocalDateTime.class));
        verify(trackingVerificationRepository).deleteExpired(any(LocalDateTime.class));
    }
}
