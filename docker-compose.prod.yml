version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: logipool-postgres-prod
    environment:
      POSTGRES_DB: ${DATABASE_NAME:-logipool_prod}
      POSTGRES_USER: ${DATABASE_USERNAME:-logipool}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    ports:
      - "${DATABASE_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - logipool-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USERNAME:-logipool} -d ${DATABASE_NAME:-logipool_prod}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: logipool-redis-prod
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
    networks:
      - logipool-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # LogiPool Backend Application
  logipool-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: logipool-backend-prod
    environment:
      # Database Configuration
      DATABASE_URL: *******************************/${DATABASE_NAME:-logipool_prod}
      DATABASE_USERNAME: ${DATABASE_USERNAME:-logipool}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD}

      # Redis Configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}

      # JWT Configuration
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRATION: ${JWT_EXPIRATION:-86400000}
      JWT_REFRESH_EXPIRATION: ${JWT_REFRESH_EXPIRATION:-604800000}

      # Application Configuration
      APP_BASE_URL: https://${DOMAIN_NAME:-api-logistics.kanjan.co.zw}
      PORT: 8080

      # File Upload Configuration
      UPLOAD_DIR: /app/uploads
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-50MB}
      MAX_REQUEST_SIZE: ${MAX_REQUEST_SIZE:-50MB}

      # Cloud Storage Configuration
      CLOUD_STORAGE_ENABLED: ${CLOUD_STORAGE_ENABLED:-true}
      CLOUD_STORAGE_PROVIDER: ${CLOUD_STORAGE_PROVIDER:-aws}
      S3_BUCKET_NAME: ${S3_BUCKET_NAME}
      AWS_REGION: ${AWS_REGION:-us-east-1}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}

      # Email Configuration
      MAIL_HOST: ${MAIL_HOST:-smtp.gmail.com}
      MAIL_PORT: ${MAIL_PORT:-587}
      MAIL_USERNAME: ${MAIL_USERNAME}
      MAIL_PASSWORD: ${MAIL_PASSWORD}

      # SMS Configuration
      SMS_PROVIDER: ${SMS_PROVIDER:-twilio}
      SMS_ENABLED: ${SMS_ENABLED:-true}
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_FROM_NUMBER: ${TWILIO_FROM_NUMBER}

      # Payment Configuration
      STRIPE_ENABLED: ${STRIPE_ENABLED:-true}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_PUBLIC_KEY: ${STRIPE_PUBLIC_KEY}
      COMMISSION_RATE: ${COMMISSION_RATE:-0.075}

      # Rate Limiting
      RATE_LIMIT_PER_MINUTE: ${RATE_LIMIT_PER_MINUTE:-100}
      RATE_LIMIT_PER_HOUR: ${RATE_LIMIT_PER_HOUR:-5000}

      # CORS Configuration
      CORS_ALLOWED_ORIGINS: ${CORS_ALLOWED_ORIGINS:-https://app.api-logistics.kanjan.co.zw,https://admin.api-logistics.kanjan.co.zw}

    # Remove exposed ports as Traefik will handle routing
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    networks:
      - logipool-network
      - traefik-public
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    labels:
      # Traefik configuration
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.logipool-backend.rule=Host(`${DOMAIN_NAME:-api-logistics.kanjan.co.zw}`)"
      - "traefik.http.routers.logipool-backend.entrypoints=websecure"
      - "traefik.http.routers.logipool-backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.logipool-backend.loadbalancer.server.port=8080"
      # Apply middlewares
      - "traefik.http.routers.logipool-backend.middlewares=security-headers,gzip-compression"
      # Special routing for auth endpoints with stricter rate limiting
      - "traefik.http.routers.logipool-auth.rule=Host(`${DOMAIN_NAME:-api-logistics.kanjan.co.zw}`) && PathPrefix(`/api/auth`)"
      - "traefik.http.routers.logipool-auth.entrypoints=websecure"
      - "traefik.http.routers.logipool-auth.tls.certresolver=letsencrypt"
      - "traefik.http.routers.logipool-auth.middlewares=auth-ratelimit,security-headers"
      - "traefik.http.routers.logipool-auth.priority=100"
      # Special routing for file uploads
      - "traefik.http.routers.logipool-upload.rule=Host(`${DOMAIN_NAME:-api-logistics.kanjan.co.zw}`) && PathPrefix(`/api/documents/upload`)"
      - "traefik.http.routers.logipool-upload.entrypoints=websecure"
      - "traefik.http.routers.logipool-upload.tls.certresolver=letsencrypt"
      - "traefik.http.routers.logipool-upload.middlewares=upload-ratelimit,upload-size-limit,security-headers"
      - "traefik.http.routers.logipool-upload.priority=200"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # pgAdmin for Database Administration (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: logipool-pgadmin-prod
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - logipool-network
      - traefik-public
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.pgadmin.rule=Host(`pgadmin.${DOMAIN_NAME:-api-logistics.kanjan.co.zw}`)"
      - "traefik.http.routers.pgadmin.entrypoints=websecure"
      - "traefik.http.routers.pgadmin.tls.certresolver=letsencrypt"
      - "traefik.http.services.pgadmin.loadbalancer.server.port=80"
      - "traefik.http.routers.pgadmin.middlewares=admin-whitelist,security-headers"

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local
  pgadmin_data:
    driver: local

networks:
  logipool-network:
    driver: bridge
  traefik-public:
    external: true
