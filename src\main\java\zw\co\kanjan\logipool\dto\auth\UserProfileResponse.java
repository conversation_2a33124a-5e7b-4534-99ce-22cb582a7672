package zw.co.kanjan.logipool.dto.auth;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.User;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserProfileResponse {
    
    private String id;
    private String username;
    private String email;
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String role;
    private boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private CompanyInfo company;
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CompanyInfo {
        private String id;
        private String name;
        private String description;
        private String address;
        private String phoneNumber;
        private String email;
        private String website;
        private String verificationStatus;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }
    
    public static UserProfileResponse fromUser(User user) {
        UserProfileResponseBuilder builder = UserProfileResponse.builder()
                .id(user.getId().toString())
                .username(user.getUsername())
                .email(user.getEmail())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .phoneNumber(user.getPhoneNumber())
                .role(user.getRoles().stream()
                        .findFirst()
                        .map(role -> role.getName().name())
                        .orElse("CLIENT"))
                .isActive(user.getStatus() == User.UserStatus.ACTIVE)
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt());
        
        // Add company info if user has a company
        if (user.getCompany() != null) {
            builder.company(CompanyInfo.builder()
                    .id(user.getCompany().getId().toString())
                    .name(user.getCompany().getName())
                    .description(user.getCompany().getDescription())
                    .address(user.getCompany().getAddress())
                    .phoneNumber(user.getCompany().getPhoneNumber())
                    .email(user.getCompany().getEmail())
                    .website(user.getCompany().getWebsite())
                    .verificationStatus(user.getCompany().getVerificationStatus().name())
                    .createdAt(user.getCompany().getCreatedAt())
                    .updatedAt(user.getCompany().getUpdatedAt())
                    .build());
        }
        
        return builder.build();
    }
}
