# Middleware Configuration for LogiPool API
http:
  middlewares:
    # Security Headers
    security-headers:
      headers:
        frameDeny: true
        contentTypeNosniff: true
        browserXssFilter: true
        referrerPolicy: "strict-origin-when-cross-origin"
        forceSTSHeader: true
        stsSeconds: 31536000
        stsIncludeSubdomains: true
        stsPreload: true
        customRequestHeaders:
          X-Forwarded-Proto: "https"
        customResponseHeaders:
          Server: ""
          X-Powered-By: ""

    # CORS Headers for API
    cors-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - POST
          - PUT
          - DELETE
          - OPTIONS
        accessControlAllowHeaders:
          - "*"
        accessControlAllowOriginList:
          - "https://app.api-logistics.kanjan.co.zw"
          - "https://admin.api-logistics.kanjan.co.zw"
          - "https://api-logistics.kanjan.co.zw"
          - "https://api-logistics.kanjan.co.zw"
          - "*"
        accessControlAllowCredentials: true
        accessControlMaxAge: 86400

    # Rate Limiting for API endpoints
    api-ratelimit:
      rateLimit:
        burst: 100
        average: 50
        period: "1m"
        sourceCriterion:
          ipStrategy:
            depth: 1

    # Stricter rate limiting for authentication endpoints
    auth-ratelimit:
      rateLimit:
        burst: 10
        average: 5
        period: "1m"
        sourceCriterion:
          ipStrategy:
            depth: 1

    # File upload rate limiting
    upload-ratelimit:
      rateLimit:
        burst: 5
        average: 2
        period: "1m"
        sourceCriterion:
          ipStrategy:
            depth: 1

    # Compression middleware
    gzip-compression:
      compress:
        excludedContentTypes:
          - "text/event-stream"
          - "application/grpc"

    # Request size limits for file uploads
    upload-size-limit:
      buffering:
        maxRequestBodyBytes: 52428800  # 50MB
        memRequestBodyBytes: 1048576   # 1MB
        maxResponseBodyBytes: 52428800 # 50MB
        memResponseBodyBytes: 1048576  # 1MB

    # IP Whitelist for admin/monitoring endpoints
    admin-whitelist:
      ipWhiteList:
        sourceRange:
          - "127.0.0.1/32"
          - "10.0.0.0/8"
          - "**********/12"
          - "***********/16"

    # Redirect HTTP to HTTPS
    redirect-to-https:
      redirectScheme:
        scheme: https
        permanent: true

    # Circuit breaker for resilience
    circuit-breaker:
      circuitBreaker:
        expression: "NetworkErrorRatio() > 0.30"
        checkPeriod: "3s"
        fallbackDuration: "10s"
        recoveryDuration: "10s"

    # Retry policy
    retry-policy:
      retry:
        attempts: 3
        initialInterval: "100ms"