import 'package:flutter/material.dart';
import '../../../shared/models/company_member_model.dart';

class CompanyMemberCard extends StatelessWidget {
  final CompanyMemberModel member;
  final VoidCallback? onTap;
  final Function(CompanyMemberModel)? onEdit;
  final Function(CompanyMemberModel)? onRemove;

  const CompanyMemberCard({
    super.key,
    required this.member,
    this.onTap,
    this.onEdit,
    this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: _getRoleColor(member.role),
                    child: Text(
                      _getInitials(member.displayName),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          member.displayName,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          member.email,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildStatusChip(member.status),
                  if (onEdit != null || onRemove != null) ...[
                    const SizedBox(width: 8),
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            onEdit?.call(member);
                            break;
                          case 'remove':
                            onRemove?.call(member);
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        if (onEdit != null)
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, size: 20),
                                SizedBox(width: 8),
                                Text('Edit'),
                              ],
                            ),
                          ),
                        if (onRemove != null)
                          const PopupMenuItem(
                            value: 'remove',
                            child: Row(
                              children: [
                                Icon(Icons.delete, size: 20, color: Colors.red),
                                SizedBox(width: 8),
                                Text('Remove', style: TextStyle(color: Colors.red)),
                              ],
                            ),
                          ),
                      ],
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  _buildRoleChip(member.role),
                  const SizedBox(width: 8),
                  if (member.phoneNumber != null) ...[
                    Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      member.phoneNumber!,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    const SizedBox(width: 16),
                  ],
                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    _formatJoinDate(member),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              if (_hasSpecialPermissions(member)) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: _buildPermissionChips(member),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(MemberStatus status) {
    Color color;
    IconData icon;
    
    switch (status) {
      case MemberStatus.active:
        color = Colors.green;
        icon = Icons.check_circle;
        break;
      case MemberStatus.pending:
        color = Colors.orange;
        icon = Icons.pending;
        break;
      case MemberStatus.inactive:
        color = Colors.grey;
        icon = Icons.pause_circle;
        break;
      case MemberStatus.suspended:
        color = Colors.red;
        icon = Icons.block;
        break;
    }

    return Chip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: Colors.white),
          const SizedBox(width: 4),
          Text(
            member.statusDisplayName,
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ],
      ),
      backgroundColor: color,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Widget _buildRoleChip(CompanyRole role) {
    return Chip(
      label: Text(
        member.roleDisplayName,
        style: TextStyle(
          color: _getRoleColor(role),
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: _getRoleColor(role).withOpacity(0.1),
      side: BorderSide(color: _getRoleColor(role)),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  List<Widget> _buildPermissionChips(CompanyMemberModel member) {
    final permissions = <Widget>[];
    
    if (member.canManageMembers) {
      permissions.add(_buildPermissionChip('Manage Team', Icons.people));
    }
    if (member.canManageLoads) {
      permissions.add(_buildPermissionChip('Manage Loads', Icons.local_shipping));
    }
    if (member.canGenerateInvoices) {
      permissions.add(_buildPermissionChip('Invoices', Icons.receipt));
    }
    if (member.canViewFinancials) {
      permissions.add(_buildPermissionChip('Financials', Icons.attach_money));
    }
    if (member.canTrackLocation) {
      permissions.add(_buildPermissionChip('Location', Icons.location_on));
    }
    
    return permissions;
  }

  Widget _buildPermissionChip(String label, IconData icon) {
    return Chip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: Colors.blue),
          const SizedBox(width: 2),
          Text(
            label,
            style: const TextStyle(fontSize: 10, color: Colors.blue),
          ),
        ],
      ),
      backgroundColor: Colors.blue.withOpacity(0.1),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  Color _getRoleColor(CompanyRole role) {
    switch (role) {
      case CompanyRole.owner:
        return Colors.purple;
      case CompanyRole.manager:
        return Colors.blue;
      case CompanyRole.driver:
        return Colors.green;
      case CompanyRole.dispatcher:
        return Colors.orange;
      case CompanyRole.accountant:
        return Colors.teal;
      case CompanyRole.viewer:
        return Colors.grey;
    }
  }

  String _getInitials(String name) {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else if (parts.isNotEmpty) {
      return parts[0][0].toUpperCase();
    }
    return '?';
  }

  String _formatJoinDate(CompanyMemberModel member) {
    if (member.joinedAt != null) {
      final now = DateTime.now();
      final difference = now.difference(member.joinedAt!);
      
      if (difference.inDays > 30) {
        return 'Joined ${(difference.inDays / 30).floor()} months ago';
      } else if (difference.inDays > 0) {
        return 'Joined ${difference.inDays} days ago';
      } else {
        return 'Joined today';
      }
    } else if (member.invitedAt != null) {
      return 'Invited ${_formatDate(member.invitedAt!)}';
    }
    return 'Recently added';
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else {
      return '${difference.inMinutes} minutes ago';
    }
  }

  bool _hasSpecialPermissions(CompanyMemberModel member) {
    return member.canManageMembers ||
           member.canManageLoads ||
           member.canGenerateInvoices ||
           member.canViewFinancials ||
           member.canTrackLocation;
  }
}
