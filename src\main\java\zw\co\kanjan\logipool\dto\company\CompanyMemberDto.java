package zw.co.kanjan.logipool.dto.company;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.CompanyMember;

import java.time.LocalDateTime;
import java.util.List;

public class CompanyMemberDto {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Request to invite a user to join a company")
    public static class InviteRequest {
        
        @NotBlank(message = "Email is required")
        @Email(message = "Email should be valid")
        @Size(max = 100, message = "Email should not exceed 100 characters")
        @Schema(description = "Email of the user to invite", example = "<EMAIL>")
        private String email;
        
        @NotNull(message = "Role is required")
        @Schema(description = "Role to assign to the user", example = "DRIVER")
        private CompanyMember.CompanyRole role;
        
        @Schema(description = "Whether the member can manage other members", example = "false")
        @Builder.Default
        private Boolean canManageMembers = false;
        
        @Schema(description = "Whether the member can manage loads", example = "true")
        @Builder.Default
        private Boolean canManageLoads = false;
        
        @Schema(description = "Whether the member can update load status", example = "true")
        @Builder.Default
        private Boolean canUpdateLoadStatus = false;
        
        @Schema(description = "Whether the member can upload documents", example = "true")
        @Builder.Default
        private Boolean canUploadDocuments = false;
        
        @Schema(description = "Whether the member can generate invoices", example = "false")
        @Builder.Default
        private Boolean canGenerateInvoices = false;
        
        @Schema(description = "Whether the member can view financials", example = "false")
        @Builder.Default
        private Boolean canViewFinancials = false;
        
        @Schema(description = "Whether the member can track location", example = "true")
        @Builder.Default
        private Boolean canTrackLocation = false;
        
        @Size(max = 500, message = "Message should not exceed 500 characters")
        @Schema(description = "Optional invitation message", example = "Welcome to our logistics team!")
        private String invitationMessage;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Request to update a company member")
    public static class UpdateRequest {
        
        @NotNull(message = "Member ID is required")
        @Schema(description = "ID of the member to update", example = "1")
        private Long memberId;
        
        @Schema(description = "New role for the member", example = "MANAGER")
        private CompanyMember.CompanyRole role;
        
        @Schema(description = "New status for the member", example = "ACTIVE")
        private CompanyMember.MemberStatus status;
        
        @Schema(description = "Whether the member can manage other members")
        private Boolean canManageMembers;
        
        @Schema(description = "Whether the member can manage loads")
        private Boolean canManageLoads;
        
        @Schema(description = "Whether the member can update load status")
        private Boolean canUpdateLoadStatus;
        
        @Schema(description = "Whether the member can upload documents")
        private Boolean canUploadDocuments;
        
        @Schema(description = "Whether the member can generate invoices")
        private Boolean canGenerateInvoices;
        
        @Schema(description = "Whether the member can view financials")
        private Boolean canViewFinancials;
        
        @Schema(description = "Whether the member can track location")
        private Boolean canTrackLocation;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Company member response")
    public static class MemberResponse {
        
        @Schema(description = "Member ID", example = "1")
        private Long id;
        
        @Schema(description = "User ID", example = "123")
        private Long userId;
        
        @Schema(description = "User's username", example = "john.doe")
        private String username;
        
        @Schema(description = "User's email", example = "<EMAIL>")
        private String email;
        
        @Schema(description = "User's first name", example = "John")
        private String firstName;
        
        @Schema(description = "User's last name", example = "Doe")
        private String lastName;
        
        @Schema(description = "User's phone number", example = "+263771234567")
        private String phoneNumber;
        
        @Schema(description = "Member's role in the company", example = "DRIVER")
        private CompanyMember.CompanyRole role;
        
        @Schema(description = "Member's status", example = "ACTIVE")
        private CompanyMember.MemberStatus status;
        
        @Schema(description = "Whether the member can manage other members", example = "false")
        private Boolean canManageMembers;
        
        @Schema(description = "Whether the member can manage loads", example = "true")
        private Boolean canManageLoads;
        
        @Schema(description = "Whether the member can update load status", example = "true")
        private Boolean canUpdateLoadStatus;
        
        @Schema(description = "Whether the member can upload documents", example = "true")
        private Boolean canUploadDocuments;
        
        @Schema(description = "Whether the member can generate invoices", example = "false")
        private Boolean canGenerateInvoices;
        
        @Schema(description = "Whether the member can view financials", example = "false")
        private Boolean canViewFinancials;
        
        @Schema(description = "Whether the member can track location", example = "true")
        private Boolean canTrackLocation;
        
        @Schema(description = "Who invited this member", example = "admin")
        private String invitedBy;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "When the invitation was sent", example = "2024-01-15 10:30:00")
        private LocalDateTime invitedAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "When the member joined", example = "2024-01-15 11:00:00")
        private LocalDateTime joinedAt;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "When the member was created", example = "2024-01-15 10:30:00")
        private LocalDateTime createdAt;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "List of company members response")
    public static class MemberListResponse {
        
        @Schema(description = "List of company members")
        private List<MemberResponse> members;
        
        @Schema(description = "Total number of members", example = "15")
        private Long totalMembers;
        
        @Schema(description = "Number of active members", example = "12")
        private Long activeMembers;
        
        @Schema(description = "Number of pending invitations", example = "2")
        private Long pendingInvitations;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Request to accept company invitation")
    public static class AcceptInvitationRequest {
        
        @NotNull(message = "Member ID is required")
        @Schema(description = "ID of the membership to accept", example = "1")
        private Long memberId;
        
        @Schema(description = "Whether to accept the invitation", example = "true")
        @Builder.Default
        private Boolean accept = true;
    }
}
