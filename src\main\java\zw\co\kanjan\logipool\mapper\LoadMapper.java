package zw.co.kanjan.logipool.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import zw.co.kanjan.logipool.dto.load.LoadCreateRequest;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.entity.Load;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface LoadMapper {
    
    Load toEntity(LoadCreateRequest request);
    
    @Mapping(source = "client.id", target = "clientId")
    @Mapping(source = "client.firstName", target = "clientName")
    @Mapping(source = "assignedCompany.id", target = "assignedCompanyId")
    @Mapping(source = "assignedCompany.name", target = "assignedCompanyName")
    @Mapping(expression = "java(load.getBids() != null ? load.getBids().size() : 0)", target = "bidCount")
    LoadResponse toResponse(Load load);
}
