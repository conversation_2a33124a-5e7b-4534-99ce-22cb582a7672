import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/widgets/custom_app_bar.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../auth/bloc/auth_bloc.dart';
import '../bloc/admin_bloc.dart';

class AdminAnalyticsScreen extends StatefulWidget {
  const AdminAnalyticsScreen({super.key});

  @override
  State<AdminAnalyticsScreen> createState() => _AdminAnalyticsScreenState();
}

class _AdminAnalyticsScreenState extends State<AdminAnalyticsScreen> {
  @override
  void initState() {
    super.initState();
    context.read<AdminBloc>().add(const LoadAnalytics());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: 'System Analytics',
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => context.read<AdminBloc>().add(const LoadAnalytics()),
          ),
          PopupMenuButton<String>(
            onSelected: (value) => _handleMenuAction(value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'logout',
                child: Row(
                  children: [
                    Icon(Icons.logout, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Logout', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: BlocConsumer<AdminBloc, AdminState>(
        listener: (context, state) {
          if (state is AdminError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(state.message)),
            );
          }
        },
        builder: (context, state) {
          if (state is AdminLoading) {
            return const LoadingWidget();
          } else if (state is AdminError) {
            return CustomErrorWidget(
              message: state.message,
              onRetry: () => context.read<AdminBloc>().add(const LoadAnalytics()),
            );
          } else if (state is AnalyticsLoaded) {
            return _buildAnalyticsContent(state);
          }
          return const LoadingWidget();
        },
      ),
    );
  }

  Widget _buildAnalyticsContent(AnalyticsLoaded state) {
    final analytics = state.analytics;
    
    return RefreshIndicator(
      onRefresh: () async {
        context.read<AdminBloc>().add(const LoadAnalytics());
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (analytics.userAnalytics != null) ...[
              _buildUserAnalytics(analytics.userAnalytics!),
              const SizedBox(height: 24),
            ],
            if (analytics.loadAnalytics != null) ...[
              _buildLoadAnalytics(analytics.loadAnalytics!),
              const SizedBox(height: 24),
            ],
            if (analytics.revenueAnalytics != null) ...[
              _buildRevenueAnalytics(analytics.revenueAnalytics!),
              const SizedBox(height: 24),
            ],
            if (analytics.performanceAnalytics != null) ...[
              _buildPerformanceAnalytics(analytics.performanceAnalytics!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildUserAnalytics(userAnalytics) {
        final screenWidth = MediaQuery.of(context).size.width;
    final itemWidth = screenWidth / (screenWidth > 768 ? 2 : 1);
    final itemHeight = 130 + (screenWidth > 768 ? 0 : 20); // desired height
    final aspectRatio = itemWidth / itemHeight;
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.people, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  'User Analytics',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: aspectRatio,
              children: [
                _buildAnalyticsCard(
                  'Total Users',
                  userAnalytics.totalUsers?.toString() ?? '0',
                  Icons.people,
                  Colors.blue,
                ),
                _buildAnalyticsCard(
                  'Active Users',
                  userAnalytics.activeUsers?.toString() ?? '0',
                  Icons.people_alt,
                  Colors.green,
                ),
                _buildAnalyticsCard(
                  'New This Month',
                  userAnalytics.newUsersThisMonth?.toString() ?? '0',
                  Icons.person_add,
                  Colors.orange,
                ),
                _buildAnalyticsCard(
                  'Client Users',
                  userAnalytics.clientUsers?.toString() ?? '0',
                  Icons.business_center,
                  Colors.purple,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadAnalytics(loadAnalytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.local_shipping, color: Colors.orange),
                const SizedBox(width: 8),
                Text(
                  'Load Analytics',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 2,
              children: [
                _buildAnalyticsCard(
                  'Total Loads',
                  loadAnalytics.totalLoads?.toString() ?? '0',
                  Icons.local_shipping,
                  Colors.orange,
                ),
                _buildAnalyticsCard(
                  'Active Loads',
                  loadAnalytics.activeLoads?.toString() ?? '0',
                  Icons.local_shipping_outlined,
                  Colors.blue,
                ),
                _buildAnalyticsCard(
                  'Completed',
                  loadAnalytics.completedLoads?.toString() ?? '0',
                  Icons.check_circle,
                  Colors.green,
                ),
                _buildAnalyticsCard(
                  'Avg Value',
                  '\$${loadAnalytics.averageLoadValue?.toStringAsFixed(2) ?? '0.00'}',
                  Icons.attach_money,
                  Colors.purple,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRevenueAnalytics(revenueAnalytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.attach_money, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'Revenue Analytics',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 2,
              children: [
                _buildAnalyticsCard(
                  'Total Revenue',
                  '\$${revenueAnalytics.totalRevenue?.toStringAsFixed(2) ?? '0.00'}',
                  Icons.attach_money,
                  Colors.green,
                ),
                _buildAnalyticsCard(
                  'Monthly Revenue',
                  '\$${revenueAnalytics.monthlyRevenue?.toStringAsFixed(2) ?? '0.00'}',
                  Icons.trending_up,
                  Colors.blue,
                ),
                _buildAnalyticsCard(
                  'Total Commissions',
                  '\$${revenueAnalytics.totalCommissions?.toStringAsFixed(2) ?? '0.00'}',
                  Icons.percent,
                  Colors.orange,
                ),
                _buildAnalyticsCard(
                  'Avg Commission',
                  '${revenueAnalytics.averageCommission?.toStringAsFixed(1) ?? '0.0'}%',
                  Icons.analytics,
                  Colors.purple,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceAnalytics(performanceAnalytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.speed, color: Colors.purple),
                const SizedBox(width: 8),
                Text(
                  'Performance Analytics',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 2,
              children: [
                _buildAnalyticsCard(
                  'Avg Response Time',
                  '${performanceAnalytics.averageResponseTime?.toStringAsFixed(0) ?? '0'}ms',
                  Icons.speed,
                  Colors.purple,
                ),
                _buildAnalyticsCard(
                  'Total API Calls',
                  performanceAnalytics.totalApiCalls?.toString() ?? '0',
                  Icons.api,
                  Colors.blue,
                ),
                _buildAnalyticsCard(
                  'Error Count',
                  performanceAnalytics.errorCount?.toString() ?? '0',
                  Icons.error,
                  Colors.red,
                ),
                _buildAnalyticsCard(
                  'Error Rate',
                  '${performanceAnalytics.errorRate?.toStringAsFixed(2) ?? '0.00'}%',
                  Icons.warning,
                  Colors.orange,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const Spacer(),
            ],
          ),
          const Spacer(),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey[600],
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'logout':
        _showLogoutDialog();
        break;
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AuthBloc>().add(AuthLogoutRequested());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
