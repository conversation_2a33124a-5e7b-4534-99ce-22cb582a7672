package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.load.LoadCreateRequest;
import zw.co.kanjan.logipool.dto.load.LoadResponse;
import zw.co.kanjan.logipool.dto.load.LoadUpdateRequest;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.CompanyMember;
import zw.co.kanjan.logipool.entity.Bid;
import zw.co.kanjan.logipool.entity.Role;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.mapper.LoadMapper;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.UserRepository;
import zw.co.kanjan.logipool.repository.CompanyRepository;
import zw.co.kanjan.logipool.repository.CompanyMemberRepository;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class LoadService {

    private final LoadRepository loadRepository;
    private final UserRepository userRepository;
    private final CompanyRepository companyRepository;
    private final CompanyMemberRepository companyMemberRepository;
    private final LoadMapper loadMapper;
    private final NotificationService notificationService;
    
    public LoadResponse createLoad(LoadCreateRequest request, String username) {
        User client = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        // Validate client has permission to post loads
        if (!hasClientRole(client)) {
            throw new BusinessException("Only clients can post loads");
        }

        Load load = loadMapper.toEntity(request);
        load.setClient(client);
        load.setStatus(Load.LoadStatus.POSTED);

        // Generate unique tracking number
        load.setTrackingNumber(generateTrackingNumber());

        // Set default bidding close time if not provided
        if (load.getBiddingClosesAt() == null) {
            load.setBiddingClosesAt(LocalDateTime.now().plusDays(7)); // Default 7 days
        }

        Load savedLoad = loadRepository.save(load);

        // Send notification about new load to transporters
        notificationService.sendLoadPostedNotification(savedLoad);

        // Send notification to admins about new load
        notificationService.sendAdminNewLoadNotification(savedLoad);

        log.info("Load created successfully: {} by user: {}", savedLoad.getTitle(), username);
        return loadMapper.toResponse(savedLoad);
    }

    public LoadResponse updateLoad(Long loadId, LoadUpdateRequest request, String username) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        // Verify ownership
        if (!load.getClient().getUsername().equals(username)) {
            throw new BusinessException("You can only update your own loads");
        }

        // Verify load can be updated (not assigned or in transit)
        if (load.getStatus() == Load.LoadStatus.ASSIGNED ||
            load.getStatus() == Load.LoadStatus.IN_TRANSIT ||
            load.getStatus() == Load.LoadStatus.DELIVERED) {
            throw new BusinessException("Cannot update load in current status: " + load.getStatus());
        }

        // Update load fields
        updateLoadFromRequest(load, request);

        Load savedLoad = loadRepository.save(load);
        log.info("Load updated successfully: {} by user: {}", savedLoad.getTitle(), username);
        return loadMapper.toResponse(savedLoad);
    }

    public void deleteLoad(Long loadId, String username) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        // Verify ownership
        if (!load.getClient().getUsername().equals(username)) {
            throw new BusinessException("You can only delete your own loads");
        }

        // Verify load can be deleted (only if posted and no accepted bids)
        if (load.getStatus() != Load.LoadStatus.POSTED) {
            throw new BusinessException("Cannot delete load in current status: " + load.getStatus());
        }

        boolean hasAcceptedBids = load.getBids().stream()
                .anyMatch(bid -> bid.getStatus() == Bid.BidStatus.ACCEPTED);

        if (hasAcceptedBids) {
            throw new BusinessException("Cannot delete load with accepted bids");
        }

        loadRepository.delete(load);
        log.info("Load deleted successfully: {} by user: {}", load.getTitle(), username);
    }

    public LoadResponse assignLoad(Long loadId, Long companyId, String username) {
        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        // Verify ownership
        if (!load.getClient().getUsername().equals(username)) {
            throw new BusinessException("You can only assign your own loads");
        }

        Company company = companyRepository.findById(companyId)
                .orElseThrow(() -> new ResourceNotFoundException("Company not found with id: " + companyId));

        // Verify company has a bid for this load
        boolean hasBid = load.getBids().stream()
                .anyMatch(bid -> bid.getCompany().getId().equals(companyId) &&
                               bid.getStatus() == Bid.BidStatus.PENDING);

        if (!hasBid) {
            throw new BusinessException("Company must have a pending bid to be assigned");
        }

        // Update load status and assign company
        load.setAssignedCompany(company);
        load.setStatus(Load.LoadStatus.ASSIGNED);

        // Accept the company's bid and reject others
        load.getBids().forEach(bid -> {
            if (bid.getCompany().getId().equals(companyId)) {
                bid.setStatus(Bid.BidStatus.ACCEPTED);
                bid.setAcceptedAt(LocalDateTime.now());
                notificationService.sendBidAcceptedNotification(bid);
            } else if (bid.getStatus() == Bid.BidStatus.PENDING) {
                bid.setStatus(Bid.BidStatus.REJECTED);
                bid.setRejectedAt(LocalDateTime.now());
                notificationService.sendBidRejectedNotification(bid);
            }
        });

        Load savedLoad = loadRepository.save(load);
        log.info("Load assigned successfully: {} to company: {}", savedLoad.getTitle(), company.getName());
        return loadMapper.toResponse(savedLoad);
    }
    
    @Transactional(readOnly = true)
    public Page<LoadResponse> getAllLoads(Pageable pageable) {
        Page<Load> loads = loadRepository.findAll(pageable);
        return loads.map(loadMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Page<LoadResponse> getAllLoads(String username, Pageable pageable) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        // Admin can see all loads
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return getAllLoads(pageable);
        }

        // Get loads visible to this user
        Page<Load> loads = loadRepository.findLoadsVisibleToUser(user.getId(), pageable);
        return loads.map(loadMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Page<LoadResponse> getLoadsByStatus(Load.LoadStatus status, Pageable pageable) {
        Page<Load> loads = loadRepository.findByStatus(status, pageable);
        return loads.map(loadMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Page<LoadResponse> getLoadsByStatus(Load.LoadStatus status, String username, Pageable pageable) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        // Admin can see all loads
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return getLoadsByStatus(status, pageable);
        }

        // Get loads with specific status visible to this user
        Page<Load> loads = loadRepository.findLoadsByStatusVisibleToUser(status, user.getId(), pageable);
        return loads.map(loadMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public Page<LoadResponse> getVerifiedLoads(Pageable pageable) {
        Page<Load> loads = loadRepository.findByStatusAndVerified(Load.LoadStatus.POSTED, true, pageable);
        return loads.map(loadMapper::toResponse);
    }
    
    @Transactional(readOnly = true)
    public LoadResponse getLoadById(Long id) {
        Load load = loadRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + id));
        return loadMapper.toResponse(load);
    }
    
    @Transactional(readOnly = true)
    public Page<LoadResponse> getMyLoads(String username, Pageable pageable) {
        User client = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        Page<Load> loads = loadRepository.findByClient(client, pageable);
        return loads.map(loadMapper::toResponse);
    }
    
    public LoadResponse updateLoadStatus(Long id, Load.LoadStatus status) {
        Load load = loadRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + id));

        load.setStatus(status);
        Load updatedLoad = loadRepository.save(load);
        return loadMapper.toResponse(updatedLoad);
    }

    public LoadResponse updateLoadStatus(Long loadId, Load.LoadStatus newStatus, String username) {
        log.info("Updating load {} status to {} by user {}", loadId, newStatus, username);

        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        // Validate user has permission to update load status
        validateLoadStatusUpdatePermission(load, user, newStatus);

        // Validate status transition is allowed
        validateStatusTransition(load.getStatus(), newStatus);

        Load.LoadStatus oldStatus = load.getStatus();
        load.setStatus(newStatus);

        Load updatedLoad = loadRepository.save(load);

        // Send notifications about status change
        notificationService.sendLoadStatusUpdateNotification(updatedLoad, oldStatus.toString());

        log.info("Load {} status updated from {} to {} by {}", loadId, oldStatus, newStatus, username);
        return loadMapper.toResponse(updatedLoad);
    }

    public LoadResponse updateLoadStatusWithNotes(Long loadId, Load.LoadStatus newStatus, String notes, String username) {
        log.info("Updating load {} status to {} with notes by user {}", loadId, newStatus, username);

        Load load = loadRepository.findById(loadId)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + loadId));

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        // Validate user has permission to update load status
        validateLoadStatusUpdatePermission(load, user, newStatus);

        // Validate status transition is allowed
        validateStatusTransition(load.getStatus(), newStatus);

        Load.LoadStatus oldStatus = load.getStatus();
        load.setStatus(newStatus);

        // Update special instructions if notes provided
        if (notes != null && !notes.trim().isEmpty()) {
            String existingInstructions = load.getSpecialInstructions() != null ? load.getSpecialInstructions() : "";
            String timestamp = LocalDateTime.now().toString();
            String updatedInstructions = existingInstructions + "\n[" + timestamp + " - " + username + "]: " + notes;
            load.setSpecialInstructions(updatedInstructions);
        }

        Load updatedLoad = loadRepository.save(load);

        // Send notifications about status change
        notificationService.sendLoadStatusUpdateNotification(updatedLoad, oldStatus.toString());

        log.info("Load {} status updated from {} to {} by {} with notes", loadId, oldStatus, newStatus, username);
        return loadMapper.toResponse(updatedLoad);
    }
    
    // Helper methods
    private boolean hasClientRole(User user) {
        return user.getRoles().stream()
                .anyMatch(role -> role.getName() == Role.RoleName.CLIENT || role.getName() == Role.RoleName.ADMIN);
    }

    private void updateLoadFromRequest(Load load, LoadUpdateRequest request) {
        if (request.getTitle() != null) load.setTitle(request.getTitle());
        if (request.getDescription() != null) load.setDescription(request.getDescription());
        if (request.getCargoType() != null) load.setCargoType(request.getCargoType());
        if (request.getWeight() != null) load.setWeight(request.getWeight());
        if (request.getWeightUnit() != null) load.setWeightUnit(request.getWeightUnit());
        if (request.getVolume() != null) load.setVolume(request.getVolume());
        if (request.getVolumeUnit() != null) load.setVolumeUnit(request.getVolumeUnit());
        if (request.getPickupLocation() != null) load.setPickupLocation(request.getPickupLocation());
        if (request.getDeliveryLocation() != null) load.setDeliveryLocation(request.getDeliveryLocation());
        if (request.getPickupDate() != null) load.setPickupDate(request.getPickupDate());
        if (request.getDeliveryDate() != null) load.setDeliveryDate(request.getDeliveryDate());
        if (request.getEstimatedDistance() != null) load.setEstimatedDistance(request.getEstimatedDistance());
        if (request.getDistanceUnit() != null) load.setDistanceUnit(request.getDistanceUnit());
        if (request.getLoadType() != null) load.setLoadType(request.getLoadType());
        if (request.getPaymentType() != null) load.setPaymentType(request.getPaymentType());
        if (request.getPaymentRate() != null) load.setPaymentRate(request.getPaymentRate());
        if (request.getPaymentUnit() != null) load.setPaymentUnit(request.getPaymentUnit());
        if (request.getEstimatedValue() != null) load.setEstimatedValue(request.getEstimatedValue());
        if (request.getPriority() != null) load.setPriority(request.getPriority());
        if (request.getRequiresInsurance() != null) load.setRequiresInsurance(request.getRequiresInsurance());
        if (request.getRequiresSpecialHandling() != null) load.setRequiresSpecialHandling(request.getRequiresSpecialHandling());
        if (request.getSpecialInstructions() != null) load.setSpecialInstructions(request.getSpecialInstructions());
        if (request.getBiddingClosesAt() != null) load.setBiddingClosesAt(request.getBiddingClosesAt());
    }

    private void validateLoadStatusUpdatePermission(Load load, User user, Load.LoadStatus newStatus) {
        // Load client can always update their own loads
        if (load.getClient().getId().equals(user.getId())) {
            return;
        }

        // Admin can update any load
        if (user.getRoles().stream().anyMatch(role -> role.getName() == Role.RoleName.ADMIN)) {
            return;
        }

        // Only assigned company members can update load status
        if (load.getAssignedCompany() == null) {
            throw new BusinessException("Load is not assigned to any company");
        }

        // Check if user is the company owner
        if (load.getAssignedCompany().getUser().getId().equals(user.getId())) {
            return;
        }

        // Check if user is a company member with load status update permission
        CompanyMember member = companyMemberRepository.findByCompanyAndUser(load.getAssignedCompany(), user)
                .orElse(null);

        if (member == null || member.getStatus() != CompanyMember.MemberStatus.ACTIVE) {
            throw new BusinessException("You are not an active member of the assigned company");
        }

        if (!member.getCanUpdateLoadStatus()) {
            throw new BusinessException("You don't have permission to update load status");
        }

        // Additional role-based validation
        switch (newStatus) {
            case IN_TRANSIT:
            case DELIVERED:
                // Only drivers, dispatchers, managers, and owners can update to these statuses
                if (member.getRole() != CompanyMember.CompanyRole.DRIVER &&
                    member.getRole() != CompanyMember.CompanyRole.DISPATCHER &&
                    member.getRole() != CompanyMember.CompanyRole.MANAGER &&
                    member.getRole() != CompanyMember.CompanyRole.OWNER) {
                    throw new BusinessException("Only drivers, dispatchers, managers, or owners can update load to " + newStatus);
                }
                break;
            case CANCELLED:
                // Only managers and owners can cancel loads
                if (member.getRole() != CompanyMember.CompanyRole.MANAGER &&
                    member.getRole() != CompanyMember.CompanyRole.OWNER) {
                    throw new BusinessException("Only managers or owners can cancel loads");
                }
                break;
            default:
                // Other statuses can be updated by anyone with permission
                break;
        }
    }

    private void validateStatusTransition(Load.LoadStatus currentStatus, Load.LoadStatus newStatus) {
        // Define allowed status transitions
        switch (currentStatus) {
            case POSTED:
                if (newStatus != Load.LoadStatus.BIDDING_CLOSED &&
                    newStatus != Load.LoadStatus.ASSIGNED &&
                    newStatus != Load.LoadStatus.CANCELLED) {
                    throw new BusinessException("Invalid status transition from " + currentStatus + " to " + newStatus);
                }
                break;
            case BIDDING_CLOSED:
                if (newStatus != Load.LoadStatus.ASSIGNED &&
                    newStatus != Load.LoadStatus.POSTED &&
                    newStatus != Load.LoadStatus.CANCELLED) {
                    throw new BusinessException("Invalid status transition from " + currentStatus + " to " + newStatus);
                }
                break;
            case ASSIGNED:
                if (newStatus != Load.LoadStatus.IN_TRANSIT &&
                    newStatus != Load.LoadStatus.CANCELLED) {
                    throw new BusinessException("Invalid status transition from " + currentStatus + " to " + newStatus);
                }
                break;
            case IN_TRANSIT:
                if (newStatus != Load.LoadStatus.DELIVERED &&
                    newStatus != Load.LoadStatus.CANCELLED) {
                    throw new BusinessException("Invalid status transition from " + currentStatus + " to " + newStatus);
                }
                break;
            case DELIVERED:
                if (newStatus != Load.LoadStatus.COMPLETED) {
                    throw new BusinessException("Delivered loads can only be marked as completed");
                }
                break;
            case COMPLETED:
                // Completed loads cannot be changed
                throw new BusinessException("Cannot change status of completed load");
            case CANCELLED:
                // Cancelled loads cannot be changed except by admin
                throw new BusinessException("Cannot change status of cancelled load");
        }
    }

    /**
     * Generate a unique tracking number for a load
     * Format: LP-YYYYMMDD-XXXXX (LP = LogiPool, YYYYMMDD = date, XXXXX = random 5-digit number)
     */
    private String generateTrackingNumber() {
        String datePrefix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        Random random = new Random();
        int randomNumber = 10000 + random.nextInt(90000); // 5-digit random number

        String trackingNumber = "LP-" + datePrefix + "-" + randomNumber;

        // Check if tracking number already exists and regenerate if needed
        while (loadRepository.findByTrackingNumber(trackingNumber).isPresent()) {
            randomNumber = 10000 + random.nextInt(90000);
            trackingNumber = "LP-" + datePrefix + "-" + randomNumber;
        }

        return trackingNumber;
    }

    /**
     * Find load by tracking number for public access
     */
    @Transactional(readOnly = true)
    public LoadResponse getLoadByTrackingNumber(String trackingNumber) {
        Load load = loadRepository.findByTrackingNumber(trackingNumber)
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with tracking number: " + trackingNumber));
        return loadMapper.toResponse(load);
    }
}
