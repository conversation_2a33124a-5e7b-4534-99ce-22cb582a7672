// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'vehicle_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VehicleModel _$VehicleModelFromJson(Map<String, dynamic> json) => VehicleModel(
      id: (json['id'] as num?)?.toInt(),
      registrationNumber: json['registrationNumber'] as String?,
      make: json['make'] as String?,
      model: json['model'] as String?,
      year: (json['year'] as num?)?.toInt(),
      type: $enumDecodeNullable(_$VehicleTypeEnumMap, json['type']),
      maxWeight: (json['maxWeight'] as num?)?.toDouble(),
      maxVolume: (json['maxVolume'] as num?)?.toDouble(),
      weightUnit: json['weightUnit'] as String?,
      volumeUnit: json['volumeUnit'] as String?,
      status: $enumDecodeNullable(_$VehicleStatusEnumMap, json['status']),
      description: json['description'] as String?,
      dailyRate: (json['dailyRate'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      features: (json['features'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      imageUrl: json['imageUrl'] as String?,
      isAvailableForRent: json['isAvailableForRent'] as bool?,
      isFeatured: json['isFeatured'] as bool?,
      isPubliclyVisible: json['isPubliclyVisible'] as bool?,
      publicApprovalStatus: $enumDecodeNullable(
          _$PublicApprovalStatusEnumMap, json['publicApprovalStatus']),
      company: json['company'] == null
          ? null
          : CompanyInfo.fromJson(json['company'] as Map<String, dynamic>),
      hasInsurance: json['hasInsurance'] as bool?,
      hasFitnessCertificate: json['hasFitnessCertificate'] as bool?,
      hasPermits: json['hasPermits'] as bool?,
      insuranceExpiryDate: json['insuranceExpiryDate'] == null
          ? null
          : DateTime.parse(json['insuranceExpiryDate'] as String),
      fitnessExpiryDate: json['fitnessExpiryDate'] == null
          ? null
          : DateTime.parse(json['fitnessExpiryDate'] as String),
      permitExpiryDate: json['permitExpiryDate'] == null
          ? null
          : DateTime.parse(json['permitExpiryDate'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$VehicleModelToJson(VehicleModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'registrationNumber': instance.registrationNumber,
      'make': instance.make,
      'model': instance.model,
      'year': instance.year,
      'type': _$VehicleTypeEnumMap[instance.type],
      'maxWeight': instance.maxWeight,
      'maxVolume': instance.maxVolume,
      'weightUnit': instance.weightUnit,
      'volumeUnit': instance.volumeUnit,
      'status': _$VehicleStatusEnumMap[instance.status],
      'description': instance.description,
      'dailyRate': instance.dailyRate,
      'currency': instance.currency,
      'features': instance.features,
      'imageUrl': instance.imageUrl,
      'isAvailableForRent': instance.isAvailableForRent,
      'isFeatured': instance.isFeatured,
      'isPubliclyVisible': instance.isPubliclyVisible,
      'publicApprovalStatus':
          _$PublicApprovalStatusEnumMap[instance.publicApprovalStatus],
      'company': instance.company,
      'hasInsurance': instance.hasInsurance,
      'hasFitnessCertificate': instance.hasFitnessCertificate,
      'hasPermits': instance.hasPermits,
      'insuranceExpiryDate': instance.insuranceExpiryDate?.toIso8601String(),
      'fitnessExpiryDate': instance.fitnessExpiryDate?.toIso8601String(),
      'permitExpiryDate': instance.permitExpiryDate?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$VehicleTypeEnumMap = {
  VehicleType.truck: 'TRUCK',
  VehicleType.trailer: 'TRAILER',
  VehicleType.flatbed: 'FLATBED',
  VehicleType.refrigerated: 'REFRIGERATED',
  VehicleType.tanker: 'TANKER',
  VehicleType.container: 'CONTAINER',
  VehicleType.van: 'VAN',
  VehicleType.pickup: 'PICKUP',
};

const _$VehicleStatusEnumMap = {
  VehicleStatus.available: 'AVAILABLE',
  VehicleStatus.inUse: 'IN_USE',
  VehicleStatus.maintenance: 'MAINTENANCE',
  VehicleStatus.outOfService: 'OUT_OF_SERVICE',
};

const _$PublicApprovalStatusEnumMap = {
  PublicApprovalStatus.pending: 'PENDING',
  PublicApprovalStatus.approved: 'APPROVED',
  PublicApprovalStatus.rejected: 'REJECTED',
  PublicApprovalStatus.suspended: 'SUSPENDED',
};

CompanyInfo _$CompanyInfoFromJson(Map<String, dynamic> json) => CompanyInfo(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      rating: (json['rating'] as num?)?.toDouble(),
      verificationStatus: json['verificationStatus'] as String?,
      location: json['location'] as String?,
      phoneNumber: json['phoneNumber'] as String?,
      email: json['email'] as String?,
    );

Map<String, dynamic> _$CompanyInfoToJson(CompanyInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'rating': instance.rating,
      'verificationStatus': instance.verificationStatus,
      'location': instance.location,
      'phoneNumber': instance.phoneNumber,
      'email': instance.email,
    };
