name: logipool_flutter
description: LogiPool - Logistics Marketplace Flutter Application

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.8.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  provider: ^6.1.1
  bloc: ^9.0.0
  flutter_bloc: ^9.1.1

  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1

  # Local Storage & Caching
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Authentication & Security
  jwt_decoder: ^2.0.1
  crypto: ^3.0.3

  # UI & Navigation
  go_router: ^16.0.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^3.3.1

  # Maps & Location
  google_maps_flutter: ^2.5.0
  location: ^8.0.1
  geolocator: ^14.0.2

  # File Handling
  file_picker: ^10.2.0
  image_picker: ^1.0.4
  path_provider: ^2.1.1

  # Notifications
  firebase_core: ^3.15.1
  firebase_messaging: ^15.2.9
  flutter_local_notifications: ^19.3.0
  timezone: ^0.10.0

  # Real-time Communication
  web_socket_channel: ^3.0.3
  socket_io_client: ^3.1.2

  # Utilities
  intl: ^0.20.2
  uuid: ^4.1.0
  url_launcher: ^6.2.1
  permission_handler: ^12.0.1

  # UI Components
  cupertino_icons: ^1.0.6
  equatable: ^2.0.7
  get_it: ^8.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  retrofit_generator: ^9.2.0
  json_serializable: ^6.7.1
  hive_generator: ^2.0.1

  # Linting
  flutter_lints: ^6.0.0

  # Testing
  mockito: ^5.4.2
  bloc_test: ^10.0.0
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/

  # An image asset can refer to one or more resolution-variant files by
  # listing them with the assets path. For example:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "name" field with the font family name, and a "fonts" field with a
  # list of font files for this font family. For example:
  # fonts:
  #   - family: Roboto
  #     fonts:
  #       - asset: assets/fonts/Roboto-Regular.ttf
  #       - asset: assets/fonts/Roboto-Bold.ttf
  #         weight: 700

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
