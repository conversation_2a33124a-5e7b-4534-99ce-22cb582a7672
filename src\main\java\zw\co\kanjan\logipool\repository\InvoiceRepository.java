package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Invoice;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.Bid;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface InvoiceRepository extends JpaRepository<Invoice, Long> {
    
    // Find invoice by invoice number
    Optional<Invoice> findByInvoiceNumber(String invoiceNumber);
    
    // Find invoices by status
    Page<Invoice> findByStatus(Invoice.InvoiceStatus status, Pageable pageable);
    
    // Find invoices by client
    Page<Invoice> findByClientOrderByCreatedAtDesc(User client, Pageable pageable);
    
    // Find invoices by transporter
    Page<Invoice> findByTransporterOrderByCreatedAtDesc(User transporter, Pageable pageable);
    
    // Find invoices by company
    Page<Invoice> findByCompanyOrderByCreatedAtDesc(Company company, Pageable pageable);
    
    // Find invoices by load
    List<Invoice> findByLoad(Load load);

    Page<Invoice> findByLoad(Load load, Pageable pageable);

    // Find invoices by bid
    List<Invoice> findByBid(Bid bid);

    // Check if invoice exists for load
    boolean existsByLoad(Load load);
    
    // Find overdue invoices
    @Query("SELECT i FROM Invoice i WHERE i.dueDate < :now AND i.status IN ('SENT', 'VIEWED')")
    List<Invoice> findOverdueInvoices(@Param("now") LocalDateTime now);
    
    // Find invoices by date range
    @Query("SELECT i FROM Invoice i WHERE i.createdAt BETWEEN :startDate AND :endDate")
    List<Invoice> findByDateRange(@Param("startDate") LocalDateTime startDate, 
                                 @Param("endDate") LocalDateTime endDate);
    
    // Calculate total invoice amount by status
    @Query("SELECT SUM(i.totalAmount) FROM Invoice i WHERE i.status = :status")
    BigDecimal getTotalAmountByStatus(@Param("status") Invoice.InvoiceStatus status);
    
    // Calculate total commission from invoices
    @Query("SELECT SUM(i.commissionAmount) FROM Invoice i WHERE i.status = 'PAID'")
    BigDecimal getTotalCommissionFromInvoices();
    
    // Find invoices by type
    List<Invoice> findByType(Invoice.InvoiceType type);
    
    // Find unpaid invoices for user
    @Query("SELECT i FROM Invoice i WHERE (i.client = :user OR i.transporter = :user) AND i.status IN ('SENT', 'VIEWED', 'OVERDUE')")
    List<Invoice> findUnpaidInvoicesForUser(@Param("user") User user);
    
    // Count invoices by status
    long countByStatus(Invoice.InvoiceStatus status);
    
    // Find recent invoices
    @Query("SELECT i FROM Invoice i ORDER BY i.createdAt DESC")
    Page<Invoice> findRecentInvoices(Pageable pageable);
    
    // Check if invoice number exists
    boolean existsByInvoiceNumber(String invoiceNumber);
    
    // Find invoices due soon
    @Query("SELECT i FROM Invoice i WHERE i.dueDate BETWEEN :now AND :futureDate AND i.status IN ('SENT', 'VIEWED')")
    List<Invoice> findInvoicesDueSoon(@Param("now") LocalDateTime now, @Param("futureDate") LocalDateTime futureDate);

    // Find invoices for user (as client, transporter, or company member)
    @Query("SELECT DISTINCT i FROM Invoice i " +
           "LEFT JOIN CompanyMember cm ON cm.company = i.company " +
           "WHERE i.client.id = :userId OR i.transporter.id = :userId OR " +
           "(cm.user.id = :userId AND cm.status = 'ACTIVE') " +
           "ORDER BY i.createdAt DESC")
    Page<Invoice> findByClientOrTransporterOrCompanyMember(@Param("userId") Long userId, Pageable pageable);

    // Find invoices for user (as client, transporter, or company member) - non-paginated
    @Query("SELECT DISTINCT i FROM Invoice i " +
           "LEFT JOIN CompanyMember cm ON cm.company = i.company " +
           "WHERE i.client.id = :userId OR i.transporter.id = :userId OR " +
           "(cm.user.id = :userId AND cm.status = 'ACTIVE') " +
           "ORDER BY i.createdAt DESC")
    List<Invoice> findByClientOrTransporterOrCompanyMember(@Param("userId") Long userId);
}
