package zw.co.kanjan.logipool.dto.company;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.Data;
import zw.co.kanjan.logipool.entity.Company;

@Data
public class CompanyUpdateRequest {
    
    @Size(max = 100)
    private String name;
    
    @Size(max = 1000)
    private String description;
    
    @Size(max = 50)
    private String registrationNumber;
    
    @Size(max = 50)
    private String taxNumber;
    
    private Company.CompanyType type;
    
    @Size(max = 200)
    private String address;
    
    @Size(max = 50)
    private String city;
    
    @Size(max = 50)
    private String country;
    
    @Size(max = 20)
    private String phoneNumber;
    
    @Email
    @Size(max = 100)
    private String email;
    
    @Size(max = 100)
    private String website;
}
