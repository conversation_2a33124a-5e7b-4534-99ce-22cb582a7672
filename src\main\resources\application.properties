# Development Configuration

# Database Configuration
spring.datasource.url=*****************************************
spring.datasource.username=logipool
spring.datasource.password=logipool123
spring.datasource.driver-class-name=org.postgresql.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# Redis Configuration
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.timeout=60000

# JWT Configuration
app.jwt.secret=logipool-secret-key-for-jwt-token-generation-2025
app.jwt.expiration=86400000
app.jwt.refresh-expiration=604800000

# Test Data Configuration
app.test-data.enabled=false

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
app.upload.dir=./uploads

# Logging Configuration
logging.level.zw.co.kanjan.logipool=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN

# Development Tools
spring.devtools.restart.enabled=true
spring.devtools.livereload.enabled=true


# Mail Configuration
spring.mail.host=smtp.hostinger.com
spring.mail.port=465
spring.mail.username=<EMAIL>
spring.mail.password=GK28~N!$80p>
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.connectiontimeout=5000
spring.mail.properties.mail.smtp.timeout=5000
spring.mail.properties.mail.smtp.writetimeout=5000

# Notification Configuration
app.notification.email.enabled=false
app.notification.sms.enabled=false
app.notification.realtime.enabled=true
app.notification.persistent.enabled=true

# Admin Report Configuration
app.admin.daily-report.enabled=true

# Tracking Verification Configuration
app.tracking.verification.token-expiry-hours=24
app.tracking.verification.code-expiry-minutes=15
app.tracking.verification.max-requests-per-hour=3
app.tracking.verification.max-ip-requests-per-hour=10
app.tracking.verification.max-access-count=5
app.tracking.verification.cleanup-enabled=true
app.tracking.verification.monitoring-enabled=true