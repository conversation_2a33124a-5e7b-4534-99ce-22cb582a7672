import 'package:dio/dio.dart';

import '../models/invoice_model.dart';
import '../../core/constants/app_constants.dart';
import '../../core/network/api_client.dart';
import '../../core/network/api_exception.dart';

class InvoiceService {
  final ApiClient _apiClient;

  InvoiceService(this._apiClient);

  /// Generate automatic invoice for a delivered load
  Future<InvoiceResponse> generateAutomaticInvoice(int loadId) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/invoices/loads/$loadId/generate',
      );

      if (response.statusCode == 200) {
        return InvoiceResponse.fromJson(response.data!);
      } else {
        throw ApiException(
          message: 'Failed to generate invoice',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to generate invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  /// Preview automatic invoice for a delivered load
  Future<InvoicePreviewResponse> previewAutomaticInvoice(int loadId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/invoices/loads/$loadId/preview',
      );

      if (response.statusCode == 200) {
        return InvoicePreviewResponse.fromJson(response.data!);
      } else {
        throw ApiException(
          message: 'Failed to preview invoice',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to preview invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  /// Generate invoice with modifications for a delivered load
  Future<InvoiceResponse> generateInvoiceWithModifications(
    int loadId,
    InvoiceModificationRequest request,
  ) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/invoices/loads/$loadId/generate-with-modifications',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        return InvoiceResponse.fromJson(response.data!);
      } else {
        throw ApiException(
          message: 'Failed to generate invoice with modifications',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to generate invoice with modifications',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  /// Create custom invoice
  Future<InvoiceResponse> createInvoice(InvoiceCreateRequest request) async {
    try {
      final response = await _apiClient.post<Map<String, dynamic>>(
        '/invoices',
        data: request.toJson(),
      );

      if (response.statusCode == 201) {
        return InvoiceResponse.fromJson(response.data!);
      } else {
        throw ApiException(
          message: 'Failed to create invoice',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to create invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  /// Get invoice by ID
  Future<InvoiceResponse> getInvoiceById(int invoiceId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/invoices/$invoiceId',
      );

      if (response.statusCode == 200) {
        return InvoiceResponse.fromJson(response.data!);
      } else {
        throw ApiException(
          message: 'Failed to get invoice',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  /// Get invoice for a specific load
  Future<InvoiceResponse?> getInvoiceByLoadId(int loadId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
        '/invoices/loads/$loadId',
        queryParameters: {'page': 0, 'size': 1}, // Get first invoice only
      );

      if (response.statusCode == 200) {
        final pageData = response.data!;
        final content = pageData['content'] as List<dynamic>?;

        if (content != null && content.isNotEmpty) {
          // Return the first invoice from the paginated response
          return InvoiceResponse.fromJson(
              content.first as Map<String, dynamic>);
        } else {
          return null; // No invoices found for this load
        }
      } else if (response.statusCode == 404) {
        return null; // No invoice found for this load
      } else {
        throw ApiException(
          message: 'Failed to get invoice',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      if (e.response?.statusCode == 404) {
        return null; // No invoice found
      }
      throw ApiException(
        message: e.message ?? 'Failed to get invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  /// Update invoice
  Future<InvoiceResponse> updateInvoice(
      int invoiceId, InvoiceUpdateRequest request) async {
    try {
      final response = await _apiClient.put<Map<String, dynamic>>(
        '/invoices/$invoiceId',
        data: request.toJson(),
      );

      if (response.statusCode == 200) {
        return InvoiceResponse.fromJson(response.data!);
      } else {
        throw ApiException(
          message: 'Failed to update invoice',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to update invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  /// Download invoice PDF
  Future<List<int>> downloadInvoicePdf(int invoiceId) async {
    try {
      final response = await _apiClient.get<List<int>>(
        '/invoices/$invoiceId/pdf',
        options: Options(responseType: ResponseType.bytes),
      );

      if (response.statusCode == 200) {
        return response.data!;
      } else {
        throw ApiException(
          message: 'Failed to download invoice',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to download invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  /// Send invoice via email
  Future<void> sendInvoiceEmail(int invoiceId, String recipientEmail) async {
    try {
      final response = await _apiClient.post<void>(
        '/invoices/$invoiceId/send',
        data: {'recipientEmail': recipientEmail},
      );

      if (response.statusCode != 200) {
        throw ApiException(
          message: 'Failed to send invoice',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to send invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  /// Get all invoices for the current user/company
  Future<List<InvoiceResponse>> getInvoices({
    int page = 0,
    int size = 20,
    InvoiceStatus? status,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final queryParameters = <String, dynamic>{
        'page': page,
        'size': size,
      };

      if (status != null) {
        queryParameters['status'] = status.name.toUpperCase();
      }
      if (fromDate != null) {
        queryParameters['fromDate'] = fromDate.toIso8601String();
      }
      if (toDate != null) {
        queryParameters['toDate'] = toDate.toIso8601String();
      }

      final response = await _apiClient.get<Map<String, dynamic>>(
        '/invoices',
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final data = response.data!;
        final content = data['content'] as List<dynamic>;
        return content
            .map((json) =>
                InvoiceResponse.fromJson(json as Map<String, dynamic>))
            .toList();
      } else {
        throw ApiException(
          message: 'Failed to get invoices',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to get invoices',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  /// Mark invoice as paid
  Future<InvoiceResponse> markInvoiceAsPaid(
    int invoiceId, {
    String? paymentReference,
    DateTime? paymentDate,
  }) async {
    try {
      final data = <String, dynamic>{
        'status': 'PAID',
      };

      if (paymentReference != null) {
        data['paymentReference'] = paymentReference;
      }
      if (paymentDate != null) {
        data['paymentDate'] = paymentDate.toIso8601String();
      }

      final response = await _apiClient.patch<Map<String, dynamic>>(
        '/invoices/$invoiceId/payment',
        data: data,
      );

      if (response.statusCode == 200) {
        return InvoiceResponse.fromJson(response.data!);
      } else {
        throw ApiException(
          message: 'Failed to update payment status',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to update payment status',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }

  /// Cancel invoice
  Future<InvoiceResponse> cancelInvoice(int invoiceId, String reason) async {
    try {
      final response = await _apiClient.patch<Map<String, dynamic>>(
        '/invoices/$invoiceId/cancel',
        data: {'reason': reason},
      );

      if (response.statusCode == 200) {
        return InvoiceResponse.fromJson(response.data!);
      } else {
        throw ApiException(
          message: 'Failed to cancel invoice',
          statusCode: response.statusCode ?? 500,
        );
      }
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to cancel invoice',
        statusCode: e.response?.statusCode ?? 500,
      );
    }
  }
}
