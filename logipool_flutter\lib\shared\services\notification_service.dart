import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:timezone/timezone.dart' as tz;

import '../../core/constants/app_constants.dart';
import 'storage_service.dart';

class NotificationService {
  static NotificationService? _instance;
  static FlutterLocalNotificationsPlugin? _localNotifications;
  static FirebaseMessaging? _firebaseMessaging;
  
  final StorageService _storageService = StorageService();
  
  NotificationService._();
  
  static NotificationService get instance {
    _instance ??= NotificationService._();
    return _instance!;
  }
  
  factory NotificationService() => instance;

  static Future<void> init() async {
    _localNotifications = FlutterLocalNotificationsPlugin();

    // Initialize local notifications (always available)
    await instance._initializeLocalNotifications();

    // Initialize Firebase messaging (only if Firebase is available)
    try {
      _firebaseMessaging = FirebaseMessaging.instance;
      await instance._initializeFirebaseMessaging();
    } catch (e) {
      if (kDebugMode) {
        print('Firebase messaging initialization failed: $e');
        print('Push notifications will not be available');
      }
      _firebaseMessaging = null;
    }
  }

  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: false, // Don't request permissions during initialization
      requestBadgePermission: false,
      requestSoundPermission: false,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications?.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Don't request permissions during initialization
    // Permissions will be requested later when needed
  }

  Future<void> _initializeFirebaseMessaging() async {
    if (_firebaseMessaging == null) {
      if (kDebugMode) {
        print('Firebase messaging not available, skipping initialization');
      }
      return;
    }

    try {
      // Don't request permissions during initialization
      // Permissions will be requested later when needed

      // Get FCM token (this doesn't require permissions)
      final token = await _firebaseMessaging?.getToken();
      if (token != null) {
        await _storageService.setString('fcm_token', token);
        if (kDebugMode) {
          print('FCM Token: $token');
        }
      }

      // Listen for token refresh
      _firebaseMessaging?.onTokenRefresh.listen((token) async {
        await _storageService.setString('fcm_token', token);
        if (kDebugMode) {
          print('FCM Token refreshed: $token');
        }
      });

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle background messages
      FirebaseMessaging.onBackgroundMessage(_handleBackgroundMessage);

      // Handle notification taps when app is in background
      FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

      // Handle notification tap when app is terminated
      final initialMessage = await _firebaseMessaging?.getInitialMessage();
      if (initialMessage != null) {
        _handleNotificationTap(initialMessage);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing Firebase messaging: $e');
      }
    }
  }

  Future<void> _requestNotificationPermissions() async {
    final status = await Permission.notification.request();
    if (status.isDenied) {
      if (kDebugMode) {
        print('Notification permission denied');
      }
    }
  }

  // Public method to request permissions when needed
  Future<bool> requestPermissions() async {
    try {
      // Request local notification permissions
      final localStatus = await Permission.notification.request();

      // Request Firebase messaging permissions if available
      if (_firebaseMessaging != null) {
        final firebaseSettings = await _firebaseMessaging?.requestPermission(
          alert: true,
          badge: true,
          sound: true,
          provisional: false,
        );

        if (kDebugMode) {
          print('Firebase permission status: ${firebaseSettings?.authorizationStatus}');
        }
      }

      final isGranted = localStatus.isGranted;
      if (kDebugMode) {
        print('Notification permissions granted: $isGranted');
      }

      return isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting notification permissions: $e');
      }
      return false;
    }
  }

  // Check if permissions are already granted
  Future<bool> arePermissionsGranted() async {
    try {
      final status = await Permission.notification.status;
      return status.isGranted;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking notification permissions: $e');
      }
      return false;
    }
  }

  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    if (kDebugMode) {
      print('Received foreground message: ${message.messageId}');
    }

    // Show local notification when app is in foreground
    await showLocalNotification(
      title: message.notification?.title ?? 'LogiPool',
      body: message.notification?.body ?? 'New notification',
      payload: jsonEncode(message.data),
    );
  }

  static Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    if (kDebugMode) {
      print('Received background message: ${message.messageId}');
    }
    // Handle background message processing here
  }

  void _handleNotificationTap(RemoteMessage message) {
    if (kDebugMode) {
      print('Notification tapped: ${message.messageId}');
    }
    
    // Navigate to appropriate screen based on notification data
    _navigateFromNotification(message.data);
  }

  void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!) as Map<String, dynamic>;
        _navigateFromNotification(data);
      } catch (e) {
        if (kDebugMode) {
          print('Error parsing notification payload: $e');
        }
      }
    }
  }

  void _navigateFromNotification(Map<String, dynamic> data) {
    final type = data['type'] as String?;
    final id = data['id'] as String?;

    if (type == null || id == null) return;

    // TODO: Implement navigation logic based on notification type
    switch (type) {
      case AppConstants.notificationTypeLoad:
        // Navigate to load details
        break;
      case AppConstants.notificationTypeBid:
        // Navigate to bid details
        break;
      case AppConstants.notificationTypePayment:
        // Navigate to payment details
        break;
      case AppConstants.notificationTypeTracking:
        // Navigate to tracking screen
        break;
      case AppConstants.notificationTypeSystem:
        // Navigate to notifications screen
        break;
    }
  }

  Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    int id = 0,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'logipool_channel',
      'LogiPool Notifications',
      channelDescription: 'Notifications for LogiPool app',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications?.show(
      id,
      title,
      body,
      details,
      payload: payload,
    );
  }

  Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    int id = 0,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'logipool_scheduled_channel',
      'LogiPool Scheduled Notifications',
      channelDescription: 'Scheduled notifications for LogiPool app',
      importance: Importance.high,
      priority: Priority.high,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications?.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledDate, tz.local),
      details,
      payload: payload,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
    );
  }

  Future<void> cancelNotification(int id) async {
    await _localNotifications?.cancel(id);
  }

  Future<void> cancelAllNotifications() async {
    await _localNotifications?.cancelAll();
  }

  Future<String?> getFCMToken() async {
    if (_firebaseMessaging == null) {
      if (kDebugMode) {
        print('Firebase messaging not available, cannot get FCM token');
      }
      return null;
    }
    return await _storageService.getString('fcm_token');
  }

  Future<void> subscribeToTopic(String topic) async {
    if (_firebaseMessaging == null) {
      if (kDebugMode) {
        print('Firebase messaging not available, cannot subscribe to topic: $topic');
      }
      return;
    }
    try {
      await _firebaseMessaging?.subscribeToTopic(topic);
    } catch (e) {
      if (kDebugMode) {
        print('Error subscribing to topic $topic: $e');
      }
    }
  }

  Future<void> unsubscribeFromTopic(String topic) async {
    if (_firebaseMessaging == null) {
      if (kDebugMode) {
        print('Firebase messaging not available, cannot unsubscribe from topic: $topic');
      }
      return;
    }
    try {
      await _firebaseMessaging?.unsubscribeFromTopic(topic);
    } catch (e) {
      if (kDebugMode) {
        print('Error unsubscribing from topic $topic: $e');
      }
    }
  }

  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _localNotifications?.pendingNotificationRequests() ?? [];
  }

  Future<void> updateBadgeCount(int count) async {
    // iOS only
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      await _localNotifications?.resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin>()?.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      );
    }
  }
}
