package zw.co.kanjan.logipool.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;
import zw.co.kanjan.logipool.dto.company.CompanyMemberDto;
import zw.co.kanjan.logipool.entity.CompanyMember;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CompanyMemberMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "user", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "invitedBy", ignore = true)
    @Mapping(target = "invitedAt", ignore = true)
    @Mapping(target = "joinedAt", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    CompanyMember toEntity(CompanyMemberDto.InviteRequest request);
    
    @Mapping(source = "user.id", target = "userId")
    @Mapping(source = "user.username", target = "username")
    @Mapping(source = "user.email", target = "email")
    @Mapping(source = "user.firstName", target = "firstName")
    @Mapping(source = "user.lastName", target = "lastName")
    @Mapping(source = "user.phoneNumber", target = "phoneNumber")
    @Mapping(source = "invitedBy.username", target = "invitedBy")
    CompanyMemberDto.MemberResponse toResponse(CompanyMember member);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "user", ignore = true)
    @Mapping(target = "invitedBy", ignore = true)
    @Mapping(target = "invitedAt", ignore = true)
    @Mapping(target = "joinedAt", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateMemberFromRequest(CompanyMemberDto.UpdateRequest request, @MappingTarget CompanyMember member);
}
