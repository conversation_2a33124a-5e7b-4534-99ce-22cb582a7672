package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.LoadTracking;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.LoadTrackingRepository;

import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class TrackingMaintenanceService {

    private final LoadTrackingRepository trackingRepository;
    private final LoadRepository loadRepository;
    private final NotificationService notificationService;
    private final RealTimeNotificationService realTimeNotificationService;

    @Value("${app.tracking.stale-threshold-hours:6}")
    private int staleThresholdHours;

    @Value("${app.tracking.cleanup.enabled:true}")
    private boolean cleanupEnabled;

    @Value("${app.tracking.monitoring.enabled:true}")
    private boolean monitoringEnabled;

    /**
     * Check for stale loads that haven't been updated in a while
     * Runs every 30 minutes
     */
    @Scheduled(fixedRate = 1800000) // 30 minutes
    @Transactional
    public void checkStaleLoads() {
        if (!monitoringEnabled) {
            return;
        }

        log.info("Checking for stale loads...");
        
        LocalDateTime cutoffTime = LocalDateTime.now().minusHours(staleThresholdHours);
        List<Load> staleLoads = trackingRepository.findStaleLoads(cutoffTime);
        
        if (!staleLoads.isEmpty()) {
            log.warn("Found {} stale loads that haven't been updated in {} hours", 
                    staleLoads.size(), staleThresholdHours);
            
            for (Load load : staleLoads) {
                handleStaleLoad(load);
            }
        } else {
            log.info("No stale loads found");
        }
    }

    /**
     * Monitor loads for potential delays
     * Runs every hour
     */
    @Scheduled(fixedRate = 3600000) // 1 hour
    @Transactional(readOnly = true)
    public void monitorLoadDelays() {
        if (!monitoringEnabled) {
            return;
        }

        log.info("Monitoring loads for potential delays...");
        
        // Find loads that are past their expected delivery time
        List<Load> potentiallyDelayedLoads = loadRepository.findAll().stream()
                .filter(load -> load.getStatus() == Load.LoadStatus.IN_TRANSIT)
                .filter(load -> load.getDeliveryDate().isBefore(LocalDateTime.now()))
                .filter(load -> !hasDelayedStatus(load))
                .toList();
        
        if (!potentiallyDelayedLoads.isEmpty()) {
            log.warn("Found {} potentially delayed loads", potentiallyDelayedLoads.size());
            
            for (Load load : potentiallyDelayedLoads) {
                notifyPotentialDelay(load);
            }
        }
    }

    /**
     * Generate tracking reports
     * Runs daily at 2 AM
     */
    @Scheduled(cron = "0 0 2 * * *")
    @Transactional(readOnly = true)
    public void generateDailyTrackingReport() {
        if (!monitoringEnabled) {
            return;
        }

        log.info("Generating daily tracking report...");
        
        LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
        LocalDateTime today = LocalDateTime.now();
        
        // Get tracking statistics for yesterday
        List<LoadTracking> yesterdayTracking = trackingRepository.findByTimestampBetween(yesterday, today);
        
        long totalUpdates = yesterdayTracking.size();
        long automatedUpdates = yesterdayTracking.stream()
                .mapToLong(tracking -> tracking.getIsAutomated() ? 1 : 0)
                .sum();
        long manualUpdates = totalUpdates - automatedUpdates;
        
        // Count loads by status
        long deliveredLoads = trackingRepository.countLoadsByStatus(LoadTracking.TrackingStatus.DELIVERED);
        long inTransitLoads = trackingRepository.countLoadsByStatus(LoadTracking.TrackingStatus.IN_TRANSIT_TO_DELIVERY);
        long delayedLoads = trackingRepository.countLoadsByStatus(LoadTracking.TrackingStatus.DELAYED);
        long issueLoads = trackingRepository.countLoadsByStatus(LoadTracking.TrackingStatus.ISSUE_REPORTED);
        
        log.info("Daily Tracking Report:");
        log.info("- Total tracking updates: {}", totalUpdates);
        log.info("- Automated updates: {}", automatedUpdates);
        log.info("- Manual updates: {}", manualUpdates);
        log.info("- Delivered loads: {}", deliveredLoads);
        log.info("- In transit loads: {}", inTransitLoads);
        log.info("- Delayed loads: {}", delayedLoads);
        log.info("- Loads with issues: {}", issueLoads);
        
        // Send report to admins if there are concerning metrics
        if (delayedLoads > 0 || issueLoads > 0) {
            sendTrackingAlertToAdmins(delayedLoads, issueLoads);
        }
    }

    /**
     * Clean up old tracking data
     * Runs weekly on Sunday at 3 AM
     */
    @Scheduled(cron = "0 0 3 * * SUN")
    @Transactional
    public void cleanupOldTrackingData() {
        if (!cleanupEnabled) {
            return;
        }

        log.info("Starting cleanup of old tracking data...");
        
        // Keep tracking data for 6 months
        LocalDateTime cutoffDate = LocalDateTime.now().minusMonths(6);
        
        // Find old tracking entries for delivered loads
        List<LoadTracking> oldTrackingEntries = trackingRepository.findByTimestampBetween(
                LocalDateTime.of(2020, 1, 1, 0, 0), cutoffDate)
                .stream()
                .filter(tracking -> tracking.getLoad().getStatus() == Load.LoadStatus.DELIVERED)
                .toList();
        
        if (!oldTrackingEntries.isEmpty()) {
            log.info("Found {} old tracking entries to archive", oldTrackingEntries.size());
            
            // In a real implementation, you might archive to a separate table or storage
            // For now, we'll just log the cleanup action
            log.info("Archiving {} old tracking entries", oldTrackingEntries.size());
            
            // trackingRepository.deleteAll(oldTrackingEntries);
            // Commented out to prevent accidental data loss in demo
        }
    }

    /**
     * Check for loads with GPS tracking issues
     * Runs every 2 hours
     */
    @Scheduled(fixedRate = 7200000) // 2 hours
    @Transactional(readOnly = true)
    public void checkGpsTrackingIssues() {
        if (!monitoringEnabled) {
            return;
        }

        log.info("Checking for GPS tracking issues...");
        
        // Find loads in transit without recent GPS coordinates
        List<Load> loadsInTransit = trackingRepository.findLoadsInTransit();
        
        for (Load load : loadsInTransit) {
            LoadTracking latestTracking = trackingRepository.findLatestByLoad(load).orElse(null);
            
            if (latestTracking != null) {
                boolean hasGpsIssue = false;
                
                // Check if coordinates are missing
                if (latestTracking.getLatitude() == null || latestTracking.getLongitude() == null) {
                    hasGpsIssue = true;
                }
                
                // Check if last GPS update is too old (more than 2 hours)
                if (latestTracking.getTimestamp().isBefore(LocalDateTime.now().minusHours(2))) {
                    hasGpsIssue = true;
                }
                
                if (hasGpsIssue) {
                    log.warn("GPS tracking issue detected for load: {}", load.getId());
                    notifyGpsTrackingIssue(load, latestTracking);
                }
            }
        }
    }

    /**
     * Update load statuses based on tracking
     * Runs every 15 minutes
     */
    @Scheduled(fixedRate = 900000) // 15 minutes
    @Transactional
    public void updateLoadStatusesFromTracking() {
        if (!monitoringEnabled) {
            return;
        }

        log.debug("Updating load statuses from tracking...");
        
        // Find loads that need status updates
        List<Load> loadsToUpdate = loadRepository.findAll().stream()
                .filter(load -> load.getStatus() == Load.LoadStatus.IN_TRANSIT)
                .toList();
        
        for (Load load : loadsToUpdate) {
            LoadTracking latestTracking = trackingRepository.findLatestByLoad(load).orElse(null);
            
            if (latestTracking != null) {
                Load.LoadStatus newStatus = determineLoadStatusFromTracking(latestTracking.getStatus());
                
                if (newStatus != load.getStatus()) {
                    log.info("Updating load {} status from {} to {} based on tracking", 
                            load.getId(), load.getStatus(), newStatus);
                    
                    load.setStatus(newStatus);
                    loadRepository.save(load);
                    
                    // Send notification about status change
                    notificationService.sendLoadStatusUpdateNotification(load, newStatus.toString());
                }
            }
        }
    }

    // Private helper methods
    
    private void handleStaleLoad(Load load) {
        log.warn("Handling stale load: {} - Last updated: {}", load.getId(), 
                trackingRepository.findLatestByLoad(load)
                        .map(LoadTracking::getTimestamp)
                        .orElse(null));
        
        // Create a system tracking entry to mark as potentially stale
        LoadTracking staleTracking = LoadTracking.builder()
                .load(load)
                .location("System Alert: No recent updates")
                .status(LoadTracking.TrackingStatus.ISSUE_REPORTED)
                .notes("Automatic alert: Load hasn't been updated in " + staleThresholdHours + " hours")
                .isAutomated(true)
                .timestamp(LocalDateTime.now())
                .build();
        
        trackingRepository.save(staleTracking);
        
        // Notify relevant parties
        notificationService.sendLoadStatusUpdateNotification(load, "STALE_TRACKING_DETECTED");
        
        // Send real-time alert
        realTimeNotificationService.sendNotificationToUser(
                load.getClient().getId(),
                buildStaleLoadNotification(load)
        );
        
        if (load.getAssignedCompany() != null) {
            realTimeNotificationService.sendNotificationToUser(
                    load.getAssignedCompany().getUser().getId(),
                    buildStaleLoadNotification(load)
            );
        }
    }

    private boolean hasDelayedStatus(Load load) {
        return trackingRepository.findLatestByLoad(load)
                .map(tracking -> tracking.getStatus() == LoadTracking.TrackingStatus.DELAYED)
                .orElse(false);
    }

    private void notifyPotentialDelay(Load load) {
        log.warn("Potential delay detected for load: {} - Expected delivery: {}", 
                load.getId(), load.getDeliveryDate());
        
        // Send notification to client
        realTimeNotificationService.sendNotificationToUser(
                load.getClient().getId(),
                buildDelayNotification(load)
        );
        
        // Send notification to assigned company
        if (load.getAssignedCompany() != null) {
            realTimeNotificationService.sendNotificationToUser(
                    load.getAssignedCompany().getUser().getId(),
                    buildDelayNotification(load)
            );
        }
    }

    private void notifyGpsTrackingIssue(Load load, LoadTracking latestTracking) {
        log.warn("GPS tracking issue for load: {} - Last GPS update: {}", 
                load.getId(), latestTracking.getTimestamp());
        
        if (load.getAssignedCompany() != null) {
            realTimeNotificationService.sendNotificationToUser(
                    load.getAssignedCompany().getUser().getId(),
                    buildGpsIssueNotification(load)
            );
        }
    }

    private void sendTrackingAlertToAdmins(long delayedLoads, long issueLoads) {
        String message = String.format("Daily tracking alert: %d delayed loads, %d loads with issues", 
                delayedLoads, issueLoads);
        
        realTimeNotificationService.sendRoleBasedNotification(
                "ADMIN",
                "Tracking Alert",
                message,
                "TRACKING_ALERT",
                null
        );
    }

    private Load.LoadStatus determineLoadStatusFromTracking(LoadTracking.TrackingStatus trackingStatus) {
        return switch (trackingStatus) {
            case DELIVERED -> Load.LoadStatus.DELIVERED;
            case IN_TRANSIT_TO_PICKUP, IN_TRANSIT_TO_DELIVERY, 
                 ARRIVED_AT_PICKUP, ARRIVED_AT_DELIVERY,
                 LOADING_IN_PROGRESS, LOADED, UNLOADING_IN_PROGRESS -> Load.LoadStatus.IN_TRANSIT;
            default -> Load.LoadStatus.IN_TRANSIT;
        };
    }

    private zw.co.kanjan.logipool.dto.NotificationDto buildStaleLoadNotification(Load load) {
        return zw.co.kanjan.logipool.dto.NotificationDto.builder()
                .title("Tracking Alert")
                .message(String.format("Load '%s' hasn't been updated in %d hours", 
                        load.getTitle(), staleThresholdHours))
                .type("STALE_TRACKING")
                .timestamp(LocalDateTime.now())
                .build();
    }

    private zw.co.kanjan.logipool.dto.NotificationDto buildDelayNotification(Load load) {
        return zw.co.kanjan.logipool.dto.NotificationDto.builder()
                .title("Potential Delay")
                .message(String.format("Load '%s' may be delayed - expected delivery was %s", 
                        load.getTitle(), load.getDeliveryDate()))
                .type("POTENTIAL_DELAY")
                .timestamp(LocalDateTime.now())
                .build();
    }

    private zw.co.kanjan.logipool.dto.NotificationDto buildGpsIssueNotification(Load load) {
        return zw.co.kanjan.logipool.dto.NotificationDto.builder()
                .title("GPS Tracking Issue")
                .message(String.format("GPS tracking issue detected for load '%s'", load.getTitle()))
                .type("GPS_ISSUE")
                .timestamp(LocalDateTime.now())
                .build();
    }
}
