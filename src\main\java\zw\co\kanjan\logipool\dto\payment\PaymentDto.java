package zw.co.kanjan.logipool.dto.payment;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Builder;
import lombok.Data;
import zw.co.kanjan.logipool.entity.Payment;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class PaymentDto {
    
    @Data
    public static class PaymentResponse {
        private Long id;
        private BigDecimal amount;
        private BigDecimal commissionAmount;
        private BigDecimal netAmount;
        private BigDecimal commissionRate;
        private Payment.PaymentStatus status;
        private Payment.PaymentMethod method;
        private Payment.PaymentType type;
        private String transactionId;
        private String paymentGatewayReference;
        private String description;
        private String notes;
        private Long loadId;
        private String loadTitle;
        private Long bidId;
        private Long payerId;
        private String payerName;
        private Long payeeId;
        private String payeeName;
        private Long invoiceId;
        private String invoiceNumber;
        private LocalDateTime paidAt;
        private LocalDateTime dueDate;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;
    }
    
    @Data
    public static class PaymentCreateRequest {
        @NotNull
        @Positive
        private BigDecimal amount;
        
        @NotNull
        private Payment.PaymentMethod method;
        
        @NotNull
        private Payment.PaymentType type;
        
        private String description;
        private String notes;
        
        @NotNull
        private Long loadId;
        
        private Long bidId;
        
        @NotNull
        private Long payeeId;
        
        private LocalDateTime dueDate;
    }
    
    @Data
    public static class PaymentUpdateRequest {
        private Payment.PaymentStatus status;
        private Payment.PaymentMethod method;
        private String notes;
        private LocalDateTime dueDate;
    }
    
    @Data
    public static class PaymentProcessRequest {
        @NotNull
        private Long paymentId;
        
        @NotNull
        private Payment.PaymentMethod method;
        
        private String paymentToken;
        private String cardNumber;
        private String expiryMonth;
        private String expiryYear;
        private String cvv;
        private String cardHolderName;
        
        // Bank transfer details
        private String bankAccount;
        private String bankCode;
        
        // Mobile money details
        private String mobileNumber;
        private String mobileProvider;
    }
    
    @Data
    @Builder
    public static class PaymentStatusResponse {
        private Long paymentId;
        private Payment.PaymentStatus status;
        private String transactionId;
        private String gatewayReference;
        private String message;
        private LocalDateTime processedAt;
    }
    
    @Data
    @Builder
    public static class PaymentSummary {
        private BigDecimal totalAmount;
        private BigDecimal totalCommission;
        private BigDecimal netAmount;
        private long totalCount;
        private long pendingCount;
        private long completedCount;
        private long failedCount;
    }
}
