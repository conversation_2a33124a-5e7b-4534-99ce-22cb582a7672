package zw.co.kanjan.logipool.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValuePropertyMappingStrategy;
import zw.co.kanjan.logipool.dto.payment.PaymentDto;
import zw.co.kanjan.logipool.entity.Payment;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PaymentMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "commissionAmount", ignore = true)
    @Mapping(target = "netAmount", ignore = true)
    @Mapping(target = "commissionRate", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "transactionId", ignore = true)
    @Mapping(target = "paymentGatewayReference", ignore = true)
    @Mapping(target = "load", ignore = true)
    @Mapping(target = "bid", ignore = true)
    @Mapping(target = "payer", ignore = true)
    @Mapping(target = "payee", ignore = true)
    @Mapping(target = "invoice", ignore = true)
    @Mapping(target = "paidAt", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Payment toEntity(PaymentDto.PaymentCreateRequest request);
    
    @Mapping(source = "load.id", target = "loadId")
    @Mapping(source = "load.title", target = "loadTitle")
    @Mapping(source = "bid.id", target = "bidId")
    @Mapping(source = "payer.id", target = "payerId")
    @Mapping(source = "payer.firstName", target = "payerName")
    @Mapping(source = "payee.id", target = "payeeId")
    @Mapping(source = "payee.firstName", target = "payeeName")
    @Mapping(source = "invoice.id", target = "invoiceId")
    @Mapping(source = "invoice.invoiceNumber", target = "invoiceNumber")
    PaymentDto.PaymentResponse toResponse(Payment payment);
}
