import 'package:flutter/material.dart';
import '../../../shared/widgets/public_footer.dart';

class OrderTrackingScreen extends StatefulWidget {
  const OrderTrackingScreen({super.key});

  @override
  State<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

enum TrackingStep {
  enterDetails,
  verificationSent,
  enterCode,
  showResults
}

class _OrderTrackingScreenState extends State<OrderTrackingScreen> {
  final _trackingController = TextEditingController();
  final _contactController = TextEditingController();
  final _verificationCodeController = TextEditingController();

  Map<String, dynamic>? _trackingResult;
  bool _isLoading = false;

  // Tracking verification states
  TrackingStep _currentStep = TrackingStep.enterDetails;
  String? _verificationToken;
  String? _maskedContact;
  bool _requiresCode = false;
  int _expiryMinutes = 0;
  String _selectedMethod = 'EMAIL';

  @override
  void dispose() {
    _trackingController.dispose();
    _contactController.dispose();
    _verificationCodeController.dispose();
    super.dispose();
  }
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildHeroSection(context),
          _buildCurrentStepContent(context),
          if (_currentStep == TrackingStep.showResults && _trackingResult != null)
            _buildTrackingResult(context),
          _buildHelpSection(context),
          const PublicFooter(),
        ],
      ),
    );
  }

  Widget _buildCurrentStepContent(BuildContext context) {
    switch (_currentStep) {
      case TrackingStep.enterDetails:
        return _buildTrackingForm(context);
      case TrackingStep.verificationSent:
        return _buildVerificationSentForm(context);
      case TrackingStep.enterCode:
        return _buildCodeVerificationForm(context);
      case TrackingStep.showResults:
        return const SizedBox.shrink();
    }
  }

  Widget _buildHeroSection(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background image
          Positioned.fill(
            child: Image.asset(
              'assets/images/pexels-tima-miroshnichenko-6169639.jpg',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
          ),
          // Overlay
          Positioned.fill(
            child: Container(
              color: Theme.of(context).primaryColor.withOpacity(0.7),
            ),
          ),
          // Content
          Positioned.fill(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 64),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.track_changes,
                    size: 64,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Track Your Order',
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 48,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Enter your tracking number to get real-time updates on your shipment',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white70,
                      fontSize: 18,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrackingForm(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Secure Tracking Access',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[800],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Enter your tracking number and contact information to receive secure access to your shipment details.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 24),
              TextField(
                controller: _trackingController,
                decoration: InputDecoration(
                  labelText: 'Tracking Number',
                  hintText: 'Enter your tracking number (e.g., LP123456789)',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.local_shipping),
                  suffixIcon: IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _trackingController.clear();
                      _resetForm();
                    },
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _contactController,
                decoration: InputDecoration(
                  labelText: _selectedMethod == 'EMAIL' ? 'Email Address' : 'Phone Number',
                  hintText: _selectedMethod == 'EMAIL'
                      ? 'Enter your email address'
                      : 'Enter your phone number',
                  border: const OutlineInputBorder(),
                  prefixIcon: Icon(_selectedMethod == 'EMAIL' ? Icons.email : Icons.phone),
                ),
                keyboardType: _selectedMethod == 'EMAIL'
                    ? TextInputType.emailAddress
                    : TextInputType.phone,
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Text(
                    'Verification Method:',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: SegmentedButton<String>(
                      segments: const [
                        ButtonSegment(
                          value: 'EMAIL',
                          label: Text('Email'),
                          icon: Icon(Icons.email, size: 16),
                        ),
                        ButtonSegment(
                          value: 'SMS',
                          label: Text('SMS'),
                          icon: Icon(Icons.sms, size: 16),
                        ),
                      ],
                      selected: {_selectedMethod},
                      onSelectionChanged: (Set<String> selection) {
                        setState(() {
                          _selectedMethod = selection.first;
                          _contactController.clear();
                        });
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.security, color: Colors.blue[700], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'We\'ll send you a secure verification ${_selectedMethod.toLowerCase() == 'email' ? 'link' : 'code'} to access your tracking information.',
                        style: TextStyle(
                          color: Colors.blue[700],
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _requestVerification,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.security),
                  label: Text(_isLoading ? 'Sending...' : 'Request Secure Access'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildVerificationSentForm(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Icon(
                _selectedMethod == 'EMAIL' ? Icons.email : Icons.sms,
                size: 64,
                color: Colors.green[600],
              ),
              const SizedBox(height: 16),
              Text(
                'Verification Sent!',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.green[700],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                _selectedMethod == 'EMAIL'
                    ? 'We\'ve sent a secure tracking link to $_maskedContact'
                    : 'We\'ve sent a verification code to $_maskedContact',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              if (_selectedMethod == 'EMAIL') ...[
                Text(
                  'Check your email and click the secure link to access your tracking information.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Text(
                  'Link expires in $_expiryMinutes hours',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.orange[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ] else ...[
                Text(
                  'Enter the 6-digit code below to access your tracking information.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _currentStep = TrackingStep.enterCode;
                    });
                  },
                  child: const Text('Enter Verification Code'),
                ),
                const SizedBox(height: 16),
                Text(
                  'Code expires in $_expiryMinutes minutes',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.orange[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
              const SizedBox(height: 24),
              TextButton(
                onPressed: _resetForm,
                child: const Text('Start Over'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCodeVerificationForm(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Icon(
                Icons.verified_user,
                size: 64,
                color: Colors.blue[600],
              ),
              const SizedBox(height: 16),
              Text(
                'Enter Verification Code',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Enter the 6-digit code sent to $_maskedContact',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              TextField(
                controller: _verificationCodeController,
                decoration: const InputDecoration(
                  labelText: 'Verification Code',
                  hintText: '000000',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.lock),
                ),
                keyboardType: TextInputType.number,
                maxLength: 6,
                textAlign: TextAlign.center,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 8,
                ),
              ),
              const SizedBox(height: 24),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _verifyCode,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.verified),
                  label: Text(_isLoading ? 'Verifying...' : 'Verify Code'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: _resetForm,
                child: const Text('Start Over'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrackingResult(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.local_shipping,
                    color: Theme.of(context).primaryColor,
                    size: 32,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Tracking #${_trackingResult!['trackingNumber']}',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _trackingResult!['status'],
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: _getStatusColor(_trackingResult!['status']),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _buildOrderDetails(context),
              const SizedBox(height: 24),
              _buildTrackingTimeline(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderDetails(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Order Details',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        _buildDetailRow('From:', _trackingResult!['origin']),
        _buildDetailRow('To:', _trackingResult!['destination']),
        _buildDetailRow('Carrier:', _trackingResult!['carrier']),
        _buildDetailRow('Estimated Delivery:', _trackingResult!['estimatedDelivery']),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrackingTimeline(BuildContext context) {
    final events = _trackingResult!['events'] as List<Map<String, dynamic>>;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Tracking History',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: events.length,
          itemBuilder: (context, index) {
            final event = events[index];
            final isLast = index == events.length - 1;
            
            return Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: event['completed'] 
                            ? Theme.of(context).primaryColor 
                            : Colors.grey,
                        shape: BoxShape.circle,
                      ),
                    ),
                    if (!isLast)
                      Container(
                        width: 2,
                        height: 40,
                        color: Colors.grey.withOpacity(0.3),
                      ),
                  ],
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          event['description'],
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${event['date']} • ${event['location']}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildHelpSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      color: Theme.of(context).primaryColor.withOpacity(0.05),
      child: Column(
        children: [
          Text(
            'Need Help?',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'If you have any questions about your shipment, our support team is here to help.',
            style: Theme.of(context).textTheme.bodyLarge,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ElevatedButton.icon(
                onPressed: () => Navigator.pushNamed(context, '/contact'),
                icon: const Icon(Icons.support_agent),
                label: const Text('Contact Support'),
              ),
              const SizedBox(width: 16),
              OutlinedButton.icon(
                onPressed: () => Navigator.pushNamed(context, '/faq'),
                icon: const Icon(Icons.help),
                label: const Text('FAQ'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _requestVerification() async {
    if (_trackingController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a tracking number')),
      );
      return;
    }

    if (_contactController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Please enter your ${_selectedMethod.toLowerCase() == 'email' ? 'email address' : 'phone number'}')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock response
      setState(() {
        _verificationToken = 'mock_token_${DateTime.now().millisecondsSinceEpoch}';
        _maskedContact = _maskContact(_contactController.text);
        _requiresCode = _selectedMethod == 'SMS';
        _expiryMinutes = _selectedMethod == 'EMAIL' ? 24 : 15;
        _currentStep = TrackingStep.verificationSent;
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Verification ${_selectedMethod.toLowerCase() == 'email' ? 'link' : 'code'} sent successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to send verification: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _verifyCode() async {
    if (_verificationCodeController.text.isEmpty || _verificationCodeController.text.length != 6) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a valid 6-digit verification code')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: Replace with actual API call
      await Future.delayed(const Duration(seconds: 2));

      // Mock verification success
      await _loadTrackingData();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Verification failed: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _loadTrackingData() async {
    try {
      // TODO: Replace with actual API call using verification token
      await Future.delayed(const Duration(seconds: 1));

      // Mock tracking data
      setState(() {
        _trackingResult = {
          'trackingNumber': _trackingController.text,
          'status': 'In Transit',
          'origin': 'Harare, Zimbabwe',
          'destination': 'Bulawayo, Zimbabwe',
          'carrier': 'ABC Logistics',
          'estimatedDelivery': 'Dec 20, 2024',
          'events': [
            {
              'description': 'Package delivered',
              'date': 'Dec 18, 2024 2:30 PM',
              'location': 'Bulawayo',
              'completed': false,
            },
            {
              'description': 'Out for delivery',
              'date': 'Dec 18, 2024 8:00 AM',
              'location': 'Bulawayo',
              'completed': true,
            },
            {
              'description': 'Package arrived at destination facility',
              'date': 'Dec 17, 2024 6:45 PM',
              'location': 'Bulawayo',
              'completed': true,
            },
            {
              'description': 'Package in transit',
              'date': 'Dec 17, 2024 10:30 AM',
              'location': 'Gweru',
              'completed': true,
            },
            {
              'description': 'Package picked up',
              'date': 'Dec 16, 2024 2:15 PM',
              'location': 'Harare',
              'completed': true,
            },
          ],
        };
        _currentStep = TrackingStep.showResults;
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Tracking information loaded successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to load tracking data: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _resetForm() {
    setState(() {
      _currentStep = TrackingStep.enterDetails;
      _trackingController.clear();
      _contactController.clear();
      _verificationCodeController.clear();
      _trackingResult = null;
      _verificationToken = null;
      _maskedContact = null;
      _requiresCode = false;
      _expiryMinutes = 0;
      _selectedMethod = 'EMAIL';
      _isLoading = false;
    });
  }

  String _maskContact(String contact) {
    if (contact.contains('@')) {
      // Email masking
      final parts = contact.split('@');
      final username = parts[0];
      final domain = parts[1];

      if (username.length <= 2) {
        return '**@$domain';
      }

      return '${username.substring(0, 2)}***@$domain';
    } else {
      // Phone number masking
      if (contact.length <= 4) {
        return '***${contact.substring(contact.length - 1)}';
      }

      return '***${contact.substring(contact.length - 4)}';
    }
  }

  // Keep the old method for backward compatibility (deprecated)
  @deprecated
  void _trackOrder() async {
    _requestVerification();
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'delivered':
        return Colors.green;
      case 'in transit':
        return Colors.blue;
      case 'pending':
        return Colors.orange;
      case 'delayed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
