version: '3.8'

services:
  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    container_name: logipool-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard (remove in production)
    environment:
      - TRAEFIK_API_DASHBOARD=true
      - TRAEFIK_API_INSECURE=true
      - TRAEFIK_PROVIDERS_DOCKER=true
      - TRAEFIK_PROVIDERS_DOCKER_EXPOSEDBYDEFAULT=false
      - TRAEFIK_PROVIDERS_DOCKER_NETWORK=traefik-public
      - TRAEFIK_ENTRYPOINTS_WEB_ADDRESS=:80
      - TRAEFIK_ENTRYPOINTS_WEBSECURE_ADDRESS=:443
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_EMAIL=${ACME_EMAIL}
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_STORAGE=/letsencrypt/acme.json
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_HTTPCHALLENGE_ENTRYPOINT=web
      - TRAEFIK_LOG_LEVEL=INFO
      - TRAEFIK_ACCESSLOG=true
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_letsencrypt:/letsencrypt
      - traefik_logs:/var/log/traefik
    networks:
      - traefik-public
      - logipool-network
    labels:
      - "traefik.enable=true"
      # Dashboard
      - "traefik.http.routers.traefik-dashboard.rule=Host(`traefik.${DOMAIN_NAME}`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=websecure"
      - "traefik.http.routers.traefik-dashboard.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"
      # Global redirect to https
      - "traefik.http.routers.http-catchall.rule=hostregexp(`{host:.+}`)"
      - "traefik.http.routers.http-catchall.entrypoints=web"
      - "traefik.http.routers.http-catchall.middlewares=redirect-to-https"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: logipool-postgres-prod
    environment:
      POSTGRES_DB: ${DATABASE_NAME:-logipool_prod}
      POSTGRES_USER: ${DATABASE_USERNAME:-logipool}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - logipool-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USERNAME:-logipool} -d ${DATABASE_NAME:-logipool_prod}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: logipool-redis-prod
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - logipool-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # LogiPool Backend Application
  logipool-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: logipool-backend-prod
    environment:
      # Database Configuration
      DATABASE_URL: *******************************/${DATABASE_NAME:-logipool_prod}
      DATABASE_USERNAME: ${DATABASE_USERNAME:-logipool}
      DATABASE_PASSWORD: ${DATABASE_PASSWORD}
      
      # Redis Configuration
      REDIS_HOST: redis
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD}
      
      # JWT Configuration
      JWT_SECRET: ${JWT_SECRET}
      JWT_EXPIRATION: ${JWT_EXPIRATION:-86400000}
      JWT_REFRESH_EXPIRATION: ${JWT_REFRESH_EXPIRATION:-604800000}
      
      # Application Configuration
      APP_BASE_URL: https://${DOMAIN_NAME}
      PORT: 8080
      
      # File Upload Configuration
      UPLOAD_DIR: /app/uploads
      MAX_FILE_SIZE: ${MAX_FILE_SIZE:-50MB}
      MAX_REQUEST_SIZE: ${MAX_REQUEST_SIZE:-50MB}
      
      # Cloud Storage Configuration
      CLOUD_STORAGE_ENABLED: ${CLOUD_STORAGE_ENABLED:-true}
      CLOUD_STORAGE_PROVIDER: ${CLOUD_STORAGE_PROVIDER:-aws}
      S3_BUCKET_NAME: ${S3_BUCKET_NAME}
      AWS_REGION: ${AWS_REGION:-us-east-1}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
      
      # Email Configuration
      MAIL_HOST: ${MAIL_HOST:-smtp.gmail.com}
      MAIL_PORT: ${MAIL_PORT:-587}
      MAIL_USERNAME: ${MAIL_USERNAME}
      MAIL_PASSWORD: ${MAIL_PASSWORD}
      
      # SMS Configuration
      SMS_PROVIDER: ${SMS_PROVIDER:-twilio}
      SMS_ENABLED: ${SMS_ENABLED:-true}
      TWILIO_ACCOUNT_SID: ${TWILIO_ACCOUNT_SID}
      TWILIO_AUTH_TOKEN: ${TWILIO_AUTH_TOKEN}
      TWILIO_FROM_NUMBER: ${TWILIO_FROM_NUMBER}
      
      # Payment Configuration
      STRIPE_ENABLED: ${STRIPE_ENABLED:-true}
      STRIPE_SECRET_KEY: ${STRIPE_SECRET_KEY}
      STRIPE_PUBLIC_KEY: ${STRIPE_PUBLIC_KEY}
      COMMISSION_RATE: ${COMMISSION_RATE:-0.075}
      
      # Rate Limiting
      RATE_LIMIT_PER_MINUTE: ${RATE_LIMIT_PER_MINUTE:-100}
      RATE_LIMIT_PER_HOUR: ${RATE_LIMIT_PER_HOUR:-5000}
      
      # CORS Configuration
      CORS_ALLOWED_ORIGINS: ${CORS_ALLOWED_ORIGINS:-https://app.${DOMAIN_NAME},https://admin.${DOMAIN_NAME}}
      
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    networks:
      - logipool-network
      - traefik-public
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.logipool-backend.rule=Host(`${DOMAIN_NAME}`)"
      - "traefik.http.routers.logipool-backend.entrypoints=websecure"
      - "traefik.http.routers.logipool-backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.logipool-backend.loadbalancer.server.port=8080"
      # Rate limiting middleware
      - "traefik.http.middlewares.api-ratelimit.ratelimit.burst=100"
      - "traefik.http.middlewares.api-ratelimit.ratelimit.average=50"
      - "traefik.http.routers.logipool-backend.middlewares=api-ratelimit"
      # Security headers
      - "traefik.http.middlewares.security-headers.headers.frameDeny=true"
      - "traefik.http.middlewares.security-headers.headers.contentTypeNosniff=true"
      - "traefik.http.middlewares.security-headers.headers.browserXssFilter=true"
      - "traefik.http.middlewares.security-headers.headers.referrerPolicy=strict-origin-when-cross-origin"
      - "traefik.http.middlewares.security-headers.headers.stsSeconds=31536000"
      - "traefik.http.middlewares.security-headers.headers.stsIncludeSubdomains=true"
      - "traefik.http.routers.logipool-backend.middlewares=api-ratelimit,security-headers"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # pgAdmin for Database Administration (optional)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: logipool-pgadmin-prod
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-admin@${DOMAIN_NAME}}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - logipool-network
      - traefik-public
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.pgadmin.rule=Host(`pgadmin.${DOMAIN_NAME}`)"
      - "traefik.http.routers.pgadmin.entrypoints=websecure"
      - "traefik.http.routers.pgadmin.tls.certresolver=letsencrypt"
      - "traefik.http.services.pgadmin.loadbalancer.server.port=80"



  # Odoo server
  
  odoo:
    container_name: odoo
    image: odoo:17
    ports:
      - "9001:8069"
    links:
      - postgresOdoo:db
    volumes:
      - ../odoo_config/conf:/etc/odoo
      - ../odoo_config/custom-addons:/mnt/custom-addons
      - o17-w-data:/var/lib/odoo
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.odoo.rule=Host(`erp.kanjan.co.zw`)"
      - "traefik.http.routers.odoo.entrypoints=websecure"
      - "traefik.http.routers.odoo.tls.certresolver=letsencrypt"
      - "traefik.http.services.odoo.loadbalancer.server.port=8069"
    depends_on:
      - postgresOdoo
    networks:
      - logipool-network
      - traefik-public
  # Odoo database
  postgresOdoo:
    container_name: postgresOdoo
    image: postgres:15
    ports:
      - 5433:5432
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_PASSWORD=odoo
      - POSTGRES_USER=odoo
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - p16-datawork:/var/lib/postgresql/data/pgdata
    networks:
      - logipool-network
      - traefik-public

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local
  pgadmin_data:
    driver: local
  traefik_letsencrypt:
    driver: local
  traefik_logs:
    driver: local
  p16-datawork:
  o17-w-data:
networks:
  traefik-public:
    name: traefik-public
    driver: bridge
    attachable: true
  logipool-network:
    driver: bridge
