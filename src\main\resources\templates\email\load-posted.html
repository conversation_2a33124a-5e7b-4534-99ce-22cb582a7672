<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Load Available - LogiPool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
            border-radius: 8px 8px 0 0;
        }
        .content {
            background-color: #f8f9fa;
            padding: 30px;
            border-radius: 0 0 8px 8px;
        }
        .load-details {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
        .detail-row {
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
        }
        .label {
            font-weight: bold;
            color: #2c3e50;
        }
        .value {
            color: #555;
        }
        .cta-button {
            background-color: #3498db;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚛 New Load Available</h1>
        <p>A new transportation opportunity is waiting for you!</p>
    </div>
    
    <div class="content">
        <h2>Hello {{transporterName}},</h2>
        
        <p>A new load has been posted that matches your service area. Here are the details:</p>
        
        <div class="load-details">
            <h3>{{loadTitle}}</h3>
            
            <div class="detail-row">
                <span class="label">Pickup Location:</span>
                <span class="value">{{pickupLocation}}</span>
            </div>
            
            <div class="detail-row">
                <span class="label">Delivery Location:</span>
                <span class="value">{{deliveryLocation}}</span>
            </div>
            
            <div class="detail-row">
                <span class="label">Weight:</span>
                <span class="value">{{weight}} kg</span>
            </div>
            
            <div class="detail-row">
                <span class="label">Pickup Date:</span>
                <span class="value">{{pickupDate}}</span>
            </div>
            
            <div class="detail-row">
                <span class="label">Delivery Date:</span>
                <span class="value">{{deliveryDate}}</span>
            </div>
            
            <div class="detail-row">
                <span class="label">Load Type:</span>
                <span class="value">{{loadType}}</span>
            </div>
        </div>
        
        <p><strong>Description:</strong></p>
        <p>{{description}}</p>
        
        <div style="text-align: center;">
            <a href="{{bidUrl}}" class="cta-button">Place Your Bid Now</a>
        </div>
        
        <p>Don't miss this opportunity! Log in to your LogiPool account to submit your competitive bid.</p>
        
        <p>Best regards,<br>
        The LogiPool Team</p>
    </div>
    
    <div class="footer">
        <p>© 2025 LogiPool. All rights reserved.</p>
        <p>You received this email because you're registered as a transporter on LogiPool.</p>
        <p><a href="{{unsubscribeUrl}}">Unsubscribe</a> | <a href="{{supportUrl}}">Contact Support</a></p>
    </div>
</body>
</html>
