# LogiPool Flutter App

LogiPool is a comprehensive logistics marketplace mobile application built with Flutter. It connects logistics service providers with clients needing transportation services through a bid-based platform.

## Features

- **User Authentication & Authorization**
  - JWT-based authentication
  - Role-based access control (Admin, Client, Transporter)
  - Profile management
  - Password reset functionality

- **Load Management**
  - Post and manage load requests
  - View available loads
  - Load tracking and status updates

- **Bidding System**
  - Submit bids on available loads
  - Bid management and evaluation
  - Real-time bid notifications

- **Company Profiles**
  - Company registration and verification
  - Document management
  - Company directory

- **Real-time Tracking**
  - GPS-based location tracking
  - Live delivery updates
  - Route optimization

- **Payment Processing**
  - Secure payment integration
  - Invoice generation
  - Transaction history

- **Notifications**
  - Push notifications
  - In-app notifications
  - Email notifications

- **Admin Dashboard**
  - User management
  - System monitoring
  - Analytics and reporting

## Architecture

The app follows a clean architecture pattern with the following structure:

```
lib/
├── core/                   # Core functionality
│   ├── constants/         # App constants
│   ├── errors/           # Error handling
│   ├── network/          # API client
│   └── utils/            # Utilities and themes
├── features/             # Feature modules
│   ├── auth/            # Authentication
│   ├── loads/           # Load management
│   ├── bids/            # Bidding system
│   ├── companies/       # Company profiles
│   ├── tracking/        # Real-time tracking
│   ├── documents/       # Document management
│   ├── payments/        # Payment processing
│   ├── notifications/   # Notifications
│   └── admin/           # Admin dashboard
├── shared/              # Shared components
│   ├── models/         # Data models
│   ├── services/       # Services
│   └── widgets/        # Reusable widgets
└── main.dart           # App entry point
```

## State Management

The app uses **BLoC (Business Logic Component)** pattern for state management:
- **Provider** for dependency injection
- **flutter_bloc** for state management
- **Equatable** for value equality

## Key Dependencies

### Core Dependencies
- `flutter_bloc` - State management
- `provider` - Dependency injection
- `go_router` - Navigation
- `dio` - HTTP client
- `hive` - Local storage
- `shared_preferences` - Simple key-value storage

### UI Dependencies
- `flutter_svg` - SVG support
- `cached_network_image` - Image caching
- `shimmer` - Loading animations
- `lottie` - Lottie animations

### Functionality Dependencies
- `firebase_core` & `firebase_messaging` - Push notifications
- `flutter_local_notifications` - Local notifications
- `google_maps_flutter` - Maps integration
- `location` & `geolocator` - Location services
- `file_picker` & `image_picker` - File handling
- `web_socket_channel` - Real-time communication
- `jwt_decoder` - JWT token handling
- `permission_handler` - Permissions

## Getting Started

### Prerequisites
- Flutter SDK (>=3.0.0)
- Dart SDK (>=3.0.0)
- Android Studio / VS Code
- Android SDK / Xcode (for iOS)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd logipool_flutter
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code (if needed)**
   ```bash
   flutter packages pub run build_runner build
   ```

4. **Configure Firebase**
   - Add `google-services.json` (Android) to `android/app/`
   - Add `GoogleService-Info.plist` (iOS) to `ios/Runner/`

5. **Configure API endpoints**
   - Update `lib/core/constants/app_constants.dart` with your backend URL

6. **Run the app**
   ```bash
   flutter run
   ```

## Configuration

### API Configuration
Update the base URL in `lib/core/constants/app_constants.dart`:
```dart
static const String baseUrl = 'http://your-backend-url/api';
static const String wsUrl = 'ws://your-backend-url/ws';
```

### Firebase Configuration
1. Create a Firebase project
2. Add Android/iOS apps to the project
3. Download and add configuration files
4. Enable Firebase Messaging

### Maps Configuration
1. Get Google Maps API key
2. Add to `android/app/src/main/AndroidManifest.xml`:
   ```xml
   <meta-data android:name="com.google.android.geo.API_KEY"
              android:value="YOUR_API_KEY"/>
   ```
3. Add to `ios/Runner/AppDelegate.swift`:
   ```swift
   GMSServices.provideAPIKey("YOUR_API_KEY")
   ```

## Development

### Code Generation
The app uses code generation for:
- JSON serialization (`json_serializable`)
- API clients (`retrofit`)
- Local storage models (`hive_generator`)

Run code generation:
```bash
flutter packages pub run build_runner build --delete-conflicting-outputs
```

### Testing
```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage

# Run integration tests
flutter drive --target=test_driver/app.dart
```

### Building

#### Android
```bash
# Debug APK
flutter build apk --debug

# Release APK
flutter build apk --release

# App Bundle (recommended for Play Store)
flutter build appbundle --release
```

#### iOS
```bash
# Debug
flutter build ios --debug

# Release
flutter build ios --release
```

## Project Status

This is the initial project setup with:
- ✅ Project structure and architecture
- ✅ Core dependencies and configuration
- ✅ Authentication system foundation
- ✅ Navigation and routing
- ✅ State management setup
- ✅ API client and error handling
- ✅ Theme and UI foundation
- ✅ Storage services
- ✅ Notification services
- 🚧 Feature implementations (in progress)

## Next Steps

1. Implement user registration and authentication screens
2. Build load management interfaces
3. Develop bidding system UI
4. Integrate maps and tracking functionality
5. Implement payment processing
6. Add comprehensive testing
7. Performance optimization
8. Production deployment

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
