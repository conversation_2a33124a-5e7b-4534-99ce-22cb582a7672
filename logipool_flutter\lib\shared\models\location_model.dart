import 'package:json_annotation/json_annotation.dart';

part 'location_model.g.dart';

@JsonSerializable()
class LocationUpdateRequest {
  final double latitude;
  final double longitude;
  final double? accuracy;
  final double? speed;
  final double? heading;
  final double? altitude;
  final String? address;
  final LocationSource? source;
  final int? loadId;
  final bool isShared;
  final bool isOnDuty;
  final String? deviceId;
  final String? sessionId;
  final String? notes;

  const LocationUpdateRequest({
    required this.latitude,
    required this.longitude,
    this.accuracy,
    this.speed,
    this.heading,
    this.altitude,
    this.address,
    this.source,
    this.loadId,
    this.isShared = true,
    this.isOnDuty = false,
    this.deviceId,
    this.sessionId,
    this.notes,
  });

  factory LocationUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$LocationUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LocationUpdateRequestToJson(this);
}

@JsonSerializable()
class LocationResponse {
  final int id;
  final int driverId;
  final String driverName;
  final int? loadId;
  final String? loadTitle;
  final int? companyId;
  final String? companyName;
  final double latitude;
  final double longitude;
  final double? accuracy;
  final double? speed;
  final double? heading;
  final double? altitude;
  final String? address;
  final LocationSource source;
  final LocationStatus status;
  final bool isShared;
  final bool isOnDuty;
  final String? deviceId;
  final String? sessionId;
  final String? notes;
  final DateTime timestamp;
  final DateTime? expiresAt;

  const LocationResponse({
    required this.id,
    required this.driverId,
    required this.driverName,
    this.loadId,
    this.loadTitle,
    this.companyId,
    this.companyName,
    required this.latitude,
    required this.longitude,
    this.accuracy,
    this.speed,
    this.heading,
    this.altitude,
    this.address,
    required this.source,
    required this.status,
    required this.isShared,
    required this.isOnDuty,
    this.deviceId,
    this.sessionId,
    this.notes,
    required this.timestamp,
    this.expiresAt,
  });

  factory LocationResponse.fromJson(Map<String, dynamic> json) =>
      _$LocationResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LocationResponseToJson(this);

  String get statusDisplayName {
    switch (status) {
      case LocationStatus.active:
        return 'Active';
      case LocationStatus.historical:
        return 'Historical';
      case LocationStatus.estimated:
        return 'Estimated';
      case LocationStatus.offline:
        return 'Offline';
    }
  }

  String get sourceDisplayName {
    switch (source) {
      case LocationSource.gps:
        return 'GPS';
      case LocationSource.network:
        return 'Network';
      case LocationSource.manual:
        return 'Manual';
      case LocationSource.estimated:
        return 'Estimated';
    }
  }

  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    return difference.inMinutes <= 5;
  }
}

@JsonSerializable()
class TrackingSummary {
  final int totalActiveDrivers;
  final int driversOnDuty;
  final int driversWithSharedLocation;
  final int activeTrackedLoads;
  final int recentLocationUpdates;
  final List<LocationResponse> activeDrivers;
  final DateTime lastUpdated;

  const TrackingSummary({
    required this.totalActiveDrivers,
    required this.driversOnDuty,
    required this.driversWithSharedLocation,
    required this.activeTrackedLoads,
    required this.recentLocationUpdates,
    required this.activeDrivers,
    required this.lastUpdated,
  });

  factory TrackingSummary.fromJson(Map<String, dynamic> json) =>
      _$TrackingSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$TrackingSummaryToJson(this);
}

@JsonSerializable()
class LocationPermissionRequest {
  final int driverId;
  final int companyId;
  final int? loadId;
  final PermissionType permissionType;
  final bool allowRealTimeTracking;
  final bool allowHistoricalData;
  final bool allowClientAccess;
  final bool allowEmergencyAccess;
  final DateTime? validFrom;
  final DateTime? validUntil;
  final String? conditions;
  final String? notes;

  const LocationPermissionRequest({
    required this.driverId,
    required this.companyId,
    this.loadId,
    required this.permissionType,
    this.allowRealTimeTracking = false,
    this.allowHistoricalData = false,
    this.allowClientAccess = false,
    this.allowEmergencyAccess = true,
    this.validFrom,
    this.validUntil,
    this.conditions,
    this.notes,
  });

  factory LocationPermissionRequest.fromJson(Map<String, dynamic> json) =>
      _$LocationPermissionRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LocationPermissionRequestToJson(this);
}

@JsonSerializable()
class LocationPermissionResponse {
  final int id;
  final int driverId;
  final String driverName;
  final int companyId;
  final String companyName;
  final int? loadId;
  final String? loadTitle;
  final PermissionType permissionType;
  final PermissionStatus status;
  final bool isActive;
  final bool allowRealTimeTracking;
  final bool allowHistoricalData;
  final bool allowClientAccess;
  final bool allowEmergencyAccess;
  final DateTime? validFrom;
  final DateTime? validUntil;
  final String? conditions;
  final String? notes;
  final String? grantedBy;
  final DateTime? grantedAt;
  final String? revokedBy;
  final DateTime? revokedAt;
  final String? revokeReason;
  final DateTime createdAt;

  const LocationPermissionResponse({
    required this.id,
    required this.driverId,
    required this.driverName,
    required this.companyId,
    required this.companyName,
    this.loadId,
    this.loadTitle,
    required this.permissionType,
    required this.status,
    required this.isActive,
    required this.allowRealTimeTracking,
    required this.allowHistoricalData,
    required this.allowClientAccess,
    required this.allowEmergencyAccess,
    this.validFrom,
    this.validUntil,
    this.conditions,
    this.notes,
    this.grantedBy,
    this.grantedAt,
    this.revokedBy,
    this.revokedAt,
    this.revokeReason,
    required this.createdAt,
  });

  factory LocationPermissionResponse.fromJson(Map<String, dynamic> json) =>
      _$LocationPermissionResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LocationPermissionResponseToJson(this);

  String get statusDisplayName {
    switch (status) {
      case PermissionStatus.pending:
        return 'Pending';
      case PermissionStatus.granted:
        return 'Granted';
      case PermissionStatus.denied:
        return 'Denied';
      case PermissionStatus.revoked:
        return 'Revoked';
      case PermissionStatus.expired:
        return 'Expired';
    }
  }

  bool get isPending => status == PermissionStatus.pending;
  bool get isGranted => status == PermissionStatus.granted && isActive;
}

enum LocationSource {
  @JsonValue('GPS')
  gps,
  @JsonValue('NETWORK')
  network,
  @JsonValue('MANUAL')
  manual,
  @JsonValue('ESTIMATED')
  estimated,
}

enum LocationStatus {
  @JsonValue('ACTIVE')
  active,
  @JsonValue('HISTORICAL')
  historical,
  @JsonValue('ESTIMATED')
  estimated,
  @JsonValue('OFFLINE')
  offline,
}

enum PermissionType {
  @JsonValue('GENERAL')
  general,
  @JsonValue('LOAD_SPECIFIC')
  loadSpecific,
  @JsonValue('EMERGENCY_ONLY')
  emergencyOnly,
  @JsonValue('COMPANY_WIDE')
  companyWide,
}

enum PermissionStatus {
  @JsonValue('PENDING')
  pending,
  @JsonValue('GRANTED')
  granted,
  @JsonValue('DENIED')
  denied,
  @JsonValue('REVOKED')
  revoked,
  @JsonValue('EXPIRED')
  expired,
}
