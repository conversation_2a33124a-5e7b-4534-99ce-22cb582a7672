package zw.co.kanjan.logipool.dto.tracking;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.LoadTracking;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class TrackingDto {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Tracking update request")
    public static class TrackingUpdateRequest {
        
        @NotNull(message = "Load ID is required")
        @Schema(description = "Load ID", example = "1")
        private Long loadId;
        
        @NotBlank(message = "Location is required")
        @Size(max = 200, message = "Location must not exceed 200 characters")
        @Schema(description = "Current location description", example = "Highway A1, 50km from Harare")
        private String location;
        
        @DecimalMin(value = "-90.0", message = "Latitude must be between -90 and 90")
        @DecimalMax(value = "90.0", message = "Latitude must be between -90 and 90")
        @Schema(description = "Latitude coordinate", example = "-17.8252")
        private BigDecimal latitude;
        
        @DecimalMin(value = "-180.0", message = "Longitude must be between -180 and 180")
        @DecimalMax(value = "180.0", message = "Longitude must be between -180 and 180")
        @Schema(description = "Longitude coordinate", example = "31.0335")
        private BigDecimal longitude;
        
        @NotNull(message = "Status is required")
        @Schema(description = "Tracking status", example = "IN_TRANSIT_TO_DELIVERY")
        private LoadTracking.TrackingStatus status;
        
        @Size(max = 1000, message = "Notes must not exceed 1000 characters")
        @Schema(description = "Additional notes", example = "Traffic delay expected")
        private String notes;
        
        @Schema(description = "Whether this is an automated update", example = "false")
        private Boolean isAutomated = false;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Location update request for GPS tracking")
    public static class LocationUpdateRequest {
        
        @NotNull(message = "Load ID is required")
        @Schema(description = "Load ID", example = "1")
        private Long loadId;
        
        @NotNull(message = "Latitude is required")
        @DecimalMin(value = "-90.0", message = "Latitude must be between -90 and 90")
        @DecimalMax(value = "90.0", message = "Latitude must be between -90 and 90")
        @Schema(description = "Latitude coordinate", example = "-17.8252")
        private BigDecimal latitude;
        
        @NotNull(message = "Longitude is required")
        @DecimalMin(value = "-180.0", message = "Longitude must be between -180 and 180")
        @DecimalMax(value = "180.0", message = "Longitude must be between -180 and 180")
        @Schema(description = "Longitude coordinate", example = "31.0335")
        private BigDecimal longitude;
        
        @Size(max = 200, message = "Location must not exceed 200 characters")
        @Schema(description = "Location description", example = "Highway A1")
        private String location;
        
        @Schema(description = "Speed in km/h", example = "80.5")
        private BigDecimal speed;
        
        @Schema(description = "Heading/direction in degrees", example = "45.0")
        private BigDecimal heading;
        
        @Schema(description = "GPS accuracy in meters", example = "5.0")
        private BigDecimal accuracy;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Tracking response")
    public static class TrackingResponse {
        
        @Schema(description = "Tracking ID", example = "1")
        private Long id;
        
        @Schema(description = "Load ID", example = "1")
        private Long loadId;
        
        @Schema(description = "Load title", example = "Electronics shipment to Bulawayo")
        private String loadTitle;
        
        @Schema(description = "Current location", example = "Highway A1, 50km from Harare")
        private String location;
        
        @Schema(description = "Latitude coordinate", example = "-17.8252")
        private BigDecimal latitude;
        
        @Schema(description = "Longitude coordinate", example = "31.0335")
        private BigDecimal longitude;
        
        @Schema(description = "Tracking status", example = "IN_TRANSIT_TO_DELIVERY")
        private LoadTracking.TrackingStatus status;
        
        @Schema(description = "Additional notes", example = "Traffic delay expected")
        private String notes;
        
        @Schema(description = "Whether this is an automated update", example = "false")
        private Boolean isAutomated;
        
        @Schema(description = "User who updated the tracking", example = "1")
        private Long updatedById;
        
        @Schema(description = "Name of user who updated", example = "John Driver")
        private String updatedByName;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "Timestamp of update", example = "2024-01-15 14:30:00")
        private LocalDateTime timestamp;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Load tracking history")
    public static class LoadTrackingHistory {
        
        @Schema(description = "Load ID", example = "1")
        private Long loadId;
        
        @Schema(description = "Load title", example = "Electronics shipment to Bulawayo")
        private String loadTitle;
        
        @Schema(description = "Current status", example = "IN_TRANSIT_TO_DELIVERY")
        private LoadTracking.TrackingStatus currentStatus;
        
        @Schema(description = "Latest location", example = "Highway A1, 50km from Harare")
        private String currentLocation;
        
        @Schema(description = "Latest latitude", example = "-17.8252")
        private BigDecimal currentLatitude;
        
        @Schema(description = "Latest longitude", example = "31.0335")
        private BigDecimal currentLongitude;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "Last update time", example = "2024-01-15 14:30:00")
        private LocalDateTime lastUpdated;
        
        @Schema(description = "Tracking history entries")
        private List<TrackingResponse> trackingHistory;
        
        @Schema(description = "Total tracking entries", example = "5")
        private Integer totalEntries;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Real-time location data")
    public static class RealTimeLocation {
        
        @Schema(description = "Load ID", example = "1")
        private Long loadId;
        
        @Schema(description = "Vehicle/Driver identifier", example = "TRUCK-001")
        private String vehicleId;
        
        @Schema(description = "Latitude coordinate", example = "-17.8252")
        private BigDecimal latitude;
        
        @Schema(description = "Longitude coordinate", example = "31.0335")
        private BigDecimal longitude;
        
        @Schema(description = "Speed in km/h", example = "80.5")
        private BigDecimal speed;
        
        @Schema(description = "Heading/direction in degrees", example = "45.0")
        private BigDecimal heading;
        
        @Schema(description = "GPS accuracy in meters", example = "5.0")
        private BigDecimal accuracy;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "Timestamp", example = "2024-01-15 14:30:00")
        private LocalDateTime timestamp;
        
        @Schema(description = "Current status", example = "IN_TRANSIT_TO_DELIVERY")
        private LoadTracking.TrackingStatus status;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Tracking statistics")
    public static class TrackingStatistics {
        
        @Schema(description = "Total loads being tracked", example = "25")
        private Long totalLoads;
        
        @Schema(description = "Loads in transit to pickup", example = "5")
        private Long loadsInTransitToPickup;
        
        @Schema(description = "Loads in transit to delivery", example = "12")
        private Long loadsInTransitToDelivery;
        
        @Schema(description = "Loads delivered", example = "8")
        private Long loadsDelivered;
        
        @Schema(description = "Loads with delays", example = "2")
        private Long loadsDelayed;
        
        @Schema(description = "Loads with issues", example = "1")
        private Long loadsWithIssues;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "Statistics generated at", example = "2024-01-15 14:30:00")
        private LocalDateTime generatedAt;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Delivery confirmation request")
    public static class DeliveryConfirmationRequest {
        
        @NotNull(message = "Load ID is required")
        @Schema(description = "Load ID", example = "1")
        private Long loadId;
        
        @NotBlank(message = "Delivery location is required")
        @Size(max = 200, message = "Location must not exceed 200 characters")
        @Schema(description = "Delivery location", example = "123 Main Street, Bulawayo")
        private String deliveryLocation;
        
        @Schema(description = "Delivery latitude", example = "-20.1569")
        private BigDecimal deliveryLatitude;
        
        @Schema(description = "Delivery longitude", example = "28.5906")
        private BigDecimal deliveryLongitude;
        
        @Size(max = 1000, message = "Notes must not exceed 1000 characters")
        @Schema(description = "Delivery notes", example = "Package delivered to reception")
        private String deliveryNotes;
        
        @Schema(description = "Recipient name", example = "Jane Smith")
        private String recipientName;
        
        @Schema(description = "Recipient signature (base64)", example = "data:image/png;base64,...")
        private String recipientSignature;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Live tracking response")
    public static class LiveTrackingResponse {

        @Schema(description = "Load ID", example = "1")
        private Long loadId;

        @Schema(description = "Whether live tracking is active", example = "true")
        private Boolean isActive;

        @Schema(description = "Tracking device/user identifier", example = "user123")
        private String trackingDevice;

        @Schema(description = "When live tracking started")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startedAt;

        @Schema(description = "Message about the operation", example = "Live tracking started successfully")
        private String message;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Live tracking status")
    public static class LiveTrackingStatus {

        @Schema(description = "Load ID", example = "1")
        private Long loadId;

        @Schema(description = "Load title", example = "Electronics shipment to Bulawayo")
        private String loadTitle;

        @Schema(description = "Whether live tracking is active", example = "true")
        private Boolean isActive;

        @Schema(description = "Tracking device/user identifier", example = "user123")
        private String trackingDevice;

        @Schema(description = "Tracking user name", example = "John Driver")
        private String trackingUserName;

        @Schema(description = "When live tracking started")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime startedAt;

        @Schema(description = "Last location update time")
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime lastUpdateAt;

        @Schema(description = "Current latitude", example = "-17.8252")
        private BigDecimal currentLatitude;

        @Schema(description = "Current longitude", example = "31.0335")
        private BigDecimal currentLongitude;

        @Schema(description = "Current location description", example = "Highway A1, 50km from Harare")
        private String currentLocation;
    }
}
