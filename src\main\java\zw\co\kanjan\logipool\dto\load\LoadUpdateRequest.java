package zw.co.kanjan.logipool.dto.load;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Data;
import zw.co.kanjan.logipool.entity.Load;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class LoadUpdateRequest {
    
    @NotBlank
    @Size(max = 100)
    private String title;
    
    @Size(max = 1000)
    private String description;
    
    @NotBlank
    @Size(max = 50)
    private String cargoType;
    
    @Positive
    private BigDecimal weight;
    
    @Size(max = 20)
    private String weightUnit;
    
    private BigDecimal volume;
    
    @Size(max = 20)
    private String volumeUnit;
    
    @NotBlank
    @Size(max = 200)
    private String pickupLocation;
    
    @NotBlank
    @Size(max = 200)
    private String deliveryLocation;
    
    @NotNull
    private LocalDateTime pickupDate;
    
    @NotNull
    private LocalDateTime deliveryDate;
    
    private BigDecimal estimatedDistance;
    
    @Size(max = 20)
    private String distanceUnit;
    
    private Load.LoadType loadType;
    
    private Load.PaymentType paymentType;
    
    private BigDecimal paymentRate;
    
    @Size(max = 20)
    private String paymentUnit;
    
    private BigDecimal estimatedValue;
    
    private Load.Priority priority;
    
    private Boolean requiresInsurance;
    
    private Boolean requiresSpecialHandling;
    
    @Size(max = 1000)
    private String specialInstructions;
    
    private LocalDateTime biddingClosesAt;
}
