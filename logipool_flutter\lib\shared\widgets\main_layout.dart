import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';

import '../services/auth_service.dart';
import '../../core/constants/app_constants.dart';

class MainLayout extends StatefulWidget {
  final Widget child;

  const MainLayout({
    super.key,
    required this.child,
  });

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  int _currentIndex = 0;

  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: Icons.local_shipping,
      label: 'Loads',
      route: '/loads',
      roles: [AppConstants.roleClient, AppConstants.roleTransporter, AppConstants.roleAdmin],
    ),
    NavigationItem(
      icon: Icons.gavel,
      label: 'Bids',
      route: '/bids',
      roles: [
        AppConstants.roleTransporter
      ], // Removed CLIENT - clients view bids through load details
    ),
    NavigationItem(
      icon: Icons.business,
      label: 'Companies',
      route: '/companies',
      roles: [
        AppConstants.roleClient
      ], // Only clients see companies in bottom nav - transporters access via header menu
    ),
    NavigationItem(
      icon: Icons.location_on,
      label: 'Tracking',
      route: '/tracking',
      roles: [AppConstants.roleClient, AppConstants.roleTransporter, AppConstants.roleAdmin],
    ),
    NavigationItem(
      icon: Icons.payment,
      label: 'Payments',
      route: '/payments',
      roles: [AppConstants.roleClient, AppConstants.roleTransporter, AppConstants.roleAdmin],
    ),
    NavigationItem(
      icon: Icons.notifications,
      label: 'Notifications',
      route: '/notifications',
      roles: [
        AppConstants.roleClient,
        AppConstants.roleTransporter,
        AppConstants.roleAdmin
      ],
    ),
    NavigationItem(
      icon: Icons.admin_panel_settings,
      label: 'Admin',
      route: '/admin',
      roles: [AppConstants.roleAdmin],
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        final userRole = authService.userRole;
        final filteredItems = _navigationItems
            .where((item) => item.roles.contains(userRole))
            .toList();

        return SelectionArea(
          child: Scaffold(
            body: widget.child,
            bottomNavigationBar: filteredItems.length > 1
                ? BottomNavigationBar(
                    currentIndex: _getCurrentIndex(filteredItems),
                    onTap: (index) {
                      if (index < filteredItems.length) {
                        context.go(filteredItems[index].route);
                      }
                    },
                    type: BottomNavigationBarType.fixed,
                    items: filteredItems
                        .map((item) => BottomNavigationBarItem(
                              icon: Icon(item.icon),
                              label: item.label,
                            ))
                        .toList(),
                  )
                : null,
          ),
        );
      },
    );
  }

  int _getCurrentIndex(List<NavigationItem> items) {
    final currentLocation = GoRouterState.of(context).matchedLocation;
    for (int i = 0; i < items.length; i++) {
      if (currentLocation.startsWith(items[i].route)) {
        return i;
      }
    }
    return 0;
  }
}

class NavigationItem {
  final IconData icon;
  final String label;
  final String route;
  final List<String> roles;

  NavigationItem({
    required this.icon,
    required this.label,
    required this.route,
    required this.roles,
  });
}
