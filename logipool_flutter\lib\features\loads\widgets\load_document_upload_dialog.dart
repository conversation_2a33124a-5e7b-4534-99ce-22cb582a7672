import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../shared/models/document_model.dart';
import '../../../shared/models/load_model.dart';
import '../../../shared/services/cross_platform_file_service.dart';
import '../../documents/bloc/document_bloc.dart';

class LoadDocumentUploadDialog extends StatefulWidget {
  final LoadModel load;

  const LoadDocumentUploadDialog({
    super.key,
    required this.load,
  });

  @override
  State<LoadDocumentUploadDialog> createState() =>
      _LoadDocumentUploadDialogState();
}

class _LoadDocumentUploadDialogState extends State<LoadDocumentUploadDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  DocumentType _selectedType = DocumentType.proofOfDelivery;
  CrossPlatformFile? _selectedFile;
  bool _isUploading = false;

  // Load-specific document types
  final List<DocumentType> _loadDocumentTypes = [
    DocumentType.proofOfDelivery,
    DocumentType.invoice,
    DocumentType.contract,
    DocumentType.waybill,
    DocumentType.customsDeclaration,
    DocumentType.other,
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _pickFile() async {
    try {
      final file = await CrossPlatformFileService.pickLogiPoolDocument();

      if (file != null) {
        setState(() {
          _selectedFile = file;
          // Auto-populate name if empty
          if (_nameController.text.isEmpty) {
            _nameController.text = file.name;
          }
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to pick file: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _uploadDocument() {
    if (!_formKey.currentState!.validate() || _selectedFile == null) {
      return;
    }

    setState(() {
      _isUploading = true;
    });

    final request = DocumentUploadRequest(
      name: _nameController.text.trim(),
      type: _selectedType,
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      loadId: widget.load.id,
    );

    context.read<DocumentBloc>().add(DocumentUploadRequested(
          file: _selectedFile!,
          request: request,
        ));
  }

  String _getDocumentTypeDescription(DocumentType type) {
    switch (type) {
      case DocumentType.proofOfDelivery:
        return 'Upload proof that the load has been delivered successfully';
      case DocumentType.invoice:
        return 'Upload invoice for payment processing';
      case DocumentType.contract:
        return 'Upload signed contract or agreement';
      case DocumentType.waybill:
        return 'Upload waybill or shipping document';
      case DocumentType.customsDeclaration:
        return 'Upload customs declaration for international shipments';
      case DocumentType.other:
        return 'Upload any other relevant document';
      default:
        return 'Upload document related to this load';
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<DocumentBloc, DocumentState>(
      listener: (context, state) {
        if (state is DocumentUploaded) {
          Navigator.of(context).pop(true); // Return true to indicate success
        } else if (state is DocumentError) {
          setState(() {
            _isUploading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: AlertDialog(
        title: Text('Upload Document for Load #${widget.load.id}'),
        content: SizedBox(
          width: double.maxFinite,
          child: Form(
            key: _formKey,
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Document Type Selection
                  Text(
                    'Document Type',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  DropdownButtonFormField<DocumentType>(
                    value: _selectedType,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    items: _loadDocumentTypes.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type.displayName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedType = value;
                        });
                      }
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'Please select a document type';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getDocumentTypeDescription(_selectedType),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                  ),
                  const SizedBox(height: 16),

                  // File Selection
                  Text(
                    'Select File',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 8),
                  InkWell(
                    onTap: _pickFile,
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            _selectedFile != null
                                ? Icons.description
                                : Icons.upload_file,
                            size: 48,
                            color: _selectedFile != null
                                ? Colors.green
                                : Colors.grey,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _selectedFile != null
                                ? _selectedFile!.name
                                : 'Tap to select file',
                            style: Theme.of(context).textTheme.bodyMedium,
                            textAlign: TextAlign.center,
                          ),
                          if (_selectedFile == null) ...[
                            const SizedBox(height: 4),
                            Text(
                              'Supported: PDF, JPG, PNG, DOC, DOCX',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall
                                  ?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Document Name
                  TextFormField(
                    controller: _nameController,
                    decoration: const InputDecoration(
                      labelText: 'Document Name',
                      border: OutlineInputBorder(),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter a document name';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),

                  // Description (Optional)
                  TextFormField(
                    controller: _descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description (Optional)',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                ],
              ),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: _isUploading ? null : () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed:
                _isUploading || _selectedFile == null ? null : _uploadDocument,
            child: _isUploading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Upload'),
          ),
        ],
      ),
    );
  }
}
