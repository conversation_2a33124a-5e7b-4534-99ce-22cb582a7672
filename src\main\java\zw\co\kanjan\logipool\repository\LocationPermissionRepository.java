package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.LocationPermission;
import zw.co.kanjan.logipool.entity.User;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface LocationPermissionRepository extends JpaRepository<LocationPermission, Long> {
    
    // Find permission by driver and company
    Optional<LocationPermission> findByDriverAndCompany(User driver, Company company);
    
    // Find permission by driver, company and load
    Optional<LocationPermission> findByDriverAndCompanyAndLoad(User driver, Company company, Load load);
    
    // Find all permissions for driver
    List<LocationPermission> findByDriverOrderByCreatedAtDesc(User driver);
    
    // Find all permissions for company
    Page<LocationPermission> findByCompanyOrderByCreatedAtDesc(Company company, Pageable pageable);
    
    // Find permissions by status
    List<LocationPermission> findByStatus(LocationPermission.PermissionStatus status);
    
    // Find active permissions for driver
    @Query("SELECT lp FROM LocationPermission lp WHERE lp.driver = :driver AND lp.status = 'GRANTED' AND lp.isActive = true AND " +
           "(lp.validUntil IS NULL OR lp.validUntil > :now) AND (lp.validFrom IS NULL OR lp.validFrom <= :now)")
    List<LocationPermission> findActivePermissionsByDriver(@Param("driver") User driver, @Param("now") LocalDateTime now);
    
    // Find active permissions for company
    @Query("SELECT lp FROM LocationPermission lp WHERE lp.company = :company AND lp.status = 'GRANTED' AND lp.isActive = true AND " +
           "(lp.validUntil IS NULL OR lp.validUntil > :now) AND (lp.validFrom IS NULL OR lp.validFrom <= :now)")
    List<LocationPermission> findActivePermissionsByCompany(@Param("company") Company company, @Param("now") LocalDateTime now);
    
    // Find active permission for driver and company
    @Query("SELECT lp FROM LocationPermission lp WHERE lp.driver = :driver AND lp.company = :company AND lp.status = 'GRANTED' AND lp.isActive = true AND " +
           "(lp.validUntil IS NULL OR lp.validUntil > :now) AND (lp.validFrom IS NULL OR lp.validFrom <= :now)")
    Optional<LocationPermission> findActivePermissionByDriverAndCompany(@Param("driver") User driver, @Param("company") Company company, @Param("now") LocalDateTime now);
    
    // Find load-specific permissions
    @Query("SELECT lp FROM LocationPermission lp WHERE lp.load = :load AND lp.status = 'GRANTED' AND lp.isActive = true AND " +
           "(lp.validUntil IS NULL OR lp.validUntil > :now) AND (lp.validFrom IS NULL OR lp.validFrom <= :now)")
    List<LocationPermission> findActivePermissionsByLoad(@Param("load") Load load, @Param("now") LocalDateTime now);
    
    // Find permissions with real-time tracking enabled
    @Query("SELECT lp FROM LocationPermission lp WHERE lp.company = :company AND lp.allowRealTimeTracking = true AND lp.status = 'GRANTED' AND lp.isActive = true AND " +
           "(lp.validUntil IS NULL OR lp.validUntil > :now) AND (lp.validFrom IS NULL OR lp.validFrom <= :now)")
    List<LocationPermission> findRealTimeTrackingPermissions(@Param("company") Company company, @Param("now") LocalDateTime now);
    
    // Find permissions allowing client access
    @Query("SELECT lp FROM LocationPermission lp WHERE lp.load = :load AND lp.allowClientAccess = true AND lp.status = 'GRANTED' AND lp.isActive = true AND " +
           "(lp.validUntil IS NULL OR lp.validUntil > :now) AND (lp.validFrom IS NULL OR lp.validFrom <= :now)")
    List<LocationPermission> findClientAccessPermissions(@Param("load") Load load, @Param("now") LocalDateTime now);
    
    // Find pending permissions for company
    @Query("SELECT lp FROM LocationPermission lp WHERE lp.company = :company AND lp.status = 'PENDING' ORDER BY lp.createdAt DESC")
    List<LocationPermission> findPendingPermissionsByCompany(@Param("company") Company company);
    
    // Find expired permissions
    @Query("SELECT lp FROM LocationPermission lp WHERE lp.validUntil IS NOT NULL AND lp.validUntil < :now AND lp.status = 'GRANTED'")
    List<LocationPermission> findExpiredPermissions(@Param("now") LocalDateTime now);
    
    // Update expired permissions
    @Modifying
    @Query("UPDATE LocationPermission lp SET lp.status = 'EXPIRED' WHERE lp.validUntil IS NOT NULL AND lp.validUntil < :now AND lp.status = 'GRANTED'")
    int markExpiredPermissions(@Param("now") LocalDateTime now);
    
    // Check if driver has granted permission for company
    @Query("SELECT CASE WHEN COUNT(lp) > 0 THEN true ELSE false END FROM LocationPermission lp WHERE " +
           "lp.driver = :driver AND lp.company = :company AND lp.status = 'GRANTED' AND lp.isActive = true AND " +
           "(lp.validUntil IS NULL OR lp.validUntil > :now) AND (lp.validFrom IS NULL OR lp.validFrom <= :now)")
    boolean hasActivePermission(@Param("driver") User driver, @Param("company") Company company, @Param("now") LocalDateTime now);
    
    // Check if driver has real-time tracking permission for company
    @Query("SELECT CASE WHEN COUNT(lp) > 0 THEN true ELSE false END FROM LocationPermission lp WHERE " +
           "lp.driver = :driver AND lp.company = :company AND lp.allowRealTimeTracking = true AND lp.status = 'GRANTED' AND lp.isActive = true AND " +
           "(lp.validUntil IS NULL OR lp.validUntil > :now) AND (lp.validFrom IS NULL OR lp.validFrom <= :now)")
    boolean hasRealTimeTrackingPermission(@Param("driver") User driver, @Param("company") Company company, @Param("now") LocalDateTime now);
    
    // Find permissions by type
    List<LocationPermission> findByPermissionTypeAndStatus(LocationPermission.PermissionType type, LocationPermission.PermissionStatus status);
    
    // Count permissions by status for company
    @Query("SELECT COUNT(lp) FROM LocationPermission lp WHERE lp.company = :company AND lp.status = :status")
    Long countByCompanyAndStatus(@Param("company") Company company, @Param("status") LocationPermission.PermissionStatus status);
    
    // Find permissions granted by user
    List<LocationPermission> findByGrantedByOrderByGrantedAtDesc(User grantedBy);
    
    // Find permissions revoked by user
    List<LocationPermission> findByRevokedByOrderByRevokedAtDesc(User revokedBy);
}
