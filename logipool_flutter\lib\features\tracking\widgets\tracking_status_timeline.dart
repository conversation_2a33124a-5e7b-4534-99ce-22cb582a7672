import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/tracking_model.dart';

class TrackingStatusTimeline extends StatelessWidget {
  final List<TrackingModel> tracking;
  final VoidCallback? onLoadMore;

  const TrackingStatusTimeline({
    super.key,
    required this.tracking,
    this.onLoadMore,
  });

  @override
  Widget build(BuildContext context) {
    if (tracking.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.timeline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No tracking history available',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: tracking.length + (onLoadMore != null ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == tracking.length) {
          return _buildLoadMoreButton();
        }

        final trackingItem = tracking[index];
        final isLast = index == tracking.length - 1;

        return _buildTimelineItem(
          context,
          trackingItem,
          isLast,
        );
      },
    );
  }

  Widget _buildTimelineItem(
    BuildContext context,
    TrackingModel trackingItem,
    bool isLast,
  ) {
    final theme = Theme.of(context);
    final statusColor = _getStatusColor(trackingItem.status);
    final statusIcon = _getStatusIcon(trackingItem.status);

    return IntrinsicHeight(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Timeline indicator
          Column(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: statusColor,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  statusIcon,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              if (!isLast)
                Expanded(
                  child: Container(
                    width: 2,
                    color: Colors.grey.shade300,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                  ),
                ),
            ],
          ),
          const SizedBox(width: 16),
          // Content
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(bottom: isLast ? 0 : 24),
              child: Card(
                margin: EdgeInsets.zero,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Status and timestamp
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              trackingItem.status.displayName,
                              style: theme.textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: statusColor,
                              ),
                            ),
                          ),
                          Text(
                            DateFormat('MMM dd, HH:mm').format(trackingItem.timestamp),
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      
                      // Location
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 16,
                            color: Colors.grey.shade600,
                          ),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              trackingItem.location,
                              style: theme.textTheme.bodyMedium,
                            ),
                          ),
                        ],
                      ),
                      
                      // Coordinates (if available)
                      if (trackingItem.latitude != null && trackingItem.longitude != null) ...[
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Icon(
                              Icons.gps_fixed,
                              size: 16,
                              color: Colors.grey.shade600,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${trackingItem.latitude!.toStringAsFixed(6)}, ${trackingItem.longitude!.toStringAsFixed(6)}',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: Colors.grey.shade600,
                                fontFamily: 'monospace',
                              ),
                            ),
                          ],
                        ),
                      ],
                      
                      // Notes (if available)
                      if (trackingItem.notes != null && trackingItem.notes!.isNotEmpty) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.grey.shade100,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.note,
                                size: 16,
                                color: Colors.grey.shade600,
                              ),
                              const SizedBox(width: 4),
                              Expanded(
                                child: Text(
                                  trackingItem.notes!,
                                  style: theme.textTheme.bodySmall,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                      
                      // Updated by and automation indicator
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Icon(
                                trackingItem.isAutomated ? Icons.smart_toy : Icons.person,
                                size: 14,
                                color: Colors.grey.shade600,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                trackingItem.isAutomated 
                                    ? 'Automated Update'
                                    : 'Updated by ${trackingItem.updatedByName}',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                          if (trackingItem.isAutomated)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.blue.shade100,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                'AUTO',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: Colors.blue.shade700,
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadMoreButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Center(
        child: ElevatedButton.icon(
          onPressed: onLoadMore,
          icon: const Icon(Icons.expand_more),
          label: const Text('Load More'),
        ),
      ),
    );
  }

  Color _getStatusColor(TrackingStatus status) {
    switch (status) {
      case TrackingStatus.loadPosted:
        return Colors.blue;
      case TrackingStatus.bidAccepted:
        return Colors.green;
      case TrackingStatus.pickupScheduled:
        return Colors.orange;
      case TrackingStatus.inTransitToPickup:
      case TrackingStatus.inTransitToDelivery:
        return Colors.purple;
      case TrackingStatus.arrivedAtPickup:
      case TrackingStatus.arrivedAtDelivery:
        return Colors.amber;
      case TrackingStatus.loadingInProgress:
      case TrackingStatus.unloadingInProgress:
        return Colors.indigo;
      case TrackingStatus.loaded:
        return Colors.teal;
      case TrackingStatus.delivered:
        return Colors.green;
      case TrackingStatus.delayed:
        return Colors.orange;
      case TrackingStatus.issueReported:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(TrackingStatus status) {
    switch (status) {
      case TrackingStatus.loadPosted:
        return Icons.post_add;
      case TrackingStatus.bidAccepted:
        return Icons.handshake;
      case TrackingStatus.pickupScheduled:
        return Icons.schedule;
      case TrackingStatus.inTransitToPickup:
        return Icons.directions_car;
      case TrackingStatus.arrivedAtPickup:
        return Icons.location_on;
      case TrackingStatus.loadingInProgress:
        return Icons.upload;
      case TrackingStatus.loaded:
        return Icons.check_box;
      case TrackingStatus.inTransitToDelivery:
        return Icons.local_shipping;
      case TrackingStatus.arrivedAtDelivery:
        return Icons.place;
      case TrackingStatus.unloadingInProgress:
        return Icons.download;
      case TrackingStatus.delivered:
        return Icons.check_circle;
      case TrackingStatus.delayed:
        return Icons.access_time;
      case TrackingStatus.issueReported:
        return Icons.warning;
    }
  }
}
