package zw.co.kanjan.logipool.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import zw.co.kanjan.logipool.dto.payment.PaymentDto;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.mapper.PaymentMapper;
import zw.co.kanjan.logipool.repository.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class PaymentService {
    
    private final PaymentRepository paymentRepository;
    private final LoadRepository loadRepository;
    private final BidRepository bidRepository;
    private final UserRepository userRepository;
    private final InvoiceRepository invoiceRepository;
    private final CommissionRepository commissionRepository;
    private final TransactionRepository transactionRepository;
    private final PaymentMapper paymentMapper;
    private final NotificationService notificationService;
    
    @Value("${app.payment.commission.rate:0.075}")
    private BigDecimal defaultCommissionRate;
    
    @Value("${app.payment.commission.min:5.00}")
    private BigDecimal minimumCommission;
    
    @Value("${app.payment.commission.max:500.00}")
    private BigDecimal maximumCommission;
    
    public PaymentDto.PaymentResponse createPayment(PaymentDto.PaymentCreateRequest request, String username) {
        User payer = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        Load load = loadRepository.findById(request.getLoadId())
                .orElseThrow(() -> new ResourceNotFoundException("Load not found with id: " + request.getLoadId()));
        
        User payee = userRepository.findById(request.getPayeeId())
                .orElseThrow(() -> new ResourceNotFoundException("Payee not found with id: " + request.getPayeeId()));
        
        // Validate load status
        if (load.getStatus() != Load.LoadStatus.DELIVERED) {
            throw new BusinessException("Payment can only be created for delivered loads");
        }
        
        // Validate user permissions
        if (!load.getClient().equals(payer)) {
            throw new BusinessException("Only the load client can create payments");
        }
        
        Bid bid = null;
        if (request.getBidId() != null) {
            bid = bidRepository.findById(request.getBidId())
                    .orElseThrow(() -> new ResourceNotFoundException("Bid not found with id: " + request.getBidId()));
            
            if (!bid.getLoad().equals(load)) {
                throw new BusinessException("Bid does not belong to the specified load");
            }
            
            if (bid.getStatus() != Bid.BidStatus.ACCEPTED) {
                throw new BusinessException("Payment can only be created for accepted bids");
            }
        }
        
        // Calculate commission
        BigDecimal commissionAmount = calculateCommission(request.getAmount());
        BigDecimal netAmount = request.getAmount().subtract(commissionAmount);
        
        // Create payment
        Payment payment = paymentMapper.toEntity(request);
        payment.setPayer(payer);
        payment.setPayee(payee);
        payment.setLoad(load);
        payment.setBid(bid);
        payment.setCommissionAmount(commissionAmount);
        payment.setNetAmount(netAmount);
        payment.setCommissionRate(defaultCommissionRate);
        payment.setTransactionId(generateTransactionId());
        payment.setStatus(Payment.PaymentStatus.PENDING);
        
        Payment savedPayment = paymentRepository.save(payment);
        
        // Create commission record
        createCommissionRecord(savedPayment);
        
        // Send notifications
        notificationService.sendPaymentCreatedNotification(savedPayment);
        
        log.info("Payment created: {} for load: {} by user: {}", 
                savedPayment.getAmount(), load.getTitle(), username);
        
        return paymentMapper.toResponse(savedPayment);
    }
    
    public PaymentDto.PaymentStatusResponse processPayment(PaymentDto.PaymentProcessRequest request, String username) {
        Payment payment = paymentRepository.findById(request.getPaymentId())
                .orElseThrow(() -> new ResourceNotFoundException("Payment not found with id: " + request.getPaymentId()));
        
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));
        
        // Validate user permissions
        if (!payment.getPayer().equals(user)) {
            throw new BusinessException("Only the payer can process this payment");
        }
        
        if (payment.getStatus() != Payment.PaymentStatus.PENDING) {
            throw new BusinessException("Payment is not in pending status");
        }
        
        try {
            // Update payment status to processing
            payment.setStatus(Payment.PaymentStatus.PROCESSING);
            payment.setMethod(request.getMethod());
            paymentRepository.save(payment);
            
            // Create transaction record
            Transaction transaction = createTransactionRecord(payment, user);
            
            // Process payment based on method
            boolean success = processPaymentByMethod(payment, request, transaction);
            
            if (success) {
                payment.setStatus(Payment.PaymentStatus.COMPLETED);
                payment.setPaidAt(LocalDateTime.now());
                transaction.setStatus(Transaction.TransactionStatus.COMPLETED);
                transaction.setProcessedAt(LocalDateTime.now());
                
                // Update commission status
                updateCommissionStatus(payment);
                
                // Send success notifications
                notificationService.sendPaymentCompletedNotification(payment);
                
                log.info("Payment processed successfully: {} for amount: {}", 
                        payment.getTransactionId(), payment.getAmount());
            } else {
                payment.setStatus(Payment.PaymentStatus.FAILED);
                transaction.setStatus(Transaction.TransactionStatus.FAILED);
                
                // Send failure notifications
                notificationService.sendPaymentFailedNotification(payment);
                
                log.warn("Payment processing failed: {} for amount: {}", 
                        payment.getTransactionId(), payment.getAmount());
            }
            
            paymentRepository.save(payment);
            transactionRepository.save(transaction);
            
            return PaymentDto.PaymentStatusResponse.builder()
                    .paymentId(payment.getId())
                    .status(payment.getStatus())
                    .transactionId(payment.getTransactionId())
                    .gatewayReference(payment.getPaymentGatewayReference())
                    .message(success ? "Payment processed successfully" : "Payment processing failed")
                    .processedAt(payment.getPaidAt())
                    .build();
                    
        } catch (Exception e) {
            payment.setStatus(Payment.PaymentStatus.FAILED);
            paymentRepository.save(payment);
            
            log.error("Error processing payment: {}", payment.getTransactionId(), e);
            throw new BusinessException("Payment processing failed: " + e.getMessage());
        }
    }
    
    private BigDecimal calculateCommission(BigDecimal amount) {
        BigDecimal commission = amount.multiply(defaultCommissionRate)
                .setScale(2, RoundingMode.HALF_UP);
        
        // Apply minimum and maximum commission limits
        if (commission.compareTo(minimumCommission) < 0) {
            commission = minimumCommission;
        } else if (commission.compareTo(maximumCommission) > 0) {
            commission = maximumCommission;
        }
        
        return commission;
    }
    
    private String generateTransactionId() {
        return "TXN-" + System.currentTimeMillis() + "-" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
    
    private void createCommissionRecord(Payment payment) {
        Commission commission = Commission.builder()
                .loadAmount(payment.getAmount())
                .commissionRate(payment.getCommissionRate())
                .commissionAmount(payment.getCommissionAmount())
                .netAmount(payment.getNetAmount())
                .status(Commission.CommissionStatus.PENDING)
                .type(Commission.CommissionType.PLATFORM_COMMISSION)
                .description("Platform commission for load: " + payment.getLoad().getTitle())
                .load(payment.getLoad())
                .bid(payment.getBid())
                .payment(payment)
                .calculatedAt(LocalDateTime.now())
                .build();
        
        commissionRepository.save(commission);
        log.debug("Commission record created for payment: {}", payment.getTransactionId());
    }
    
    private Transaction createTransactionRecord(Payment payment, User user) {
        Transaction transaction = Transaction.builder()
                .transactionId(generateTransactionId())
                .amount(payment.getAmount())
                .type(Transaction.TransactionType.PAYMENT)
                .status(Transaction.TransactionStatus.PENDING)
                .description("Payment for load: " + payment.getLoad().getTitle())
                .payment(payment)
                .fromUser(user)
                .toUser(payment.getPayee())
                .build();
        
        return transactionRepository.save(transaction);
    }
    
    private boolean processPaymentByMethod(Payment payment, PaymentDto.PaymentProcessRequest request, Transaction transaction) {
        // Mock payment processing - in real implementation, integrate with payment gateways
        switch (request.getMethod()) {
            case CREDIT_CARD:
            case DEBIT_CARD:
                return processCardPayment(payment, request, transaction);
            case BANK_TRANSFER:
                return processBankTransfer(payment, request, transaction);
            case MOBILE_MONEY:
                return processMobileMoneyPayment(payment, request, transaction);
            case PAYPAL:
            case STRIPE:
                return processGatewayPayment(payment, request, transaction);
            default:
                return false;
        }
    }
    
    private boolean processCardPayment(Payment payment, PaymentDto.PaymentProcessRequest request, Transaction transaction) {
        // Mock card payment processing
        log.info("Processing card payment for: {}", payment.getTransactionId());
        
        // Validate card details (basic validation)
        if (request.getCardNumber() == null || request.getCardNumber().length() < 13) {
            transaction.setGatewayResponse("Invalid card number");
            return false;
        }
        
        // Simulate payment gateway response
        String gatewayTxnId = "CARD-" + System.currentTimeMillis();
        payment.setPaymentGatewayReference(gatewayTxnId);
        transaction.setGatewayTransactionId(gatewayTxnId);
        transaction.setGatewayResponse("Card payment successful");
        
        return true;
    }
    
    private boolean processBankTransfer(Payment payment, PaymentDto.PaymentProcessRequest request, Transaction transaction) {
        // Mock bank transfer processing
        log.info("Processing bank transfer for: {}", payment.getTransactionId());
        
        String gatewayTxnId = "BANK-" + System.currentTimeMillis();
        payment.setPaymentGatewayReference(gatewayTxnId);
        transaction.setGatewayTransactionId(gatewayTxnId);
        transaction.setGatewayResponse("Bank transfer initiated");
        
        return true;
    }
    
    private boolean processMobileMoneyPayment(Payment payment, PaymentDto.PaymentProcessRequest request, Transaction transaction) {
        // Mock mobile money processing
        log.info("Processing mobile money payment for: {}", payment.getTransactionId());
        
        String gatewayTxnId = "MOBILE-" + System.currentTimeMillis();
        payment.setPaymentGatewayReference(gatewayTxnId);
        transaction.setGatewayTransactionId(gatewayTxnId);
        transaction.setGatewayResponse("Mobile money payment successful");
        
        return true;
    }
    
    private boolean processGatewayPayment(Payment payment, PaymentDto.PaymentProcessRequest request, Transaction transaction) {
        // Mock gateway payment processing
        log.info("Processing gateway payment for: {}", payment.getTransactionId());
        
        String gatewayTxnId = "GATEWAY-" + System.currentTimeMillis();
        payment.setPaymentGatewayReference(gatewayTxnId);
        transaction.setGatewayTransactionId(gatewayTxnId);
        transaction.setGatewayResponse("Gateway payment successful");
        
        return true;
    }
    
    private void updateCommissionStatus(Payment payment) {
        commissionRepository.findByPayment(payment).forEach(commission -> {
            commission.setStatus(Commission.CommissionStatus.COLLECTED);
            commission.setCollectedAt(LocalDateTime.now());
            commissionRepository.save(commission);
        });
    }

    @Transactional(readOnly = true)
    public PaymentDto.PaymentResponse getPaymentById(Long paymentId) {
        Payment payment = paymentRepository.findById(paymentId)
                .orElseThrow(() -> new ResourceNotFoundException("Payment not found with id: " + paymentId));
        return paymentMapper.toResponse(payment);
    }

    @Transactional(readOnly = true)
    public Page<PaymentDto.PaymentResponse> getMyPayments(String username, Pageable pageable) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        Page<Payment> payments = paymentRepository.findByPayerOrderByCreatedAtDesc(user, pageable);
        return payments.map(paymentMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Page<PaymentDto.PaymentResponse> getPaymentsToMe(String username, Pageable pageable) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        Page<Payment> payments = paymentRepository.findByPayeeOrderByCreatedAtDesc(user, pageable);
        return payments.map(paymentMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Page<PaymentDto.PaymentResponse> getAllPayments(Pageable pageable) {
        Page<Payment> payments = paymentRepository.findRecentPayments(pageable);
        return payments.map(paymentMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Page<PaymentDto.PaymentResponse> getPaymentsByStatus(Payment.PaymentStatus status, Pageable pageable) {
        Page<Payment> payments = paymentRepository.findByStatus(status, pageable);
        return payments.map(paymentMapper::toResponse);
    }

    public PaymentDto.PaymentResponse updatePayment(Long paymentId, PaymentDto.PaymentUpdateRequest request, String username) {
        Payment payment = paymentRepository.findById(paymentId)
                .orElseThrow(() -> new ResourceNotFoundException("Payment not found with id: " + paymentId));

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        // Validate user permissions
        if (!payment.getPayer().equals(user) && !hasAdminRole(user)) {
            throw new BusinessException("Insufficient permissions to update this payment");
        }

        // Update allowed fields
        if (request.getStatus() != null && hasAdminRole(user)) {
            payment.setStatus(request.getStatus());
        }

        if (request.getMethod() != null && payment.getStatus() == Payment.PaymentStatus.PENDING) {
            payment.setMethod(request.getMethod());
        }

        if (request.getNotes() != null) {
            payment.setNotes(request.getNotes());
        }

        if (request.getDueDate() != null && payment.getStatus() == Payment.PaymentStatus.PENDING) {
            payment.setDueDate(request.getDueDate());
        }

        Payment savedPayment = paymentRepository.save(payment);

        log.info("Payment updated: {} by user: {}", payment.getTransactionId(), username);
        return paymentMapper.toResponse(savedPayment);
    }

    public PaymentDto.PaymentResponse cancelPayment(Long paymentId, String username) {
        Payment payment = paymentRepository.findById(paymentId)
                .orElseThrow(() -> new ResourceNotFoundException("Payment not found with id: " + paymentId));

        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        // Validate user permissions
        if (!payment.getPayer().equals(user) && !hasAdminRole(user)) {
            throw new BusinessException("Insufficient permissions to cancel this payment");
        }

        if (payment.getStatus() != Payment.PaymentStatus.PENDING) {
            throw new BusinessException("Only pending payments can be cancelled");
        }

        payment.setStatus(Payment.PaymentStatus.CANCELLED);
        Payment savedPayment = paymentRepository.save(payment);

        // Update commission status
        commissionRepository.findByPayment(payment).forEach(commission -> {
            commission.setStatus(Commission.CommissionStatus.PENDING);
            commissionRepository.save(commission);
        });

        // Send notification
        notificationService.sendPaymentCancelledNotification(payment);

        log.info("Payment cancelled: {} by user: {}", payment.getTransactionId(), username);
        return paymentMapper.toResponse(savedPayment);
    }

    @Transactional(readOnly = true)
    public PaymentDto.PaymentSummary getPaymentSummary() {
        BigDecimal totalAmount = paymentRepository.getTotalAmountByStatus(Payment.PaymentStatus.COMPLETED);
        BigDecimal totalCommission = paymentRepository.getTotalCommissionCollected();
        BigDecimal netAmount = totalAmount != null ? totalAmount.subtract(totalCommission != null ? totalCommission : BigDecimal.ZERO) : BigDecimal.ZERO;

        long totalCount = paymentRepository.count();
        long pendingCount = paymentRepository.countByStatus(Payment.PaymentStatus.PENDING);
        long completedCount = paymentRepository.countByStatus(Payment.PaymentStatus.COMPLETED);
        long failedCount = paymentRepository.countByStatus(Payment.PaymentStatus.FAILED);

        return PaymentDto.PaymentSummary.builder()
                .totalAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO)
                .totalCommission(totalCommission != null ? totalCommission : BigDecimal.ZERO)
                .netAmount(netAmount)
                .totalCount(totalCount)
                .pendingCount(pendingCount)
                .completedCount(completedCount)
                .failedCount(failedCount)
                .build();
    }

    @Transactional(readOnly = true)
    public PaymentDto.PaymentSummary getUserPaymentSummary(String username) {
        User user = userRepository.findByUsername(username)
                .orElseThrow(() -> new ResourceNotFoundException("User not found"));

        BigDecimal totalAmount = paymentRepository.getTotalAmountByUserAndStatus(user, Payment.PaymentStatus.COMPLETED);
        BigDecimal totalCommission = paymentRepository.getTotalCommissionByUser(user);
        BigDecimal netAmount = totalAmount != null ? totalAmount.subtract(totalCommission != null ? totalCommission : BigDecimal.ZERO) : BigDecimal.ZERO;

        long totalCount = paymentRepository.countByUser(user);
        long pendingCount = paymentRepository.countByUserAndStatus(user, Payment.PaymentStatus.PENDING);
        long completedCount = paymentRepository.countByUserAndStatus(user, Payment.PaymentStatus.COMPLETED);
        long failedCount = paymentRepository.countByUserAndStatus(user, Payment.PaymentStatus.FAILED);

        return PaymentDto.PaymentSummary.builder()
                .totalAmount(totalAmount != null ? totalAmount : BigDecimal.ZERO)
                .totalCommission(totalCommission != null ? totalCommission : BigDecimal.ZERO)
                .netAmount(netAmount)
                .totalCount(totalCount)
                .pendingCount(pendingCount)
                .completedCount(completedCount)
                .failedCount(failedCount)
                .build();
    }

    private boolean hasAdminRole(User user) {
        return user.getRoles().stream()
                .anyMatch(role -> role.getName() == Role.RoleName.ADMIN);
    }
}
