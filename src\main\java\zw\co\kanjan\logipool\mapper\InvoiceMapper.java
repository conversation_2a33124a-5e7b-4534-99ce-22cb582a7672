package zw.co.kanjan.logipool.mapper;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValuePropertyMappingStrategy;
import zw.co.kanjan.logipool.dto.invoice.InvoiceDto;
import zw.co.kanjan.logipool.entity.Invoice;
import zw.co.kanjan.logipool.entity.InvoiceItem;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface InvoiceMapper {
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "invoiceNumber", ignore = true)
    @Mapping(target = "subtotal", ignore = true)
    @Mapping(target = "taxAmount", ignore = true)
    @Mapping(target = "discountAmount", ignore = true)
    @Mapping(target = "totalAmount", ignore = true)
    @Mapping(target = "commissionAmount", ignore = true)
    @Mapping(target = "netAmount", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "load", ignore = true)
    @Mapping(target = "bid", ignore = true)
    @Mapping(target = "client", ignore = true)
    @Mapping(target = "transporter", ignore = true)
    @Mapping(target = "company", ignore = true)
    @Mapping(target = "items", ignore = true)
    @Mapping(target = "payments", ignore = true)
    @Mapping(target = "sentAt", ignore = true)
    @Mapping(target = "paidAt", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Invoice toEntity(InvoiceDto.InvoiceCreateRequest request);
    
    @Mapping(source = "load.id", target = "loadId")
    @Mapping(source = "load.title", target = "loadTitle")
    @Mapping(source = "bid.id", target = "bidId")
    @Mapping(source = "client.id", target = "clientId")
    @Mapping(source = "client.firstName", target = "clientName")
    @Mapping(source = "transporter.id", target = "transporterId")
    @Mapping(source = "transporter.firstName", target = "transporterName")
    @Mapping(source = "company.id", target = "companyId")
    @Mapping(source = "company.name", target = "companyName")
    InvoiceDto.InvoiceResponse toResponse(Invoice invoice);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "totalPrice", ignore = true)
    @Mapping(target = "invoice", ignore = true)
    InvoiceItem toItemEntity(InvoiceDto.InvoiceItemRequest request);
    
    InvoiceDto.InvoiceItemResponse toItemResponse(InvoiceItem item);
}
