package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "equipment")
@Data
@EqualsAndHashCode(exclude = {"company", "approvedBy", "documents"})
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Equipment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 100)
    private String name;
    
    @NotBlank
    @Size(max = 50)
    private String make;
    
    @NotBlank
    @Size(max = 50)
    private String model;
    
    @Column(name = "manufacture_year")
    private Integer year;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    private EquipmentType type;
    
    @Size(max = 50)
    private String serialNumber;
    
    private BigDecimal capacity;
    
    @Size(max = 20)
    private String capacityUnit;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private EquipmentStatus status = EquipmentStatus.AVAILABLE;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    // Public visibility and approval fields
    @Builder.Default
    private Boolean isPubliclyVisible = false;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private PublicApprovalStatus publicApprovalStatus = PublicApprovalStatus.PENDING;
    
    private LocalDateTime approvedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approved_by")
    private User approvedBy;
    
    // Additional fields for public display
    private BigDecimal dailyRate;
    
    private BigDecimal hourlyRate;
    
    @Size(max = 10)
    private String currency = "USD";
    
    @Column(columnDefinition = "TEXT")
    private String features; // JSON string of features
    
    @Size(max = 500)
    private String imageUrl;
    
    @Builder.Default
    private Boolean isAvailableForRent = false;
    
    // Maintenance and certification fields
    @Builder.Default
    private Boolean hasInsurance = false;
    
    @Builder.Default
    private Boolean hasCertification = false;
    
    @Builder.Default
    private Boolean hasOperatorLicense = false;
    
    private LocalDateTime insuranceExpiryDate;
    
    private LocalDateTime certificationExpiryDate;
    
    private LocalDateTime lastMaintenanceDate;
    
    private LocalDateTime nextMaintenanceDate;
    
    // Location and availability
    @Size(max = 100)
    private String location;
    
    private BigDecimal latitude;
    
    private BigDecimal longitude;
    
    @Builder.Default
    private Boolean requiresOperator = false;
    
    @Builder.Default
    private Boolean operatorIncluded = false;
    
    // Relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
    
    @OneToMany(mappedBy = "equipment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Document> documents = new ArrayList<>();
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    public enum EquipmentType {
        CRANE, FORKLIFT, EXCAVATOR, BULLDOZER, LOADER, TRAILER, 
        CONTAINER, PALLET_JACK, CONVEYOR, GENERATOR, COMPRESSOR, 
        WELDING_EQUIPMENT, LIFTING_EQUIPMENT, MATERIAL_HANDLING, OTHER
    }
    
    public enum EquipmentStatus {
        AVAILABLE, IN_USE, MAINTENANCE, OUT_OF_SERVICE, RESERVED
    }
    
    public enum PublicApprovalStatus {
        PENDING, APPROVED, REJECTED, SUSPENDED
    }
}
