class ApiException implements Exception {
  final String message;
  final int? statusCode;
  final String? errorCode;
  final Map<String, dynamic>? details;

  const ApiException({
    required this.message,
    this.statusCode,
    this.errorCode,
    this.details,
  });

  factory ApiException.fromResponse(dynamic response, int statusCode) {
    if (response is Map<String, dynamic>) {
      return ApiException(
        message: response['message'] ?? 'Unknown error occurred',
        statusCode: statusCode,
        errorCode: response['errorCode']?.toString(),
        details: response['details'],
      );
    }
    
    return ApiException(
      message: response?.toString() ?? 'Unknown error occurred',
      statusCode: statusCode,
    );
  }

  factory ApiException.networkError([String? message]) {
    return ApiException(
      message: message ?? 'Network error occurred',
      statusCode: null,
      errorCode: 'NETWORK_ERROR',
    );
  }

  factory ApiException.timeout([String? message]) {
    return ApiException(
      message: message ?? 'Request timeout',
      statusCode: 408,
      errorCode: 'TIMEOUT',
    );
  }

  factory ApiException.unauthorized([String? message]) {
    return ApiException(
      message: message ?? 'Unauthorized access',
      statusCode: 401,
      errorCode: 'UNAUTHORIZED',
    );
  }

  factory ApiException.forbidden([String? message]) {
    return ApiException(
      message: message ?? 'Access forbidden',
      statusCode: 403,
      errorCode: 'FORBIDDEN',
    );
  }

  factory ApiException.notFound([String? message]) {
    return ApiException(
      message: message ?? 'Resource not found',
      statusCode: 404,
      errorCode: 'NOT_FOUND',
    );
  }

  factory ApiException.serverError([String? message]) {
    return ApiException(
      message: message ?? 'Internal server error',
      statusCode: 500,
      errorCode: 'SERVER_ERROR',
    );
  }

  bool get isNetworkError => errorCode == 'NETWORK_ERROR';
  bool get isTimeout => statusCode == 408;
  bool get isUnauthorized => statusCode == 401;
  bool get isForbidden => statusCode == 403;
  bool get isNotFound => statusCode == 404;
  bool get isServerError => statusCode != null && statusCode! >= 500;

  @override
  String toString() {
    return 'ApiException: $message (Status: $statusCode, Code: $errorCode)';
  }
}
