import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../shared/models/location_model.dart';
import '../../../shared/models/company_model.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../bloc/location_tracking_bloc.dart';
import '../widgets/tracking_summary_card.dart';
import '../widgets/driver_location_card.dart';
import '../widgets/tracking_map_widget.dart';

class TrackingDashboardScreen extends StatefulWidget {
  final CompanyModel company;

  const TrackingDashboardScreen({
    super.key,
    required this.company,
  });

  @override
  State<TrackingDashboardScreen> createState() => _TrackingDashboardScreenState();
}

class _TrackingDashboardScreenState extends State<TrackingDashboardScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    context.read<LocationTrackingBloc>().add(
      LoadCompanyTrackingSummary(widget.company.id),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.company.name} - Tracking'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
            Tab(text: 'Map View', icon: Icon(Icons.map)),
            Tab(text: 'Drivers', icon: Icon(Icons.people)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => context.read<LocationTrackingBloc>().add(
              LoadCompanyTrackingSummary(widget.company.id),
            ),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: BlocConsumer<LocationTrackingBloc, LocationTrackingState>(
        listener: (context, state) {
          if (state is LocationTrackingError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is LocationTrackingLoading) {
            return const LoadingWidget();
          } else if (state is LocationTrackingError) {
            return ErrorDisplayWidget(
              message: state.message,
              onRetry: () => context.read<LocationTrackingBloc>().add(
                LoadCompanyTrackingSummary(widget.company.id),
              ),
            );
          } else if (state is TrackingSummaryLoaded) {
            return TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(state.summary),
                _buildMapTab(state.summary),
                _buildDriversTab(state.summary),
              ],
            );
          }
          return const Center(child: Text('No tracking data available'));
        },
      ),
    );
  }

  Widget _buildOverviewTab(TrackingSummary summary) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TrackingSummaryCard(summary: summary),
          const SizedBox(height: 20),
          
          Text(
            'Recent Activity',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          
          if (summary.activeDrivers.isEmpty)
            const Card(
              child: Padding(
                padding: EdgeInsets.all(20),
                child: Center(
                  child: Column(
                    children: [
                      Icon(Icons.location_off, size: 48, color: Colors.grey),
                      SizedBox(height: 8),
                      Text(
                        'No active drivers',
                        style: TextStyle(fontSize: 16, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
            )
          else
            ...summary.activeDrivers.take(5).map((location) => 
              DriverLocationCard(
                location: location,
                onTap: () => _showDriverDetails(location),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildMapTab(TrackingSummary summary) {
    return TrackingMapWidget(
      locations: summary.activeDrivers,
      onLocationTap: _showDriverDetails,
    );
  }

  Widget _buildDriversTab(TrackingSummary summary) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: summary.activeDrivers.length,
      itemBuilder: (context, index) {
        final location = summary.activeDrivers[index];
        return DriverLocationCard(
          location: location,
          onTap: () => _showDriverDetails(location),
          showFullDetails: true,
        );
      },
    );
  }

  void _showDriverDetails(LocationResponse location) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.9,
        minChildSize: 0.3,
        builder: (context, scrollController) => Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          CircleAvatar(
                            backgroundColor: location.isOnDuty ? Colors.green : Colors.grey,
                            child: Icon(
                              location.isOnDuty ? Icons.work : Icons.work_off,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  location.driverName,
                                  style: Theme.of(context).textTheme.headlineSmall,
                                ),
                                Text(
                                  location.isOnDuty ? 'On Duty' : 'Off Duty',
                                  style: TextStyle(
                                    color: location.isOnDuty ? Colors.green : Colors.grey,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      
                      _buildDetailRow('Location', location.address ?? 'Unknown'),
                      _buildDetailRow('Coordinates', '${location.latitude}, ${location.longitude}'),
                      if (location.speed != null)
                        _buildDetailRow('Speed', '${location.speed!.toStringAsFixed(1)} km/h'),
                      if (location.heading != null)
                        _buildDetailRow('Heading', '${location.heading!.toStringAsFixed(0)}°'),
                      _buildDetailRow('Source', location.sourceDisplayName),
                      _buildDetailRow('Status', location.statusDisplayName),
                      _buildDetailRow('Last Update', _formatDateTime(location.timestamp)),
                      
                      if (location.loadTitle != null) ...[
                        const SizedBox(height: 16),
                        const Divider(),
                        const SizedBox(height: 16),
                        Text(
                          'Current Load',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        _buildDetailRow('Load', location.loadTitle!),
                      ],
                      
                      const SizedBox(height: 20),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () {
                            Navigator.pop(context);
                            // Navigate to driver location history
                            context.read<LocationTrackingBloc>().add(
                              LoadDriverLocationHistory(location.driverId),
                            );
                          },
                          icon: const Icon(Icons.history),
                          label: const Text('View Location History'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}
