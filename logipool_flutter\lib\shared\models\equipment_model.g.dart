// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'equipment_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EquipmentModel _$EquipmentModelFromJson(Map<String, dynamic> json) =>
    EquipmentModel(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      make: json['make'] as String?,
      model: json['model'] as String?,
      year: (json['year'] as num?)?.toInt(),
      type: $enumDecodeNullable(_$EquipmentTypeEnumMap, json['type']),
      serialNumber: json['serialNumber'] as String?,
      capacity: (json['capacity'] as num?)?.toDouble(),
      capacityUnit: json['capacityUnit'] as String?,
      status: $enumDecodeNullable(_$EquipmentStatusEnumMap, json['status']),
      description: json['description'] as String?,
      dailyRate: (json['dailyRate'] as num?)?.toDouble(),
      hourlyRate: (json['hourlyRate'] as num?)?.toDouble(),
      currency: json['currency'] as String?,
      features: (json['features'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      imageUrl: json['imageUrl'] as String?,
      location: json['location'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      requiresOperator: json['requiresOperator'] as bool?,
      operatorIncluded: json['operatorIncluded'] as bool?,
      isAvailableForRent: json['isAvailableForRent'] as bool?,
      isPubliclyVisible: json['isPubliclyVisible'] as bool?,
      publicApprovalStatus: $enumDecodeNullable(
          _$PublicApprovalStatusEnumMap, json['publicApprovalStatus']),
      company: json['company'] == null
          ? null
          : CompanyInfo.fromJson(json['company'] as Map<String, dynamic>),
      hasInsurance: json['hasInsurance'] as bool?,
      hasCertification: json['hasCertification'] as bool?,
      insuranceExpiryDate: json['insuranceExpiryDate'] == null
          ? null
          : DateTime.parse(json['insuranceExpiryDate'] as String),
      certificationExpiryDate: json['certificationExpiryDate'] == null
          ? null
          : DateTime.parse(json['certificationExpiryDate'] as String),
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] == null
          ? null
          : DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$EquipmentModelToJson(EquipmentModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'make': instance.make,
      'model': instance.model,
      'year': instance.year,
      'type': _$EquipmentTypeEnumMap[instance.type],
      'serialNumber': instance.serialNumber,
      'capacity': instance.capacity,
      'capacityUnit': instance.capacityUnit,
      'status': _$EquipmentStatusEnumMap[instance.status],
      'description': instance.description,
      'dailyRate': instance.dailyRate,
      'hourlyRate': instance.hourlyRate,
      'currency': instance.currency,
      'features': instance.features,
      'imageUrl': instance.imageUrl,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'requiresOperator': instance.requiresOperator,
      'operatorIncluded': instance.operatorIncluded,
      'isAvailableForRent': instance.isAvailableForRent,
      'isPubliclyVisible': instance.isPubliclyVisible,
      'publicApprovalStatus':
          _$PublicApprovalStatusEnumMap[instance.publicApprovalStatus],
      'company': instance.company,
      'hasInsurance': instance.hasInsurance,
      'hasCertification': instance.hasCertification,
      'insuranceExpiryDate': instance.insuranceExpiryDate?.toIso8601String(),
      'certificationExpiryDate':
          instance.certificationExpiryDate?.toIso8601String(),
      'createdAt': instance.createdAt?.toIso8601String(),
      'updatedAt': instance.updatedAt?.toIso8601String(),
    };

const _$EquipmentTypeEnumMap = {
  EquipmentType.crane: 'CRANE',
  EquipmentType.forklift: 'FORKLIFT',
  EquipmentType.excavator: 'EXCAVATOR',
  EquipmentType.bulldozer: 'BULLDOZER',
  EquipmentType.loader: 'LOADER',
  EquipmentType.trailer: 'TRAILER',
  EquipmentType.container: 'CONTAINER',
  EquipmentType.palletJack: 'PALLET_JACK',
  EquipmentType.conveyor: 'CONVEYOR',
  EquipmentType.generator: 'GENERATOR',
  EquipmentType.compressor: 'COMPRESSOR',
  EquipmentType.weldingEquipment: 'WELDING_EQUIPMENT',
  EquipmentType.liftingEquipment: 'LIFTING_EQUIPMENT',
  EquipmentType.materialHandling: 'MATERIAL_HANDLING',
  EquipmentType.other: 'OTHER',
};

const _$EquipmentStatusEnumMap = {
  EquipmentStatus.available: 'AVAILABLE',
  EquipmentStatus.inUse: 'IN_USE',
  EquipmentStatus.maintenance: 'MAINTENANCE',
  EquipmentStatus.outOfService: 'OUT_OF_SERVICE',
  EquipmentStatus.reserved: 'RESERVED',
};

const _$PublicApprovalStatusEnumMap = {
  PublicApprovalStatus.pending: 'PENDING',
  PublicApprovalStatus.approved: 'APPROVED',
  PublicApprovalStatus.rejected: 'REJECTED',
  PublicApprovalStatus.suspended: 'SUSPENDED',
};
