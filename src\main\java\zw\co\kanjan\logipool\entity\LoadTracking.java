package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "load_tracking")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LoadTracking {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 200)
    private String location;
    
    private BigDecimal latitude;
    
    private BigDecimal longitude;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    private TrackingStatus status;
    
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    @Builder.Default
    private Boolean isAutomated = false;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "load_id")
    private Load load;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "updated_by")
    private User updatedBy;
    
    @CreationTimestamp
    private LocalDateTime timestamp;
    
    public enum TrackingStatus {
        LOAD_POSTED,
        BID_ACCEPTED,
        PICKUP_SCHEDULED,
        IN_TRANSIT_TO_PICKUP,
        ARRIVED_AT_PICKUP,
        LOADING_IN_PROGRESS,
        LOADED,
        IN_TRANSIT_TO_DELIVERY,
        ARRIVED_AT_DELIVERY,
        UNLOADING_IN_PROGRESS,
        DELIVERED,
        DELAYED,
        ISSUE_REPORTED
    }
}
