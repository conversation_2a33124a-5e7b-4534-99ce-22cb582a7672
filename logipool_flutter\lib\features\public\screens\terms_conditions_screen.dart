import 'package:flutter/material.dart';
import '../../../shared/widgets/public_footer.dart';

class TermsConditionsScreen extends StatelessWidget {
  const TermsConditionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildHeader(context),
          _buildContent(context),
          const PublicFooter(),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.5,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background image
          Positioned.fill(
            child: Image.asset(
              'assets/images/pexels-jplenio-2566850.jpg',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => Container(
                color: Theme.of(context).primaryColor.withOpacity(0.3),
              ),
            ),
          ),
          // Overlay
          Positioned.fill(
            child: Container(
              color: Theme.of(context).primaryColor.withOpacity(0.7),
            ),
          ),
          // Content
          Positioned.fill(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 64),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.description,
                    size: 64,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Terms & Conditions',
                    style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 48,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Last updated: December 2024',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.white70,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSection(
            context,
            '1. Acceptance of Terms',
            'By accessing and using LogiPool services, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.',
          ),
          _buildSection(
            context,
            '2. Service Description',
            'LogiPool is a logistics marketplace platform that connects clients with verified logistics service providers. We facilitate the booking, tracking, and management of freight transportation services.',
          ),
          _buildSection(
            context,
            '3. User Responsibilities',
            '''Users are responsible for:
• Providing accurate and complete information
• Maintaining the confidentiality of account credentials
• Complying with all applicable laws and regulations
• Ensuring cargo descriptions are accurate and complete
• Paying for services as agreed upon''',
          ),
          _buildSection(
            context,
            '4. Service Provider Verification',
            'LogiPool verifies service providers through documentation review, insurance verification, and background checks. However, users should exercise due diligence when selecting service providers.',
          ),
          _buildSection(
            context,
            '5. Payments and Fees',
            '''• LogiPool charges a commission fee on completed transactions
• Payment terms are agreed upon between clients and service providers
• All payments are processed securely through our platform
• Refunds are subject to our refund policy''',
          ),
          _buildSection(
            context,
            '6. Liability and Insurance',
            'Service providers are required to maintain appropriate insurance coverage. LogiPool is not liable for loss, damage, or delay of cargo during transportation. Users should verify insurance coverage with service providers.',
          ),
          _buildSection(
            context,
            '7. Privacy Policy',
            'Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, to understand our practices.',
          ),
          _buildSection(
            context,
            '8. Prohibited Uses',
            '''You may not use our service:
• For any unlawful purpose or to solicit others to unlawful acts
• To violate any international, federal, provincial, or state regulations, rules, laws, or local ordinances
• To transmit, or procure the sending of, any advertising or promotional material without our prior written consent
• To impersonate or attempt to impersonate the Company, a Company employee, another user, or any other person or entity''',
          ),
          _buildSection(
            context,
            '9. Termination',
            'We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever and without limitation.',
          ),
          _buildSection(
            context,
            '10. Changes to Terms',
            'We reserve the right, at our sole discretion, to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect.',
          ),
          _buildSection(
            context,
            '11. Contact Information',
            '''If you have any questions about these Terms and Conditions, please contact us:
• Email: <EMAIL>
• Phone: +263 4 123 4567
• Address: 123 Business District, Harare, Zimbabwe''',
          ),
          const SizedBox(height: 32),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'These terms and conditions are effective as of December 2024 and were last updated on December 15, 2024.',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(BuildContext context, String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).primaryColor,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }
}
