import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class DocumentUploadFab extends StatelessWidget {
  final int? companyId;
  final int? vehicleId;
  final int? loadId;
  final VoidCallback? onUploadSuccess;

  const DocumentUploadFab({
    super.key,
    this.companyId,
    this.vehicleId,
    this.loadId,
    this.onUploadSuccess,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () => _showUploadOptions(context),
      icon: const Icon(Icons.upload_file),
      label: const Text('Upload'),
    );
  }

  void _showUploadOptions(BuildContext context) {
    showModalBottomSheet<void>(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Upload Document',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 24),
            
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('Take Photo'),
              subtitle: const Text('Capture document with camera'),
              onTap: () {
                Navigator.of(context).pop();
                _navigateToUpload(context, 'camera');
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('Choose from Gallery'),
              subtitle: const Text('Select image from gallery'),
              onTap: () {
                Navigator.of(context).pop();
                _navigateToUpload(context, 'gallery');
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.folder),
              title: const Text('Choose File'),
              subtitle: const Text('Select document file'),
              onTap: () {
                Navigator.of(context).pop();
                _navigateToUpload(context, 'file');
              },
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  void _navigateToUpload(BuildContext context, String source) {
    final extra = <String, dynamic>{
      'source': source,
      if (companyId != null) 'companyId': companyId,
      if (vehicleId != null) 'vehicleId': vehicleId,
      if (loadId != null) 'loadId': loadId,
    };

    context.push('/documents/upload', extra: extra).then((_) {
      // Call success callback when returning from upload screen
      onUploadSuccess?.call();
    });
  }
}
