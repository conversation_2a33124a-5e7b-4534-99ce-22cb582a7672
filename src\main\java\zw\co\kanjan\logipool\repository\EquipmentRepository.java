package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Equipment;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface EquipmentRepository extends JpaRepository<Equipment, Long> {
    
    // Find by company
    Page<Equipment> findByCompany(Company company, Pageable pageable);
    
    List<Equipment> findByCompanyAndStatus(Company company, Equipment.EquipmentStatus status);

    Page<Equipment> findByCompanyAndStatus(Company company, Equipment.EquipmentStatus status, Pageable pageable);
    
    // Find by serial number
    Optional<Equipment> findBySerialNumber(String serialNumber);
    
    Boolean existsBySerialNumber(String serialNumber);
    
    // Find by status
    Page<Equipment> findByStatus(Equipment.EquipmentStatus status, Pageable pageable);
    
    // Find by type
    Page<Equipment> findByType(Equipment.EquipmentType type, Pageable pageable);
    
    // Find available equipment
    @Query("SELECT e FROM Equipment e WHERE e.status = 'AVAILABLE' AND e.company.verificationStatus = 'VERIFIED'")
    Page<Equipment> findAvailableVerifiedEquipment(Pageable pageable);
    
    // Find publicly visible and approved equipment
    @Query("SELECT e FROM Equipment e WHERE e.isPubliclyVisible = true AND e.publicApprovalStatus = 'APPROVED' AND e.status = 'AVAILABLE'")
    Page<Equipment> findPubliclyVisibleEquipment(Pageable pageable);
    
    // Find publicly visible equipment by type
    @Query("SELECT e FROM Equipment e WHERE e.isPubliclyVisible = true AND e.publicApprovalStatus = 'APPROVED' AND e.type = :type AND e.status = 'AVAILABLE'")
    Page<Equipment> findPubliclyVisibleEquipmentByType(@Param("type") Equipment.EquipmentType type, Pageable pageable);
    
    // Find publicly visible equipment by location
    @Query("SELECT e FROM Equipment e WHERE e.isPubliclyVisible = true AND e.publicApprovalStatus = 'APPROVED' AND e.location = :location AND e.status = 'AVAILABLE'")
    Page<Equipment> findPubliclyVisibleEquipmentByLocation(@Param("location") String location, Pageable pageable);
    
    // Find equipment by company and type
    List<Equipment> findByCompanyAndType(Company company, Equipment.EquipmentType type);

    Page<Equipment> findByCompanyAndType(Company company, Equipment.EquipmentType type, Pageable pageable);

    // Find equipment by company, status and type
    Page<Equipment> findByCompanyAndStatusAndType(Company company, Equipment.EquipmentStatus status, Equipment.EquipmentType type, Pageable pageable);
    
    // Find equipment with expiring certifications
    @Query("SELECT e FROM Equipment e WHERE e.certificationExpiryDate BETWEEN :now AND :futureDate AND e.hasCertification = true")
    List<Equipment> findEquipmentWithExpiringCertifications(@Param("now") LocalDateTime now, @Param("futureDate") LocalDateTime futureDate);
    
    // Find equipment with expiring insurance
    @Query("SELECT e FROM Equipment e WHERE e.insuranceExpiryDate BETWEEN :now AND :futureDate AND e.hasInsurance = true")
    List<Equipment> findEquipmentWithExpiringInsurance(@Param("now") LocalDateTime now, @Param("futureDate") LocalDateTime futureDate);
    
    // Find equipment without required certifications
    @Query("SELECT e FROM Equipment e WHERE e.hasInsurance = false OR e.hasCertification = false")
    List<Equipment> findEquipmentWithMissingCertifications();
    
    // Find equipment due for maintenance
    @Query("SELECT e FROM Equipment e WHERE e.nextMaintenanceDate <= :date")
    List<Equipment> findEquipmentDueForMaintenance(@Param("date") LocalDateTime date);
    
    // Count equipment by company and status
    @Query("SELECT COUNT(e) FROM Equipment e WHERE e.company = :company AND e.status = :status")
    long countByCompanyAndStatus(@Param("company") Company company, @Param("status") Equipment.EquipmentStatus status);
    
    // Find equipment by make and model
    List<Equipment> findByMakeAndModel(String make, String model);
    
    // Find equipment by year range
    @Query("SELECT e FROM Equipment e WHERE e.year BETWEEN :startYear AND :endYear")
    List<Equipment> findByYearBetween(@Param("startYear") Integer startYear, @Param("endYear") Integer endYear);
    
    // Find equipment pending approval
    @Query("SELECT e FROM Equipment e WHERE e.publicApprovalStatus = 'PENDING' ORDER BY e.createdAt ASC")
    Page<Equipment> findPendingApproval(Pageable pageable);
    
    // Find equipment by approval status
    Page<Equipment> findByPublicApprovalStatus(Equipment.PublicApprovalStatus status, Pageable pageable);
    
    // Search equipment by name or description
    @Query("SELECT e FROM Equipment e WHERE e.isPubliclyVisible = true AND e.publicApprovalStatus = 'APPROVED' AND " +
           "(LOWER(e.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(e.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(e.make) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(e.model) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Equipment> searchPublicEquipment(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    // Find equipment available for rent
    @Query("SELECT e FROM Equipment e WHERE e.isAvailableForRent = true AND e.isPubliclyVisible = true AND e.publicApprovalStatus = 'APPROVED'")
    Page<Equipment> findAvailableForRent(Pageable pageable);
    
    // Find equipment by location within radius (simplified - for more complex geo queries, consider using PostGIS)
    @Query("SELECT e FROM Equipment e WHERE e.isPubliclyVisible = true AND e.publicApprovalStatus = 'APPROVED' AND " +
           "e.latitude IS NOT NULL AND e.longitude IS NOT NULL AND " +
           "SQRT(POWER(e.latitude - :lat, 2) + POWER(e.longitude - :lng, 2)) <= :radius")
    Page<Equipment> findEquipmentNearLocation(@Param("lat") BigDecimal latitude,
                                            @Param("lng") BigDecimal longitude,
                                            @Param("radius") BigDecimal radius,
                                            Pageable pageable);

    // Count publicly visible equipment
    @Query("SELECT COUNT(e) FROM Equipment e WHERE e.isPubliclyVisible = true AND e.publicApprovalStatus = 'APPROVED'")
    long countPubliclyVisibleEquipment();
}
