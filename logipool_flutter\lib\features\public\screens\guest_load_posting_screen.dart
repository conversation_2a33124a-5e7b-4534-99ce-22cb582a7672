import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../core/constants/app_constants.dart';
import '../../../shared/utils/validators.dart';
import '../../../shared/services/guest_load_service.dart';
import '../../../shared/models/guest_load_inquiry_model.dart';

class GuestLoadPostingScreen extends StatefulWidget {
  const GuestLoadPostingScreen({super.key});

  @override
  State<GuestLoadPostingScreen> createState() => _GuestLoadPostingScreenState();
}

class _GuestLoadPostingScreenState extends State<GuestLoadPostingScreen> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();
  
  // Contact Information Controllers
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final _companyController = TextEditingController();
  
  // Load Information Controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _pickupLocationController = TextEditingController();
  final _deliveryLocationController = TextEditingController();
  final _weightController = TextEditingController();
  final _dimensionsController = TextEditingController();
  final _specialInstructionsController = TextEditingController();
  
  // Form State
  String _loadType = 'GENERAL';
  DateTime? _pickupDate;
  DateTime? _deliveryDate;
  bool _requiresSpecialHandling = false;
  bool _isUrgent = false;
  bool _isSubmitting = false;

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _companyController.dispose();
    _titleController.dispose();
    _descriptionController.dispose();
    _pickupLocationController.dispose();
    _deliveryLocationController.dispose();
    _weightController.dispose();
    _dimensionsController.dispose();
    _specialInstructionsController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Post Load Request'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          controller: _scrollController,
          padding: const EdgeInsets.all(16),
          children: [
            // Header Section
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange.shade50, Colors.orange.shade100],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.local_shipping,
                    size: 48,
                    color: Colors.orange.shade700,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    'Tell us about your shipping needs',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade800,
                        ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Fill out the form below and we\'ll connect you with verified logistics companies for competitive quotes.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.orange.shade700,
                        ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            
            // Contact Information Section
            _buildSectionCard(
              'Your Contact Information',
              Icons.person,
              [
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Full Name *',
                          prefixIcon: Icon(Icons.person_outline),
                        ),
                        validator: Validators.required,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _companyController,
                        decoration: const InputDecoration(
                          labelText: 'Company (Optional)',
                          prefixIcon: Icon(Icons.business_outlined),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _emailController,
                        decoration: const InputDecoration(
                          labelText: 'Email Address *',
                          prefixIcon: Icon(Icons.email_outlined),
                        ),
                        validator: Validators.email,
                        keyboardType: TextInputType.emailAddress,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _phoneController,
                        decoration: const InputDecoration(
                          labelText: 'Phone Number *',
                          prefixIcon: Icon(Icons.phone_outlined),
                        ),
                        validator: Validators.required,
                        keyboardType: TextInputType.phone,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Load Information Section
            _buildSectionCard(
              'Load Information',
              Icons.inventory,
              [
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'Load Title *',
                    hintText: 'e.g., Electronics shipment to Harare',
                    prefixIcon: Icon(Icons.title),
                  ),
                  validator: Validators.required,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Description *',
                    hintText: 'Describe what you need to ship',
                    prefixIcon: Icon(Icons.description),
                  ),
                  validator: Validators.required,
                  maxLines: 3,
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _loadType,
                  decoration: const InputDecoration(
                    labelText: 'Load Type *',
                    prefixIcon: Icon(Icons.category),
                  ),
                  items: const [
                    DropdownMenuItem(value: 'GENERAL', child: Text('General Cargo')),
                    DropdownMenuItem(value: 'FRAGILE', child: Text('Fragile Items')),
                    DropdownMenuItem(value: 'HAZARDOUS', child: Text('Hazardous Materials')),
                    DropdownMenuItem(value: 'PERISHABLE', child: Text('Perishable Goods')),
                    DropdownMenuItem(value: 'OVERSIZED', child: Text('Oversized/Heavy')),
                    DropdownMenuItem(value: 'LIQUID', child: Text('Liquid/Bulk')),
                    DropdownMenuItem(value: 'VEHICLE', child: Text('Vehicle Transport')),
                    DropdownMenuItem(value: 'CONTAINER', child: Text('Container')),
                  ],
                  onChanged: (value) => setState(() => _loadType = value!),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _weightController,
                        decoration: const InputDecoration(
                          labelText: 'Weight (kg) *',
                          prefixIcon: Icon(Icons.scale),
                        ),
                        validator: Validators.required,
                        keyboardType: TextInputType.number,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: TextFormField(
                        controller: _dimensionsController,
                        decoration: const InputDecoration(
                          labelText: 'Dimensions (L×W×H)',
                          hintText: 'e.g., 2m×1m×1m',
                          prefixIcon: Icon(Icons.straighten),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Location and Timing Section
            _buildSectionCard(
              'Pickup & Delivery',
              Icons.location_on,
              [
                TextFormField(
                  controller: _pickupLocationController,
                  decoration: const InputDecoration(
                    labelText: 'Pickup Location *',
                    hintText: 'Full address or area',
                    prefixIcon: Icon(Icons.my_location),
                  ),
                  validator: Validators.required,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _deliveryLocationController,
                  decoration: const InputDecoration(
                    labelText: 'Delivery Location *',
                    hintText: 'Full address or area',
                    prefixIcon: Icon(Icons.location_on),
                  ),
                  validator: Validators.required,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectDate(context, true),
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Pickup Date *',
                            prefixIcon: Icon(Icons.calendar_today),
                          ),
                          child: Text(
                            _pickupDate != null
                                ? '${_pickupDate!.day}/${_pickupDate!.month}/${_pickupDate!.year}'
                                : 'Select date',
                            style: TextStyle(
                              color: _pickupDate != null ? null : Colors.grey,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectDate(context, false),
                        child: InputDecorator(
                          decoration: const InputDecoration(
                            labelText: 'Delivery Date',
                            prefixIcon: Icon(Icons.event),
                          ),
                          child: Text(
                            _deliveryDate != null
                                ? '${_deliveryDate!.day}/${_deliveryDate!.month}/${_deliveryDate!.year}'
                                : 'Select date (optional)',
                            style: TextStyle(
                              color: _deliveryDate != null ? null : Colors.grey,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 24),
            
            // Additional Options Section
            _buildSectionCard(
              'Additional Options',
              Icons.settings,
              [
                CheckboxListTile(
                  title: const Text('Requires Special Handling'),
                  subtitle: const Text('Fragile, temperature-controlled, etc.'),
                  value: _requiresSpecialHandling,
                  onChanged: (value) => setState(() => _requiresSpecialHandling = value!),
                  controlAffinity: ListTileControlAffinity.leading,
                ),
                CheckboxListTile(
                  title: const Text('Urgent Delivery'),
                  subtitle: const Text('Priority handling required'),
                  value: _isUrgent,
                  onChanged: (value) => setState(() => _isUrgent = value!),
                  controlAffinity: ListTileControlAffinity.leading,
                ),
                if (_requiresSpecialHandling) ...[
                  const SizedBox(height: 16),
                  TextFormField(
                    controller: _specialInstructionsController,
                    decoration: const InputDecoration(
                      labelText: 'Special Instructions',
                      hintText: 'Describe special handling requirements',
                      prefixIcon: Icon(Icons.note),
                    ),
                    maxLines: 3,
                  ),
                ],
              ],
            ),
            const SizedBox(height: 32),
            
            // Submit Button
            ElevatedButton.icon(
              onPressed: _isSubmitting ? null : _submitForm,
              icon: _isSubmitting
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.send),
              label: Text(_isSubmitting ? 'Submitting...' : 'Submit Load Request'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // Alternative Contact Options
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                children: [
                  Text(
                    'Prefer to contact us directly?',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    alignment: WrapAlignment.center,
                    spacing: 12,
                    runSpacing: 8,
                    children: [
                      _buildContactButton(
                        'WhatsApp',
                        Icons.chat,
                        Colors.green,
                        () => _launchWhatsApp(),
                      ),
                      _buildContactButton(
                        'Call',
                        Icons.phone,
                        Colors.blue,
                        () => _launchPhone(),
                      ),
                      _buildContactButton(
                        'Email',
                        Icons.email,
                        Colors.orange,
                        () => _launchEmail(),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 100), // Space for floating elements
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard(String title, IconData icon, List<Widget> children) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Theme.of(context).primaryColor),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildContactButton(String label, IconData icon, Color color, VoidCallback onTap) {
    return OutlinedButton.icon(
      onPressed: onTap,
      icon: Icon(icon, size: 18),
      label: Text(label),
      style: OutlinedButton.styleFrom(
        foregroundColor: color,
        side: BorderSide(color: color),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isPickupDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isPickupDate) {
          _pickupDate = picked;
        } else {
          _deliveryDate = picked;
        }
      });
    }
  }

  void _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_pickupDate == null) {
      _showError('Please select a pickup date');
      return;
    }

    setState(() => _isSubmitting = true);

    try {
      // Create the inquiry model
      final inquiry = GuestLoadInquiryModel(
        name: _nameController.text.trim(),
        email: _emailController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        company: _companyController.text.trim().isNotEmpty ? _companyController.text.trim() : null,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        loadType: _loadType,
        weight: double.parse(_weightController.text.trim()),
        dimensions: _dimensionsController.text.trim().isNotEmpty ? _dimensionsController.text.trim() : null,
        pickupLocation: _pickupLocationController.text.trim(),
        deliveryLocation: _deliveryLocationController.text.trim(),
        pickupDate: _pickupDate!,
        deliveryDate: _deliveryDate,
        requiresSpecialHandling: _requiresSpecialHandling,
        isUrgent: _isUrgent,
        specialInstructions: _specialInstructionsController.text.trim().isNotEmpty
            ? _specialInstructionsController.text.trim()
            : null,
      );

      // Submit the inquiry
      final response = await GuestLoadService.instance.submitLoadInquiry(inquiry);

      if (mounted) {
        _showSuccessDialog(response['referenceNumber'] ?? '#${response['inquiryId']}');
      }
    } catch (e) {
      if (mounted) {
        _showError(e.toString().replaceFirst('Exception: ', ''));
      }
    } finally {
      if (mounted) {
        setState(() => _isSubmitting = false);
      }
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showSuccessDialog(String referenceNumber) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        icon: const Icon(
          Icons.check_circle,
          color: Colors.green,
          size: 48,
        ),
        title: const Text('Request Submitted!'),
        content: Text(
          'Thank you for your load request. We\'ve received your information and will connect you with verified logistics companies within 24 hours.\n\nReference Number: $referenceNumber\n\nYou\'ll receive quotes via email and phone.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.go('/');
            },
            child: const Text('Back to Home'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _clearForm();
            },
            child: const Text('Submit Another'),
          ),
        ],
      ),
    );
  }

  void _clearForm() {
    _formKey.currentState?.reset();
    _nameController.clear();
    _emailController.clear();
    _phoneController.clear();
    _companyController.clear();
    _titleController.clear();
    _descriptionController.clear();
    _pickupLocationController.clear();
    _deliveryLocationController.clear();
    _weightController.clear();
    _dimensionsController.clear();
    _specialInstructionsController.clear();

    setState(() {
      _loadType = 'GENERAL';
      _pickupDate = null;
      _deliveryDate = null;
      _requiresSpecialHandling = false;
      _isUrgent = false;
    });
  }

  Future<void> _launchWhatsApp() async {
    final phoneNumber = AppConstants.whatsAppNumberForDialing;
    final message = 'Hello! I just submitted a load request on LogiPool. '
        'Load: ${_titleController.text.isNotEmpty ? _titleController.text : "Load request"} '
        'from ${_pickupLocationController.text.isNotEmpty ? _pickupLocationController.text : "pickup location"} '
        'to ${_deliveryLocationController.text.isNotEmpty ? _deliveryLocationController.text : "delivery location"}. '
        'Can you help me with this shipment?';

    final url = 'https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}';

    try {
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      _showError('Could not open WhatsApp');
    }
  }

  Future<void> _launchPhone() async {
    final phoneNumber = 'tel:${AppConstants.phoneNumberForDialing}';
    try {
      if (await canLaunchUrl(Uri.parse(phoneNumber))) {
        await launchUrl(Uri.parse(phoneNumber));
      }
    } catch (e) {
      _showError('Could not make phone call');
    }
  }

  Future<void> _launchEmail() async {
    final subject = 'Load Request: ${_titleController.text.isNotEmpty ? _titleController.text : "New Load"}';
    final body = 'Hello,\n\nI have submitted a load request with the following details:\n\n'
        'Load: ${_titleController.text}\n'
        'From: ${_pickupLocationController.text}\n'
        'To: ${_deliveryLocationController.text}\n'
        'Weight: ${_weightController.text} kg\n'
        'Pickup Date: ${_pickupDate != null ? "${_pickupDate!.day}/${_pickupDate!.month}/${_pickupDate!.year}" : "Not specified"}\n\n'
        'Please contact me for more details.\n\n'
        'Best regards,\n${_nameController.text}';

    final email = 'mailto:${AppConstants.companyEmail}?subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}';

    try {
      if (await canLaunchUrl(Uri.parse(email))) {
        await launchUrl(Uri.parse(email));
      }
    } catch (e) {
      _showError('Could not open email client');
    }
  }
}
