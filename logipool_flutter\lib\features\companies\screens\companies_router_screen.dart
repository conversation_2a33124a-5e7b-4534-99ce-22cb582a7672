import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../core/constants/app_constants.dart';
import '../../../shared/widgets/loading_widget.dart';
import 'company_profile_screen.dart';
import 'company_directory_screen.dart';

/// Router screen that directs users to the appropriate companies view
/// based on their role:
/// - Clients: Company Directory (to browse logistics providers)
/// - Transporters: Company Profile (to manage their own company)
/// - Admins: Company Directory (to manage all companies)
class CompaniesRouterScreen extends StatelessWidget {
  const CompaniesRouterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<UserModel?>(
      future: context.read<AuthService>().getCurrentUser(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: LoadingWidget(),
          );
        }

        if (snapshot.hasError || snapshot.data == null) {
          return const Scaffold(
            body: Center(
              child: Text('Error loading user information'),
            ),
          );
        }

        final user = snapshot.data!;
        
        // Route based on user role
        switch (user.role) {
          case AppConstants.roleClient:
          case AppConstants.roleAdmin:
            // Clients and admins see the company directory
            return const CompanyDirectoryScreen();
          case AppConstants.roleTransporter:
          default:
            // Transporters see their company profile
            return const CompanyProfileScreen();
        }
      },
    );
  }
}
