# LogiPool Production Environment Configuration
# Copy this file to .env and fill in the actual values

# Database Configuration
DATABASE_NAME=logipool_prod
DATABASE_USERNAME=logipool
DATABASE_PASSWORD=your_secure_database_password
DATABASE_PORT=5432

# pgAdmin Configuration
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=your_secure_pgadmin_password
PGADMIN_PORT=5050

# Redis Configuration
REDIS_PASSWORD=your_secure_redis_password
REDIS_PORT=6379

# JWT Configuration
JWT_SECRET=your_very_long_and_secure_jwt_secret_key_here_minimum_256_bits
JWT_EXPIRATION=86400000
JWT_REFRESH_EXPIRATION=604800000

# Application Configuration
APP_BASE_URL=https://api.logipool.com
APP_PORT=8080

# File Upload Configuration
MAX_FILE_SIZE=50MB
MAX_REQUEST_SIZE=50MB
UPLOAD_DIR=/app/uploads

# Cloud Storage Configuration (AWS S3)
CLOUD_STORAGE_ENABLED=true
CLOUD_STORAGE_PROVIDER=aws
S3_BUCKET_NAME=logipool-documents-prod
AWS_REGION=us-east-1
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key

# Email Configuration
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=465
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=GK28~N!$80p>

# SMS Configuration (Twilio)
SMS_PROVIDER=twilio
SMS_ENABLED=true
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=+**********

# Payment Configuration (Stripe)
STRIPE_ENABLED=true
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_PUBLIC_KEY=pk_live_your_stripe_public_key
COMMISSION_RATE=0.075
MIN_COMMISSION=5.00
MAX_COMMISSION=500.00

# PayPal Configuration (Optional)
PAYPAL_ENABLED=false
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret

# Rate Limiting Configuration
RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_PER_HOUR=5000

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://app.logipool.com,https://admin.logipool.com

# SSL Configuration
SSL_ENABLED=true
SSL_KEYSTORE_PATH=/etc/nginx/ssl/logipool.p12
SSL_KEYSTORE_PASSWORD=your_ssl_keystore_password
SSL_KEYSTORE_TYPE=PKCS12

# Notification Configuration
EMAIL_NOTIFICATIONS_ENABLED=true
SMS_NOTIFICATIONS_ENABLED=true

# Monitoring Configuration
MONITORING_ENABLED=true
METRICS_ENABLED=true

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=logipool-backups-prod

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_PATH=/app/logs/logipool.log
LOG_MAX_FILE_SIZE=100MB
LOG_MAX_FILES=10

# Security Configuration
SECURITY_HEADERS_ENABLED=true
CSRF_PROTECTION_ENABLED=true
XSS_PROTECTION_ENABLED=true

# Performance Configuration
CONNECTION_POOL_SIZE=20
CACHE_TTL=3600
SESSION_TIMEOUT=1800

# External API Configuration
GOOGLE_MAPS_API_KEY=AIzaSyCsckoNwV2YYzU71_NE78SMbNcbblZ0CMg
WEATHER_API_KEY=your_weather_api_key

# Social Login Configuration (Optional)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
