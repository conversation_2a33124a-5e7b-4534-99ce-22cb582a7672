package zw.co.kanjan.logipool.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.LiveTrackingSession;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface LiveTrackingSessionRepository extends JpaRepository<LiveTrackingSession, Long> {

    /**
     * Find active tracking session for a specific load
     */
    @Query("SELECT lts FROM LiveTrackingSession lts WHERE lts.load.id = :loadId AND lts.isActive = true")
    Optional<LiveTrackingSession> findActiveSessionByLoadId(@Param("loadId") Long loadId);

    /**
     * Find active tracking session for a specific user
     */
    @Query("SELECT lts FROM LiveTrackingSession lts WHERE lts.trackingUser.id = :userId AND lts.isActive = true")
    Optional<LiveTrackingSession> findActiveSessionByUserId(@Param("userId") Long userId);

    /**
     * Find all active tracking sessions
     */
    @Query("SELECT lts FROM LiveTrackingSession lts WHERE lts.isActive = true ORDER BY lts.startedAt DESC")
    List<LiveTrackingSession> findAllActiveSessions();

    /**
     * Find active sessions for a specific company
     */
    @Query("SELECT lts FROM LiveTrackingSession lts " +
           "WHERE lts.load.assignedCompany.id = :companyId AND lts.isActive = true " +
           "ORDER BY lts.startedAt DESC")
    List<LiveTrackingSession> findActiveSessionsByCompanyId(@Param("companyId") Long companyId);

    /**
     * End all active sessions for a specific load
     */
    @Modifying
    @Query("UPDATE LiveTrackingSession lts SET lts.isActive = false, " +
           "lts.sessionStatus = :status, lts.endReason = :reason, lts.endedAt = :endTime " +
           "WHERE lts.load.id = :loadId AND lts.isActive = true")
    int endActiveSessionsForLoad(@Param("loadId") Long loadId, 
                                @Param("status") LiveTrackingSession.SessionStatus status,
                                @Param("reason") String reason,
                                @Param("endTime") LocalDateTime endTime);

    /**
     * End all active sessions for a specific user
     */
    @Modifying
    @Query("UPDATE LiveTrackingSession lts SET lts.isActive = false, " +
           "lts.sessionStatus = :status, lts.endReason = :reason, lts.endedAt = :endTime " +
           "WHERE lts.trackingUser.id = :userId AND lts.isActive = true")
    int endActiveSessionsForUser(@Param("userId") Long userId,
                                @Param("status") LiveTrackingSession.SessionStatus status,
                                @Param("reason") String reason,
                                @Param("endTime") LocalDateTime endTime);

    /**
     * Find sessions that haven't been updated for a specified duration (for cleanup)
     */
    @Query("SELECT lts FROM LiveTrackingSession lts " +
           "WHERE lts.isActive = true AND lts.lastUpdateAt < :cutoffTime")
    List<LiveTrackingSession> findStaleActiveSessions(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Count active sessions for a user
     */
    @Query("SELECT COUNT(lts) FROM LiveTrackingSession lts " +
           "WHERE lts.trackingUser.id = :userId AND lts.isActive = true")
    long countActiveSessionsByUserId(@Param("userId") Long userId);

    /**
     * Get tracking history for a load
     */
    @Query("SELECT lts FROM LiveTrackingSession lts " +
           "WHERE lts.load.id = :loadId ORDER BY lts.startedAt DESC")
    List<LiveTrackingSession> findTrackingHistoryByLoadId(@Param("loadId") Long loadId);
}
