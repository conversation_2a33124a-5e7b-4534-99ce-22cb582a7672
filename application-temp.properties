# Temporary configuration for testing
spring.application.name=logipool-test

# H2 In-Memory Database for Testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA Configuration for Testing
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.format_sql=false

# JWT Configuration for Testing
app.jwt.secret=test-secret-key-for-jwt-token-generation-2025-testing-only
app.jwt.expiration=3600000
app.jwt.refresh-expiration=7200000

# Disable Redis and Mail for testing
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration,org.springframework.boot.autoconfigure.mail.MailSenderAutoConfiguration

# File Upload Configuration for Testing
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
app.upload.dir=./test-uploads

# Notification Configuration for Testing
app.notification.email.enabled=false
app.notification.sms.enabled=false
app.notification.realtime.enabled=true
app.notification.persistent.enabled=true

# Test Data Configuration - ENABLED for testing
app.test-data.enabled=true

# Logging Configuration for Testing
logging.level.zw.co.kanjan.logipool=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN

# Tracking Verification Configuration
app.tracking.verification.token-expiry-hours=24
app.tracking.verification.code-expiry-minutes=15
app.tracking.verification.max-requests-per-hour=3
app.tracking.verification.max-ip-requests-per-hour=10
app.tracking.verification.max-access-count=5
app.tracking.verification.cleanup-enabled=true
app.tracking.verification.monitoring-enabled=true

# Server port
server.port=8081
