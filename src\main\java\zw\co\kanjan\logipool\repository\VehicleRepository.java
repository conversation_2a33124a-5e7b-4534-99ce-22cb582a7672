package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Vehicle;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface VehicleRepository extends JpaRepository<Vehicle, Long> {
    
    // Find by company
    Page<Vehicle> findByCompany(Company company, Pageable pageable);
    
    List<Vehicle> findByCompanyAndStatus(Company company, Vehicle.VehicleStatus status);

    Page<Vehicle> findByCompanyAndStatus(Company company, Vehicle.VehicleStatus status, Pageable pageable);
    
    // Find by registration number
    Optional<Vehicle> findByRegistrationNumber(String registrationNumber);
    
    Boolean existsByRegistrationNumber(String registrationNumber);
    
    // Find by status
    Page<Vehicle> findByStatus(Vehicle.VehicleStatus status, Pageable pageable);
    
    // Find by type
    Page<Vehicle> findByType(Vehicle.VehicleType type, Pageable pageable);
    
    // Find available vehicles
    @Query("SELECT v FROM Vehicle v WHERE v.status = 'AVAILABLE' AND v.company.verificationStatus = 'VERIFIED'")
    Page<Vehicle> findAvailableVerifiedVehicles(Pageable pageable);

    // Find publicly visible and approved vehicles
    @Query("SELECT v FROM Vehicle v WHERE v.isPubliclyVisible = true AND v.publicApprovalStatus = 'APPROVED' AND v.status = 'AVAILABLE'")
    Page<Vehicle> findPubliclyVisibleVehicles(Pageable pageable);

    // Find publicly visible vehicles by type
    @Query("SELECT v FROM Vehicle v WHERE v.isPubliclyVisible = true AND v.publicApprovalStatus = 'APPROVED' AND v.type = :type AND v.status = 'AVAILABLE'")
    Page<Vehicle> findPubliclyVisibleVehiclesByType(@Param("type") Vehicle.VehicleType type, Pageable pageable);

    // Find vehicles pending approval
    @Query("SELECT v FROM Vehicle v WHERE v.publicApprovalStatus = 'PENDING' ORDER BY v.createdAt ASC")
    Page<Vehicle> findPendingApproval(Pageable pageable);

    // Find vehicles by approval status
    Page<Vehicle> findByPublicApprovalStatus(Vehicle.PublicApprovalStatus status, Pageable pageable);

    // Search vehicles by name or description
    @Query("SELECT v FROM Vehicle v WHERE v.isPubliclyVisible = true AND v.publicApprovalStatus = 'APPROVED' AND " +
           "(LOWER(v.make) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(v.model) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(v.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Vehicle> searchPublicVehicles(@Param("searchTerm") String searchTerm, Pageable pageable);

    // Find vehicles available for rent
    @Query("SELECT v FROM Vehicle v WHERE v.isAvailableForRent = true AND v.isPubliclyVisible = true AND v.publicApprovalStatus = 'APPROVED'")
    Page<Vehicle> findAvailableForRent(Pageable pageable);
    
    // Find vehicles by company and type
    List<Vehicle> findByCompanyAndType(Company company, Vehicle.VehicleType type);

    Page<Vehicle> findByCompanyAndType(Company company, Vehicle.VehicleType type, Pageable pageable);

    // Find vehicles by company, status and type
    Page<Vehicle> findByCompanyAndStatusAndType(Company company, Vehicle.VehicleStatus status, Vehicle.VehicleType type, Pageable pageable);
    
    // Find vehicles with expiring documents
    @Query("SELECT DISTINCT v FROM Vehicle v JOIN v.documents d WHERE d.expiryDate BETWEEN :now AND :futureDate AND d.status = 'VERIFIED'")
    List<Vehicle> findVehiclesWithExpiringDocuments(@Param("now") LocalDateTime now, @Param("futureDate") LocalDateTime futureDate);
    
    // Find vehicles without required documents
    @Query("SELECT v FROM Vehicle v WHERE v.hasInsurance = false OR v.hasFitnessCertificate = false OR v.hasPermits = false")
    List<Vehicle> findVehiclesWithMissingDocuments();
    
    // Count vehicles by company and status
    @Query("SELECT COUNT(v) FROM Vehicle v WHERE v.company = :company AND v.status = :status")
    long countByCompanyAndStatus(@Param("company") Company company, @Param("status") Vehicle.VehicleStatus status);
    
    // Find vehicles by make and model
    List<Vehicle> findByMakeAndModel(String make, String model);
    
    // Find vehicles by year range
    @Query("SELECT v FROM Vehicle v WHERE v.year BETWEEN :startYear AND :endYear")
    List<Vehicle> findByYearBetween(@Param("startYear") Integer startYear, @Param("endYear") Integer endYear);

    // Count publicly visible vehicles
    @Query("SELECT COUNT(v) FROM Vehicle v WHERE v.isPubliclyVisible = true AND v.publicApprovalStatus = 'APPROVED'")
    long countPubliclyVisibleVehicles();

    // Find featured vehicles
    @Query("SELECT v FROM Vehicle v WHERE v.isFeatured = true AND v.isPubliclyVisible = true AND v.publicApprovalStatus = 'APPROVED' AND v.status = 'AVAILABLE'")
    Page<Vehicle> findFeaturedVehicles(Pageable pageable);

    // Find publicly visible vehicles available for rent
    @Query("SELECT v FROM Vehicle v WHERE v.isAvailableForRent = true AND v.isPubliclyVisible = true AND v.publicApprovalStatus = 'APPROVED' AND v.status = 'AVAILABLE'")
    Page<Vehicle> findPubliclyVisibleVehiclesAvailableForRent(Pageable pageable);
}
