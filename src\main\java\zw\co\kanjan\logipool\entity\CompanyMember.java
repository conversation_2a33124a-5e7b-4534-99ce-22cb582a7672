package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "company_members",
       uniqueConstraints = @UniqueConstraint(columnNames = {"company_id", "user_id"}))
@Data
@EqualsAndHashCode(exclude = {"company", "user", "invitedBy"})
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CompanyMember {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id", nullable = false)
    @NotNull
    private Company company;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @NotNull
    private User user;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20, nullable = false)
    @NotNull
    private CompanyRole role;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    @Builder.Default
    private MemberStatus status = MemberStatus.ACTIVE;
    
    @Builder.Default
    private Boolean canManageMembers = false;
    
    @Builder.Default
    private Boolean canManageLoads = false;
    
    @Builder.Default
    private Boolean canUpdateLoadStatus = false;
    
    @Builder.Default
    private Boolean canUploadDocuments = false;
    
    @Builder.Default
    private Boolean canGenerateInvoices = false;
    
    @Builder.Default
    private Boolean canViewFinancials = false;
    
    @Builder.Default
    private Boolean canTrackLocation = false;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "invited_by")
    private User invitedBy;
    
    private LocalDateTime invitedAt;
    
    private LocalDateTime joinedAt;
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    public enum CompanyRole {
        OWNER,          // Full access to everything
        MANAGER,        // Can manage loads, members, and operations
        DRIVER,         // Can update load status and location
        DISPATCHER,     // Can manage loads and tracking
        ACCOUNTANT,     // Can manage invoices and view financials
        VIEWER          // Read-only access
    }
    
    public enum MemberStatus {
        PENDING,        // Invitation sent but not accepted
        ACTIVE,         // Active member
        INACTIVE,       // Temporarily inactive
        SUSPENDED       // Suspended by company owner/manager
    }
}
