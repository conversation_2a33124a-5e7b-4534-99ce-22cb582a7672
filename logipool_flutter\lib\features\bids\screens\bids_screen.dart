import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/di/service_locator.dart';
import '../../../shared/models/bid_model.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../../../shared/widgets/unified_header.dart';
import '../bloc/bid_bloc.dart';

class BidsScreen extends StatefulWidget {
  const BidsScreen({super.key});

  @override
  State<BidsScreen> createState() => _BidsScreenState();
}

class _BidsScreenState extends State<BidsScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  BidStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => BidBloc(getIt())..add(const BidFetchAllRequested()),
      child: Scaffold(
        appBar: UnifiedHeader(
          title: 'Bids',
          actions: [
            IconButton(
              icon: Icon(
                Icons.filter_list,
                color: Theme.of(context).colorScheme.onPrimary,
              ),
              onPressed: () => _showFilterDialog(context),
              tooltip: 'Filter Bids',
            ),
          ],
        ),
        body: Column(
          children: [
            Container(
              color: Theme.of(context).colorScheme.surface,
              child: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: 'My Bids', icon: Icon(Icons.send)),
                  Tab(text: 'Received Bids', icon: Icon(Icons.inbox)),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildMyBidsTab(),
                  _buildReceivedBidsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMyBidsTab() {
    return BlocBuilder<BidBloc, BidState>(
      builder: (context, state) {
        if (state is BidLoading && state.bids.isEmpty) {
          return const LoadingWidget();
        }

        if (state is BidError && state.bids.isEmpty) {
          return CustomErrorWidget(
            message: state.message,
            onRetry: () => context.read<BidBloc>().add(
                  const BidFetchAllRequested(isRefresh: true),
                ),
          );
        }

        final myBids = state.bids
            .where((bid) => bid.companyId == _getCurrentCompanyId())
            .toList();

        if (myBids.isEmpty) {
          return const EmptyStateWidget(
            icon: Icons.gavel,
            title: 'No Bids Submitted',
            message: 'Browse loads and submit your first bid',
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            context.read<BidBloc>().add(
                  const BidFetchAllRequested(isRefresh: true),
                );
          },
          child: ListView.builder(
            itemCount: myBids.length,
            itemBuilder: (context, index) {
              final bid = myBids[index];
              return _buildBidCard(context, bid, isMyBid: true);
            },
          ),
        );
      },
    );
  }

  Widget _buildReceivedBidsTab() {
    return BlocBuilder<BidBloc, BidState>(
      builder: (context, state) {
        if (state is BidLoading && state.bids.isEmpty) {
          return const LoadingWidget();
        }

        if (state is BidError && state.bids.isEmpty) {
          return CustomErrorWidget(
            message: state.message,
            onRetry: () => context.read<BidBloc>().add(
                  const BidFetchAllRequested(isRefresh: true),
                ),
          );
        }

        final receivedBids = state.bids
            .where((bid) => bid.companyId != _getCurrentCompanyId())
            .toList();

        if (receivedBids.isEmpty) {
          return const EmptyStateWidget(
            icon: Icons.inbox,
            title: 'No Bids Received',
            message: 'Create loads to receive bids from transporters',
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            context.read<BidBloc>().add(
                  const BidFetchAllRequested(isRefresh: true),
                );
          },
          child: ListView.builder(
            itemCount: receivedBids.length,
            itemBuilder: (context, index) {
              final bid = receivedBids[index];
              return _buildBidCard(context, bid, isMyBid: false);
            },
          ),
        );
      },
    );
  }

  Widget _buildBidCard(BuildContext context, BidModel bid,
      {required bool isMyBid}) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: () => context.push('/bids/${bid.id}'),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      bid.loadTitle ?? 'Load #${bid.loadId}',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                    ),
                  ),
                  _buildBidStatusBadge(bid.status),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.attach_money, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '\$${bid.amount.toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.green[700],
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.business, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      bid.companyName ?? 'Company #${bid.companyId}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Pickup: ${_formatDate(bid.estimatedPickupTime)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.schedule, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    'Delivery: ${_formatDate(bid.estimatedDeliveryTime)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
              if (bid.proposal != null && bid.proposal!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Text(
                  'Proposal:',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 4),
                Text(
                  bid.proposal!,
                  style: Theme.of(context).textTheme.bodySmall,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              if (!isMyBid && bid.status == BidStatus.pending) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => _rejectBid(context, bid),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                        ),
                        child: const Text('Reject'),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _acceptBid(context, bid),
                        child: const Text('Accept'),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBidStatusBadge(BidStatus status) {
    Color color;
    String text;

    switch (status) {
      case BidStatus.pending:
        color = Colors.orange;
        text = 'Pending';
        break;
      case BidStatus.accepted:
        color = Colors.green;
        text = 'Accepted';
        break;
      case BidStatus.rejected:
        color = Colors.red;
        text = 'Rejected';
        break;
      case BidStatus.withdrawn:
        color = Colors.grey;
        text = 'Withdrawn';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  int _getCurrentCompanyId() {
    // TODO: Get from auth service or user context
    return 1; // Placeholder
  }

  void _acceptBid(BuildContext context, BidModel bid) {
    context.read<BidBloc>().add(BidAcceptRequested(bidId: bid.id!));
  }

  void _rejectBid(BuildContext context, BidModel bid) {
    context.read<BidBloc>().add(BidRejectRequested(bidId: bid.id!));
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Bids'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Status'),
              subtitle: DropdownButton<BidStatus?>(
                value: _selectedStatus,
                isExpanded: true,
                items: const [
                  DropdownMenuItem(value: null, child: Text('All')),
                  DropdownMenuItem(
                      value: BidStatus.pending, child: Text('Pending')),
                  DropdownMenuItem(
                      value: BidStatus.accepted, child: Text('Accepted')),
                  DropdownMenuItem(
                      value: BidStatus.rejected, child: Text('Rejected')),
                  DropdownMenuItem(
                      value: BidStatus.withdrawn, child: Text('Withdrawn')),
                ],
                onChanged: (value) {
                  setState(() {
                    _selectedStatus = value;
                  });
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              if (_selectedStatus != null) {
                context.read<BidBloc>().add(
                      BidFetchByStatusRequested(status: _selectedStatus!),
                    );
              } else {
                context.read<BidBloc>().add(
                      const BidFetchAllRequested(isRefresh: true),
                    );
              }
            },
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }
}
