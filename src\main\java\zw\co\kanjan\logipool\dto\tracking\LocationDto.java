package zw.co.kanjan.logipool.dto.tracking;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import zw.co.kanjan.logipool.entity.DriverLocation;
import zw.co.kanjan.logipool.entity.LocationPermission;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class LocationDto {
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Driver location update request")
    public static class LocationUpdateRequest {
        
        @NotNull(message = "Latitude is required")
        @DecimalMin(value = "-90.0", message = "Latitude must be between -90 and 90")
        @DecimalMax(value = "90.0", message = "Latitude must be between -90 and 90")
        @Schema(description = "Latitude coordinate", example = "-17.8292", required = true)
        private BigDecimal latitude;
        
        @NotNull(message = "Longitude is required")
        @DecimalMin(value = "-180.0", message = "Longitude must be between -180 and 180")
        @DecimalMax(value = "180.0", message = "Longitude must be between -180 and 180")
        @Schema(description = "Longitude coordinate", example = "31.0522", required = true)
        private BigDecimal longitude;
        
        @Schema(description = "Location accuracy in meters", example = "5.0")
        private BigDecimal accuracy;
        
        @Schema(description = "Speed in km/h", example = "60.5")
        private BigDecimal speed;
        
        @Schema(description = "Heading in degrees (0-360)", example = "45.0")
        private BigDecimal heading;
        
        @Schema(description = "Altitude in meters", example = "1200.5")
        private BigDecimal altitude;
        
        @Schema(description = "Human-readable address", example = "Harare CBD, Zimbabwe")
        private String address;
        
        @Schema(description = "Location source", example = "GPS")
        private DriverLocation.LocationSource source;
        
        @Schema(description = "Load ID if tracking for specific load", example = "1")
        private Long loadId;
        
        @Schema(description = "Whether to share this location", example = "true")
        @Builder.Default
        private Boolean isShared = true;
        
        @Schema(description = "Whether driver is on duty", example = "true")
        @Builder.Default
        private Boolean isOnDuty = false;
        
        @Schema(description = "Device identifier", example = "device-123")
        private String deviceId;
        
        @Schema(description = "Session identifier", example = "session-456")
        private String sessionId;
        
        @Schema(description = "Additional notes", example = "Stopped for fuel")
        private String notes;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Driver location response")
    public static class LocationResponse {
        
        @Schema(description = "Location ID", example = "1")
        private Long id;
        
        @Schema(description = "Driver ID", example = "123")
        private Long driverId;
        
        @Schema(description = "Driver name", example = "John Doe")
        private String driverName;
        
        @Schema(description = "Load ID", example = "1")
        private Long loadId;
        
        @Schema(description = "Load title", example = "Harare to Bulawayo")
        private String loadTitle;
        
        @Schema(description = "Company ID", example = "1")
        private Long companyId;
        
        @Schema(description = "Company name", example = "ABC Logistics")
        private String companyName;
        
        @Schema(description = "Latitude coordinate", example = "-17.8292")
        private BigDecimal latitude;
        
        @Schema(description = "Longitude coordinate", example = "31.0522")
        private BigDecimal longitude;
        
        @Schema(description = "Location accuracy in meters", example = "5.0")
        private BigDecimal accuracy;
        
        @Schema(description = "Speed in km/h", example = "60.5")
        private BigDecimal speed;
        
        @Schema(description = "Heading in degrees", example = "45.0")
        private BigDecimal heading;
        
        @Schema(description = "Altitude in meters", example = "1200.5")
        private BigDecimal altitude;
        
        @Schema(description = "Human-readable address", example = "Harare CBD, Zimbabwe")
        private String address;
        
        @Schema(description = "Location source", example = "GPS")
        private DriverLocation.LocationSource source;
        
        @Schema(description = "Location status", example = "ACTIVE")
        private DriverLocation.LocationStatus status;
        
        @Schema(description = "Whether location is shared", example = "true")
        private Boolean isShared;
        
        @Schema(description = "Whether driver is on duty", example = "true")
        private Boolean isOnDuty;
        
        @Schema(description = "Device identifier", example = "device-123")
        private String deviceId;
        
        @Schema(description = "Session identifier", example = "session-456")
        private String sessionId;
        
        @Schema(description = "Additional notes", example = "Stopped for fuel")
        private String notes;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "Location timestamp", example = "2024-01-15 14:30:00")
        private LocalDateTime timestamp;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "Location expiry time", example = "2024-01-15 18:30:00")
        private LocalDateTime expiresAt;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Location permission request")
    public static class PermissionRequest {
        
        @NotNull(message = "Driver ID is required")
        @Schema(description = "Driver ID", example = "123", required = true)
        private Long driverId;
        
        @NotNull(message = "Company ID is required")
        @Schema(description = "Company ID", example = "1", required = true)
        private Long companyId;
        
        @Schema(description = "Load ID for load-specific permission", example = "1")
        private Long loadId;
        
        @NotNull(message = "Permission type is required")
        @Schema(description = "Permission type", example = "GENERAL", required = true)
        private LocationPermission.PermissionType permissionType;
        
        @Schema(description = "Allow real-time tracking", example = "true")
        @Builder.Default
        private Boolean allowRealTimeTracking = false;
        
        @Schema(description = "Allow historical data access", example = "true")
        @Builder.Default
        private Boolean allowHistoricalData = false;
        
        @Schema(description = "Allow client access to location", example = "false")
        @Builder.Default
        private Boolean allowClientAccess = false;
        
        @Schema(description = "Allow emergency access", example = "true")
        @Builder.Default
        private Boolean allowEmergencyAccess = true;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "Permission valid from", example = "2024-01-15 00:00:00")
        private LocalDateTime validFrom;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "Permission valid until", example = "2024-12-31 23:59:59")
        private LocalDateTime validUntil;
        
        @Schema(description = "Permission conditions", example = "Only during working hours")
        private String conditions;
        
        @Schema(description = "Additional notes", example = "Emergency contact permission")
        private String notes;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Location permission response")
    public static class PermissionResponse {
        
        @Schema(description = "Permission ID", example = "1")
        private Long id;
        
        @Schema(description = "Driver ID", example = "123")
        private Long driverId;
        
        @Schema(description = "Driver name", example = "John Doe")
        private String driverName;
        
        @Schema(description = "Company ID", example = "1")
        private Long companyId;
        
        @Schema(description = "Company name", example = "ABC Logistics")
        private String companyName;
        
        @Schema(description = "Load ID", example = "1")
        private Long loadId;
        
        @Schema(description = "Load title", example = "Harare to Bulawayo")
        private String loadTitle;
        
        @Schema(description = "Permission type", example = "GENERAL")
        private LocationPermission.PermissionType permissionType;
        
        @Schema(description = "Permission status", example = "GRANTED")
        private LocationPermission.PermissionStatus status;
        
        @Schema(description = "Whether permission is active", example = "true")
        private Boolean isActive;
        
        @Schema(description = "Allow real-time tracking", example = "true")
        private Boolean allowRealTimeTracking;
        
        @Schema(description = "Allow historical data access", example = "true")
        private Boolean allowHistoricalData;
        
        @Schema(description = "Allow client access to location", example = "false")
        private Boolean allowClientAccess;
        
        @Schema(description = "Allow emergency access", example = "true")
        private Boolean allowEmergencyAccess;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "Permission valid from", example = "2024-01-15 00:00:00")
        private LocalDateTime validFrom;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "Permission valid until", example = "2024-12-31 23:59:59")
        private LocalDateTime validUntil;
        
        @Schema(description = "Permission conditions", example = "Only during working hours")
        private String conditions;
        
        @Schema(description = "Additional notes", example = "Emergency contact permission")
        private String notes;
        
        @Schema(description = "Who granted the permission", example = "admin")
        private String grantedBy;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "When permission was granted", example = "2024-01-15 10:00:00")
        private LocalDateTime grantedAt;
        
        @Schema(description = "Who revoked the permission", example = "manager")
        private String revokedBy;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "When permission was revoked", example = "2024-01-20 15:00:00")
        private LocalDateTime revokedAt;
        
        @Schema(description = "Reason for revocation", example = "Job completed")
        private String revokeReason;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "When permission was created", example = "2024-01-15 09:00:00")
        private LocalDateTime createdAt;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Location tracking summary")
    public static class TrackingSummary {
        
        @Schema(description = "Total active drivers", example = "15")
        private Long totalActiveDrivers;
        
        @Schema(description = "Drivers on duty", example = "12")
        private Long driversOnDuty;
        
        @Schema(description = "Drivers with shared location", example = "10")
        private Long driversWithSharedLocation;
        
        @Schema(description = "Active loads being tracked", example = "8")
        private Long activeTrackedLoads;
        
        @Schema(description = "Recent location updates", example = "25")
        private Long recentLocationUpdates;
        
        @Schema(description = "List of active driver locations")
        private List<LocationResponse> activeDrivers;
        
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        @Schema(description = "Last updated", example = "2024-01-15 14:30:00")
        private LocalDateTime lastUpdated;
    }
}
