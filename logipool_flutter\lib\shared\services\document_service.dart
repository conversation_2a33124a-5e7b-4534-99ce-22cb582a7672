import 'package:dio/dio.dart';
import '../models/document_model.dart';
import '../models/paginated_response.dart';
import '../../core/network/api_client.dart';
import '../../core/errors/exceptions.dart';
import 'cross_platform_file_service.dart';
import 'file_download_service.dart';

class DocumentService {
  final ApiClient _apiClient;

  DocumentService(this._apiClient);

  // Upload document with file
  Future<FileUploadResponse> uploadDocument({
    required CrossPlatformFile file,
    required DocumentUploadRequest request,
  }) async {
    try {
      final formData = FormData.fromMap({
        'file': file.toMultipartFile(),
        'name': request.name,
        'type': _getDocumentTypeValue(request.type),
        if (request.description != null) 'description': request.description,
        if (request.expiryDate != null)
          'expiryDate': request.expiryDate!.toIso8601String(),
        if (request.companyId != null) 'companyId': request.companyId,
        if (request.vehicleId != null) 'vehicleId': request.vehicleId,
        if (request.loadId != null) 'loadId': request.loadId,
      });

      final response = await _apiClient.post<Map<String, dynamic>>(
        '/documents/upload',
        data: formData,
        options: Options(
          contentType: 'multipart/form-data',
        ),
      );

      return FileUploadResponse.fromJson(response.data!);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to upload document',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to upload document: $e',
        statusCode: 500,
      );
    }
  }

  // Upload document specifically for a load
  Future<FileUploadResponse> uploadLoadDocument({
    required int loadId,
    required CrossPlatformFile file,
    required DocumentUploadRequest request,
  }) async {
    try {
      final formData = FormData.fromMap({
        'file': file.toMultipartFile(),
        'name': request.name,
        'type': _getDocumentTypeValue(request.type),
        if (request.description != null) 'description': request.description,
      });

      final response = await _apiClient.post<Map<String, dynamic>>(
        '/documents/loads/$loadId/upload',
        data: formData,
        options: Options(
          headers: {'Content-Type': 'multipart/form-data'},
        ),
      );

      return FileUploadResponse.fromJson(response.data!);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to upload load document',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to upload load document: $e',
        statusCode: 500,
      );
    }
  }

  // Get documents by company
  Future<PaginatedResponse<DocumentModel>> getCompanyDocuments({
    required int companyId,
    int page = 0,
    int size = 20,
    String? type,
    String? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'size': size,
        if (type != null) 'type': type,
        if (status != null) 'status': status,
      };

      final response = await _apiClient.get(
        '/documents/company/$companyId',
        queryParameters: queryParams,
      );

      return PaginatedResponse<DocumentModel>.fromJson(
        response.data,
        (json) => DocumentModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch company documents',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to fetch company documents: $e',
        statusCode: 500,
      );
    }
  }

  // Get documents by vehicle
  Future<PaginatedResponse<DocumentModel>> getVehicleDocuments({
    required int vehicleId,
    int page = 0,
    int size = 20,
    String? type,
    String? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'size': size,
        if (type != null) 'type': type,
        if (status != null) 'status': status,
      };

      final response = await _apiClient.get(
        '/documents/vehicle/$vehicleId',
        queryParameters: queryParams,
      );

      return PaginatedResponse<DocumentModel>.fromJson(
        response.data,
        (json) => DocumentModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch vehicle documents',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to fetch vehicle documents: $e',
        statusCode: 500,
      );
    }
  }

  // Get documents by load
  Future<PaginatedResponse<DocumentModel>> getLoadDocuments({
    required int loadId,
    int page = 0,
    int size = 20,
    String? type,
    String? status,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'size': size,
        if (type != null) 'type': type,
        if (status != null) 'status': status,
      };

      final response = await _apiClient.get(
        '/documents/loads/$loadId',
        queryParameters: queryParams,
      );

      return PaginatedResponse<DocumentModel>.fromJson(
        response.data,
        (json) => DocumentModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch load documents',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to fetch load documents: $e',
        statusCode: 500,
      );
    }
  }

  // Get all documents with filtering
  Future<PaginatedResponse<DocumentModel>> getAllDocuments({
    int page = 0,
    int size = 20,
    String? type,
    String? status,
    bool? isRequired,
    bool? expiringSoon,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'size': size,
        if (type != null) 'type': type,
        if (status != null) 'status': status,
        if (isRequired != null) 'isRequired': isRequired,
        if (expiringSoon != null) 'expiringSoon': expiringSoon,
      };

      final response = await _apiClient.get(
        '/documents',
        queryParameters: queryParams,
      );

      return PaginatedResponse<DocumentModel>.fromJson(
        response.data,
        (json) => DocumentModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch documents',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to fetch documents: $e',
        statusCode: 500,
      );
    }
  }

  // Get document by ID
  Future<DocumentModel> getDocumentById(int id) async {
    try {
      final response = await _apiClient.get('/documents/$id');
      return DocumentModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch document',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to fetch document: $e',
        statusCode: 500,
      );
    }
  }

  // Update document
  Future<DocumentModel> updateDocument({
    required int id,
    required DocumentUpdateRequest request,
  }) async {
    try {
      final response = await _apiClient.put(
        '/documents/$id',
        data: request.toJson(),
      );
      return DocumentModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to update document',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to update document: $e',
        statusCode: 500,
      );
    }
  }

  // Delete document
  Future<void> deleteDocument(int id) async {
    try {
      await _apiClient.delete('/documents/$id');
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to delete document',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to delete document: $e',
        statusCode: 500,
      );
    }
  }

  // Verify document (Admin only)
  Future<DocumentModel> verifyDocument({
    required int id,
    required DocumentVerificationRequest request,
  }) async {
    try {
      final response = await _apiClient.post(
        '/documents/$id/verify',
        data: request.toJson(),
      );
      return DocumentModel.fromJson(response.data);
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to verify document',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to verify document: $e',
        statusCode: 500,
      );
    }
  }

  // Download document
  Future<String> downloadDocument(int id) async {
    try {
      // Get the base URL from the API client
      final baseUrl = _apiClient.baseUrl;

      // Return the complete download URL for the frontend to handle
      return '$baseUrl/documents/$id/download';
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to download document',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to download document: $e',
        statusCode: 500,
      );
    }
  }

  // Download document file to device
  Future<bool> downloadDocumentFile(int id, String fileName) async {
    try {
      final downloadUrl = '${_apiClient.baseUrl}/documents/$id/download';

      return await FileDownloadService.downloadFile(
        url: downloadUrl,
        fileName: fileName,
        dio: _apiClient.dio,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to download document file: $e',
        statusCode: 500,
      );
    }
  }

  // Get documents requiring verification (Admin only)
  Future<PaginatedResponse<DocumentModel>> getDocumentsForVerification({
    int page = 0,
    int size = 20,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'size': size,
        'status': 'PENDING',
      };

      final response = await _apiClient.get(
        '/documents/verification',
        queryParameters: queryParams,
      );

      return PaginatedResponse<DocumentModel>.fromJson(
        response.data,
        (json) => DocumentModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch documents for verification',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to fetch documents for verification: $e',
        statusCode: 500,
      );
    }
  }

  // Get expiring documents
  Future<PaginatedResponse<DocumentModel>> getExpiringDocuments({
    int page = 0,
    int size = 20,
    int? days = 30,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'size': size,
        if (days != null) 'days': days,
      };

      final response = await _apiClient.get(
        '/documents/expiring',
        queryParameters: queryParams,
      );

      return PaginatedResponse<DocumentModel>.fromJson(
        response.data,
        (json) => DocumentModel.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      throw ApiException(
        message: e.message ?? 'Failed to fetch expiring documents',
        statusCode: e.response?.statusCode ?? 500,
      );
    } catch (e) {
      throw ApiException(
        message: 'Failed to fetch expiring documents: $e',
        statusCode: 500,
      );
    }
  }

  // Helper method to get the correct string value for DocumentType enum
  String _getDocumentTypeValue(DocumentType type) {
    switch (type) {
      case DocumentType.companyRegistration:
        return 'COMPANY_REGISTRATION';
      case DocumentType.taxClearance:
        return 'TAX_CLEARANCE';
      case DocumentType.businessLicense:
        return 'BUSINESS_LICENSE';
      case DocumentType.insuranceCertificate:
        return 'INSURANCE_CERTIFICATE';
      case DocumentType.vehicleRegistration:
        return 'VEHICLE_REGISTRATION';
      case DocumentType.fitnessCertificate:
        return 'FITNESS_CERTIFICATE';
      case DocumentType.roadPermit:
        return 'ROAD_PERMIT';
      case DocumentType.zinaraPermit:
        return 'ZINARA_PERMIT';
      case DocumentType.vehicleInsurance:
        return 'VEHICLE_INSURANCE';
      case DocumentType.vehiclePhotos:
        return 'VEHICLE_PHOTOS';
      case DocumentType.proofOfDelivery:
        return 'PROOF_OF_DELIVERY';
      case DocumentType.invoice:
        return 'INVOICE';
      case DocumentType.contract:
        return 'CONTRACT';
      case DocumentType.waybill:
        return 'WAYBILL';
      case DocumentType.customsDeclaration:
        return 'CUSTOMS_DECLARATION';
      case DocumentType.profilePhoto:
        return 'PROFILE_PHOTO';
      case DocumentType.other:
        return 'OTHER';
    }
  }

  /// Get uploaded invoice documents information for a load
  Future<Map<String, dynamic>> getUploadedInvoiceDocuments(int loadId) async {
    try {
      final response = await _apiClient.get<Map<String, dynamic>>(
          '/invoices/loads/$loadId/uploaded-documents');
      return response.data ?? {};
    } catch (e) {
      throw Exception('Failed to get uploaded invoice documents: $e');
    }
  }
}
