import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../shared/models/document_model.dart';

class DocumentCard extends StatelessWidget {
  final DocumentModel document;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final Function(DocumentStatus)? onVerify;
  final VoidCallback? onDownload;

  const DocumentCard({
    super.key,
    required this.document,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onVerify,
    this.onDownload,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and status
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          document.name,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          document.type.displayName,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  _buildStatusChip(context),
                ],
              ),

              if (document.description != null) ...[
                const SizedBox(height: 12),
                Text(
                  document.description!,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              const SizedBox(height: 12),

              // File info and metadata
              Row(
                children: [
                  Icon(
                    _getFileIcon(),
                    size: 16,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    document.fileName,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '• ${document.formattedFileSize}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Dates and expiry info
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    'Uploaded ${DateFormat('MMM dd, yyyy').format(document.createdAt)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),

              if (document.expiryDate != null) ...[
                const SizedBox(height: 4),
                Row(
                  children: [
                    Icon(
                      document.isExpired
                          ? Icons.error
                          : document.isExpiringSoon
                              ? Icons.warning
                              : Icons.schedule,
                      size: 16,
                      color: document.isExpired
                          ? colorScheme.error
                          : document.isExpiringSoon
                              ? Colors.orange
                              : colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      document.isExpired
                          ? 'Expired ${DateFormat('MMM dd, yyyy').format(document.expiryDate!)}'
                          : 'Expires ${DateFormat('MMM dd, yyyy').format(document.expiryDate!)}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: document.isExpired
                            ? colorScheme.error
                            : document.isExpiringSoon
                                ? Colors.orange
                                : colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ],

              if (document.isRequired) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.star,
                      size: 16,
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Required Document',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.amber.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],

              const SizedBox(height: 16),

              // Action buttons
              Row(
                children: [
                  if (onDownload != null) ...[
                    OutlinedButton.icon(
                      onPressed: onDownload,
                      icon: const Icon(Icons.download, size: 16),
                      label: const Text('Download'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],

                  if (onEdit != null) ...[
                    OutlinedButton.icon(
                      onPressed: onEdit,
                      icon: const Icon(Icons.edit, size: 16),
                      label: const Text('Edit'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],

                  if (onVerify != null && document.status == DocumentStatus.pending) ...[
                    FilledButton.icon(
                      onPressed: () => _showVerificationDialog(context),
                      icon: const Icon(Icons.verified, size: 16),
                      label: const Text('Verify'),
                      style: FilledButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        minimumSize: Size.zero,
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],

                  const Spacer(),

                  if (onDelete != null)
                    IconButton(
                      onPressed: onDelete,
                      icon: const Icon(Icons.delete_outline),
                      color: colorScheme.error,
                      iconSize: 20,
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    Color backgroundColor;
    Color foregroundColor;
    IconData icon;

    switch (document.status) {
      case DocumentStatus.verified:
        backgroundColor = Colors.green.shade100;
        foregroundColor = Colors.green.shade700;
        icon = Icons.verified;
        break;
      case DocumentStatus.rejected:
        backgroundColor = colorScheme.errorContainer;
        foregroundColor = colorScheme.onErrorContainer;
        icon = Icons.cancel;
        break;
      case DocumentStatus.expired:
        backgroundColor = Colors.red.shade100;
        foregroundColor = Colors.red.shade700;
        icon = Icons.event_busy;
        break;
      case DocumentStatus.pending:
      default:
        backgroundColor = Colors.orange.shade100;
        foregroundColor = Colors.orange.shade700;
        icon = Icons.pending;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: foregroundColor),
          const SizedBox(width: 4),
          Text(
            document.status.displayName,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: foregroundColor,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getFileIcon() {
    final fileType = document.fileType.toLowerCase();
    
    if (fileType.contains('pdf')) {
      return Icons.picture_as_pdf;
    } else if (fileType.contains('image')) {
      return Icons.image;
    } else if (fileType.contains('word') || fileType.contains('document')) {
      return Icons.description;
    } else if (fileType.contains('excel') || fileType.contains('spreadsheet')) {
      return Icons.table_chart;
    } else {
      return Icons.insert_drive_file;
    }
  }

  void _showVerificationDialog(BuildContext context) {
    showDialog<DocumentStatus>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Verify Document'),
        content: Text('How would you like to verify "${document.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(DocumentStatus.rejected),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Reject'),
          ),
          FilledButton(
            onPressed: () => Navigator.of(context).pop(DocumentStatus.verified),
            child: const Text('Approve'),
          ),
        ],
      ),
    ).then((status) {
      if (status != null && onVerify != null) {
        onVerify!(status);
      }
    });
  }
}
