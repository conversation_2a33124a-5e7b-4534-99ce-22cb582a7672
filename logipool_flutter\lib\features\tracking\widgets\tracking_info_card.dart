import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/tracking_model.dart';

class TrackingInfoCard extends StatelessWidget {
  final TrackingModel tracking;
  final VoidCallback? onTap;

  const TrackingInfoCard({
    super.key,
    required this.tracking,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final statusColor = _getStatusColor(tracking.status);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with status and timestamp
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: statusColor,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            tracking.status.displayName,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: statusColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    DateFormat('MMM dd, HH:mm').format(tracking.timestamp),
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Location information
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    Icons.location_on,
                    size: 20,
                    color: Colors.grey.shade600,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          tracking.location,
                          style: theme.textTheme.bodyMedium,
                        ),
                        if (tracking.latitude != null && tracking.longitude != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            '${tracking.latitude!.toStringAsFixed(6)}, ${tracking.longitude!.toStringAsFixed(6)}',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: Colors.grey.shade600,
                              fontFamily: 'monospace',
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),

              // Notes (if available)
              if (tracking.notes != null && tracking.notes!.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.note,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          tracking.notes!,
                          style: theme.textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const SizedBox(height: 12),

              // Footer with update info
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        tracking.isAutomated ? Icons.smart_toy : Icons.person,
                        size: 16,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        tracking.isAutomated 
                            ? 'Automated'
                            : tracking.updatedByName,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                  if (tracking.isAutomated)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'AUTO',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.blue.shade700,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getStatusColor(TrackingStatus status) {
    switch (status) {
      case TrackingStatus.loadPosted:
        return Colors.blue;
      case TrackingStatus.bidAccepted:
        return Colors.green;
      case TrackingStatus.pickupScheduled:
        return Colors.orange;
      case TrackingStatus.inTransitToPickup:
      case TrackingStatus.inTransitToDelivery:
        return Colors.purple;
      case TrackingStatus.arrivedAtPickup:
      case TrackingStatus.arrivedAtDelivery:
        return Colors.amber;
      case TrackingStatus.loadingInProgress:
      case TrackingStatus.unloadingInProgress:
        return Colors.indigo;
      case TrackingStatus.loaded:
        return Colors.teal;
      case TrackingStatus.delivered:
        return Colors.green;
      case TrackingStatus.delayed:
        return Colors.orange;
      case TrackingStatus.issueReported:
        return Colors.red;
    }
  }
}

class TrackingInfoList extends StatelessWidget {
  final List<TrackingModel> tracking;
  final Function(TrackingModel)? onTrackingTap;
  final VoidCallback? onLoadMore;
  final bool hasMore;

  const TrackingInfoList({
    super.key,
    required this.tracking,
    this.onTrackingTap,
    this.onLoadMore,
    this.hasMore = false,
  });

  @override
  Widget build(BuildContext context) {
    if (tracking.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.timeline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No tracking information available',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: tracking.length + (hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == tracking.length) {
          return Padding(
            padding: const EdgeInsets.all(16),
            child: Center(
              child: ElevatedButton.icon(
                onPressed: onLoadMore,
                icon: const Icon(Icons.expand_more),
                label: const Text('Load More'),
              ),
            ),
          );
        }

        final trackingItem = tracking[index];
        return TrackingInfoCard(
          tracking: trackingItem,
          onTap: onTrackingTap != null 
              ? () => onTrackingTap!(trackingItem)
              : null,
        );
      },
    );
  }
}

class TrackingStatusChip extends StatelessWidget {
  final TrackingStatus status;
  final bool showIcon;

  const TrackingStatusChip({
    super.key,
    required this.status,
    this.showIcon = true,
  });

  @override
  Widget build(BuildContext context) {
    final color = _getStatusColor(status);
    final icon = _getStatusIcon(status);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showIcon) ...[
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 6),
          ],
          Text(
            status.displayName,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(TrackingStatus status) {
    switch (status) {
      case TrackingStatus.loadPosted:
        return Colors.blue;
      case TrackingStatus.bidAccepted:
        return Colors.green;
      case TrackingStatus.pickupScheduled:
        return Colors.orange;
      case TrackingStatus.inTransitToPickup:
      case TrackingStatus.inTransitToDelivery:
        return Colors.purple;
      case TrackingStatus.arrivedAtPickup:
      case TrackingStatus.arrivedAtDelivery:
        return Colors.amber;
      case TrackingStatus.loadingInProgress:
      case TrackingStatus.unloadingInProgress:
        return Colors.indigo;
      case TrackingStatus.loaded:
        return Colors.teal;
      case TrackingStatus.delivered:
        return Colors.green;
      case TrackingStatus.delayed:
        return Colors.orange;
      case TrackingStatus.issueReported:
        return Colors.red;
    }
  }

  IconData _getStatusIcon(TrackingStatus status) {
    switch (status) {
      case TrackingStatus.loadPosted:
        return Icons.post_add;
      case TrackingStatus.bidAccepted:
        return Icons.handshake;
      case TrackingStatus.pickupScheduled:
        return Icons.schedule;
      case TrackingStatus.inTransitToPickup:
        return Icons.directions_car;
      case TrackingStatus.arrivedAtPickup:
        return Icons.location_on;
      case TrackingStatus.loadingInProgress:
        return Icons.upload;
      case TrackingStatus.loaded:
        return Icons.check_box;
      case TrackingStatus.inTransitToDelivery:
        return Icons.local_shipping;
      case TrackingStatus.arrivedAtDelivery:
        return Icons.place;
      case TrackingStatus.unloadingInProgress:
        return Icons.download;
      case TrackingStatus.delivered:
        return Icons.check_circle;
      case TrackingStatus.delayed:
        return Icons.access_time;
      case TrackingStatus.issueReported:
        return Icons.warning;
    }
  }
}
