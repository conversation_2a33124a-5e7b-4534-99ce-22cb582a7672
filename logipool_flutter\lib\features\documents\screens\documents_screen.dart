import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/models/document_model.dart';
import '../../../shared/widgets/app_bar.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../shared/widgets/empty_state_widget.dart';
import '../bloc/document_bloc.dart';
import '../widgets/document_card.dart';
import '../widgets/document_filter_sheet.dart';
import '../widgets/document_upload_fab.dart';

class DocumentsScreen extends StatefulWidget {
  final int? companyId;
  final int? vehicleId;
  final int? loadId;
  final String?
      mode; // 'all', 'company', 'vehicle', 'load', 'verification', 'expiring'

  const DocumentsScreen({
    super.key,
    this.companyId,
    this.vehicleId,
    this.loadId,
    this.mode = 'all',
  });

  @override
  State<DocumentsScreen> createState() => _DocumentsScreenState();
}

class _DocumentsScreenState extends State<DocumentsScreen> {
  final ScrollController _scrollController = ScrollController();
  String? _selectedType;
  String? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadDocuments();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadDocuments() {
    switch (widget.mode) {
      case 'company':
        if (widget.companyId != null) {
          context.read<DocumentBloc>().add(CompanyDocumentsRequested(
                companyId: widget.companyId!,
                type: _selectedType,
                status: _selectedStatus,
                refresh: true,
              ));
        }
        break;
      case 'vehicle':
        if (widget.vehicleId != null) {
          context.read<DocumentBloc>().add(VehicleDocumentsRequested(
                vehicleId: widget.vehicleId!,
                type: _selectedType,
                status: _selectedStatus,
                refresh: true,
              ));
        }
        break;
      case 'load':
        if (widget.loadId != null) {
          context.read<DocumentBloc>().add(LoadDocumentsRequested(
                loadId: widget.loadId!,
                type: _selectedType,
                status: _selectedStatus,
                refresh: true,
              ));
        }
        break;
      case 'verification':
        context
            .read<DocumentBloc>()
            .add(const DocumentsForVerificationRequested(refresh: true));
        break;
      case 'expiring':
        context
            .read<DocumentBloc>()
            .add(const ExpiringDocumentsRequested(refresh: true));
        break;
      default:
        context.read<DocumentBloc>().add(DocumentsRequested(
              type: _selectedType,
              status: _selectedStatus,
              refresh: true,
            ));
    }
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<DocumentBloc>().add(const DocumentLoadMoreRequested());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  void _showFilterSheet() {
    showModalBottomSheet<Map<String, String?>>(
      context: context,
      isScrollControlled: true,
      builder: (context) => DocumentFilterSheet(
        selectedType: _selectedType,
        selectedStatus: _selectedStatus,
        mode: widget.mode ?? 'all',
      ),
    ).then((filters) {
      if (filters != null) {
        setState(() {
          _selectedType = filters['type'];
          _selectedStatus = filters['status'];
        });
        _loadDocuments();
      }
    });
  }

  String get _screenTitle {
    switch (widget.mode) {
      case 'company':
        return 'Company Documents';
      case 'vehicle':
        return 'Vehicle Documents';
      case 'load':
        return 'Load Documents';
      case 'verification':
        return 'Documents for Verification';
      case 'expiring':
        return 'Expiring Documents';
      default:
        return 'Documents';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: LogiPoolAppBar(
        title: _screenTitle,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterSheet,
          ),
        ],
      ),
      body: BlocConsumer<DocumentBloc, DocumentState>(
        listener: (context, state) {
          if (state is DocumentError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          } else if (state is DocumentUploaded) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Document uploaded successfully'),
                backgroundColor: Colors.green,
              ),
            );
            _loadDocuments(); // Refresh the list
          } else if (state is DocumentDeleted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Document deleted successfully'),
                backgroundColor: Colors.green,
              ),
            );
            _loadDocuments(); // Refresh the list
          } else if (state is DocumentVerified) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Document verification updated'),
                backgroundColor: Colors.green,
              ),
            );
            _loadDocuments(); // Refresh the list
          } else if (state is DocumentFileDownloaded) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is DocumentLoading) {
            return const LoadingWidget();
          }

          if (state is DocumentError) {
            return AppErrorWidget(
              message: state.message,
              onRetry: _loadDocuments,
            );
          }

          if (state is DocumentsLoaded) {
            if (state.documents.isEmpty) {
              return EmptyStateWidget(
                icon: Icon(
                  Icons.description_outlined,
                  size: 64,
                  color:
                      Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                ),
                title: 'No Documents Found',
                message: _getEmptyMessage(),
                buttonText: _canUpload() ? 'Upload Document' : null,
                showActionButton: _canUpload(),
                onAction: _canUpload() ? () => _showUploadDialog() : null,
              );
            }

            return RefreshIndicator(
              onRefresh: () async => _loadDocuments(),
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.all(16),
                itemCount:
                    state.documents.length + (state.isLoadingMore ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index >= state.documents.length) {
                    return const Padding(
                      padding: EdgeInsets.all(16),
                      child: Center(child: CircularProgressIndicator()),
                    );
                  }

                  final document = state.documents[index];
                  return DocumentCard(
                    document: document,
                    onTap: () => _viewDocument(document),
                    onEdit: _canEdit(document)
                        ? () => _editDocument(document)
                        : null,
                    onDelete: _canDelete(document)
                        ? () => _deleteDocument(document)
                        : null,
                    onVerify: _canVerify(document)
                        ? (status) => _verifyDocument(document, status)
                        : null,
                    onDownload: () => _downloadDocument(document),
                  );
                },
              ),
            );
          }

          return const SizedBox.shrink();
        },
      ),
      floatingActionButton: _canUpload()
          ? DocumentUploadFab(
              companyId: widget.companyId,
              vehicleId: widget.vehicleId,
              loadId: widget.loadId,
              onUploadSuccess: _loadDocuments,
            )
          : null,
    );
  }

  String _getEmptyMessage() {
    switch (widget.mode) {
      case 'company':
        return 'No company documents found. Upload documents to get started.';
      case 'vehicle':
        return 'No vehicle documents found. Upload vehicle documents to get started.';
      case 'load':
        return 'No load documents found. Upload load-related documents.';
      case 'verification':
        return 'No documents pending verification.';
      case 'expiring':
        return 'No documents expiring soon.';
      default:
        return 'No documents found. Upload your first document to get started.';
    }
  }

  bool _canUpload() {
    // Users can upload documents except in verification and expiring modes
    return widget.mode != 'verification' && widget.mode != 'expiring';
  }

  bool _canEdit(DocumentModel document) {
    // Users can edit their own documents that are not verified
    return document.status != DocumentStatus.verified;
  }

  bool _canDelete(DocumentModel document) {
    // Users can delete their own documents that are not required
    return !document.isRequired;
  }

  bool _canVerify(DocumentModel document) {
    // Only admins can verify documents (this would be checked in the backend)
    return widget.mode == 'verification' &&
        document.status == DocumentStatus.pending;
  }

  void _viewDocument(DocumentModel document) {
    context.push('/documents/${document.id}');
  }

  void _editDocument(DocumentModel document) {
    context.push('/documents/${document.id}/edit');
  }

  void _deleteDocument(DocumentModel document) {
    showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Document'),
        content: Text('Are you sure you want to delete "${document.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    ).then((confirmed) {
      if (confirmed == true) {
        context.read<DocumentBloc>().add(DocumentDeleteRequested(document.id));
      }
    });
  }

  void _verifyDocument(DocumentModel document, DocumentStatus status) {
    context.read<DocumentBloc>().add(DocumentVerificationRequested(
          id: document.id,
          request: DocumentVerificationRequest(status: status),
        ));
  }

  void _downloadDocument(DocumentModel document) {
    // Use file download instead of URL opening
    context
        .read<DocumentBloc>()
        .add(DocumentFileDownloadRequested(document.id, document.name));
  }

  void _showUploadDialog() {
    context.push('/documents/upload', extra: {
      'companyId': widget.companyId,
      'vehicleId': widget.vehicleId,
      'loadId': widget.loadId,
    });
  }
}
