# Application Configuration
spring.application.name=logipool
server.port=8080

# Database Configuration - H2 for Integration Testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.properties.hibernate.jdbc.lob.non_contextual_creation=true

# JWT Configuration
app.jwt.secret=logipool-secret-key-for-jwt-token-generation-2025
app.jwt.expiration=86400000
app.jwt.refresh-expiration=604800000

# Rate Limiting Configuration (disabled for integration testing)
app.rate-limit.enabled=false
app.rate-limit.requests-per-minute=60
app.rate-limit.requests-per-hour=1000

# Security Configuration
app.security.password.min-length=8
app.security.password.require-uppercase=true
app.security.password.require-lowercase=true
app.security.password.require-numbers=true
app.security.password.require-special-chars=true

# File Upload Configuration
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
app.file.upload-dir=./uploads
app.file.max-size=10485760
app.file.allowed-types=pdf,doc,docx,jpg,jpeg,png,gif,txt,csv,xlsx,xls

# Cloud Storage Configuration
app.cloud.storage.enabled=false
app.cloud.storage.provider=local
app.cloud.storage.bucket=logipool-documents
app.cloud.storage.region=us-east-1

# Mail Configuration
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=${MAIL_USERNAME:}
spring.mail.password=${MAIL_PASSWORD:}
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true

# Redis Configuration (disabled for integration testing)
# spring.data.redis.host=localhost
# spring.data.redis.port=6379
# spring.data.redis.timeout=60000
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration

# API Documentation
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# Notification Configuration
app.notification.email.enabled=true
app.notification.sms.enabled=false
app.notification.realtime.enabled=true
app.notification.persistent.enabled=true
app.base-url=http://localhost:8080

# SMS Configuration (Twilio)
app.sms.provider=mock
app.sms.enabled=false
app.sms.twilio.account-sid=${TWILIO_ACCOUNT_SID:}
app.sms.twilio.auth-token=${TWILIO_AUTH_TOKEN:}
app.sms.twilio.from-number=${TWILIO_FROM_NUMBER:}

# Payment Configuration
app.payment.commission.rate=0.075
app.payment.commission.min=5.00
app.payment.commission.max=500.00
app.payment.gateway.stripe.enabled=false
app.payment.gateway.paypal.enabled=false
app.payment.gateway.mock.enabled=true

# Tracking Configuration
app.tracking.enabled=true
app.tracking.real-time.enabled=true
app.tracking.gps.enabled=true
app.tracking.stale-threshold-hours=6
app.tracking.cleanup.enabled=true
app.tracking.monitoring.enabled=true
app.tracking.location-update-interval-seconds=30
app.tracking.max-location-history=1000

# H2 Console Configuration
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# Logging Configuration
logging.level.zw.co.kanjan.logipool=INFO
logging.level.zw.co.kanjan.logipool.service.TrackingService=INFO
logging.level.zw.co.kanjan.logipool.repository.LoadTrackingRepository=INFO
logging.level.org.springframework.security=WARN
logging.level.org.hibernate.SQL=WARN
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=WARN
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n
