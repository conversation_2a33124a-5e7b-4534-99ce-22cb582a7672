package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Payment;
import zw.co.kanjan.logipool.entity.User;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.Bid;
import zw.co.kanjan.logipool.entity.Company;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface PaymentRepository extends JpaRepository<Payment, Long> {
    
    // Find payments by status
    Page<Payment> findByStatus(Payment.PaymentStatus status, Pageable pageable);
    
    // Find payments by payer
    Page<Payment> findByPayerOrderByCreatedAtDesc(User payer, Pageable pageable);
    
    // Find payments by payee
    Page<Payment> findByPayeeOrderByCreatedAtDesc(User payee, Pageable pageable);
    
    // Find payments by load
    List<Payment> findByLoad(Load load);

    // Check if payment exists for load
    boolean existsByLoad(Load load);

    // Find payments by bid
    List<Payment> findByBid(Bid bid);
    
    // Find payment by transaction ID
    Optional<Payment> findByTransactionId(String transactionId);
    
    // Find payments by date range
    @Query("SELECT p FROM Payment p WHERE p.createdAt BETWEEN :startDate AND :endDate")
    List<Payment> findByDateRange(@Param("startDate") LocalDateTime startDate, 
                                 @Param("endDate") LocalDateTime endDate);
    
    // Find overdue payments
    @Query("SELECT p FROM Payment p WHERE p.dueDate < :now AND p.status = 'PENDING'")
    List<Payment> findOverduePayments(@Param("now") LocalDateTime now);
    
    // Calculate total payments by status
    @Query("SELECT SUM(p.amount) FROM Payment p WHERE p.status = :status")
    BigDecimal getTotalAmountByStatus(@Param("status") Payment.PaymentStatus status);
    
    // Calculate total commission collected
    @Query("SELECT SUM(p.commissionAmount) FROM Payment p WHERE p.status = 'COMPLETED'")
    BigDecimal getTotalCommissionCollected();
    
    // Find payments by method
    List<Payment> findByMethod(Payment.PaymentMethod method);
    
    // Find payments by type
    List<Payment> findByType(Payment.PaymentType type);
    
    // Find pending payments for user
    @Query("SELECT p FROM Payment p WHERE (p.payer = :user OR p.payee = :user) AND p.status = 'PENDING'")
    List<Payment> findPendingPaymentsForUser(@Param("user") User user);
    
    // Count payments by status
    long countByStatus(Payment.PaymentStatus status);
    
    // Find recent payments
    @Query("SELECT p FROM Payment p ORDER BY p.createdAt DESC")
    Page<Payment> findRecentPayments(Pageable pageable);

    // Find payments by company (through load assignment)
    @Query("SELECT p FROM Payment p WHERE p.load.assignedCompany = :company")
    List<Payment> findByLoad_AssignedCompany(@Param("company") Company company);

    // User-specific payment summary queries
    @Query("SELECT SUM(p.amount) FROM Payment p WHERE (p.payer = :user OR p.payee = :user) AND p.status = :status")
    BigDecimal getTotalAmountByUserAndStatus(@Param("user") User user, @Param("status") Payment.PaymentStatus status);

    @Query("SELECT SUM(p.commissionAmount) FROM Payment p WHERE (p.payer = :user OR p.payee = :user) AND p.status = 'COMPLETED'")
    BigDecimal getTotalCommissionByUser(@Param("user") User user);

    @Query("SELECT COUNT(p) FROM Payment p WHERE (p.payer = :user OR p.payee = :user) AND p.status = :status")
    long countByUserAndStatus(@Param("user") User user, @Param("status") Payment.PaymentStatus status);

    @Query("SELECT COUNT(p) FROM Payment p WHERE p.payer = :user OR p.payee = :user")
    long countByUser(@Param("user") User user);
}
