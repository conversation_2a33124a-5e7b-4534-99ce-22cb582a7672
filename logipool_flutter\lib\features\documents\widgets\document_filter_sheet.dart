import 'package:flutter/material.dart';
import '../../../shared/models/document_model.dart';

class DocumentFilterSheet extends StatefulWidget {
  final String? selectedType;
  final String? selectedStatus;
  final String mode;

  const DocumentFilterSheet({
    super.key,
    this.selectedType,
    this.selectedStatus,
    required this.mode,
  });

  @override
  State<DocumentFilterSheet> createState() => _DocumentFilterSheetState();
}

class _DocumentFilterSheetState extends State<DocumentFilterSheet> {
  String? _selectedType;
  String? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.selectedType;
    _selectedStatus = widget.selectedStatus;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 16,
        bottom: MediaQuery.of(context).viewInsets.bottom + 16,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Text(
                'Filter Documents',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Document Type Filter
          Text(
            'Document Type',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildFilterChip(
                label: 'All Types',
                isSelected: _selectedType == null,
                onSelected: () => setState(() => _selectedType = null),
              ),
              ..._getAvailableTypes().map((type) => _buildFilterChip(
                    label: type.displayName,
                    isSelected: _selectedType == _getDocumentTypeValue(type),
                    onSelected: () => setState(
                        () => _selectedType = _getDocumentTypeValue(type)),
                  )),
            ],
          ),

          const SizedBox(height: 24),

          // Document Status Filter
          Text(
            'Status',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildFilterChip(
                label: 'All Statuses',
                isSelected: _selectedStatus == null,
                onSelected: () => setState(() => _selectedStatus = null),
              ),
              ...DocumentStatus.values.map((status) => _buildFilterChip(
                    label: status.displayName,
                    isSelected:
                        _selectedStatus == _getDocumentStatusValue(status),
                    onSelected: () => setState(() =>
                        _selectedStatus = _getDocumentStatusValue(status)),
                  )),
            ],
          ),

          const SizedBox(height: 32),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _selectedType = null;
                      _selectedStatus = null;
                    });
                  },
                  child: const Text('Clear All'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: FilledButton(
                  onPressed: () {
                    Navigator.of(context).pop({
                      'type': _selectedType,
                      'status': _selectedStatus,
                    });
                  },
                  child: const Text('Apply Filters'),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onSelected,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (_) => onSelected(),
      selectedColor: colorScheme.primaryContainer,
      checkmarkColor: colorScheme.onPrimaryContainer,
      labelStyle: TextStyle(
        color: isSelected
            ? colorScheme.onPrimaryContainer
            : colorScheme.onSurfaceVariant,
        fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
      ),
      side: BorderSide(
        color: isSelected ? colorScheme.primary : colorScheme.outline,
      ),
    );
  }

  List<DocumentType> _getAvailableTypes() {
    // Filter document types based on the current mode
    switch (widget.mode) {
      case 'company':
        return DocumentType.values
            .where((type) => type.isCompanyDocument)
            .toList();
      case 'vehicle':
        return DocumentType.values
            .where((type) => type.isVehicleDocument)
            .toList();
      case 'load':
        return DocumentType.values
            .where((type) => type.isLoadDocument)
            .toList();
      default:
        return DocumentType.values;
    }
  }

  // Helper method to get the correct string value for DocumentType enum
  String _getDocumentTypeValue(DocumentType type) {
    switch (type) {
      case DocumentType.companyRegistration:
        return 'COMPANY_REGISTRATION';
      case DocumentType.taxClearance:
        return 'TAX_CLEARANCE';
      case DocumentType.businessLicense:
        return 'BUSINESS_LICENSE';
      case DocumentType.insuranceCertificate:
        return 'INSURANCE_CERTIFICATE';
      case DocumentType.vehicleRegistration:
        return 'VEHICLE_REGISTRATION';
      case DocumentType.fitnessCertificate:
        return 'FITNESS_CERTIFICATE';
      case DocumentType.roadPermit:
        return 'ROAD_PERMIT';
      case DocumentType.zinaraPermit:
        return 'ZINARA_PERMIT';
      case DocumentType.vehicleInsurance:
        return 'VEHICLE_INSURANCE';
      case DocumentType.vehiclePhotos:
        return 'VEHICLE_PHOTOS';
      case DocumentType.proofOfDelivery:
        return 'PROOF_OF_DELIVERY';
      case DocumentType.invoice:
        return 'INVOICE';
      case DocumentType.contract:
        return 'CONTRACT';
      case DocumentType.waybill:
        return 'WAYBILL';
      case DocumentType.customsDeclaration:
        return 'CUSTOMS_DECLARATION';
      case DocumentType.profilePhoto:
        return 'PROFILE_PHOTO';
      case DocumentType.other:
        return 'OTHER';
    }
  }

  // Helper method to get the correct string value for DocumentStatus enum
  String _getDocumentStatusValue(DocumentStatus status) {
    switch (status) {
      case DocumentStatus.pending:
        return 'PENDING';
      case DocumentStatus.verified:
        return 'VERIFIED';
      case DocumentStatus.rejected:
        return 'REJECTED';
      case DocumentStatus.expired:
        return 'EXPIRED';
    }
  }
}
