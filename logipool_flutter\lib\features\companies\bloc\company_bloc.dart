import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';

import '../../../shared/models/company_model.dart';
import '../../../shared/services/company_service.dart';
import '../../../shared/utils/pagination_model.dart';

// Events
abstract class CompanyEvent extends Equatable {
  const CompanyEvent();

  @override
  List<Object?> get props => [];
}

class CompanyCreateRequested extends CompanyEvent {
  final CompanyCreateRequest request;

  const CompanyCreateRequested({required this.request});

  @override
  List<Object?> get props => [request];
}

class CompanyUpdateRequested extends CompanyEvent {
  final int companyId;
  final CompanyUpdateRequest request;

  const CompanyUpdateRequested({
    required this.companyId,
    required this.request,
  });

  @override
  List<Object?> get props => [companyId, request];
}

class CompanyFetchByIdRequested extends CompanyEvent {
  final int companyId;

  const CompanyFetchByIdRequested({required this.companyId});

  @override
  List<Object?> get props => [companyId];
}

class MyCompanyFetchRequested extends CompanyEvent {
  const MyCompanyFetchRequested();
}

class CompaniesListRequested extends CompanyEvent {
  final int page;
  final int size;
  final String sortBy;
  final String sortDir;

  const CompaniesListRequested({
    this.page = 0,
    this.size = 10,
    this.sortBy = 'name',
    this.sortDir = 'asc',
  });

  @override
  List<Object?> get props => [page, size, sortBy, sortDir];
}

class CompaniesLoadMoreRequested extends CompanyEvent {
  const CompaniesLoadMoreRequested();
}

class CompaniesSearchRequested extends CompanyEvent {
  final String query;
  final int page;
  final int size;

  const CompaniesSearchRequested({
    required this.query,
    this.page = 0,
    this.size = 10,
  });

  @override
  List<Object?> get props => [query, page, size];
}

class CompaniesFilterByStatusRequested extends CompanyEvent {
  final VerificationStatus status;
  final int page;
  final int size;

  const CompaniesFilterByStatusRequested({
    required this.status,
    this.page = 0,
    this.size = 10,
  });

  @override
  List<Object?> get props => [status, page, size];
}

class CompanyVerificationRequested extends CompanyEvent {
  final int companyId;
  final VerificationStatus status;

  const CompanyVerificationRequested({
    required this.companyId,
    required this.status,
  });

  @override
  List<Object?> get props => [companyId, status];
}

class CompanyDeleteRequested extends CompanyEvent {
  final int companyId;

  const CompanyDeleteRequested({required this.companyId});

  @override
  List<Object?> get props => [companyId];
}

// States
abstract class CompanyState extends Equatable {
  const CompanyState();

  @override
  List<Object?> get props => [];
}

class CompanyInitial extends CompanyState {
  const CompanyInitial();
}

class CompanyLoading extends CompanyState {
  const CompanyLoading();
}

class CompanySuccess extends CompanyState {
  final CompanyModel company;

  const CompanySuccess({required this.company});

  @override
  List<Object?> get props => [company];
}

class CompanyLoaded extends CompanyState {
  final CompanyModel company;

  const CompanyLoaded({required this.company});

  @override
  List<Object?> get props => [company];
}

class CompanyUpdated extends CompanyState {
  final CompanyModel company;

  const CompanyUpdated({required this.company});

  @override
  List<Object?> get props => [company];
}

class MyCompanyLoaded extends CompanyState {
  final CompanyModel? company;

  const MyCompanyLoaded({this.company});

  @override
  List<Object?> get props => [company];
}

class CompaniesListLoaded extends CompanyState {
  final List<CompanyModel> companies;
  final bool hasReachedMax;
  final int currentPage;
  final String? searchQuery;
  final VerificationStatus? filterStatus;

  const CompaniesListLoaded({
    required this.companies,
    required this.hasReachedMax,
    required this.currentPage,
    this.searchQuery,
    this.filterStatus,
  });

  @override
  List<Object?> get props => [
        companies,
        hasReachedMax,
        currentPage,
        searchQuery,
        filterStatus,
      ];

  CompaniesListLoaded copyWith({
    List<CompanyModel>? companies,
    bool? hasReachedMax,
    int? currentPage,
    String? searchQuery,
    VerificationStatus? filterStatus,
  }) {
    return CompaniesListLoaded(
      companies: companies ?? this.companies,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      searchQuery: searchQuery ?? this.searchQuery,
      filterStatus: filterStatus ?? this.filterStatus,
    );
  }
}

class CompanyOperationSuccess extends CompanyState {
  final String message;
  final CompanyModel? company;

  const CompanyOperationSuccess({
    required this.message,
    this.company,
  });

  @override
  List<Object?> get props => [message, company];
}

class CompanyError extends CompanyState {
  final String message;

  const CompanyError({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class CompanyBloc extends Bloc<CompanyEvent, CompanyState> {
  final CompanyService _companyService;

  CompanyBloc({CompanyService? companyService})
      : _companyService = companyService ?? CompanyService(),
        super(const CompanyInitial()) {
    on<CompanyCreateRequested>(_onCompanyCreateRequested);
    on<CompanyUpdateRequested>(_onCompanyUpdateRequested);
    on<CompanyFetchByIdRequested>(_onCompanyFetchByIdRequested);
    on<MyCompanyFetchRequested>(_onMyCompanyFetchRequested);
    on<CompaniesListRequested>(_onCompaniesListRequested);
    on<CompaniesLoadMoreRequested>(_onCompaniesLoadMoreRequested);
    on<CompaniesSearchRequested>(_onCompaniesSearchRequested);
    on<CompaniesFilterByStatusRequested>(_onCompaniesFilterByStatusRequested);
    on<CompanyVerificationRequested>(_onCompanyVerificationRequested);
    on<CompanyDeleteRequested>(_onCompanyDeleteRequested);
  }

  Future<void> _onCompanyCreateRequested(
    CompanyCreateRequested event,
    Emitter<CompanyState> emit,
  ) async {
    emit(const CompanyLoading());
    try {
      final company = await _companyService.createCompany(event.request);
      emit(CompanyOperationSuccess(
        message: 'Company created successfully',
        company: company,
      ));
    } catch (e) {
      debugPrint('Error creating company: $e');
      emit(CompanyError(message: e.toString()));
    }
  }

  Future<void> _onCompanyUpdateRequested(
    CompanyUpdateRequested event,
    Emitter<CompanyState> emit,
  ) async {
    emit(const CompanyLoading());
    try {
      final company = await _companyService.updateCompany(
        event.companyId,
        event.request,
      );
      emit(CompanyOperationSuccess(
        message: 'Company updated successfully',
        company: company,
      ));
    } catch (e) {
      debugPrint('Error updating company: $e');
      emit(CompanyError(message: e.toString()));
    }
  }

  Future<void> _onCompanyFetchByIdRequested(
    CompanyFetchByIdRequested event,
    Emitter<CompanyState> emit,
  ) async {
    emit(const CompanyLoading());
    try {
      final company = await _companyService.getCompanyById(event.companyId);
      emit(CompanySuccess(company: company));
    } catch (e) {
      debugPrint('Error fetching company: $e');
      emit(CompanyError(message: e.toString()));
    }
  }

  Future<void> _onMyCompanyFetchRequested(
    MyCompanyFetchRequested event,
    Emitter<CompanyState> emit,
  ) async {
    emit(const CompanyLoading());
    try {
      final company = await _companyService.getMyCompany();
      emit(MyCompanyLoaded(company: company));
    } catch (e) {
      debugPrint('Error fetching my company: $e');
      emit(CompanyError(message: e.toString()));
    }
  }

  Future<void> _onCompaniesListRequested(
    CompaniesListRequested event,
    Emitter<CompanyState> emit,
  ) async {
    emit(const CompanyLoading());
    try {
      final result = await _companyService.getAllCompanies(
        page: event.page,
        size: event.size,
        sortBy: event.sortBy,
        sortDir: event.sortDir,
      );

      emit(CompaniesListLoaded(
        companies: result.data,
        hasReachedMax: result.isLast,
        currentPage: result.currentPage,
      ));
    } catch (e) {
      debugPrint('Error fetching companies: $e');
      emit(CompanyError(message: e.toString()));
    }
  }

  Future<void> _onCompaniesLoadMoreRequested(
    CompaniesLoadMoreRequested event,
    Emitter<CompanyState> emit,
  ) async {
    final currentState = state;
    if (currentState is! CompaniesListLoaded || currentState.hasReachedMax) {
      return;
    }

    try {
      final nextPage = currentState.currentPage + 1;
      PaginationModel<CompanyModel> result;

      if (currentState.searchQuery != null) {
        result = await _companyService.searchCompaniesByName(
          currentState.searchQuery!,
          page: nextPage,
        );
      } else if (currentState.filterStatus != null) {
        result = await _companyService.getCompaniesByVerificationStatus(
          currentState.filterStatus!,
          page: nextPage,
        );
      } else {
        result = await _companyService.getAllCompanies(page: nextPage);
      }

      emit(currentState.copyWith(
        companies: [...currentState.companies, ...result.data],
        hasReachedMax: result.isLast,
        currentPage: result.currentPage,
      ));
    } catch (e) {
      debugPrint('Error loading more companies: $e');
      emit(CompanyError(message: e.toString()));
    }
  }

  Future<void> _onCompaniesSearchRequested(
    CompaniesSearchRequested event,
    Emitter<CompanyState> emit,
  ) async {
    emit(const CompanyLoading());
    try {
      final result = await _companyService.searchCompaniesByName(
        event.query,
        page: event.page,
        size: event.size,
      );

      emit(CompaniesListLoaded(
        companies: result.data,
        hasReachedMax: result.isLast,
        currentPage: result.currentPage,
        searchQuery: event.query,
      ));
    } catch (e) {
      debugPrint('Error searching companies: $e');
      emit(CompanyError(message: e.toString()));
    }
  }

  Future<void> _onCompaniesFilterByStatusRequested(
    CompaniesFilterByStatusRequested event,
    Emitter<CompanyState> emit,
  ) async {
    emit(const CompanyLoading());
    try {
      final result = await _companyService.getCompaniesByVerificationStatus(
        event.status,
        page: event.page,
        size: event.size,
      );

      emit(CompaniesListLoaded(
        companies: result.data,
        hasReachedMax: result.isLast,
        currentPage: result.currentPage,
        filterStatus: event.status,
      ));
    } catch (e) {
      debugPrint('Error filtering companies: $e');
      emit(CompanyError(message: e.toString()));
    }
  }

  Future<void> _onCompanyVerificationRequested(
    CompanyVerificationRequested event,
    Emitter<CompanyState> emit,
  ) async {
    try {
      final company = await _companyService.verifyCompany(
        event.companyId,
        event.status,
      );
      emit(CompanyOperationSuccess(
        message: 'Company verification status updated',
        company: company,
      ));
    } catch (e) {
      debugPrint('Error verifying company: $e');
      emit(CompanyError(message: e.toString()));
    }
  }

  Future<void> _onCompanyDeleteRequested(
    CompanyDeleteRequested event,
    Emitter<CompanyState> emit,
  ) async {
    try {
      await _companyService.deleteCompany(event.companyId);
      emit(const CompanyOperationSuccess(
        message: 'Company deleted successfully',
      ));
    } catch (e) {
      debugPrint('Error deleting company: $e');
      emit(CompanyError(message: e.toString()));
    }
  }
}
