import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../shared/models/company_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/enums/user_role.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/widgets/unified_header.dart';
import '../../auth/bloc/auth_bloc.dart';
import '../bloc/company_bloc.dart';
import '../widgets/company_card.dart';

class CompanyDirectoryScreen extends StatefulWidget {
  const CompanyDirectoryScreen({super.key});

  @override
  State<CompanyDirectoryScreen> createState() => _CompanyDirectoryScreenState();
}

class _CompanyDirectoryScreenState extends State<CompanyDirectoryScreen> {
  final _searchController = TextEditingController();
  final _scrollController = ScrollController();
  VerificationStatus? _selectedStatus;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    context.read<CompanyBloc>().add(const CompaniesListRequested());
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<CompanyBloc>().add(const CompaniesLoadMoreRequested());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedHeader(
        title: 'Company Directory',
        onLogoutPressed: () => _showLogoutDialog(),
        actions: [
          FutureBuilder<UserModel?>(
            future: context.read<AuthService>().getCurrentUser(),
            builder: (context, snapshot) {
              final user = snapshot.data;
              if (user?.role == UserRole.admin.name) {
                return IconButton(
                  icon: Icon(
                    Icons.admin_panel_settings,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                  onPressed: () => context.push('/admin/companies'),
                  tooltip: 'Admin Panel',
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchAndFilter(context),
          Expanded(
            child: BlocConsumer<CompanyBloc, CompanyState>(
              listener: (context, state) {
                if (state is CompanyError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              builder: (context, state) {
                if (state is CompanyLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state is CompaniesListLoaded) {
                  return _buildCompanyList(context, state);
                }

                if (state is CompanyError) {
                  return _buildErrorView(context, state.message);
                }

                return const Center(child: CircularProgressIndicator());
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilter(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Search companies...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _isSearching
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: _clearSearch,
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  onChanged: _onSearchChanged,
                  onSubmitted: _onSearchSubmitted,
                ),
              ),
              const SizedBox(width: 12),
              PopupMenuButton<VerificationStatus?>(
                icon: Icon(
                  Icons.filter_list,
                  color: _selectedStatus != null
                      ? theme.colorScheme.primary
                      : null,
                ),
                tooltip: 'Filter by verification status',
                onSelected: _onFilterSelected,
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: null,
                    child: Text('All Companies'),
                  ),
                  ...VerificationStatus.values.map(
                    (status) => PopupMenuItem(
                      value: status,
                      child: Row(
                        children: [
                          Icon(
                            _getStatusIcon(status),
                            color: _getStatusColor(status),
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(status.displayName),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          if (_selectedStatus != null || _isSearching) ...[
            const SizedBox(height: 12),
            Row(
              children: [
                if (_isSearching)
                  Chip(
                    label: Text('Search: "${_searchController.text}"'),
                    onDeleted: _clearSearch,
                    deleteIcon: const Icon(Icons.close, size: 18),
                  ),
                if (_selectedStatus != null) ...[
                  if (_isSearching) const SizedBox(width: 8),
                  Chip(
                    label: Text('Status: ${_selectedStatus!.displayName}'),
                    onDeleted: () => _onFilterSelected(null),
                    deleteIcon: const Icon(Icons.close, size: 18),
                  ),
                ],
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCompanyList(BuildContext context, CompaniesListLoaded state) {
    if (state.companies.isEmpty) {
      return _buildEmptyView(context);
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: state.hasReachedMax
            ? state.companies.length
            : state.companies.length + 1,
        itemBuilder: (context, index) {
          if (index >= state.companies.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          final company = state.companies[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: CompanyCard(
              company: company,
              onTap: () => _onCompanyTap(context, company),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyView(BuildContext context) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.business_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _isSearching || _selectedStatus != null
                  ? 'No Companies Found'
                  : 'No Companies Available',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _isSearching || _selectedStatus != null
                  ? 'Try adjusting your search or filter criteria'
                  : 'No companies have been registered yet',
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            if (_isSearching || _selectedStatus != null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () {
                  _clearSearch();
                  _onFilterSelected(null);
                },
                icon: const Icon(Icons.clear_all),
                label: const Text('Clear Filters'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, String message) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Colors.red[300],
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Companies',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _onRefresh,
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  void _onSearchChanged(String value) {
    setState(() {
      _isSearching = value.isNotEmpty;
    });
  }

  void _onSearchSubmitted(String value) {
    if (value.trim().isNotEmpty) {
      context.read<CompanyBloc>().add(
            CompaniesSearchRequested(query: value.trim()),
          );
    } else {
      _clearSearch();
    }
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _isSearching = false;
    });
    context.read<CompanyBloc>().add(const CompaniesListRequested());
  }

  void _onFilterSelected(VerificationStatus? status) {
    setState(() {
      _selectedStatus = status;
    });

    if (status != null) {
      context.read<CompanyBloc>().add(
            CompaniesFilterByStatusRequested(status: status),
          );
    } else {
      if (_isSearching) {
        _onSearchSubmitted(_searchController.text);
      } else {
        context.read<CompanyBloc>().add(const CompaniesListRequested());
      }
    }
  }

  Future<void> _onRefresh() async {
    if (_isSearching) {
      context.read<CompanyBloc>().add(
            CompaniesSearchRequested(query: _searchController.text.trim()),
          );
    } else if (_selectedStatus != null) {
      context.read<CompanyBloc>().add(
            CompaniesFilterByStatusRequested(status: _selectedStatus!),
          );
    } else {
      context.read<CompanyBloc>().add(const CompaniesListRequested());
    }
  }

  void _onCompanyTap(BuildContext context, CompanyModel company) {
    context.push('/companies/detail/${company.id}');
  }

  IconData _getStatusIcon(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.verified:
        return Icons.verified;
      case VerificationStatus.pending:
        return Icons.pending;
      case VerificationStatus.rejected:
        return Icons.cancel;
      case VerificationStatus.suspended:
        return Icons.block;
    }
  }

  Color _getStatusColor(VerificationStatus status) {
    switch (status) {
      case VerificationStatus.verified:
        return Colors.green;
      case VerificationStatus.pending:
        return Colors.orange;
      case VerificationStatus.rejected:
        return Colors.red;
      case VerificationStatus.suspended:
        return Colors.red;
    }
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AuthBloc>().add(AuthLogoutRequested());
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}
