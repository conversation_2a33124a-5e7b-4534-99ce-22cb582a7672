package zw.co.kanjan.logipool.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import zw.co.kanjan.logipool.dto.bid.BidCreateRequest;
import zw.co.kanjan.logipool.dto.bid.BidResponse;
import zw.co.kanjan.logipool.entity.*;
import zw.co.kanjan.logipool.exception.BusinessException;
import zw.co.kanjan.logipool.exception.ResourceNotFoundException;
import zw.co.kanjan.logipool.mapper.BidMapper;
import zw.co.kanjan.logipool.repository.BidRepository;
import zw.co.kanjan.logipool.repository.LoadRepository;
import zw.co.kanjan.logipool.repository.UserRepository;
import zw.co.kanjan.logipool.repository.CompanyRepository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BidServiceTest {

    @Mock
    private BidRepository bidRepository;

    @Mock
    private LoadRepository loadRepository;

    @Mock
    private UserRepository userRepository;

    @Mock
    private CompanyRepository companyRepository;

    @Mock
    private BidMapper bidMapper;

    @Mock
    private NotificationService notificationService;

    @InjectMocks
    private BidService bidService;

    private User transporterUser;
    private User clientUser;
    private Company transporterCompany;
    private Load testLoad;
    private Bid testBid;
    private BidCreateRequest bidRequest;
    private BidResponse bidResponse;

    @BeforeEach
    void setUp() {
        // Setup transporter user
        Role transporterRole = new Role();
        transporterRole.setName(Role.RoleName.TRANSPORTER);
        Set<Role> transporterRoles = new HashSet<>();
        transporterRoles.add(transporterRole);

        transporterUser = new User();
        transporterUser.setId(1L);
        transporterUser.setUsername("transporter");
        transporterUser.setEmail("<EMAIL>");
        transporterUser.setFirstName("Test");
        transporterUser.setLastName("Transporter");
        transporterUser.setRoles(transporterRoles);

        // Setup client user
        Role clientRole = new Role();
        clientRole.setName(Role.RoleName.CLIENT);
        Set<Role> clientRoles = new HashSet<>();
        clientRoles.add(clientRole);

        clientUser = new User();
        clientUser.setId(2L);
        clientUser.setUsername("client");
        clientUser.setEmail("<EMAIL>");
        clientUser.setFirstName("Test");
        clientUser.setLastName("Client");
        clientUser.setRoles(clientRoles);

        // Setup transporter company
        transporterCompany = new Company();
        transporterCompany.setId(1L);
        transporterCompany.setName("Transport Company");
        transporterCompany.setRegistrationNumber("TRANS123");
        transporterCompany.setUser(transporterUser);
        transporterCompany.setVerificationStatus(Company.VerificationStatus.VERIFIED);

        // Setup test load
        testLoad = new Load();
        testLoad.setId(1L);
        testLoad.setTitle("Test Load");
        testLoad.setDescription("Test load description");
        testLoad.setPickupLocation("Pickup Location");
        testLoad.setDeliveryLocation("Delivery Location");
        testLoad.setEstimatedValue(BigDecimal.valueOf(1000));
        testLoad.setPaymentRate(BigDecimal.valueOf(500));
        testLoad.setWeight(BigDecimal.valueOf(100));
        testLoad.setStatus(Load.LoadStatus.POSTED);
        testLoad.setClient(clientUser);
        testLoad.setCreatedAt(LocalDateTime.now());

        // Setup test bid
        testBid = new Bid();
        testBid.setId(1L);
        testBid.setLoad(testLoad);
        testBid.setCompany(transporterCompany);
        testBid.setAmount(BigDecimal.valueOf(450));
        testBid.setEstimatedPickupTime(LocalDateTime.now().plusDays(1));
        testBid.setEstimatedDeliveryTime(LocalDateTime.now().plusDays(3));
        testBid.setProposal("Competitive bid for your load");
        testBid.setStatus(Bid.BidStatus.PENDING);
        testBid.setCreatedAt(LocalDateTime.now());

        // Setup DTOs
        bidRequest = new BidCreateRequest();
        bidRequest.setLoadId(1L);
        bidRequest.setAmount(BigDecimal.valueOf(450));
        bidRequest.setEstimatedPickupTime(LocalDateTime.now().plusDays(1));
        bidRequest.setEstimatedDeliveryTime(LocalDateTime.now().plusDays(3));
        bidRequest.setProposal("Competitive bid for your load");

        bidResponse = new BidResponse();
        bidResponse.setId(1L);
        bidResponse.setLoadId(1L);
        bidResponse.setLoadTitle("Test Load");
        bidResponse.setCompanyId(1L);
        bidResponse.setCompanyName("Transport Company");
        bidResponse.setAmount(BigDecimal.valueOf(450));
        bidResponse.setEstimatedPickupTime(LocalDateTime.now().plusDays(1));
        bidResponse.setEstimatedDeliveryTime(LocalDateTime.now().plusDays(3));
        bidResponse.setProposal("Competitive bid for your load");
        bidResponse.setStatus(Bid.BidStatus.PENDING);
        bidResponse.setCreatedAt(LocalDateTime.now());
    }

    @Test
    void createBid_WithValidData_ShouldCreateBid() {
        // Arrange
        when(userRepository.findByUsername("transporter")).thenReturn(Optional.of(transporterUser));
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(companyRepository.findByUser(transporterUser)).thenReturn(Optional.of(transporterCompany));
        when(bidRepository.existsByLoadAndCompany(testLoad, transporterCompany)).thenReturn(false);
        when(bidMapper.toEntity(bidRequest)).thenReturn(testBid);
        when(bidRepository.save(any(Bid.class))).thenReturn(testBid);
        when(bidMapper.toResponse(testBid)).thenReturn(bidResponse);

        // Act
        BidResponse result = bidService.createBid(bidRequest, "transporter");

        // Assert
        assertNotNull(result);
        assertEquals(BigDecimal.valueOf(450), result.getAmount());
        assertEquals(Bid.BidStatus.PENDING, result.getStatus());
        verify(bidRepository).save(any(Bid.class));
    }

    @Test
    void createBid_WithInvalidTransporter_ShouldThrowException() {
        // Arrange
        when(userRepository.findByUsername("invaliduser")).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> {
            bidService.createBid(bidRequest, "invaliduser");
        });
        verify(bidRepository, never()).save(any());
    }

    @Test
    void createBid_WithInvalidLoad_ShouldThrowException() {
        // Arrange
        when(userRepository.findByUsername("transporter")).thenReturn(Optional.of(transporterUser));
        when(loadRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> {
            bidService.createBid(bidRequest, "transporter");
        });
        verify(bidRepository, never()).save(any());
    }

    @Test
    void createBid_OnCompletedLoad_ShouldThrowException() {
        // Arrange
        testLoad.setStatus(Load.LoadStatus.DELIVERED);
        when(userRepository.findByUsername("transporter")).thenReturn(Optional.of(transporterUser));
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));

        // Act & Assert
        assertThrows(BusinessException.class, () -> {
            bidService.createBid(bidRequest, "transporter");
        });
        verify(bidRepository, never()).save(any());
    }

    @Test
    void getBidById_WithValidId_ShouldReturnBid() {
        // Arrange
        when(bidRepository.findById(1L)).thenReturn(Optional.of(testBid));
        when(bidMapper.toResponse(testBid)).thenReturn(bidResponse);

        // Act
        BidResponse result = bidService.getBidById(1L);

        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals(BigDecimal.valueOf(450), result.getAmount());
    }

    @Test
    void getBidById_WithInvalidId_ShouldThrowException() {
        // Arrange
        when(bidRepository.findById(1L)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(ResourceNotFoundException.class, () -> {
            bidService.getBidById(1L);
        });
    }

    @Test
    void getBidsForLoad_ShouldReturnPagedBids() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Bid> bidPage = new PageImpl<>(Arrays.asList(testBid), pageable, 1);
        when(loadRepository.findById(1L)).thenReturn(Optional.of(testLoad));
        when(bidRepository.findByLoadOrderByCreatedAtDesc(testLoad, pageable)).thenReturn(bidPage);
        when(bidMapper.toResponse(testBid)).thenReturn(bidResponse);

        // Act
        Page<BidResponse> result = bidService.getBidsForLoad(1L, pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals(BigDecimal.valueOf(450), result.getContent().get(0).getAmount());
    }

    @Test
    void withdrawBid_WithValidData_ShouldWithdrawBid() {
        // Arrange
        when(bidRepository.findById(1L)).thenReturn(Optional.of(testBid));
        when(bidRepository.save(any(Bid.class))).thenReturn(testBid);

        // Act
        bidService.withdrawBid(1L, "transporter");

        // Assert
        assertEquals(Bid.BidStatus.WITHDRAWN, testBid.getStatus());
        verify(bidRepository).save(testBid);
    }

    @Test
    void withdrawBid_WithUnauthorizedUser_ShouldThrowException() {
        // Arrange
        when(bidRepository.findById(1L)).thenReturn(Optional.of(testBid));

        // Act & Assert
        assertThrows(BusinessException.class, () -> {
            bidService.withdrawBid(1L, "wronguser");
        });
    }

    @Test
    void getMyBids_ShouldReturnPagedBids() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Bid> bidPage = new PageImpl<>(Arrays.asList(testBid), pageable, 1);
        when(userRepository.findByUsername("transporter")).thenReturn(Optional.of(transporterUser));
        when(companyRepository.findByUser(transporterUser)).thenReturn(Optional.of(transporterCompany));
        when(bidRepository.findByCompanyOrderByCreatedAtDesc(transporterCompany, pageable)).thenReturn(bidPage);
        when(bidMapper.toResponse(testBid)).thenReturn(bidResponse);

        // Act
        Page<BidResponse> result = bidService.getMyBids("transporter", pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(1, result.getContent().size());
        assertEquals(BigDecimal.valueOf(450), result.getContent().get(0).getAmount());
    }
}
