import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image_picker/image_picker.dart';
import '../../../shared/models/document_model.dart';
import '../../../shared/widgets/app_bar.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/services/cross_platform_file_service.dart';
import '../bloc/document_bloc.dart';

class DocumentUploadScreen extends StatefulWidget {
  final String? source; // 'camera', 'gallery', 'file'
  final int? companyId;
  final int? vehicleId;
  final int? loadId;

  const DocumentUploadScreen({
    super.key,
    this.source,
    this.companyId,
    this.vehicleId,
    this.loadId,
  });

  @override
  State<DocumentUploadScreen> createState() => _DocumentUploadScreenState();
}

class _DocumentUploadScreenState extends State<DocumentUploadScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _expiryDateController = TextEditingController();

  CrossPlatformFile? _selectedFile;
  DocumentType? _selectedType;
  DateTime? _expiryDate;
  bool _isUploading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.source != null) {
        _pickFile(widget.source!);
      }
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _expiryDateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const LogiPoolAppBar(
        title: 'Upload Document',
      ),
      body: BlocListener<DocumentBloc, DocumentState>(
        listener: (context, state) {
          if (state is DocumentUploading) {
            setState(() => _isUploading = true);
          } else if (state is DocumentUploaded) {
            setState(() => _isUploading = false);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Document uploaded successfully'),
                backgroundColor: Colors.green,
              ),
            );
            context.pop();
          } else if (state is DocumentError) {
            setState(() => _isUploading = false);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Theme.of(context).colorScheme.error,
              ),
            );
          }
        },
        child: _isUploading
            ? const LoadingWidget(message: 'Uploading document...')
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // File selection
                      _buildFileSelection(),
                      const SizedBox(height: 24),

                      // Document name
                      TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Document Name *',
                          hintText: 'Enter document name',
                          border: OutlineInputBorder(),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Document name is required';
                          }
                          if (value.trim().length < 3) {
                            return 'Document name must be at least 3 characters';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Document type
                      DropdownButtonFormField<DocumentType>(
                        value: _selectedType,
                        decoration: const InputDecoration(
                          labelText: 'Document Type *',
                          border: OutlineInputBorder(),
                        ),
                        items: _getAvailableTypes().map((type) {
                          return DropdownMenuItem(
                            value: type,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(type.displayName),
                                Text(
                                  type.description,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodySmall
                                      ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurfaceVariant,
                                      ),
                                ),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() => _selectedType = value);
                        },
                        validator: (value) {
                          if (value == null) {
                            return 'Document type is required';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),

                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        decoration: const InputDecoration(
                          labelText: 'Description',
                          hintText: 'Enter document description (optional)',
                          border: OutlineInputBorder(),
                        ),
                        maxLines: 3,
                        maxLength: 500,
                      ),
                      const SizedBox(height: 16),

                      // Expiry date
                      TextFormField(
                        controller: _expiryDateController,
                        decoration: const InputDecoration(
                          labelText: 'Expiry Date',
                          hintText: 'Select expiry date (optional)',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(Icons.calendar_today),
                        ),
                        readOnly: true,
                        onTap: _selectExpiryDate,
                      ),
                      const SizedBox(height: 32),

                      // Upload button
                      SizedBox(
                        width: double.infinity,
                        child: FilledButton.icon(
                          onPressed:
                              _selectedFile != null ? _uploadDocument : null,
                          icon: const Icon(Icons.upload),
                          label: const Text('Upload Document'),
                          style: FilledButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildFileSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Selected File',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            const SizedBox(height: 12),
            if (_selectedFile != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getFileIcon(),
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _selectedFile!.name,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w500,
                                ),
                          ),
                          Text(
                            CrossPlatformFileService.getFileSizeString(
                                _selectedFile!.size),
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurfaceVariant,
                                    ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => setState(() => _selectedFile = null),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _pickFile('camera'),
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Camera'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _pickFile('gallery'),
                    icon: const Icon(Icons.photo_library),
                    label: const Text('Gallery'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _pickFile('file'),
                    icon: const Icon(Icons.folder),
                    label: const Text('File'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _pickFile(String source) async {
    try {
      CrossPlatformFile? file;

      switch (source) {
        case 'camera':
          // For camera, we'll use image picker and convert to CrossPlatformFile
          final picker = ImagePicker();
          final pickedFile = await picker.pickImage(source: ImageSource.camera);
          if (pickedFile != null) {
            final bytes = await pickedFile.readAsBytes();
            file = CrossPlatformFile(
              name: pickedFile.name,
              bytes: bytes,
              size: bytes.length,
              mimeType: 'image/jpeg',
            );
          }
          break;

        case 'gallery':
          final picker = ImagePicker();
          final pickedFile =
              await picker.pickImage(source: ImageSource.gallery);
          if (pickedFile != null) {
            final bytes = await pickedFile.readAsBytes();
            file = CrossPlatformFile(
              name: pickedFile.name,
              bytes: bytes,
              size: bytes.length,
              mimeType: 'image/jpeg',
            );
          }
          break;

        case 'file':
          file = await CrossPlatformFileService.pickLogiPoolDocument();
          break;
      }

      if (file != null) {
        setState(() {
          _selectedFile = file;
          // Auto-populate name if empty
          if (_nameController.text.isEmpty) {
            _nameController.text = file!.name;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error selecting file: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _selectExpiryDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _expiryDate ?? DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)), // 10 years
    );

    if (date != null) {
      setState(() {
        _expiryDate = date;
        _expiryDateController.text = '${date.day}/${date.month}/${date.year}';
      });
    }
  }

  void _uploadDocument() {
    if (!_formKey.currentState!.validate() || _selectedFile == null) {
      return;
    }

    final request = DocumentUploadRequest(
      name: _nameController.text.trim(),
      type: _selectedType!,
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
      expiryDate: _expiryDate,
      companyId: widget.companyId,
      vehicleId: widget.vehicleId,
      loadId: widget.loadId,
    );

    context.read<DocumentBloc>().add(DocumentUploadRequested(
          file: _selectedFile!,
          request: request,
        ));
  }

  List<DocumentType> _getAvailableTypes() {
    // Filter document types based on context
    if (widget.companyId != null) {
      return DocumentType.values
          .where((type) => type.isCompanyDocument)
          .toList();
    } else if (widget.vehicleId != null) {
      return DocumentType.values
          .where((type) => type.isVehicleDocument)
          .toList();
    } else if (widget.loadId != null) {
      return DocumentType.values.where((type) => type.isLoadDocument).toList();
    }
    return DocumentType.values;
  }

  IconData _getFileIcon() {
    if (_selectedFile == null) return Icons.insert_drive_file;

    final fileName = _selectedFile!.name.toLowerCase();

    if (fileName.endsWith('.pdf')) {
      return Icons.picture_as_pdf;
    } else if (fileName.endsWith('.jpg') ||
        fileName.endsWith('.jpeg') ||
        fileName.endsWith('.png')) {
      return Icons.image;
    } else if (fileName.endsWith('.doc') || fileName.endsWith('.docx')) {
      return Icons.description;
    } else {
      return Icons.insert_drive_file;
    }
  }
}
