import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/load_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../bloc/load_bloc.dart';

class LoadStatusUpdateCard extends StatefulWidget {
  final LoadModel load;

  const LoadStatusUpdateCard({
    super.key,
    required this.load,
  });

  @override
  State<LoadStatusUpdateCard> createState() => _LoadStatusUpdateCardState();
}

class _LoadStatusUpdateCardState extends State<LoadStatusUpdateCard> {
  final _notesController = TextEditingController();
  LoadStatus? _selectedStatus;
  UserModel? _currentUser;

  @override
  void initState() {
    super.initState();
    _currentUser = context.read<AuthService>().currentUser;
    _selectedStatus = widget.load.status;
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  List<LoadStatus> _getAvailableStatusTransitions() {
    switch (widget.load.status) {
      case LoadStatus.assigned:
        return [
          LoadStatus.assigned,
          LoadStatus.inTransit,
          LoadStatus.cancelled
        ];
      case LoadStatus.inTransit:
        return [
          LoadStatus.inTransit,
          LoadStatus.delivered,
          LoadStatus.cancelled
        ];
      case LoadStatus.delivered:
        return [LoadStatus.delivered]; // Cannot change from delivered
      case LoadStatus.cancelled:
        return [LoadStatus.cancelled]; // Cannot change from cancelled
      default:
        return [widget.load.status]; // No changes allowed for other statuses
    }
  }

  bool _canUpdateStatus() {
    // Check if user has permission to update load status
    // This would typically check company membership permissions
    if (_currentUser == null || _currentUser!.company == null) return false;

    final userCompanyId = int.tryParse(_currentUser!.company!.id);
    final assignedCompanyId = widget.load.assignedCompanyId;

    return userCompanyId != null &&
        assignedCompanyId != null &&
        userCompanyId == assignedCompanyId;
  }

  void _updateStatus() {
    if (_selectedStatus == null || _selectedStatus == widget.load.status) {
      return;
    }

    final notes = _notesController.text.trim();

    context.read<LoadBloc>().add(
          LoadUpdateStatusWithNotesRequested(
            id: widget.load.id!,
            status: _selectedStatus!,
            notes: notes.isNotEmpty ? notes : null,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<LoadBloc, LoadState>(
      listener: (context, state) {
        if (state is LoadUpdateSuccess) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Load status updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
          _notesController.clear();
        } else if (state is LoadError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCurrentStatusCard(),
            const SizedBox(height: 24),
            if (_canUpdateStatus()) ...[
              _buildStatusUpdateSection(),
              const SizedBox(height: 24),
              _buildNotesSection(),
              const SizedBox(height: 24),
              _buildUpdateButton(),
            ] else
              _buildNoPermissionCard(),
            const SizedBox(height: 24),
            _buildStatusHistorySection(),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Current Status',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildStatusIcon(widget.load.status),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.load.status.displayName,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      if (widget.load.updatedAt != null)
                        Text(
                          'Last updated: ${DateFormat('MMM dd, yyyy HH:mm').format(widget.load.updatedAt!)}',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIcon(LoadStatus status) {
    IconData iconData;
    Color color;

    switch (status) {
      case LoadStatus.assigned:
        iconData = Icons.assignment;
        color = Colors.blue;
        break;
      case LoadStatus.inTransit:
        iconData = Icons.local_shipping;
        color = Colors.orange;
        break;
      case LoadStatus.delivered:
        iconData = Icons.check_circle;
        color = Colors.green;
        break;
      case LoadStatus.cancelled:
        iconData = Icons.cancel;
        color = Colors.red;
        break;
      default:
        iconData = Icons.help_outline;
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Icon(
        iconData,
        color: color,
        size: 32,
      ),
    );
  }

  Widget _buildStatusUpdateSection() {
    final availableStatuses = _getAvailableStatusTransitions();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.update,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Update Status',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<LoadStatus>(
              value: _selectedStatus,
              decoration: const InputDecoration(
                labelText: 'New Status',
                border: OutlineInputBorder(),
              ),
              items: availableStatuses.map((status) {
                return DropdownMenuItem(
                  value: status,
                  child: Row(
                    children: [
                      _buildStatusIcon(status),
                      const SizedBox(width: 12),
                      Text(status.displayName),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.note_add,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Update Notes',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes (Optional)',
                hintText: 'Add any relevant notes about this status update...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUpdateButton() {
    final hasChanges = _selectedStatus != widget.load.status;

    return BlocBuilder<LoadBloc, LoadState>(
      builder: (context, state) {
        final isLoading = state is LoadLoading;

        return SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: hasChanges && !isLoading ? _updateStatus : null,
            icon: isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.update),
            label: Text(isLoading ? 'Updating...' : 'Update Status'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNoPermissionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.lock_outline,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No Permission',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 8),
            Text(
              'You do not have permission to update the status of this load.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusHistorySection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Status History',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // TODO: Implement status history timeline
            // This would show a timeline of all status changes
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Status history will be displayed here once implemented.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
