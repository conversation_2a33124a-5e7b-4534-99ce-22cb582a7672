import '../../core/constants/app_constants.dart';

class ContactConfigModel {
  final String phoneNumber;
  final String whatsappNumber;
  final String email;
  final String supportEmail;
  final String legalEmail;
  final String address;
  final String businessHours;
  final String tawkToUrl;
  final String websiteUrl;

  const ContactConfigModel({
    required this.phoneNumber,
    required this.whatsappNumber,
    required this.email,
    required this.supportEmail,
    required this.legalEmail,
    required this.address,
    required this.businessHours,
    required this.tawkToUrl,
    required this.websiteUrl,
  });

  factory ContactConfigModel.fromJson(Map<String, dynamic> json) {
    return ContactConfigModel(
      phoneNumber: json['phoneNumber'] ?? AppConstants.companyPhoneNumber,
      whatsappNumber: json['whatsappNumber'] ?? AppConstants.companyWhatsAppNumber,
      email: json['email'] ?? AppConstants.companyEmail,
      supportEmail: json['supportEmail'] ?? AppConstants.companySupportEmail,
      legalEmail: json['legalEmail'] ?? AppConstants.companyLegalEmail,
      address: json['address'] ?? AppConstants.companyAddress,
      businessHours: json['businessHours'] ?? AppConstants.companyBusinessHours,
      tawkToUrl: json['tawkToUrl'] ?? AppConstants.tawkToUrl,
      websiteUrl: json['websiteUrl'] ?? AppConstants.companyWebsiteUrl,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'phoneNumber': phoneNumber,
      'whatsappNumber': whatsappNumber,
      'email': email,
      'supportEmail': supportEmail,
      'legalEmail': legalEmail,
      'address': address,
      'businessHours': businessHours,
      'tawkToUrl': tawkToUrl,
      'websiteUrl': websiteUrl,
    };
  }

  // Default fallback configuration
  static const ContactConfigModel defaultConfig = ContactConfigModel(
    phoneNumber: AppConstants.companyPhoneNumber,
    whatsappNumber: AppConstants.companyWhatsAppNumber,
    email: AppConstants.companyEmail,
    supportEmail: AppConstants.companySupportEmail,
    legalEmail: AppConstants.companyLegalEmail,
    address: AppConstants.companyAddress,
    businessHours: AppConstants.companyBusinessHours,
    tawkToUrl: AppConstants.tawkToUrl,
    websiteUrl: AppConstants.companyWebsiteUrl,
  );

  // Helper methods for formatted display
  String get formattedPhoneNumber => phoneNumber;
  String get formattedWhatsappNumber => whatsappNumber;
  String get phoneNumberForDialing => phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
  String get whatsappNumberForDialing => whatsappNumber.replaceAll(RegExp(r'[^\d+]'), '');
}
