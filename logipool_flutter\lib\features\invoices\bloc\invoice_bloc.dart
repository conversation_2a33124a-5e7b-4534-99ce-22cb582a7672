import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../shared/models/invoice_model.dart';
import '../../../shared/services/invoice_service.dart';
import '../../../core/network/api_exception.dart';

// Events
abstract class InvoiceEvent extends Equatable {
  const InvoiceEvent();

  @override
  List<Object?> get props => [];
}

class InvoiceGenerateAutomatic extends InvoiceEvent {
  final int loadId;

  const InvoiceGenerateAutomatic({required this.loadId});

  @override
  List<Object> get props => [loadId];
}

class InvoiceCreate extends InvoiceEvent {
  final InvoiceCreateRequest request;

  const InvoiceCreate({required this.request});

  @override
  List<Object> get props => [request];
}

class InvoiceGetById extends InvoiceEvent {
  final int invoiceId;

  const InvoiceGetById({required this.invoiceId});

  @override
  List<Object> get props => [invoiceId];
}

class InvoiceGetByLoadId extends InvoiceEvent {
  final int loadId;

  const InvoiceGetByLoadId({required this.loadId});

  @override
  List<Object> get props => [loadId];
}

class InvoicePreviewRequested extends InvoiceEvent {
  final int loadId;

  const InvoicePreviewRequested({required this.loadId});

  @override
  List<Object> get props => [loadId];
}

class InvoiceGenerateWithModifications extends InvoiceEvent {
  final int loadId;
  final InvoiceModificationRequest request;

  const InvoiceGenerateWithModifications({
    required this.loadId,
    required this.request,
  });

  @override
  List<Object> get props => [loadId, request];
}

class InvoiceUpdate extends InvoiceEvent {
  final int invoiceId;
  final InvoiceUpdateRequest request;

  const InvoiceUpdate({
    required this.invoiceId,
    required this.request,
  });

  @override
  List<Object> get props => [invoiceId, request];
}

class InvoiceDownloadPdf extends InvoiceEvent {
  final int invoiceId;

  const InvoiceDownloadPdf({required this.invoiceId});

  @override
  List<Object> get props => [invoiceId];
}

class InvoiceSendEmail extends InvoiceEvent {
  final int invoiceId;
  final String recipientEmail;

  const InvoiceSendEmail({
    required this.invoiceId,
    required this.recipientEmail,
  });

  @override
  List<Object> get props => [invoiceId, recipientEmail];
}

class InvoiceMarkAsPaid extends InvoiceEvent {
  final int invoiceId;
  final String? paymentReference;
  final DateTime? paymentDate;

  const InvoiceMarkAsPaid({
    required this.invoiceId,
    this.paymentReference,
    this.paymentDate,
  });

  @override
  List<Object?> get props => [invoiceId, paymentReference, paymentDate];
}

class InvoiceCancel extends InvoiceEvent {
  final int invoiceId;
  final String reason;

  const InvoiceCancel({
    required this.invoiceId,
    required this.reason,
  });

  @override
  List<Object> get props => [invoiceId, reason];
}

class InvoiceGetAll extends InvoiceEvent {
  final int page;
  final int size;
  final InvoiceStatus? status;
  final DateTime? fromDate;
  final DateTime? toDate;

  const InvoiceGetAll({
    this.page = 0,
    this.size = 20,
    this.status,
    this.fromDate,
    this.toDate,
  });

  @override
  List<Object?> get props => [page, size, status, fromDate, toDate];
}

// States
abstract class InvoiceState extends Equatable {
  const InvoiceState();

  @override
  List<Object?> get props => [];
}

class InvoiceInitial extends InvoiceState {}

class InvoiceLoading extends InvoiceState {}

class InvoiceGenerated extends InvoiceState {
  final InvoiceResponse invoice;

  const InvoiceGenerated({required this.invoice});

  @override
  List<Object> get props => [invoice];
}

class InvoiceCreated extends InvoiceState {
  final InvoiceResponse invoice;

  const InvoiceCreated({required this.invoice});

  @override
  List<Object> get props => [invoice];
}

class InvoiceLoaded extends InvoiceState {
  final InvoiceResponse invoice;

  const InvoiceLoaded({required this.invoice});

  @override
  List<Object> get props => [invoice];
}

class InvoiceNotFound extends InvoiceState {}

class InvoicePreviewLoaded extends InvoiceState {
  final InvoicePreviewResponse preview;

  const InvoicePreviewLoaded({required this.preview});

  @override
  List<Object> get props => [preview];
}

class InvoiceUpdated extends InvoiceState {
  final InvoiceResponse invoice;

  const InvoiceUpdated({required this.invoice});

  @override
  List<Object> get props => [invoice];
}

class InvoicePdfDownloaded extends InvoiceState {
  final List<int> pdfBytes;

  const InvoicePdfDownloaded({required this.pdfBytes});

  @override
  List<Object> get props => [pdfBytes];
}

class InvoiceEmailSent extends InvoiceState {
  final String message;

  const InvoiceEmailSent({required this.message});

  @override
  List<Object> get props => [message];
}

class InvoiceListLoaded extends InvoiceState {
  final List<InvoiceResponse> invoices;

  const InvoiceListLoaded({required this.invoices});

  @override
  List<Object> get props => [invoices];
}

class InvoiceError extends InvoiceState {
  final String message;
  final String? errorCode;

  const InvoiceError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

// Bloc
class InvoiceBloc extends Bloc<InvoiceEvent, InvoiceState> {
  final InvoiceService _invoiceService;

  InvoiceBloc({required InvoiceService invoiceService})
      : _invoiceService = invoiceService,
        super(InvoiceInitial()) {
    on<InvoiceGenerateAutomatic>(_onGenerateAutomatic);
    on<InvoiceCreate>(_onCreate);
    on<InvoiceGetById>(_onGetById);
    on<InvoiceGetByLoadId>(_onGetByLoadId);
    on<InvoicePreviewRequested>(_onPreviewRequested);
    on<InvoiceGenerateWithModifications>(_onGenerateWithModifications);
    on<InvoiceUpdate>(_onUpdate);
    on<InvoiceDownloadPdf>(_onDownloadPdf);
    on<InvoiceSendEmail>(_onSendEmail);
    on<InvoiceMarkAsPaid>(_onMarkAsPaid);
    on<InvoiceCancel>(_onCancel);
    on<InvoiceGetAll>(_onGetAll);
  }

  Future<void> _onGenerateAutomatic(
    InvoiceGenerateAutomatic event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      final invoice =
          await _invoiceService.generateAutomaticInvoice(event.loadId);
      emit(InvoiceGenerated(invoice: invoice));
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(
          InvoiceError(message: 'Failed to generate invoice: ${e.toString()}'));
    }
  }

  Future<void> _onCreate(
    InvoiceCreate event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      final invoice = await _invoiceService.createInvoice(event.request);
      emit(InvoiceCreated(invoice: invoice));
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(InvoiceError(message: 'Failed to create invoice: ${e.toString()}'));
    }
  }

  Future<void> _onGetById(
    InvoiceGetById event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      final invoice = await _invoiceService.getInvoiceById(event.invoiceId);
      emit(InvoiceLoaded(invoice: invoice));
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(InvoiceError(message: 'Failed to get invoice: ${e.toString()}'));
    }
  }

  Future<void> _onGetByLoadId(
    InvoiceGetByLoadId event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      final invoice = await _invoiceService.getInvoiceByLoadId(event.loadId);
      if (invoice != null) {
        emit(InvoiceLoaded(invoice: invoice));
      } else {
        emit(InvoiceNotFound());
      }
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(InvoiceError(message: 'Failed to get invoice: ${e.toString()}'));
    }
  }

  Future<void> _onPreviewRequested(
    InvoicePreviewRequested event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      final preview =
          await _invoiceService.previewAutomaticInvoice(event.loadId);
      emit(InvoicePreviewLoaded(preview: preview));
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(InvoiceError(message: 'Failed to preview invoice: ${e.toString()}'));
    }
  }

  Future<void> _onGenerateWithModifications(
    InvoiceGenerateWithModifications event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      final invoice = await _invoiceService.generateInvoiceWithModifications(
        event.loadId,
        event.request,
      );
      emit(InvoiceGenerated(invoice: invoice));
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(InvoiceError(
          message:
              'Failed to generate invoice with modifications: ${e.toString()}'));
    }
  }

  Future<void> _onUpdate(
    InvoiceUpdate event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      final invoice =
          await _invoiceService.updateInvoice(event.invoiceId, event.request);
      emit(InvoiceUpdated(invoice: invoice));
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(InvoiceError(message: 'Failed to update invoice: ${e.toString()}'));
    }
  }

  Future<void> _onDownloadPdf(
    InvoiceDownloadPdf event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      final pdfBytes =
          await _invoiceService.downloadInvoicePdf(event.invoiceId);
      emit(InvoicePdfDownloaded(pdfBytes: pdfBytes));
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(
          InvoiceError(message: 'Failed to download invoice: ${e.toString()}'));
    }
  }

  Future<void> _onSendEmail(
    InvoiceSendEmail event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      await _invoiceService.sendInvoiceEmail(
          event.invoiceId, event.recipientEmail);
      emit(const InvoiceEmailSent(message: 'Invoice sent successfully'));
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(InvoiceError(message: 'Failed to send invoice: ${e.toString()}'));
    }
  }

  Future<void> _onMarkAsPaid(
    InvoiceMarkAsPaid event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      final invoice = await _invoiceService.markInvoiceAsPaid(
        event.invoiceId,
        paymentReference: event.paymentReference,
        paymentDate: event.paymentDate,
      );
      emit(InvoiceUpdated(invoice: invoice));
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(InvoiceError(
          message: 'Failed to mark invoice as paid: ${e.toString()}'));
    }
  }

  Future<void> _onCancel(
    InvoiceCancel event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      final invoice =
          await _invoiceService.cancelInvoice(event.invoiceId, event.reason);
      emit(InvoiceUpdated(invoice: invoice));
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(InvoiceError(message: 'Failed to cancel invoice: ${e.toString()}'));
    }
  }

  Future<void> _onGetAll(
    InvoiceGetAll event,
    Emitter<InvoiceState> emit,
  ) async {
    emit(InvoiceLoading());
    try {
      final invoices = await _invoiceService.getInvoices(
        page: event.page,
        size: event.size,
        status: event.status,
        fromDate: event.fromDate,
        toDate: event.toDate,
      );
      emit(InvoiceListLoaded(invoices: invoices));
    } on ApiException catch (e) {
      emit(
          InvoiceError(message: e.message, errorCode: e.statusCode.toString()));
    } catch (e) {
      emit(InvoiceError(message: 'Failed to get invoices: ${e.toString()}'));
    }
  }
}
