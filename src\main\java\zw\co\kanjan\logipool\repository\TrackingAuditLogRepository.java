package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.TrackingAuditLog;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface TrackingAuditLogRepository extends JpaRepository<TrackingAuditLog, Long> {
    
    /**
     * Find audit logs by event type
     */
    List<TrackingAuditLog> findByEventTypeOrderByTimestampDesc(String eventType);
    
    /**
     * Find audit logs by tracking number
     */
    Page<TrackingAuditLog> findByTrackingNumberOrderByTimestampDesc(String trackingNumber, Pageable pageable);
    
    /**
     * Find audit logs by IP address
     */
    Page<TrackingAuditLog> findByIpAddressOrderByTimestampDesc(String ipAddress, Pageable pageable);
    
    /**
     * Find audit logs by severity
     */
    List<TrackingAuditLog> findBySeverityOrderByTimestampDesc(TrackingAuditLog.Severity severity);
    
    /**
     * Find audit logs within a time range
     */
    @Query("SELECT tal FROM TrackingAuditLog tal WHERE tal.timestamp BETWEEN :startTime AND :endTime " +
           "ORDER BY tal.timestamp DESC")
    List<TrackingAuditLog> findByTimestampBetween(@Param("startTime") LocalDateTime startTime, 
                                                 @Param("endTime") LocalDateTime endTime);
    
    /**
     * Count events by type within a time range
     */
    @Query("SELECT COUNT(tal) FROM TrackingAuditLog tal WHERE tal.eventType = :eventType " +
           "AND tal.timestamp >= :since")
    Long countByEventTypeSince(@Param("eventType") String eventType, @Param("since") LocalDateTime since);
    
    /**
     * Count failed requests from an IP address within a time range
     */
    @Query("SELECT COUNT(tal) FROM TrackingAuditLog tal WHERE tal.ipAddress = :ipAddress " +
           "AND tal.requestSuccessful = false AND tal.timestamp >= :since")
    Long countFailedRequestsByIpSince(@Param("ipAddress") String ipAddress, @Param("since") LocalDateTime since);
    
    /**
     * Find suspicious activity logs
     */
    @Query("SELECT tal FROM TrackingAuditLog tal WHERE tal.eventType IN ('SUSPICIOUS_ACTIVITY', 'SECURITY_VIOLATION', 'RATE_LIMIT_EXCEEDED') " +
           "AND tal.timestamp >= :since ORDER BY tal.timestamp DESC")
    List<TrackingAuditLog> findSuspiciousActivitySince(@Param("since") LocalDateTime since);
    
    /**
     * Find most active IP addresses
     */
    @Query("SELECT tal.ipAddress, COUNT(tal) as requestCount FROM TrackingAuditLog tal " +
           "WHERE tal.timestamp >= :since GROUP BY tal.ipAddress " +
           "ORDER BY requestCount DESC")
    List<Object[]> findMostActiveIpAddressesSince(@Param("since") LocalDateTime since);
    
    /**
     * Find most tracked numbers
     */
    @Query("SELECT tal.trackingNumber, COUNT(tal) as accessCount FROM TrackingAuditLog tal " +
           "WHERE tal.eventType = 'TRACKING_ACCESSED' AND tal.timestamp >= :since " +
           "GROUP BY tal.trackingNumber ORDER BY accessCount DESC")
    List<Object[]> findMostAccessedTrackingNumbersSince(@Param("since") LocalDateTime since);
    
    /**
     * Get security statistics for a time period
     */
    @Query("SELECT tal.eventType, tal.severity, COUNT(tal) as eventCount FROM TrackingAuditLog tal " +
           "WHERE tal.timestamp >= :since GROUP BY tal.eventType, tal.severity " +
           "ORDER BY eventCount DESC")
    List<Object[]> getSecurityStatisticsSince(@Param("since") LocalDateTime since);
    
    /**
     * Delete old audit logs (for maintenance)
     */
    @Modifying
    @Query("DELETE FROM TrackingAuditLog tal WHERE tal.timestamp < :cutoffDate")
    void deleteOldLogs(@Param("cutoffDate") LocalDateTime cutoffDate);
    
    /**
     * Find logs by verification token
     */
    List<TrackingAuditLog> findByVerificationTokenOrderByTimestampDesc(String verificationToken);
    
    /**
     * Count events by tracking number within a time range
     */
    @Query("SELECT COUNT(tal) FROM TrackingAuditLog tal WHERE tal.trackingNumber = :trackingNumber " +
           "AND tal.timestamp >= :since")
    Long countByTrackingNumberSince(@Param("trackingNumber") String trackingNumber, @Param("since") LocalDateTime since);
    
    /**
     * Find recent failed verification attempts
     */
    @Query("SELECT tal FROM TrackingAuditLog tal WHERE tal.eventType = 'VERIFICATION_FAILED' " +
           "AND tal.timestamp >= :since ORDER BY tal.timestamp DESC")
    List<TrackingAuditLog> findRecentFailedVerifications(@Param("since") LocalDateTime since);
    
    /**
     * Find logs with high severity (ERROR or CRITICAL)
     */
    @Query("SELECT tal FROM TrackingAuditLog tal WHERE tal.severity IN ('ERROR', 'CRITICAL') " +
           "AND tal.timestamp >= :since ORDER BY tal.timestamp DESC")
    List<TrackingAuditLog> findHighSeverityEventsSince(@Param("since") LocalDateTime since);
}
