import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:logipool_flutter/features/public/screens/guest_load_posting_screen.dart';
import 'package:logipool_flutter/shared/models/guest_load_inquiry_model.dart';

void main() {
  group('Guest Load Posting Tests', () {
    testWidgets('Guest load posting screen renders correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const GuestLoadPostingScreen(),
        ),
      );

      // Verify the screen renders
      expect(find.text('Post Load Request'), findsOneWidget);
      expect(find.text('Tell us about your shipping needs'), findsOneWidget);
      expect(find.text('Your Contact Information'), findsOneWidget);
      expect(find.text('Load Information'), findsOneWidget);
      expect(find.text('Pickup & Delivery'), findsOneWidget);
      expect(find.text('Additional Options'), findsOneWidget);
    });

    testWidgets('Form validation works correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const GuestLoadPostingScreen(),
        ),
      );

      // Try to submit empty form
      final submitButton = find.text('Submit Load Request');
      await tester.tap(submitButton);
      await tester.pump();

      // Should show validation errors
      expect(find.text('This field is required'), findsWidgets);
    });

    testWidgets('Contact buttons are present', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: const GuestLoadPostingScreen(),
        ),
      );

      // Scroll to bottom to find contact buttons
      await tester.scrollUntilVisible(
        find.text('Prefer to contact us directly?'),
        500.0,
      );

      expect(find.text('WhatsApp'), findsOneWidget);
      expect(find.text('Call'), findsOneWidget);
      expect(find.text('Email'), findsOneWidget);
    });

    test('GuestLoadInquiryModel serialization works correctly', () {
      final inquiry = GuestLoadInquiryModel(
        name: 'John Doe',
        email: '<EMAIL>',
        phoneNumber: '+263771234567',
        company: 'Test Company',
        title: 'Test Load',
        description: 'Test description',
        loadType: 'GENERAL',
        weight: 100.0,
        dimensions: '2m×1m×1m',
        pickupLocation: 'Harare',
        deliveryLocation: 'Bulawayo',
        pickupDate: DateTime(2024, 12, 25),
        deliveryDate: DateTime(2024, 12, 26),
        requiresSpecialHandling: true,
        isUrgent: false,
        specialInstructions: 'Handle with care',
      );

      final json = inquiry.toJson();

      expect(json['name'], equals('John Doe'));
      expect(json['email'], equals('<EMAIL>'));
      expect(json['phoneNumber'], equals('+263771234567'));
      expect(json['company'], equals('Test Company'));
      expect(json['title'], equals('Test Load'));
      expect(json['description'], equals('Test description'));
      expect(json['loadType'], equals('GENERAL'));
      expect(json['weight'], equals(100.0));
      expect(json['dimensions'], equals('2m×1m×1m'));
      expect(json['pickupLocation'], equals('Harare'));
      expect(json['deliveryLocation'], equals('Bulawayo'));
      expect(json['pickupDate'], equals('2024-12-25'));
      expect(json['deliveryDate'], equals('2024-12-26'));
      expect(json['requiresSpecialHandling'], equals(true));
      expect(json['isUrgent'], equals(false));
      expect(json['specialInstructions'], equals('Handle with care'));
    });

    test('GuestLoadInquiryModel deserialization works correctly', () {
      final json = {
        'name': 'Jane Smith',
        'email': '<EMAIL>',
        'phoneNumber': '+263771234568',
        'company': 'Another Company',
        'title': 'Another Load',
        'description': 'Another description',
        'loadType': 'FRAGILE',
        'weight': 50.0,
        'dimensions': '1m×1m×1m',
        'pickupLocation': 'Mutare',
        'deliveryLocation': 'Gweru',
        'pickupDate': '2024-12-27',
        'deliveryDate': '2024-12-28',
        'requiresSpecialHandling': false,
        'isUrgent': true,
        'specialInstructions': 'Urgent delivery needed',
      };

      final inquiry = GuestLoadInquiryModel.fromJson(json);

      expect(inquiry.name, equals('Jane Smith'));
      expect(inquiry.email, equals('<EMAIL>'));
      expect(inquiry.phoneNumber, equals('+263771234568'));
      expect(inquiry.company, equals('Another Company'));
      expect(inquiry.title, equals('Another Load'));
      expect(inquiry.description, equals('Another description'));
      expect(inquiry.loadType, equals('FRAGILE'));
      expect(inquiry.weight, equals(50.0));
      expect(inquiry.dimensions, equals('1m×1m×1m'));
      expect(inquiry.pickupLocation, equals('Mutare'));
      expect(inquiry.deliveryLocation, equals('Gweru'));
      expect(inquiry.pickupDate, equals(DateTime(2024, 12, 27)));
      expect(inquiry.deliveryDate, equals(DateTime(2024, 12, 28)));
      expect(inquiry.requiresSpecialHandling, equals(false));
      expect(inquiry.isUrgent, equals(true));
      expect(inquiry.specialInstructions, equals('Urgent delivery needed'));
    });

    test('GuestLoadInquiryModel copyWith works correctly', () {
      final original = GuestLoadInquiryModel(
        name: 'Original Name',
        email: '<EMAIL>',
        phoneNumber: '+263771234567',
        title: 'Original Title',
        description: 'Original description',
        loadType: 'GENERAL',
        weight: 100.0,
        pickupLocation: 'Original Pickup',
        deliveryLocation: 'Original Delivery',
        pickupDate: DateTime(2024, 12, 25),
      );

      final updated = original.copyWith(
        name: 'Updated Name',
        title: 'Updated Title',
        weight: 200.0,
      );

      expect(updated.name, equals('Updated Name'));
      expect(updated.title, equals('Updated Title'));
      expect(updated.weight, equals(200.0));
      
      // Unchanged fields should remain the same
      expect(updated.email, equals('<EMAIL>'));
      expect(updated.phoneNumber, equals('+263771234567'));
      expect(updated.description, equals('Original description'));
      expect(updated.loadType, equals('GENERAL'));
      expect(updated.pickupLocation, equals('Original Pickup'));
      expect(updated.deliveryLocation, equals('Original Delivery'));
      expect(updated.pickupDate, equals(DateTime(2024, 12, 25)));
    });
  });
}
