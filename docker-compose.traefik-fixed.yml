services:
  # Traefik Reverse Proxy
  traefik:
    image: traefik:v3.0
    container_name: logipool-traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Dashboard - disable in production
    environment:
      # API Configuration
      - TRAEFIK_API_DASHBOARD=true
      - TRAEFIK_API_INSECURE=true  # Remove in production
      
      # Docker Provider
      - TRAEFIK_PROVIDERS_DOCKER=true
      - TRAEFIK_PROVIDERS_DOCKER_EXPOSEDBYDEFAULT=false
      - TRAEFIK_PROVIDERS_DOCKER_NETWORK=traefik-public
      
      # Entry Points
      - TRAEFIK_ENTRYPOINTS_WEB_ADDRESS=:80
      - TRAEFIK_ENTRYPOINTS_WEBSECURE_ADDRESS=:443
      
      # HTTP to HTTPS Redirect
      - TRAEFIK_ENTRYPOINTS_WEB_HTTP_REDIRECTIONS_ENTRYPOINT_TO=websecure
      - TRAEFIK_ENTRYPOINTS_WEB_HTTP_REDIRECTIONS_ENTRYPOINT_SCHEME=https
      - TRAEFIK_ENTRYPOINTS_WEB_HTTP_REDIRECTIONS_ENTRYPOINT_PERMANENT=true
      
      # Let's Encrypt
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_EMAIL=${ACME_EMAIL}
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_STORAGE=/letsencrypt/acme.json
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_HTTPCHALLENGE_ENTRYPOINT=web
      
      # Logging
      - TRAEFIK_LOG_LEVEL=INFO
      - TRAEFIK_ACCESSLOG=true
      - TRAEFIK_LOG_FILEPATH=/var/log/traefik/traefik.log
      - TRAEFIK_ACCESSLOG_FILEPATH=/var/log/traefik/access.log
      
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik_letsencrypt:/letsencrypt
      - traefik_logs:/var/log/traefik
    networks:
      - traefik-public
      - logipool-network
    labels:
      - "traefik.enable=true"
      # Dashboard
      - "traefik.http.routers.traefik-dashboard.rule=Host(`traefik.${DOMAIN_NAME}`)"
      - "traefik.http.routers.traefik-dashboard.entrypoints=websecure"
      - "traefik.http.routers.traefik-dashboard.tls.certresolver=letsencrypt"
      - "traefik.http.routers.traefik-dashboard.service=api@internal"
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: logipool-postgres-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DATABASE_NAME}
      POSTGRES_USER: ${DATABASE_USERNAME}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data/pgdata
    networks:
      - logipool-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DATABASE_USERNAME} -d ${DATABASE_NAME}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: logipool-redis-prod
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD} --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - logipool-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # LogiPool Backend Application
  logipool-backend:
    image: logipool/backend:latest
    container_name: logipool-backend-prod
    restart: unless-stopped
    environment:
      # Database Configuration
      - SPRING_DATASOURCE_URL=*******************************/${DATABASE_NAME}
      - SPRING_DATASOURCE_USERNAME=${DATABASE_USERNAME}
      - SPRING_DATASOURCE_PASSWORD=${DATABASE_PASSWORD}
      
      # Redis Configuration
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PORT=6379
      - SPRING_REDIS_PASSWORD=${REDIS_PASSWORD}
      
      # JWT Configuration
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRATION=${JWT_EXPIRATION}
      - JWT_REFRESH_EXPIRATION=${JWT_REFRESH_EXPIRATION}
      
      # Email Configuration
      - SPRING_MAIL_HOST=${MAIL_HOST}
      - SPRING_MAIL_PORT=${MAIL_PORT}
      - SPRING_MAIL_USERNAME=${MAIL_USERNAME}
      - SPRING_MAIL_PASSWORD=${MAIL_PASSWORD}
      
      # File Upload Configuration
      - SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE=${MAX_FILE_SIZE}
      - SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE=${MAX_REQUEST_SIZE}
      
      # Cloud Storage Configuration
      - CLOUD_STORAGE_ENABLED=${CLOUD_STORAGE_ENABLED}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - S3_BUCKET_NAME=${S3_BUCKET_NAME}
      - AWS_REGION=${AWS_REGION}
      
      # Application Configuration
      - SPRING_PROFILES_ACTIVE=prod
      - SERVER_PORT=8080
      
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    networks:
      - logipool-network
      - traefik-public
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik-public"
      - "traefik.http.routers.logipool-backend.rule=Host(`${DOMAIN_NAME}`)"
      - "traefik.http.routers.logipool-backend.entrypoints=websecure"
      - "traefik.http.routers.logipool-backend.tls.certresolver=letsencrypt"
      - "traefik.http.services.logipool-backend.loadbalancer.server.port=8080"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local
  traefik_letsencrypt:
    driver: local
  traefik_logs:
    driver: local

networks:
  traefik-public:
    external: true
  logipool-network:
    driver: bridge
