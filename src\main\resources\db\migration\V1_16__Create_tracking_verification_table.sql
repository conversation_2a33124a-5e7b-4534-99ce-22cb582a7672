-- Create tracking_verification table for secure public tracking access
CREATE TABLE tracking_verification (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    token VARCHAR(100) NOT NULL UNIQUE,
    tracking_number VARCHAR(20) NOT NULL,
    email VARCHAR(100),
    phone_number VARCHAR(20),
    verification_method ENUM('EMAIL', 'SMS', 'BOTH') NOT NULL,
    verification_code VARCHAR(10),
    expiry_date TIMESTAMP NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    verified_at TIMESTAMP NULL,
    used_at TIMESTAMP NULL,
    is_verified BOOLEAN NOT NULL DEFAULT FALSE,
    is_used BOOLEAN NOT NULL DEFAULT FALSE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    access_count INT NOT NULL DEFAULT 0,
    last_accessed_at TIMESTAMP NULL,
    
    -- Indexes for performance
    INDEX idx_tracking_verification_token (token),
    INDEX idx_tracking_verification_tracking_number (tracking_number),
    INDEX idx_tracking_verification_email (email),
    INDEX idx_tracking_verification_phone (phone_number),
    INDEX idx_tracking_verification_expiry (expiry_date),
    INDEX idx_tracking_verification_created_at (created_at),
    INDEX idx_tracking_verification_verified (is_verified),
    INDEX idx_tracking_verification_used (is_used),
    INDEX idx_tracking_verification_ip (ip_address),
    INDEX idx_tracking_verification_access_count (access_count),
    
    -- Composite indexes for common queries
    INDEX idx_tracking_verification_number_code (tracking_number, verification_code),
    INDEX idx_tracking_verification_number_contact (tracking_number, email, phone_number),
    INDEX idx_tracking_verification_valid (is_verified, is_used, expiry_date),
    INDEX idx_tracking_verification_cleanup (expiry_date, is_used),
    
    -- Foreign key constraint to loads table
    CONSTRAINT fk_tracking_verification_load 
        FOREIGN KEY (tracking_number) 
        REFERENCES loads(tracking_number) 
        ON DELETE CASCADE 
        ON UPDATE CASCADE
);

-- Add comments for documentation
ALTER TABLE tracking_verification 
COMMENT = 'Stores verification tokens and codes for secure public tracking access';

ALTER TABLE tracking_verification 
MODIFY COLUMN token VARCHAR(100) NOT NULL UNIQUE 
COMMENT 'Unique secure token for tracking access';

ALTER TABLE tracking_verification 
MODIFY COLUMN tracking_number VARCHAR(20) NOT NULL 
COMMENT 'Load tracking number being accessed';

ALTER TABLE tracking_verification 
MODIFY COLUMN email VARCHAR(100) 
COMMENT 'Email address for verification (if using email method)';

ALTER TABLE tracking_verification 
MODIFY COLUMN phone_number VARCHAR(20) 
COMMENT 'Phone number for verification (if using SMS method)';

ALTER TABLE tracking_verification 
MODIFY COLUMN verification_method ENUM('EMAIL', 'SMS', 'BOTH') NOT NULL 
COMMENT 'Method used for verification';

ALTER TABLE tracking_verification 
MODIFY COLUMN verification_code VARCHAR(10) 
COMMENT 'SMS verification code (if using SMS method)';

ALTER TABLE tracking_verification 
MODIFY COLUMN expiry_date TIMESTAMP NOT NULL 
COMMENT 'When this verification expires';

ALTER TABLE tracking_verification 
MODIFY COLUMN is_verified BOOLEAN NOT NULL DEFAULT FALSE 
COMMENT 'Whether the verification has been completed';

ALTER TABLE tracking_verification 
MODIFY COLUMN is_used BOOLEAN NOT NULL DEFAULT FALSE 
COMMENT 'Whether this verification has been used for tracking access';

ALTER TABLE tracking_verification 
MODIFY COLUMN ip_address VARCHAR(45) 
COMMENT 'IP address of the requester (supports IPv6)';

ALTER TABLE tracking_verification 
MODIFY COLUMN access_count INT NOT NULL DEFAULT 0 
COMMENT 'Number of times tracking has been accessed with this verification';

-- Create a view for active verifications (for monitoring)
CREATE VIEW active_tracking_verifications AS
SELECT 
    tv.id,
    tv.token,
    tv.tracking_number,
    tv.verification_method,
    tv.created_at,
    tv.expiry_date,
    tv.access_count,
    tv.last_accessed_at,
    l.title as load_title,
    l.status as load_status,
    CASE 
        WHEN tv.expiry_date < NOW() THEN 'EXPIRED'
        WHEN tv.is_used = TRUE THEN 'USED'
        WHEN tv.is_verified = FALSE THEN 'PENDING'
        ELSE 'ACTIVE'
    END as verification_status
FROM tracking_verification tv
LEFT JOIN loads l ON tv.tracking_number = l.tracking_number
WHERE tv.is_verified = TRUE 
  AND tv.is_used = FALSE 
  AND tv.expiry_date > NOW()
ORDER BY tv.created_at DESC;

-- Create a procedure for cleanup of expired verifications
DELIMITER //
CREATE PROCEDURE CleanupExpiredTrackingVerifications()
BEGIN
    DECLARE deleted_count INT DEFAULT 0;
    
    -- Delete expired verifications older than 7 days
    DELETE FROM tracking_verification 
    WHERE expiry_date < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    SET deleted_count = ROW_COUNT();
    
    -- Log the cleanup (you might want to insert into an audit table)
    SELECT CONCAT('Cleaned up ', deleted_count, ' expired tracking verifications') as cleanup_result;
END //
DELIMITER ;

-- Create an event to automatically cleanup expired verifications daily
CREATE EVENT IF NOT EXISTS cleanup_tracking_verifications
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO
  CALL CleanupExpiredTrackingVerifications();
