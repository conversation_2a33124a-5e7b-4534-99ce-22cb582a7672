// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'tracking_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TrackingModel _$TrackingModelFromJson(Map<String, dynamic> json) =>
    TrackingModel(
      id: (json['id'] as num).toInt(),
      location: json['location'] as String,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      status: $enumDecode(_$TrackingStatusEnumMap, json['status']),
      notes: json['notes'] as String?,
      isAutomated: json['isAutomated'] as bool,
      loadId: (json['loadId'] as num).toInt(),
      updatedById: (json['updatedById'] as num).toInt(),
      updatedByName: json['updatedByName'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );

Map<String, dynamic> _$TrackingModelToJson(TrackingModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'status': _$TrackingStatusEnumMap[instance.status]!,
      'notes': instance.notes,
      'isAutomated': instance.isAutomated,
      'loadId': instance.loadId,
      'updatedById': instance.updatedById,
      'updatedByName': instance.updatedByName,
      'timestamp': instance.timestamp.toIso8601String(),
    };

const _$TrackingStatusEnumMap = {
  TrackingStatus.loadPosted: 'LOAD_POSTED',
  TrackingStatus.bidAccepted: 'BID_ACCEPTED',
  TrackingStatus.pickupScheduled: 'PICKUP_SCHEDULED',
  TrackingStatus.inTransitToPickup: 'IN_TRANSIT_TO_PICKUP',
  TrackingStatus.arrivedAtPickup: 'ARRIVED_AT_PICKUP',
  TrackingStatus.loadingInProgress: 'LOADING_IN_PROGRESS',
  TrackingStatus.loaded: 'LOADED',
  TrackingStatus.inTransitToDelivery: 'IN_TRANSIT_TO_DELIVERY',
  TrackingStatus.arrivedAtDelivery: 'ARRIVED_AT_DELIVERY',
  TrackingStatus.unloadingInProgress: 'UNLOADING_IN_PROGRESS',
  TrackingStatus.delivered: 'DELIVERED',
  TrackingStatus.delayed: 'DELAYED',
  TrackingStatus.issueReported: 'ISSUE_REPORTED',
};

TrackingUpdateRequest _$TrackingUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    TrackingUpdateRequest(
      loadId: (json['loadId'] as num).toInt(),
      location: json['location'] as String,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      status: $enumDecode(_$TrackingStatusEnumMap, json['status']),
      notes: json['notes'] as String?,
      isAutomated: json['isAutomated'] as bool? ?? false,
    );

Map<String, dynamic> _$TrackingUpdateRequestToJson(
        TrackingUpdateRequest instance) =>
    <String, dynamic>{
      'loadId': instance.loadId,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'status': _$TrackingStatusEnumMap[instance.status]!,
      'notes': instance.notes,
      'isAutomated': instance.isAutomated,
    };

LocationUpdateRequest _$LocationUpdateRequestFromJson(
        Map<String, dynamic> json) =>
    LocationUpdateRequest(
      loadId: (json['loadId'] as num).toInt(),
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      location: json['location'] as String?,
      speed: (json['speed'] as num?)?.toDouble(),
      heading: (json['heading'] as num?)?.toDouble(),
      accuracy: (json['accuracy'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$LocationUpdateRequestToJson(
        LocationUpdateRequest instance) =>
    <String, dynamic>{
      'loadId': instance.loadId,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'location': instance.location,
      'speed': instance.speed,
      'heading': instance.heading,
      'accuracy': instance.accuracy,
    };

RealTimeLocation _$RealTimeLocationFromJson(Map<String, dynamic> json) =>
    RealTimeLocation(
      loadId: (json['loadId'] as num).toInt(),
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      speed: (json['speed'] as num?)?.toDouble(),
      heading: (json['heading'] as num?)?.toDouble(),
      accuracy: (json['accuracy'] as num?)?.toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      status: $enumDecodeNullable(_$TrackingStatusEnumMap, json['status']),
    );

Map<String, dynamic> _$RealTimeLocationToJson(RealTimeLocation instance) =>
    <String, dynamic>{
      'loadId': instance.loadId,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'speed': instance.speed,
      'heading': instance.heading,
      'accuracy': instance.accuracy,
      'timestamp': instance.timestamp.toIso8601String(),
      'status': _$TrackingStatusEnumMap[instance.status],
    };

TrackingStatistics _$TrackingStatisticsFromJson(Map<String, dynamic> json) =>
    TrackingStatistics(
      totalLoads: (json['totalLoads'] as num).toInt(),
      loadsInTransitToPickup: (json['loadsInTransitToPickup'] as num).toInt(),
      loadsInTransitToDelivery:
          (json['loadsInTransitToDelivery'] as num).toInt(),
      loadsDelivered: (json['loadsDelivered'] as num).toInt(),
      loadsDelayed: (json['loadsDelayed'] as num).toInt(),
      loadsWithIssues: (json['loadsWithIssues'] as num).toInt(),
    );

Map<String, dynamic> _$TrackingStatisticsToJson(TrackingStatistics instance) =>
    <String, dynamic>{
      'totalLoads': instance.totalLoads,
      'loadsInTransitToPickup': instance.loadsInTransitToPickup,
      'loadsInTransitToDelivery': instance.loadsInTransitToDelivery,
      'loadsDelivered': instance.loadsDelivered,
      'loadsDelayed': instance.loadsDelayed,
      'loadsWithIssues': instance.loadsWithIssues,
    };

DeliveryConfirmationRequest _$DeliveryConfirmationRequestFromJson(
        Map<String, dynamic> json) =>
    DeliveryConfirmationRequest(
      loadId: (json['loadId'] as num).toInt(),
      location: json['location'] as String,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      notes: json['notes'] as String?,
      recipientName: json['recipientName'] as String?,
      recipientSignature: json['recipientSignature'] as String?,
    );

Map<String, dynamic> _$DeliveryConfirmationRequestToJson(
        DeliveryConfirmationRequest instance) =>
    <String, dynamic>{
      'loadId': instance.loadId,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'notes': instance.notes,
      'recipientName': instance.recipientName,
      'recipientSignature': instance.recipientSignature,
    };

LoadTrackingHistory _$LoadTrackingHistoryFromJson(Map<String, dynamic> json) =>
    LoadTrackingHistory(
      loadId: (json['loadId'] as num).toInt(),
      loadTitle: json['loadTitle'] as String,
      currentStatus:
          $enumDecode(_$TrackingStatusEnumMap, json['currentStatus']),
      currentLatitude: (json['currentLatitude'] as num?)?.toDouble(),
      currentLongitude: (json['currentLongitude'] as num?)?.toDouble(),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
      trackingHistory: (json['trackingHistory'] as List<dynamic>)
          .map((e) => TrackingModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalEntries: (json['totalEntries'] as num).toInt(),
    );

Map<String, dynamic> _$LoadTrackingHistoryToJson(
        LoadTrackingHistory instance) =>
    <String, dynamic>{
      'loadId': instance.loadId,
      'loadTitle': instance.loadTitle,
      'currentStatus': _$TrackingStatusEnumMap[instance.currentStatus]!,
      'currentLatitude': instance.currentLatitude,
      'currentLongitude': instance.currentLongitude,
      'lastUpdated': instance.lastUpdated.toIso8601String(),
      'trackingHistory': instance.trackingHistory,
      'totalEntries': instance.totalEntries,
    };
