package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "vehicles")
@Data
@EqualsAndHashCode(exclude = {"company", "approvedBy", "documents"})
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Vehicle {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Size(max = 20)
    @Column(unique = true)
    private String registrationNumber;
    
    @NotBlank
    @Size(max = 50)
    private String make;
    
    @NotBlank
    @Size(max = 50)
    private String model;
    
    @Column(name = "manufacture_year")
    private Integer year;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    private VehicleType type;
    
    private BigDecimal maxWeight;
    
    private BigDecimal maxVolume;
    
    @Size(max = 20)
    private String weightUnit = "kg";
    
    @Size(max = 20)
    private String volumeUnit = "m3";
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private VehicleStatus status = VehicleStatus.AVAILABLE;

    @Column(columnDefinition = "TEXT")
    private String description;

    // Public visibility and approval fields
    @Builder.Default
    private Boolean isPubliclyVisible = false;

    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private PublicApprovalStatus publicApprovalStatus = PublicApprovalStatus.PENDING;

    private LocalDateTime approvedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "approved_by")
    private User approvedBy;

    // Additional fields for public display
    private BigDecimal dailyRate;

    @Size(max = 10)
    private String currency = "USD";

    @Column(columnDefinition = "TEXT")
    private String features; // JSON string of features

    @Size(max = 500)
    private String imageUrl;

    @Builder.Default
    private Boolean isAvailableForRent = false;

    @Builder.Default
    private Boolean isFeatured = false;

    @Builder.Default
    private Boolean hasInsurance = false;
    
    @Builder.Default
    private Boolean hasFitnessCertificate = false;
    
    @Builder.Default
    private Boolean hasPermits = false;
    
    private LocalDateTime insuranceExpiryDate;
    
    private LocalDateTime fitnessExpiryDate;
    
    private LocalDateTime permitExpiryDate;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;
    
    @OneToMany(mappedBy = "vehicle", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Document> documents = new ArrayList<>();
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    public enum VehicleType {
        TRUCK, TRAILER, FLATBED, REFRIGERATED, TANKER, CONTAINER, VAN, PICKUP
    }
    
    public enum VehicleStatus {
        AVAILABLE, IN_USE, MAINTENANCE, OUT_OF_SERVICE
    }

    public enum PublicApprovalStatus {
        PENDING, APPROVED, REJECTED, SUSPENDED
    }
}
