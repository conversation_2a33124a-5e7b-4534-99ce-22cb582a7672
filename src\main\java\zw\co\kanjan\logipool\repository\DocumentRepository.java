package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.Document;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.Vehicle;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentRepository extends JpaRepository<Document, Long> {
    
    // Find by company
    Page<Document> findByCompany(Company company, Pageable pageable);
    
    List<Document> findByCompanyAndType(Company company, Document.DocumentType type);
    
    List<Document> findByCompanyAndStatus(Company company, Document.DocumentStatus status);
    
    // Find by vehicle
    Page<Document> findByVehicle(Vehicle vehicle, Pageable pageable);
    
    List<Document> findByVehicleAndType(Vehicle vehicle, Document.DocumentType type);
    
    // Find by load
    Page<Document> findByLoad(Load load, Pageable pageable);

    List<Document> findByLoad(Load load);

    List<Document> findByLoadAndType(Load load, Document.DocumentType type);
    
    // Find by type and status
    Page<Document> findByType(Document.DocumentType type, Pageable pageable);
    
    Page<Document> findByStatus(Document.DocumentStatus status, Pageable pageable);
    
    Page<Document> findByTypeAndStatus(Document.DocumentType type, Document.DocumentStatus status, Pageable pageable);
    
    // Find expired documents
    @Query("SELECT d FROM Document d WHERE d.expiryDate < :now AND d.status = 'VERIFIED'")
    List<Document> findExpiredDocuments(@Param("now") LocalDateTime now);
    
    // Find documents requiring verification
    @Query("SELECT d FROM Document d WHERE d.status = 'PENDING' ORDER BY d.createdAt ASC")
    Page<Document> findPendingVerification(Pageable pageable);
    
    // Find required documents for company
    @Query("SELECT d FROM Document d WHERE d.company = :company AND d.isRequired = true")
    List<Document> findRequiredDocumentsByCompany(@Param("company") Company company);
    
    // Find documents by file path (for cleanup)
    Optional<Document> findByFilePath(String filePath);
    
    // Count documents by status for company
    @Query("SELECT COUNT(d) FROM Document d WHERE d.company = :company AND d.status = :status")
    long countByCompanyAndStatus(@Param("company") Company company, @Param("status") Document.DocumentStatus status);
    
    // Find documents expiring soon
    @Query("SELECT d FROM Document d WHERE d.expiryDate BETWEEN :now AND :futureDate AND d.status = 'VERIFIED'")
    List<Document> findDocumentsExpiringSoon(@Param("now") LocalDateTime now, @Param("futureDate") LocalDateTime futureDate);
    
    // Check if company has all required documents
    @Query("SELECT CASE WHEN COUNT(d) > 0 THEN true ELSE false END FROM Document d " +
           "WHERE d.company = :company AND d.type IN :requiredTypes AND d.status = 'VERIFIED'")
    boolean hasAllRequiredDocuments(@Param("company") Company company, @Param("requiredTypes") List<Document.DocumentType> requiredTypes);
    
    // Find documents by file type
    List<Document> findByFileType(String fileType);
    
    // Find large files for cleanup
    @Query("SELECT d FROM Document d WHERE d.fileSize > :sizeLimit")
    List<Document> findLargeFiles(@Param("sizeLimit") Long sizeLimit);

    // Count documents by status
    long countByStatus(Document.DocumentStatus status);
}
