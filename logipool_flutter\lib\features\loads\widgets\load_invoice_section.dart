import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

import '../../../shared/models/load_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/models/invoice_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/services/file_download_service.dart';
import '../../invoices/bloc/invoice_bloc.dart';
import 'invoice_preview_dialog.dart';

class LoadInvoiceSection extends StatefulWidget {
  final LoadModel load;

  const LoadInvoiceSection({
    super.key,
    required this.load,
  });

  @override
  State<LoadInvoiceSection> createState() => _LoadInvoiceSectionState();
}

class _LoadInvoiceSectionState extends State<LoadInvoiceSection> {
  UserModel? _currentUser;
  InvoiceResponse? _currentInvoice;

  @override
  void initState() {
    super.initState();
    _currentUser = context.read<AuthService>().currentUser;
    _checkInvoiceStatus();
  }

  void _checkInvoiceStatus() {
    // Check if invoice exists for this load
    context
        .read<InvoiceBloc>()
        .add(InvoiceGetByLoadId(loadId: widget.load.id!));
  }

  bool _canGenerateInvoice() {
    // Can generate invoice if load is assigned, in transit, or delivered and no invoice exists
    return (widget.load.status == LoadStatus.assigned ||
            widget.load.status == LoadStatus.inTransit ||
            widget.load.status == LoadStatus.delivered) &&
        _currentInvoice == null;
  }

  bool _canViewInvoice() {
    // Can view invoice if it exists
    return _currentInvoice != null;
  }

  void _generateInvoice() {
    // Show invoice preview dialog
    showDialog<void>(
      context: context,
      builder: (context) => InvoicePreviewDialog(load: widget.load),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<InvoiceBloc, InvoiceState>(
      listener: (context, state) {
        if (state is InvoiceLoaded) {
          setState(() {
            _currentInvoice = state.invoice;
          });
        } else if (state is InvoiceNotFound) {
          setState(() {
            _currentInvoice = null;
          });
        } else if (state is InvoiceGenerated) {
          setState(() {
            _currentInvoice = state.invoice;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Invoice generated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        } else if (state is InvoicePdfDownloaded) {
          _handlePdfDownload(state.pdfBytes);
        } else if (state is InvoiceError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: BlocBuilder<InvoiceBloc, InvoiceState>(
        builder: (context, state) {
          final isLoading = state is InvoiceLoading;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInvoiceStatusCard(),
                const SizedBox(height: 16),
                if (_canGenerateInvoice()) _buildGenerateInvoiceCard(isLoading),
                if (_canViewInvoice()) _buildViewInvoiceCard(),
                const SizedBox(height: 16),
                _buildPaymentInfoCard(),
                const SizedBox(height: 16),
                _buildInvoiceHistoryCard(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInvoiceStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Invoice Status',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color:
                        _currentInvoice != null ? Colors.green : Colors.orange,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  _currentInvoice != null ? 'Invoice Generated' : 'No Invoice',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _currentInvoice != null
                  ? 'An invoice has been generated for this load.'
                  : widget.load.status == LoadStatus.delivered
                      ? 'Load is delivered. You can now generate an invoice.'
                      : 'Invoice can be generated once the load is delivered.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenerateInvoiceCard(bool isLoading) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.add_circle,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Generate Invoice',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Generate an invoice for this completed load. The invoice will include all relevant details and can be sent to the client.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: isLoading ? null : _generateInvoice,
                icon: isLoading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.receipt_long),
                label: Text(isLoading ? 'Generating...' : 'Generate Invoice'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildViewInvoiceCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.visibility,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Invoice Details',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInvoiceDetail(
                'Invoice Number', _currentInvoice?.invoiceNumber ?? 'N/A'),
            _buildInvoiceDetail(
                'Generated Date',
                _currentInvoice?.createdAt != null
                    ? DateFormat('MMM dd, yyyy')
                        .format(_currentInvoice!.createdAt!)
                    : 'N/A'),
            _buildInvoiceDetail(
                'Amount',
                _currentInvoice != null
                    ? '\$${_currentInvoice!.totalAmountValue.toStringAsFixed(2)}'
                    : '\$${widget.load.estimatedValue?.toStringAsFixed(2) ?? '0.00'}'),
            _buildInvoiceDetail('Status', _currentInvoice!.status.displayName),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _viewInvoice(),
                    icon: const Icon(Icons.visibility),
                    label: const Text('View'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _downloadInvoice(),
                    icon: const Icon(Icons.download),
                    label: const Text('Download'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.payment,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Payment Information',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildPaymentDetail('Payment Type',
                widget.load.paymentType?.displayName ?? 'Not specified'),
            _buildPaymentDetail(
                'Payment Rate',
                widget.load.paymentRate != null
                    ? '\$${widget.load.paymentRate!.toStringAsFixed(2)} ${widget.load.paymentUnit ?? ''}'
                    : 'Not specified'),
            _buildPaymentDetail(
                'Estimated Value',
                widget.load.estimatedValue != null
                    ? '\$${widget.load.estimatedValue!.toStringAsFixed(2)}'
                    : 'Not specified'),
            _buildPaymentDetail('Payment Status', 'Pending'),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetail(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceHistoryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.history,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                Text(
                  'Invoice History',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // TODO: Implement invoice history timeline
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Invoice history will be displayed here once implemented.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _viewInvoice() {
    if (_currentInvoice == null) return;

    // Navigate to invoice details using go_router
    context.go('/payments/invoices/${_currentInvoice!.id}');
  }

  void _downloadInvoice() {
    if (_currentInvoice == null || _currentInvoice!.id == null) return;

    // Trigger invoice PDF download
    context.read<InvoiceBloc>().add(
          InvoiceDownloadPdf(invoiceId: _currentInvoice!.id!),
        );
  }

  Future<void> _handlePdfDownload(List<int> pdfBytes) async {
    try {
      // Generate a filename for the invoice using invoice number if available
      var fileName = 'Invoice.pdf';

      if (_currentInvoice?.invoiceNumber != null) {
        fileName = 'Invoice_${_currentInvoice!.invoiceNumber}.pdf';
      } else {
        // Fallback to timestamp if invoice number is not available
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        fileName = 'Invoice_$timestamp.pdf';
      }

      // Convert List<int> to Uint8List
      final uint8List = Uint8List.fromList(pdfBytes);

      // Save the PDF file
      final filePath = await FileDownloadService.savePdfBytes(
        pdfBytes: uint8List,
        fileName: fileName,
      );

      if (!mounted) {
        return;
      }

      if (filePath != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Invoice PDF saved to: $filePath'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to save invoice PDF'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } on Exception catch (e) {
      if (!mounted) {
        return;
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving PDF: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
