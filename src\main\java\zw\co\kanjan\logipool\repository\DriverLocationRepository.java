package zw.co.kanjan.logipool.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import zw.co.kanjan.logipool.entity.Company;
import zw.co.kanjan.logipool.entity.DriverLocation;
import zw.co.kanjan.logipool.entity.Load;
import zw.co.kanjan.logipool.entity.User;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface DriverLocationRepository extends JpaRepository<DriverLocation, Long> {
    
    // Find latest location for driver
    @Query("SELECT dl FROM DriverLocation dl WHERE dl.driver = :driver AND dl.status = 'ACTIVE' ORDER BY dl.timestamp DESC")
    Optional<DriverLocation> findLatestLocationByDriver(@Param("driver") User driver);
    
    // Find latest location for driver on specific load
    @Query("SELECT dl FROM DriverLocation dl WHERE dl.driver = :driver AND dl.load = :load AND dl.status = 'ACTIVE' ORDER BY dl.timestamp DESC")
    Optional<DriverLocation> findLatestLocationByDriverAndLoad(@Param("driver") User driver, @Param("load") Load load);
    
    // Find all active drivers for company
    @Query("SELECT DISTINCT dl.driver FROM DriverLocation dl WHERE dl.company = :company AND dl.status = 'ACTIVE' AND dl.timestamp > :since")
    List<User> findActiveDriversByCompany(@Param("company") Company company, @Param("since") LocalDateTime since);
    
    // Find location history for driver
    Page<DriverLocation> findByDriverOrderByTimestampDesc(User driver, Pageable pageable);
    
    // Find location history for load
    Page<DriverLocation> findByLoadOrderByTimestampDesc(Load load, Pageable pageable);
    
    // Find location history for driver on specific load
    Page<DriverLocation> findByDriverAndLoadOrderByTimestampDesc(User driver, Load load, Pageable pageable);
    
    // Find locations within time range
    @Query("SELECT dl FROM DriverLocation dl WHERE dl.driver = :driver AND dl.timestamp BETWEEN :startTime AND :endTime ORDER BY dl.timestamp DESC")
    List<DriverLocation> findByDriverAndTimeRange(@Param("driver") User driver, 
                                                  @Param("startTime") LocalDateTime startTime, 
                                                  @Param("endTime") LocalDateTime endTime);
    
    // Find locations within geographic bounds
    @Query("SELECT dl FROM DriverLocation dl WHERE dl.latitude BETWEEN :minLat AND :maxLat AND dl.longitude BETWEEN :minLon AND :maxLon AND dl.timestamp > :since")
    List<DriverLocation> findLocationsInBounds(@Param("minLat") Double minLat, 
                                              @Param("maxLat") Double maxLat,
                                              @Param("minLon") Double minLon, 
                                              @Param("maxLon") Double maxLon,
                                              @Param("since") LocalDateTime since);
    
    // Find shared locations for company
    @Query("SELECT dl FROM DriverLocation dl WHERE dl.company = :company AND dl.isShared = true AND dl.status = 'ACTIVE' AND dl.timestamp > :since ORDER BY dl.timestamp DESC")
    List<DriverLocation> findSharedLocationsByCompany(@Param("company") Company company, @Param("since") LocalDateTime since);
    
    // Find on-duty drivers
    @Query("SELECT dl FROM DriverLocation dl WHERE dl.company = :company AND dl.isOnDuty = true AND dl.status = 'ACTIVE' AND dl.timestamp > :since ORDER BY dl.timestamp DESC")
    List<DriverLocation> findOnDutyDriversByCompany(@Param("company") Company company, @Param("since") LocalDateTime since);
    
    // Find drivers near location
    @Query(value = "SELECT * FROM driver_locations dl WHERE " +
           "dl.status = 'ACTIVE' AND dl.timestamp > :since AND " +
           "(6371 * acos(cos(radians(:lat)) * cos(radians(dl.latitude)) * " +
           "cos(radians(dl.longitude) - radians(:lon)) + sin(radians(:lat)) * " +
           "sin(radians(dl.latitude)))) < :radiusKm " +
           "ORDER BY dl.timestamp DESC", nativeQuery = true)
    List<DriverLocation> findDriversNearLocation(@Param("lat") Double latitude, 
                                                @Param("lon") Double longitude, 
                                                @Param("radiusKm") Double radiusKm,
                                                @Param("since") LocalDateTime since);
    
    // Clean up old locations
    @Modifying
    @Query("DELETE FROM DriverLocation dl WHERE dl.timestamp < :cutoffTime AND dl.status = 'HISTORICAL'")
    int deleteOldLocations(@Param("cutoffTime") LocalDateTime cutoffTime);
    
    // Update location status to historical
    @Modifying
    @Query("UPDATE DriverLocation dl SET dl.status = 'HISTORICAL' WHERE dl.driver = :driver AND dl.status = 'ACTIVE' AND dl.timestamp < :cutoffTime")
    int markLocationsAsHistorical(@Param("driver") User driver, @Param("cutoffTime") LocalDateTime cutoffTime);
    
    // Count active locations for driver
    @Query("SELECT COUNT(dl) FROM DriverLocation dl WHERE dl.driver = :driver AND dl.status = 'ACTIVE' AND dl.timestamp > :since")
    Long countActiveLocationsByDriver(@Param("driver") User driver, @Param("since") LocalDateTime since);
    
    // Find expired locations
    @Query("SELECT dl FROM DriverLocation dl WHERE dl.expiresAt IS NOT NULL AND dl.expiresAt < :now AND dl.status = 'ACTIVE'")
    List<DriverLocation> findExpiredLocations(@Param("now") LocalDateTime now);
    
    // Find locations by session
    List<DriverLocation> findBySessionIdOrderByTimestampDesc(String sessionId);
    
    // Find locations by device
    List<DriverLocation> findByDeviceIdOrderByTimestampDesc(String deviceId);
    
    // Check if driver has recent location
    @Query("SELECT CASE WHEN COUNT(dl) > 0 THEN true ELSE false END FROM DriverLocation dl WHERE dl.driver = :driver AND dl.timestamp > :since")
    boolean hasRecentLocation(@Param("driver") User driver, @Param("since") LocalDateTime since);
}
