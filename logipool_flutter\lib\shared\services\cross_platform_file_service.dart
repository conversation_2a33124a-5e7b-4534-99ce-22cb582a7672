import 'dart:io';

import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';

/// Represents a file that can be uploaded across platforms
class CrossPlatformFile {
  final String name;
  final Uint8List bytes;
  final String? mimeType;
  final int size;

  CrossPlatformFile({
    required this.name,
    required this.bytes,
    required this.size,
    this.mimeType,
  });

  /// Create from PlatformFile (from file_picker)
  static Future<CrossPlatformFile> fromPlatformFile(
      PlatformFile platformFile) async {
    Uint8List bytes;

    if (kIsWeb) {
      // On web, bytes are directly available
      bytes = platformFile.bytes!;
    } else {
      // On mobile/desktop, read from file path
      final file = File(platformFile.path!);
      bytes = await file.readAsBytes();
    }

    return CrossPlatformFile(
      name: platformFile.name,
      bytes: bytes,
      mimeType: _getMimeType(platformFile.name),
      size: platformFile.size,
    );
  }

  /// Create MultipartFile for upload
  MultipartFile toMultipartFile() {
    return MultipartFile.fromBytes(
      bytes,
      filename: name,
    );
  }

  /// Get MIME type from file extension
  static String? _getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return 'application/pdf';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'doc':
        return 'application/msword';
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      case 'txt':
        return 'text/plain';
      case 'csv':
        return 'text/csv';
      case 'xls':
        return 'application/vnd.ms-excel';
      case 'xlsx':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      default:
        return null;
    }
  }
}

/// Cross-platform file service that works on both mobile and web
class CrossPlatformFileService {
  /// Pick a file using file_picker (works on all platforms)
  static Future<CrossPlatformFile?> pickFile({
    List<String>? allowedExtensions,
    FileType type = FileType.any,
  }) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: type,
        allowedExtensions: allowedExtensions,
        allowMultiple: false,
        withData: true, // Important for web compatibility
      );

      if (result != null && result.files.isNotEmpty) {
        final platformFile = result.files.first;
        return await CrossPlatformFile.fromPlatformFile(platformFile);
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error picking file: $e');
      }
      rethrow;
    }
  }

  /// Pick an image file specifically
  static Future<CrossPlatformFile?> pickImage() async {
    return await pickFile(
      type: FileType.custom,
      allowedExtensions: ['jpg', 'jpeg', 'png', 'gif'],
    );
  }

  /// Pick a document file specifically
  static Future<CrossPlatformFile?> pickDocument() async {
    return await pickFile(
      type: FileType.custom,
      allowedExtensions: ['pdf', 'doc', 'docx', 'txt', 'csv', 'xls', 'xlsx'],
    );
  }

  /// Pick any allowed document type for LogiPool
  static Future<CrossPlatformFile?> pickLogiPoolDocument() async {
    return await pickFile(
      type: FileType.custom,
      allowedExtensions: [
        'pdf',
        'jpg',
        'jpeg',
        'png',
        'doc',
        'docx',
        'txt',
        'csv',
        'xls',
        'xlsx'
      ],
    );
  }

  /// Validate file size
  static bool isValidFileSize(CrossPlatformFile file, {int maxSizeInMB = 10}) {
    final maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return file.size <= maxSizeInBytes;
  }

  /// Validate file type
  static bool isValidFileType(
      CrossPlatformFile file, List<String> allowedExtensions) {
    final extension = file.name.split('.').last.toLowerCase();
    return allowedExtensions.contains(extension);
  }

  /// Get file size in human readable format
  static String getFileSizeString(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
}
