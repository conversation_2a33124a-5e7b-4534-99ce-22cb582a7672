import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../shared/models/company_model.dart';
import '../../../shared/models/user_model.dart';
import '../../../shared/services/auth_service.dart';
import '../../../shared/widgets/unified_header.dart';
import '../../../shared/widgets/loading_widget.dart';
import '../../../shared/widgets/error_widget.dart';
import '../../../core/constants/app_constants.dart';
import '../bloc/company_bloc.dart';

class CompanyDetailsScreen extends StatefulWidget {
  final int companyId;

  const CompanyDetailsScreen({
    super.key,
    required this.companyId,
  });

  @override
  State<CompanyDetailsScreen> createState() => _CompanyDetailsScreenState();
}

class _CompanyDetailsScreenState extends State<CompanyDetailsScreen> {
  @override
  void initState() {
    super.initState();
    context
        .read<CompanyBloc>()
        .add(CompanyFetchByIdRequested(companyId: widget.companyId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UnifiedHeader(
        title: 'Company Details',
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            onPressed: () {
              context
                  .read<CompanyBloc>()
                  .add(CompanyFetchByIdRequested(companyId: widget.companyId));
            },
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: BlocConsumer<CompanyBloc, CompanyState>(
        listener: (context, state) {
          if (state is CompanyError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: Colors.red,
              ),
            );
          }
        },
        builder: (context, state) {
          if (state is CompanyLoading) {
            return const LoadingWidget();
          }

          if (state is CompanySuccess) {
            return _buildCompanyDetailsView(context, state.company);
          }

          if (state is CompanyError) {
            return CustomErrorWidget(
              message: state.message,
              onRetry: () {
                context.read<CompanyBloc>().add(
                    CompanyFetchByIdRequested(companyId: widget.companyId));
              },
            );
          }

          return const LoadingWidget();
        },
      ),
    );
  }

  Widget _buildCompanyDetailsView(BuildContext context, CompanyModel company) {
    return RefreshIndicator(
      onRefresh: () async {
        context
            .read<CompanyBloc>()
            .add(CompanyFetchByIdRequested(companyId: widget.companyId));
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildCompanyHeader(context, company),
            const SizedBox(height: 24),
            _buildVerificationStatus(context, company),
            const SizedBox(height: 24),
            _buildCompanyStats(context, company),
            const SizedBox(height: 24),
            _buildCompanyDetails(context, company),
            const SizedBox(height: 24),
            _buildContactInfo(context, company),
            const SizedBox(height: 24),
            _buildActionButtons(context, company),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyHeader(BuildContext context, CompanyModel company) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: theme.colorScheme.primary,
                  child: Text(
                    company.name.isNotEmpty
                        ? company.name[0].toUpperCase()
                        : 'C',
                    style: theme.textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        company.name,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        company.type.displayName,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      if (company.description != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          company.description!,
                          style: theme.textTheme.bodyMedium,
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVerificationStatus(BuildContext context, CompanyModel company) {
    final theme = Theme.of(context);
    final status = company.verificationStatus;

    Color statusColor;
    IconData statusIcon;

    switch (status) {
      case VerificationStatus.verified:
        statusColor = Colors.green;
        statusIcon = Icons.verified;
        break;
      case VerificationStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.pending;
        break;
      case VerificationStatus.rejected:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      case VerificationStatus.suspended:
        statusColor = Colors.red;
        statusIcon = Icons.block;
        break;
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(statusIcon, color: statusColor, size: 32),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Verification Status',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    status.displayName,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    status.description,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyStats(BuildContext context, CompanyModel company) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Company Statistics',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Rating',
                    '${company.rating.toStringAsFixed(1)}/5.0',
                    Icons.star,
                    Colors.amber,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total Jobs',
                    company.totalJobs.toString(),
                    Icons.work,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Completed',
                    company.completedJobs.toString(),
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Success Rate',
                    '${company.completionRate.toStringAsFixed(1)}%',
                    Icons.trending_up,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildCompanyDetails(BuildContext context, CompanyModel company) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Company Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow('Registration Number', company.registrationNumber),
            if (company.taxNumber != null)
              _buildDetailRow('Tax Number', company.taxNumber!),
            _buildDetailRow('Address', company.address),
            _buildDetailRow('City', company.city),
            _buildDetailRow('Country', company.country),
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfo(BuildContext context, CompanyModel company) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Contact Information',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (company.phoneNumber != null)
              _buildContactRow(
                'Phone',
                company.phoneNumber!,
                Icons.phone,
                () => _launchPhone(company.phoneNumber!),
              ),
            if (company.email != null)
              _buildContactRow(
                'Email',
                company.email!,
                Icons.email,
                () => _launchEmail(company.email!),
              ),
            if (company.website != null)
              _buildContactRow(
                'Website',
                company.website!,
                Icons.language,
                () => _launchWebsite(company.website!),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactRow(
      String label, String value, IconData icon, VoidCallback onTap) {
    final theme = Theme.of(context);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: InkWell(
              onTap: onTap,
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      value,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.primary,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    icon,
                    size: 16,
                    color: theme.colorScheme.primary,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, CompanyModel company) {
    return FutureBuilder<UserModel?>(
      future: context.read<AuthService>().getCurrentUser(),
      builder: (context, snapshot) {
        final currentUser = snapshot.data;
        final isClient = currentUser?.role == AppConstants.roleClient;

        return Column(
          children: [
            // Contact actions for clients
            if (isClient) ...[
              Row(
                children: [
                  if (company.phoneNumber != null)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _launchPhone(company.phoneNumber!),
                        icon: const Icon(Icons.phone),
                        label: const Text('Call'),
                      ),
                    ),
                  if (company.phoneNumber != null && company.email != null)
                    const SizedBox(width: 16),
                  if (company.email != null)
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _launchEmail(company.email!),
                        icon: const Icon(Icons.email),
                        label: const Text('Email'),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 16),
            ],
            // View loads from this company
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  context.push('/loads?companyId=${company.id}');
                },
                icon: const Icon(Icons.local_shipping),
                label: const Text('View Company Loads'),
              ),
            ),
          ],
        );
      },
    );
  }

  Future<void> _launchPhone(String phoneNumber) async {
    final uri = Uri.parse('tel:$phoneNumber');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not launch phone dialer'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _launchEmail(String email) async {
    final uri = Uri.parse('mailto:$email');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not launch email client'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _launchWebsite(String website) async {
    String url = website;
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }

    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Could not launch website'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
