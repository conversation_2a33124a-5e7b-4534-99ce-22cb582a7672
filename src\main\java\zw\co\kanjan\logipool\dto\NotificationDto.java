package zw.co.kanjan.logipool.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationDto {
    private String title;
    private String message;
    private String type;
    private LocalDateTime timestamp;
    private Map<String, Object> data;
    private boolean read;
    private String priority; // LOW, MEDIUM, HIGH, URGENT
}
