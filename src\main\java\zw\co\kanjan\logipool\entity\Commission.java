package zw.co.kanjan.logipool.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Entity
@Table(name = "commissions")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Commission {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull
    @Positive
    private BigDecimal loadAmount;
    
    @NotNull
    private BigDecimal commissionRate;
    
    @NotNull
    @Positive
    private BigDecimal commissionAmount;
    
    @NotNull
    @Positive
    private BigDecimal netAmount;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private CommissionStatus status = CommissionStatus.PENDING;
    
    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private CommissionType type;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(columnDefinition = "TEXT")
    private String notes;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "load_id")
    private Load load;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "bid_id")
    private Bid bid;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "payment_id")
    private Payment payment;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "invoice_id")
    private Invoice invoice;
    
    private LocalDateTime calculatedAt;
    
    private LocalDateTime collectedAt;
    
    @CreationTimestamp
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    private LocalDateTime updatedAt;
    
    public enum CommissionStatus {
        PENDING, CALCULATED, COLLECTED, PAID_OUT, DISPUTED, REFUNDED
    }
    
    public enum CommissionType {
        PLATFORM_COMMISSION, PAYMENT_PROCESSING_FEE, TRANSACTION_FEE, PENALTY_FEE
    }
}
