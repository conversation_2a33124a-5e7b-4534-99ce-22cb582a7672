# Test Data Seeder

The LogiPool application includes a comprehensive test data seeder that generates realistic dummy data for end-to-end testing. This seeder creates a complete dataset including users, companies, vehicles, equipment, loads, bids, and tracking information.

## Overview

The `TestDataSeeder` component automatically populates the database with realistic test data when enabled. It creates:

- **Admin Users**: System administrators with full access
- **Client Users**: Users who post loads for transportation
- **Transporter Users**: Users who own logistics companies
- **Companies**: Logistics companies with various verification statuses
- **Company Members**: Team members with different roles and permissions
- **Vehicles**: Fleet vehicles with specifications and documentation
- **Equipment**: Logistics equipment like cranes, forklifts, etc.
- **Loads**: Transportation jobs with various statuses and requirements
- **Bids**: Company bids on available loads
- **Tracking Data**: Location and status updates for loads in transit

## Configuration

The test data seeder is controlled by the `app.test-data.enabled` property:

```properties
# Enable test data seeding
app.test-data.enabled=true

# Disable test data seeding (default)
app.test-data.enabled=false
```

### Environment-Specific Configuration

- **Production** (`application.properties`): Disabled by default
- **Development** (`application-dev.properties`): Enabled for development
- **Testing** (`application-test.properties`): Enabled for testing

## Generated Data

### Users
- **3 Admin Users**: Including a main admin with email `<EMAIL>` and password `admin123`
- **15 Client Users**: With emails `<EMAIL>` to `<EMAIL>`, password `password123`
- **12 Transporter Users**: With emails `<EMAIL>` to `<EMAIL>`, password `password123`

### Companies
- **12 Logistics Companies**: One for each transporter user
- Various company types: Logistics Provider, Client, Both
- Different verification statuses: Pending, Verified, Rejected, Suspended
- Realistic company information including registration numbers, addresses, and contact details

### Company Members
- **Owner memberships** for each company user
- **1-3 additional members** per company with various roles:
  - Manager, Driver, Dispatcher, Accountant, Viewer
- Appropriate permissions based on roles

### Vehicles
- **2-6 vehicles per company** (total ~60 vehicles)
- Various vehicle types: Truck, Trailer, Flatbed, Refrigerated, Tanker, Container, Van, Pickup
- Realistic specifications including weight/volume capacity
- Different statuses: Available, In Use, Maintenance, Out of Service
- Insurance and certification information

### Equipment
- **1-4 equipment items per company** (total ~30 items)
- Equipment types: Crane, Forklift, Excavator, Bulldozer, Loader, etc.
- Capacity specifications and maintenance schedules
- Rental availability and operator requirements

### Loads
- **50 transportation loads** with realistic details
- Various cargo types: General Cargo, Electronics, Furniture, Machinery, etc.
- Different load types: Local, Regional, Contract, Once-off
- Payment types: Per KM, Per Tonne, Fixed Rate, Negotiable
- Load statuses: Posted, Bidding Closed, Assigned, In Transit, Delivered, Cancelled
- Priority levels: Low, Normal, High, Urgent
- Realistic pickup/delivery locations within Zimbabwe

### Bids
- **1-5 bids per posted load** (total ~150 bids)
- Competitive pricing (80%-120% of asking price)
- Professional proposals and notes
- Various bid statuses: Pending, Accepted, Rejected, Withdrawn

### Tracking Data
- **2-6 tracking entries per active load** (total ~100 entries)
- Realistic tracking statuses and location updates
- Automated and manual tracking entries
- Progress notes and status updates

## Usage

### Running with Test Data

1. **Development Environment**:
   ```bash
   mvn spring-boot:run -Dspring.profiles.active=dev
   ```

2. **Specific Profile**:
   ```bash
   mvn spring-boot:run -Dspring.profiles.active=dev -Dapp.test-data.enabled=true
   ```

3. **Command Line Override**:
   ```bash
   java -jar logipool.jar --app.test-data.enabled=true
   ```

### Test Credentials

After seeding, you can log in with these test accounts using email addresses:

#### Admin Users
- Email: `<EMAIL>`, Password: `admin123`
- Email: `<EMAIL>`, Password: `password123`
- Email: `<EMAIL>`, Password: `password123`

#### Client Users
- Email: `<EMAIL>` to `<EMAIL>`, Password: `password123`

#### Transporter Users
- Email: `<EMAIL>` to `<EMAIL>`, Password: `password123`

### Data Safety

The seeder includes a safety check that prevents seeding if the database already contains more than 10 users. This prevents accidental data duplication in production environments.

## Customization

To customize the generated data, modify the arrays in `TestDataSeeder.java`:

- `firstNames` and `lastNames`: User names
- `companyNames`: Company names
- `cities`: Zimbabwe cities for locations
- `cargoTypes`: Types of cargo for loads
- `vehicleMakes` and `vehicleModels`: Vehicle specifications

## Database Reset

To reset the database and regenerate test data:

1. **Drop and recreate the database**:
   ```sql
   DROP DATABASE logipool;
   CREATE DATABASE logipool;
   ```

2. **Restart the application** with test data enabled

## Troubleshooting

### Common Issues

1. **Seeder not running**: Check that `app.test-data.enabled=true` is set
2. **Data not generated**: Verify database is empty or has fewer than 10 users
3. **Constraint violations**: Ensure database schema is up to date

### Logs

The seeder provides detailed logging:
```
🌱 Starting test data seeding...
Creating admin users...
Created 3 admin users
Creating client users...
Created 15 client users
...
✅ Test data seeding completed successfully!
📊 Created: 30 users, 12 companies, 50 loads
```

## Performance

The seeder typically takes 10-30 seconds to complete, depending on database performance. It uses batch operations and transactions for efficiency.
