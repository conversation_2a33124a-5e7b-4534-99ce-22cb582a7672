import 'dart:convert';
import 'package:dio/dio.dart';
import '../models/notification_model.dart';
import '../models/paginated_response.dart';
import '../../core/network/api_client.dart';

class NotificationApiService {
  final ApiClient _apiClient;

  NotificationApiService(this._apiClient);

  // Get user notifications with pagination
  Future<PaginatedResponse<NotificationModel>> getNotifications({
    int page = 0,
    int size = 20,
    String? sort,
  }) async {
    try {
      final response = await _apiClient.get(
        '/notifications',
        queryParameters: {
          'page': page,
          'size': size,
          if (sort != null) 'sort': sort,
        },
      );

      return PaginatedResponse<NotificationModel>.fromJson(
        response.data,
        (json) => NotificationModel.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw Exception('Failed to load notifications: $e');
    }
  }

  // Get unread notifications count
  Future<int> getUnreadCount() async {
    try {
      final response = await _apiClient.get('/notifications/unread/count');
      final data = response.data;
      if (data != null && data is Map<String, dynamic>) {
        final count = data['count'];
        if (count != null) {
          return count is int ? count : int.tryParse(count.toString()) ?? 0;
        }
      }
      return 0;
    } catch (e) {
      throw Exception('Failed to get unread count: $e');
    }
  }

  // Mark notifications as read
  Future<void> markAsRead(List<int> notificationIds) async {
    try {
      final request = NotificationMarkReadRequest(
        notificationIds: notificationIds,
      );

      await _apiClient.post(
        '/notifications/mark-read',
        data: request.toJson(),
      );
    } catch (e) {
      throw Exception('Failed to mark notifications as read: $e');
    }
  }

  // Mark single notification as read
  Future<void> markSingleAsRead(int notificationId) async {
    try {
      await _apiClient.post('/notifications/$notificationId/mark-read');
    } catch (e) {
      throw Exception('Failed to mark notification as read: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      await _apiClient.post('/notifications/mark-all-read');
    } catch (e) {
      throw Exception('Failed to mark all notifications as read: $e');
    }
  }

  // Delete notifications
  Future<void> deleteNotifications(List<int> notificationIds) async {
    try {
      final request = NotificationDeleteRequest(
        notificationIds: notificationIds,
      );

      await _apiClient.delete(
        '/notifications',
        data: request.toJson(),
      );
    } catch (e) {
      throw Exception('Failed to delete notifications: $e');
    }
  }

  // Delete single notification
  Future<void> deleteSingleNotification(int notificationId) async {
    try {
      await _apiClient.delete('/notifications/$notificationId');
    } catch (e) {
      throw Exception('Failed to delete notification: $e');
    }
  }

  // Get notifications by type
  Future<List<NotificationModel>> getNotificationsByType(
    NotificationType type,
  ) async {
    try {
      final response = await _apiClient.get('/notifications/type/${type.name}');

      final List<dynamic> data = response.data;
      return data
          .map((json) =>
              NotificationModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to load notifications by type: $e');
    }
  }

  // Get notification summary
  Future<NotificationSummary> getNotificationSummary() async {
    try {
      final response = await _apiClient.get('/notifications/summary');
      return NotificationSummary.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load notification summary: $e');
    }
  }

  // Get notification settings
  Future<NotificationSettings> getNotificationSettings() async {
    try {
      final response = await _apiClient.get('/notifications/settings');
      return NotificationSettings.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to load notification settings: $e');
    }
  }

  // Update notification settings
  Future<void> updateNotificationSettings(
    NotificationSettings settings,
  ) async {
    try {
      await _apiClient.put(
        '/notifications/settings',
        data: settings.toJson(),
      );
    } catch (e) {
      throw Exception('Failed to update notification settings: $e');
    }
  }

  // Test notification (for development/testing)
  Future<void> sendTestNotification() async {
    try {
      await _apiClient.post('/notifications/test');
    } catch (e) {
      throw Exception('Failed to send test notification: $e');
    }
  }

  // Get notification statistics
  Future<Map<String, dynamic>> getNotificationStats() async {
    try {
      final response = await _apiClient.get('/notifications/stats');
      return response.data;
    } catch (e) {
      throw Exception('Failed to load notification statistics: $e');
    }
  }

  // Search notifications
  Future<PaginatedResponse<NotificationModel>> searchNotifications({
    required String query,
    int page = 0,
    int size = 20,
    NotificationType? type,
    NotificationPriority? priority,
    bool? read,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'q': query,
        'page': page,
        'size': size,
      };

      if (type != null) {
        queryParams['type'] = type.name;
      }
      if (priority != null) {
        queryParams['priority'] = priority.name;
      }
      if (read != null) {
        queryParams['read'] = read;
      }
      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String();
      }

      final response = await _apiClient.get(
        '/notifications/search',
        queryParameters: queryParams,
      );

      return PaginatedResponse<NotificationModel>.fromJson(
        response.data as Map<String, dynamic>,
        (json) => NotificationModel.fromJson(json as Map<String, dynamic>),
      );
    } catch (e) {
      throw Exception('Failed to search notifications: $e');
    }
  }

  // Get notifications for specific reference
  Future<List<NotificationModel>> getNotificationsForReference({
    required int referenceId,
    required String referenceType,
  }) async {
    try {
      final response = await _apiClient.get(
        '/notifications/reference',
        queryParameters: {
          'referenceId': referenceId,
          'referenceType': referenceType,
        },
      );

      final List<dynamic> data = response.data as List<dynamic>;
      return data
          .map((json) =>
              NotificationModel.fromJson(json as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to load notifications for reference: $e');
    }
  }

  // Clear expired notifications
  Future<void> clearExpiredNotifications() async {
    try {
      await _apiClient.delete('/notifications/expired');
    } catch (e) {
      throw Exception('Failed to clear expired notifications: $e');
    }
  }

  // Export notifications
  Future<String> exportNotifications({
    String format = 'json',
    DateTime? startDate,
    DateTime? endDate,
    NotificationType? type,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'format': format,
      };

      if (startDate != null) {
        queryParams['startDate'] = startDate.toIso8601String();
      }
      if (endDate != null) {
        queryParams['endDate'] = endDate.toIso8601String();
      }
      if (type != null) {
        queryParams['type'] = type.name;
      }

      final response = await _apiClient.get(
        '/notifications/export',
        queryParameters: queryParams,
      );

      return response.data['downloadUrl'] as String;
    } catch (e) {
      throw Exception('Failed to export notifications: $e');
    }
  }
}
