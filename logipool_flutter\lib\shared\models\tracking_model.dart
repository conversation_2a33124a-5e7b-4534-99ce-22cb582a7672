import 'package:json_annotation/json_annotation.dart';

part 'tracking_model.g.dart';

@JsonSerializable()
class TrackingModel {
  final int id;
  final String location;
  final double? latitude;
  final double? longitude;
  final TrackingStatus status;
  final String? notes;
  final bool isAutomated;
  final int loadId;
  final int updatedById;
  final String updatedByName;
  final DateTime timestamp;

  const TrackingModel({
    required this.id,
    required this.location,
    this.latitude,
    this.longitude,
    required this.status,
    this.notes,
    required this.isAutomated,
    required this.loadId,
    required this.updatedById,
    required this.updatedByName,
    required this.timestamp,
  });

  factory TrackingModel.fromJson(Map<String, dynamic> json) =>
      _$TrackingModelFromJson(json);

  Map<String, dynamic> toJson() => _$TrackingModelToJson(this);
}

@JsonEnum()
enum TrackingStatus {
  @JsonValue('LOAD_POSTED')
  loadPosted,
  @JsonValue('BID_ACCEPTED')
  bidAccepted,
  @JsonValue('PICKUP_SCHEDULED')
  pickupScheduled,
  @JsonValue('IN_TRANSIT_TO_PICKUP')
  inTransitToPickup,
  @JsonValue('ARRIVED_AT_PICKUP')
  arrivedAtPickup,
  @JsonValue('LOADING_IN_PROGRESS')
  loadingInProgress,
  @JsonValue('LOADED')
  loaded,
  @JsonValue('IN_TRANSIT_TO_DELIVERY')
  inTransitToDelivery,
  @JsonValue('ARRIVED_AT_DELIVERY')
  arrivedAtDelivery,
  @JsonValue('UNLOADING_IN_PROGRESS')
  unloadingInProgress,
  @JsonValue('DELIVERED')
  delivered,
  @JsonValue('DELAYED')
  delayed,
  @JsonValue('ISSUE_REPORTED')
  issueReported;

  String get displayName {
    switch (this) {
      case TrackingStatus.loadPosted:
        return 'Load Posted';
      case TrackingStatus.bidAccepted:
        return 'Bid Accepted';
      case TrackingStatus.pickupScheduled:
        return 'Pickup Scheduled';
      case TrackingStatus.inTransitToPickup:
        return 'In Transit to Pickup';
      case TrackingStatus.arrivedAtPickup:
        return 'Arrived at Pickup';
      case TrackingStatus.loadingInProgress:
        return 'Loading in Progress';
      case TrackingStatus.loaded:
        return 'Loaded';
      case TrackingStatus.inTransitToDelivery:
        return 'In Transit to Delivery';
      case TrackingStatus.arrivedAtDelivery:
        return 'Arrived at Delivery';
      case TrackingStatus.unloadingInProgress:
        return 'Unloading in Progress';
      case TrackingStatus.delivered:
        return 'Delivered';
      case TrackingStatus.delayed:
        return 'Delayed';
      case TrackingStatus.issueReported:
        return 'Issue Reported';
    }
  }

  String get description {
    switch (this) {
      case TrackingStatus.loadPosted:
        return 'Load has been posted and is available for bidding';
      case TrackingStatus.bidAccepted:
        return 'A bid has been accepted for this load';
      case TrackingStatus.pickupScheduled:
        return 'Pickup has been scheduled';
      case TrackingStatus.inTransitToPickup:
        return 'Vehicle is on the way to pickup location';
      case TrackingStatus.arrivedAtPickup:
        return 'Vehicle has arrived at pickup location';
      case TrackingStatus.loadingInProgress:
        return 'Load is being loaded onto the vehicle';
      case TrackingStatus.loaded:
        return 'Load has been successfully loaded';
      case TrackingStatus.inTransitToDelivery:
        return 'Vehicle is on the way to delivery location';
      case TrackingStatus.arrivedAtDelivery:
        return 'Vehicle has arrived at delivery location';
      case TrackingStatus.unloadingInProgress:
        return 'Load is being unloaded from the vehicle';
      case TrackingStatus.delivered:
        return 'Load has been successfully delivered';
      case TrackingStatus.delayed:
        return 'Delivery has been delayed';
      case TrackingStatus.issueReported:
        return 'An issue has been reported with this load';
    }
  }

  bool get isInTransit => this == TrackingStatus.inTransitToPickup || 
                         this == TrackingStatus.inTransitToDelivery;
  
  bool get isCompleted => this == TrackingStatus.delivered;
  
  bool get hasIssue => this == TrackingStatus.delayed || 
                      this == TrackingStatus.issueReported;
}

@JsonSerializable()
class TrackingUpdateRequest {
  final int loadId;
  final String location;
  final double? latitude;
  final double? longitude;
  final TrackingStatus status;
  final String? notes;
  final bool isAutomated;

  const TrackingUpdateRequest({
    required this.loadId,
    required this.location,
    this.latitude,
    this.longitude,
    required this.status,
    this.notes,
    this.isAutomated = false,
  });

  factory TrackingUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$TrackingUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$TrackingUpdateRequestToJson(this);
}

@JsonSerializable()
class LocationUpdateRequest {
  final int loadId;
  final double latitude;
  final double longitude;
  final String? location;
  final double? speed;
  final double? heading;
  final double? accuracy;

  const LocationUpdateRequest({
    required this.loadId,
    required this.latitude,
    required this.longitude,
    this.location,
    this.speed,
    this.heading,
    this.accuracy,
  });

  factory LocationUpdateRequest.fromJson(Map<String, dynamic> json) =>
      _$LocationUpdateRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LocationUpdateRequestToJson(this);
}

@JsonSerializable()
class RealTimeLocation {
  final int loadId;
  final double? latitude;
  final double? longitude;
  final double? speed;
  final double? heading;
  final double? accuracy;
  final DateTime timestamp;
  final TrackingStatus? status;

  const RealTimeLocation({
    required this.loadId,
    this.latitude,
    this.longitude,
    this.speed,
    this.heading,
    this.accuracy,
    required this.timestamp,
    this.status,
  });

  factory RealTimeLocation.fromJson(Map<String, dynamic> json) =>
      _$RealTimeLocationFromJson(json);

  Map<String, dynamic> toJson() => _$RealTimeLocationToJson(this);
}

@JsonSerializable()
class TrackingStatistics {
  final int totalLoads;
  final int loadsInTransitToPickup;
  final int loadsInTransitToDelivery;
  final int loadsDelivered;
  final int loadsDelayed;
  final int loadsWithIssues;

  const TrackingStatistics({
    required this.totalLoads,
    required this.loadsInTransitToPickup,
    required this.loadsInTransitToDelivery,
    required this.loadsDelivered,
    required this.loadsDelayed,
    required this.loadsWithIssues,
  });

  factory TrackingStatistics.fromJson(Map<String, dynamic> json) =>
      _$TrackingStatisticsFromJson(json);

  Map<String, dynamic> toJson() => _$TrackingStatisticsToJson(this);
}

@JsonSerializable()
class DeliveryConfirmationRequest {
  final int loadId;
  final String location;
  final double? latitude;
  final double? longitude;
  final String? notes;
  final String? recipientName;
  final String? recipientSignature;

  const DeliveryConfirmationRequest({
    required this.loadId,
    required this.location,
    this.latitude,
    this.longitude,
    this.notes,
    this.recipientName,
    this.recipientSignature,
  });

  factory DeliveryConfirmationRequest.fromJson(Map<String, dynamic> json) =>
      _$DeliveryConfirmationRequestFromJson(json);

  Map<String, dynamic> toJson() => _$DeliveryConfirmationRequestToJson(this);
}

@JsonSerializable()
class LoadTrackingHistory {
  final int loadId;
  final String loadTitle;
  final TrackingStatus currentStatus;
  final double? currentLatitude;
  final double? currentLongitude;
  final DateTime lastUpdated;
  final List<TrackingModel> trackingHistory;
  final int totalEntries;

  const LoadTrackingHistory({
    required this.loadId,
    required this.loadTitle,
    required this.currentStatus,
    this.currentLatitude,
    this.currentLongitude,
    required this.lastUpdated,
    required this.trackingHistory,
    required this.totalEntries,
  });

  factory LoadTrackingHistory.fromJson(Map<String, dynamic> json) =>
      _$LoadTrackingHistoryFromJson(json);

  Map<String, dynamic> toJson() => _$LoadTrackingHistoryToJson(this);
}
